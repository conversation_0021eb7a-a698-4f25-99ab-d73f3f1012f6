import com.google.protobuf.gradle.id

plugins {
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.protobuf)

    id("maven-publish")
}


dependencies {
    // Multiplier
    compileOnly(libs.multiplier.country.service.schema) {
        exclude("io.grpc", "grpc-protobuf")
    }

    compileOnly(libs.protobuf.kotlin)
    compileOnly(libs.grpc.kotlin.stub)
    compileOnly(libs.grpc.protobuf)
    compileOnly("com.multiplier.grpc:grpc-common:1.1.6") {
        exclude("io.grpc", "grpc-protobuf")
    }
}

configure<com.google.protobuf.gradle.ProtobufExtension> {
    tasks.getByName<Delete>("clean") {
        delete(generatedFilesBaseDir)
    }
    protoc {
        artifact = "com.google.protobuf:protoc:${libs.versions.protobuf.get()}"
    }
    plugins {
        id("grpc") {
            artifact = "io.grpc:protoc-gen-grpc-java:${libs.versions.grpc.get()}"
        }
        id("grpckt") {
            artifact = "io.grpc:protoc-gen-grpc-kotlin:${libs.versions.grpcKotlin.get()}:jdk8@jar"
        }
    }
    generateProtoTasks {
        all().forEach {
            it.plugins {
                id("grpc")
                id("grpckt")
            }
            it.builtins {
                id("kotlin")
            }
        }
    }
}

configure<JavaPluginExtension> {
    sourceCompatibility = JavaVersion.VERSION_11
    targetCompatibility = JavaVersion.VERSION_11

    withSourcesJar()
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    compilerOptions {
        jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_11)
    }
}

configure<PublishingExtension> {
    publications {
        val versionName: String by rootProject.extra
        create<MavenPublication>("maven") {
            groupId = "${rootProject.group}"
            artifactId = "${rootProject.name}-${project.name}"
            version = versionName

            from(components["java"])
        }
    }
}
