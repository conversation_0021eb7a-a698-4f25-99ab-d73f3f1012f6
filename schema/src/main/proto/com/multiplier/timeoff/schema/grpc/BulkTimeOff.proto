syntax = "proto3";
import "com/multiplier/grpc/common/bulkupload/v1/bulk_upload_field_requirements.proto";
import "com/multiplier/grpc/common/bulkupload/v1/bulk_upload_validation.proto";
import "com/multiplier/grpc/common/bulkupload/v1/bulk_upload_upsert.proto";

package com.multiplier.timeoff.schema;

service BulkUploadApi {
  option deprecated = true;
  rpc GetFieldRequirements(multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest) returns (multiplier.grpc.common.bulkupload.v1.FieldRequirementsResponse) {}
  rpc BulkValidateUpsertInput(com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkRequest) returns (multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkResponse);
  rpc BulkUpsert(com.multiplier.grpc.common.bulkupload.v1.UpsertBulkRequest) returns (com.multiplier.grpc.common.bulkupload.v1.UpsertBulkResponse) {}
}