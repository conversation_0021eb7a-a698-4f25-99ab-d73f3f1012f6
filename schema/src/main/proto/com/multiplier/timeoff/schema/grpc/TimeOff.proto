syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

import "com/multiplier/timeoff/schema/grpc/country/Country.proto";
import "com/multiplier/timeoff/schema/grpc/contract/Contract.proto";
import "com/multiplier/timeoff/schema/grpc/common/TimeOffCommon.proto";

package com.multiplier.timeoff.schema;

option java_multiple_files = true;

enum GrpcTimeOffUnit {
  UNSET_TIME_OFF_UNIT = 0;
  YEARS = 1;
  MONTHS = 2;
  DAYS = 3;
  WEEKS = 4;
}

enum Experience {
  UNSET_EXPERIENCE = 0;
  MEMBER = 1;
  COMPANY = 2;
  OPERATIONS = 3;
}

enum GrpcDefaultTimeoffTypeKey {
  UNSET_TIME_OFF_TYPE_KEY = 0;
  ANNUAL = 1;
  UNPAID = 2;
}

message GrpcTimeOff {
  int64 id = 1;
  int64 contract_id = 2;
  GrpcTimeOffStatus status = 3;
  google.protobuf.Timestamp startDate = 4 [deprecated = true]; // use startDateOnly
  google.protobuf.Timestamp endDate = 5 [deprecated = true]; // use endDateOnly
  GrpcTimeOffSession startSession = 6;
  GrpcTimeOffSession endSession = 7;
  double noOfDays = 8;
  string description = 9;
  GrpcTimeOffType timeOffType = 10;
  google.protobuf.Timestamp approvedOn = 11;
  google.type.Date startDateOnly = 12; // LocalDate version of startDate
  google.type.Date endDateOnly = 13; // LocalDate version of endDate
}

message GrpcTimeOffs {
  repeated GrpcTimeOff timeOffs = 1;
}

message GrpcTimeOffEntitlement {
  double value = 1;
  string type = 2;
  GrpcTimeOffUnit unit = 3;
  bool isRequired = 4;
  string label = 5;
}

message GrpcUpdateTimeOffEntitlementRequest {
  string key = 1;
  double value = 2;
  GrpcTimeOffUnit unit = 3;
}


message GrpcListOfTimeOffEntitlements {
  repeated GrpcTimeOffEntitlement entitlements = 1;
}

message GrpcTimeOffId {
  int64 id = 1;
}

message GrpcContractId {
  int64 id = 2;
}

message GrpcContractIds {
  repeated int64 ids = 1;
}

message GrpcBulkUpdateTimeOffEntitlementsRequest {
  int64 contract_id = 1;
  repeated GrpcUpdateTimeOffEntitlementRequest entitlements = 2;
  optional int64 company_id = 3;

}

message GrpcEmpty {}

message GrpcTimeOffApprovalStatusChangeEvent {
  int64 approverCompanyUserId = 1;
  int64 timeoffId = 2;
  string comment = 3;
  GrpcApprovalItemStatus status = 4;
  bool isForwarding = 5;
  UserDetails currentUserDetails = 6 [deprecated = true];

  enum GrpcApprovalItemStatus {
    UNKNOWN_APPROVAL_ITEM_STATUS = 0;
    APPROVAL_IN_PROGRESS = 1;
    APPROVED = 2;
    REJECTED = 3;
    ACTION_TAKEN = 4;
  }
}

message UserDetails {
  Experience experience = 1;
  int64 userId = 2;
}

message GrpcCountryFilter {
  country.CountryCode countryCode = 1;
  string countryStateCode = 2;

}

message GrpcDefinitionsRequest {
  GrpcCountryFilter countryCode = 1;
  contract.GrpcContractType contractType = 2;
  contract.GrpcContractTerm  contractTerm = 3;
  contract.GrpcContractStatus ContractStatus = 4;
  int64 timeoffTypeId = 5;
  int64 companyId = 6;
}

enum GrpcAllocationBasis {
  ANNUALLY = 0;
  MONTHLY = 1;
  DAILY = 2;
  QUARTERLY = 3;
  WEEKLY = 4;
  ONCE = 5;
}


message GrpcTimeOffValidation {
  double minimum = 1;
  double maximum = 2;
  double defaultValue = 3;
  GrpcTimeOffUnit unit = 4;
  repeated contract.GrpcContractStatus allowedContractStatuses = 5;
}

message GrpcAllocationConfig {
  GrpcAllocationBasis basis = 1;
  bool prorated = 2;
}

enum GrpcLimitValueType {
  FIXED = 0;
  PERCENTAGE = 1;
}

message GrpcCarryForwardLimit {
  GrpcLimitValueType type = 1;
  double value = 2;
  GrpcTimeOffUnit unit = 3;
}

message GrpcCarryForwardConfig {
  GrpcCarryForwardLimit minForEligibility = 1;
  GrpcCarryForwardLimit maxLimit = 2;
}

message GrpcTimeoffConfiguration {
  GrpcAllocationConfig allocation = 1;
  GrpcCarryForwardConfig carryForward = 2;
}

message GrpcTimeOffTypeDefinition {
  string type = 1;
  bool required = 2;
  string label = 3;
  string description = 4;
  int64 typeId = 5;
  string basis = 6;
  repeated GrpcTimeOffValidation validation = 7;
  GrpcTimeoffConfiguration configuration = 8;
}


message GrpcDefinitionsResponse {
  string clause = 1;
  repeated GrpcTimeOffTypeDefinition definitions = 2;
}

message GrpcDateRange {
  google.protobuf.Timestamp startDate = 1;
  google.protobuf.Timestamp endDate = 2;
}

message DateRange {
  google.type.Date startDate = 1;
  google.type.Date endDate = 2;
}

message GetTimeOffsForPayrollRequest {
  repeated int64 contractIds = 1;
  GrpcDateRange intersectionRange = 2;
  google.protobuf.Timestamp approvedOnGreaterThanEqTo = 3; // this is an OR condition with intersectionRange
}

message GetTimeOffsForPayrollCycleRequest {
  repeated int64 contract_ids = 1;
  DateRange time_off_range = 2;
  google.type.Date approved_on_from_inclusive = 3;
  google.type.Date approved_on_to_inclusive = 4;
}

message GrpcTimeOffForPayroll {
  int64 id = 1;
  int64 contract_id = 2;
  GrpcTimeOffStatus status = 3;
  google.protobuf.Timestamp startDate = 4;
  google.protobuf.Timestamp endDate = 5;
  GrpcTimeOffSession startSession = 6;
  GrpcTimeOffSession endSession = 7;
  double totalNoOfDays = 8;
  double noOfDaysWithinCycle = 9;
  string description = 10;
  GrpcTimeOffType timeOffType = 11;
  google.protobuf.Timestamp approvedOn = 12;
}

message GetTimeOffsForPayrollResponse {
  repeated GrpcTimeOffForPayroll timeOffs = 1;
}

message GrpcBulkTimeOffRequest {
  int64 companyId = 1;
  repeated GrpcBulkTimeOffInput inputs = 2;
}

message GrpcBulkRevokeTimeOffRequest {
  int64 companyId = 1;
  repeated string externalTimeOffIds = 2;
}

message GrpcBulkTimeOffInput {
  string externalTimeOffId = 1;        // mapped to TimeOff ID in template
  string employeeId = 2;
  string employeeName = 3;
  string type = 4;         // mapped to timeoff type key
  string noOfDays = 5;
  string startDate = 6;
  string endDate = 7;
  string description = 8;
}

message GrpcBulkValidateTimeOffResponse {
  bool success = 1;
  repeated GrpcBulkValidateTimeOffResultItem items = 2;
}

message GrpcBulkValidateTimeOffResultItem {
  string externalTimeOffId = 1;
  repeated string errors = 2;
}

message GrpcBulkTimeOffResponse {
  bool success = 1;
  string message = 2;
  repeated GrpcBulkResponseItem items = 3;
}

message GrpcBulkResponseItem {
  string externalTimeOffId = 1;
  int64 timeOffId = 2;
  repeated string errors = 3;
}

message GrpcCompanyTimeOffTypesRequest {
  int64 companyId = 1;
}

message GrpcCompanyTimeOffTypesResponse {
  repeated GrpcTimeOffType systemTimeOffTypes = 1;
  repeated GrpcTimeOffType companyTimeOffTypes = 2;
}



service TimeOffService {
  rpc GetTimeOffById(GrpcTimeOffId) returns (GrpcTimeOff) {}
  rpc GetAnnualLeaveType(GrpcEmpty) returns (GrpcTimeOffType) {}
  rpc UpdateTimeOffSchedule(GrpcEmpty) returns (GrpcEmpty) {}
  rpc GetTimeOffEntitlements(GrpcContractId) returns (GrpcListOfTimeOffEntitlements) {}
  rpc BulkUpdateTimeOffEntitlements(GrpcBulkUpdateTimeOffEntitlementsRequest) returns (GrpcEmpty) {}
  rpc SetEntitlementsToDefaultCountryRequirements(GrpcContractId) returns (GrpcEmpty) {} // @deprecated - use SetEntitlementsToDefaultRequirements instead
  rpc SetEntitlementsToDefaultRequirements(GrpcContractIds) returns (GrpcEmpty) {} // only ONBOARDING contracts can use this API to reset entitlements
  rpc RunTimeOffAllocation(GrpcContractId) returns (GrpcEmpty) {}
  rpc onTimeOffApprovalStatusChange(GrpcTimeOffApprovalStatusChangeEvent) returns (GrpcEmpty) {}
  rpc GetDefinitions(GrpcDefinitionsRequest) returns  (GrpcDefinitionsResponse) {}
  rpc GetTimeOffsForPayroll(GetTimeOffsForPayrollRequest) returns (GetTimeOffsForPayrollResponse) {}
  rpc GetTimeOffsForPayrollCycle(GetTimeOffsForPayrollCycleRequest) returns (GetTimeOffsForPayrollResponse) {}
  rpc GetTimeOffsByContractIds(GrpcContractIds) returns (GrpcTimeOffs) {} // any timeoff.status; the caller to deal with statuses itself
  rpc BulkValidateTimeOffUpsert(GrpcBulkTimeOffRequest) returns (GrpcBulkValidateTimeOffResponse) {}
  rpc BulkUpsertTimeOffs(GrpcBulkTimeOffRequest) returns (GrpcBulkTimeOffResponse) {} // insert or update time offs in bulk
  rpc GetCompanyTimeOffTypes(GrpcCompanyTimeOffTypesRequest) returns (GrpcCompanyTimeOffTypesResponse) {}
  rpc BulkRevokeTimeOffs(GrpcBulkRevokeTimeOffRequest) returns (GrpcEmpty) {} // move timeoff status to DRAFT in bulk
}
