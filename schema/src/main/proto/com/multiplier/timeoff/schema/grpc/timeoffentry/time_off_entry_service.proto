syntax = "proto3";

import "com/multiplier/timeoff/schema/grpc/common/TimeOffCommon.proto";
import "com/multiplier/timeoff/schema/grpc/timeoffentry/time_off_entry.proto";
import "google/protobuf/timestamp.proto";

package com.multiplier.timeoff.schema.grpc.timeoffentry;

option java_multiple_files = true;

service TimeOffEntryService {
    rpc getTimeOffEntriesByTimeOffIdsAndStatus(GetTimeOffEntriesByTimeOffIdsAndStatusRequest) returns (TimeOffEntriesResponse);
    rpc getTimeOffEntriesByStatusAndUpdatedOn(GetTimeOffEntriesByStatusAndUpdatedOnRequest) returns (TimeOffEntriesResponse);
    rpc getTimeOffEntriesByIdsAndStatus(GetTimeOffEntriesByIdsAndStatusRequest) returns (TimeOffEntriesResponse);
}

message GetTimeOffEntriesByTimeOffIdsAndStatusRequest {
    repeated int64 time_off_ids = 1;
    repeated com.multiplier.timeoff.schema.GrpcTimeOffStatus status = 2;
}

message GetTimeOffEntriesByStatusAndUpdatedOnRequest {
    google.protobuf.Timestamp from_exclusive = 1;
    google.protobuf.Timestamp until_inclusive = 2;
    repeated com.multiplier.timeoff.schema.GrpcTimeOffStatus status = 3;
}

message GetTimeOffEntriesByIdsAndStatusRequest {
    repeated int64 ids = 1;
    repeated com.multiplier.timeoff.schema.GrpcTimeOffStatus status = 2;
}

message TimeOffEntriesResponse {
    repeated TimeOffEntry time_off_entries = 1;
}
