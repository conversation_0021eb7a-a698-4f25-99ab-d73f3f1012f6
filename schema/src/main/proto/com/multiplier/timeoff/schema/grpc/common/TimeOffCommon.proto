syntax = "proto3";

package com.multiplier.timeoff.schema;

option java_multiple_files = true;

enum GrpcTimeOffStatus {
  UNSET_TIME_OFF_STATUS = 0;
  DRAFT = 1;
  SUBMITTED = 2; // deprecated
  DELETED = 3;
  APPROVAL_IN_PROGRESS = 4;
  APPROVED = 5;
  REJECTED = 6;
  TAKEN = 7;
}

enum GrpcTimeOffSession {
  UNSET_TIME_OFF_SESSION = 0;
  MORNING = 1;
  AFTERNOON = 2;
}

message GrpcTimeOffType {
  int64 id = 1;
  string key = 2;
  string label = 3;
  string description = 4;
  bool paid_leave = 5;
  int64 companyId = 6;
}
