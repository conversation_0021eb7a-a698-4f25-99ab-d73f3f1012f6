syntax = "proto3";

import "com/multiplier/timeoff/schema/grpc/common/TimeOffCommon.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

package com.multiplier.timeoff.schema.grpc.timeoffentry;

option java_multiple_files = true;

message TimeOffEntry {
    int64 id = 1;
    int64 created_by = 2;
    google.protobuf.Timestamp created_on = 3;
    int64 updated_by = 4;
    google.protobuf.Timestamp updated_on = 5;
    int64 contract_id = 6;
    int64 time_off_id = 7;
    google.type.Date entry_date = 8;
    double no_of_days = 9;
    com.multiplier.timeoff.schema.GrpcTimeOffStatus status = 10;
    com.multiplier.timeoff.schema.GrpcTimeOffSession start_session = 11;
    com.multiplier.timeoff.schema.GrpcTimeOffSession end_session = 12;
    com.multiplier.timeoff.schema.GrpcTimeOffType time_off_type = 13 [deprecated=true]; /* use time_off_type_info instead */
    google.type.Date effective_date = 14;
    string description = 15;
    optional google.protobuf.Timestamp approved_on = 16;
    GrpcTimeOffTypeInfo time_off_type_info = 17;
}

message GrpcTimeOffTypeInfo {
    int64 id = 1;
    string key = 2;
    string label = 3;
    string description = 4;
    int64 company_id = 6;
    PaidType paid_type = 7;
}

enum PaidType {
    PAID = 0;
    UNPAID = 1;
}