syntax = "proto3";

import "google/type/date.proto";

import "com/multiplier/timeoff/schema/grpc/common/TimeOffCommon.proto";
import "com/multiplier/timeoff/schema/grpc/timeoffentry/time_off_entry.proto";

package com.multiplier.timeoff.kafka.proto;

message TimeoffEventMessage {
  TimeoffEventType event_type = 1;
  TimeoffEvent event = 2;
}

message TimeoffEvent {
  int64 timeoff_id = 1;
  int64 contract_id = 2;
  com.multiplier.timeoff.schema.GrpcTimeOffStatus timeoff_status = 3;
  google.type.Date start_date = 4;
  google.type.Date end_date = 5;
  com.multiplier.timeoff.schema.GrpcTimeOffSession start_session = 6;
  com.multiplier.timeoff.schema.GrpcTimeOffSession end_session = 7;
  double no_of_days = 8;
  bool is_paid_leave = 9;
  repeated com.multiplier.timeoff.schema.grpc.timeoffentry.TimeOffEntry timeoff_entries = 10;
}

enum TimeoffEventType {
  TIMEOFF_CREATED = 0;
  TIMEOFF_UPDATED = 1;
  TIMEOFF_DELETED = 2;
}