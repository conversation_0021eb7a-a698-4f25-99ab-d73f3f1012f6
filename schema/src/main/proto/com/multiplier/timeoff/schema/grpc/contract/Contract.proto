syntax = "proto3";

package com.multiplier.timeoff.schema.contract;

enum GrpcContractType {
  UNSET_CONTRACT_TYPE = 0;
  EMPLOYEE = 1;
  HR_MEMBER = 2;
  FREELANCER = 3;
  CONTRACTOR = 4;
}

enum GrpcContractTerm {
  UNSET_CONTRACT_TERM = 0;
  FIXED = 1;
  PERMANENT = 2;
}

enum GrpcContractStatus {
  UNSET_CONTRACT_STATUS = 0;
  ONBOARDING = 1;
  ACTIVE = 2;
  OFFBOARDING = 3;
  ENDED = 4;
  DELETED = 5;
  NULL_CONTRACT_STATUS = 6;
}
