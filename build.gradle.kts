tasks.withType<Wrapper> {
    gradleVersion = "8.5"
}

group = "com.multiplier"
version = "1.0.0-SNAPSHOT"

val versionName by extra { generateVersionName() }
val codeartifactToken by extra { generateCodeartifactToken() }

plugins {
    alias(libs.plugins.kotlin.jvm) apply false
    alias(libs.plugins.sonarqube)
    id("maven-publish")
}


subprojects {
    apply<JacocoPlugin>()
    tasks.withType<JacocoReport> {
        dependsOn(tasks.withType<Test>()) // tests are required to run before generating the report

        reports {
            xml.required.set(true)
            html.required.set(true)
            csv.required.set(false)
        }
    }
}

allprojects {
    apply<IdeaPlugin>()

    repositories {
        mavenCentral()
        mavenLocal()
        multiplierRepository()
    }

    tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        compilerOptions {
            jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17)
        }
        kotlinOptions {
            freeCompilerArgs = listOf("-Xjsr305=strict")
        }
    }


    plugins.withType<JavaPlugin> {
        // TODO : do we need this?
        configure<JavaPluginExtension> {
            sourceCompatibility = JavaVersion.VERSION_17
            targetCompatibility = JavaVersion.VERSION_17
        }

        tasks.withType<Test> {
            useJUnitPlatform()
        }
    }

    plugins.withType<MavenPublishPlugin> {
        publishing {
            repositories {
                multiplierRepository()
            }
        }
    }
}


tasks.withType<Jar> {
    enabled = false
}

tasks.register("downloadDependencies") {
    allprojects {
        dependsOn(tasks.named("dependencies"))
    }
}

configure<org.sonarqube.gradle.SonarExtension> {
    properties {
        property("sonar.java.coveragePlugin", "jacoco")
        property("sonar.host.url", "https://sonarcloud.io")
        property("sonar.projectKey", "Multiplier-Core_timeoff-service")
        property("sonar.projectName", "timeoff-service")
        property("sonar.organization", "multiplier")
    }
}

fun RepositoryHandler.multiplierRepository() {
    maven(url = "https://multiplier-artifacts-778085304246.d.codeartifact.ap-southeast-1.amazonaws.com/maven/multiplier-artifacts/") {
        credentials {
            username = "aws"
            password = codeartifactToken
        }
    }
}

fun generateVersionName(): String = if (project.hasProperty("publishVersion")) {
    "${project.properties["publishVersion"]}"
} else {
    java.io.ByteArrayOutputStream().use {
        exec {
            commandLine("git", "describe", "--tags", "--always")
            standardOutput = it
        }
        it.toString().trim()
    }
}

fun generateCodeartifactToken(): String {
    logger.info("Generating codeartifact token...")
    return try {
        providers.environmentVariable("CODEARTIFACT_AUTH_TOKEN").getOrElse("").ifBlank {
            val profile = providers.environmentVariable("MULTIPLIER_AWS_PROFILE").getOrElse("default")
            java.io.ByteArrayOutputStream().use {
                exec {
                    commandLine(
                        "aws",
                        "codeartifact",
                        "get-authorization-token",
                        "--domain",
                        "multiplier-artifacts",
                        "--domain-owner",
                        "778085304246",
                        "--query",
                        "authorizationToken",
                        "--output",
                        "text",
                        "--profile",
                        profile,
                    )
                    standardOutput = it
                }
                it.toString().trim()
            }
        }
    } catch (ex: Exception) {
        logger.warn("Couldn't load codeartifact token", ex)
        ""
    }
}
