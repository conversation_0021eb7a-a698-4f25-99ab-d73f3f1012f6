package com.multiplier.timeoff.util

class FunctionalUtil {

    companion object {

        fun <T> filter(set: Set<T?>?, predicate: (T?) -> Boolean) =
            set?.filter(predicate)?.toSet() ?: emptySet()

        fun <T> filter(col: Collection<T?>?, predicate: (T?) -> Boolean) =
            col?.filter(predicate) ?: emptyList()

        fun <T> filterNotNull(set: Set<T?>?) =
            set?.filterNot { it == null }?.toSet() ?: emptySet()

        fun <T> filterNotNull(col: Collection<T?>?) =
            col?.filterNot { it == null } ?: emptyList()

        fun <T, R> mapNotNull(col: Collection<T?>?, mapper: (T?) -> R) =
            col?.mapNotNull(mapper) ?: emptyList()

        fun <T, R> mapNotNull(set: Set<T?>?, mapper: (T?) -> R) =
            set?.mapNotNull(mapper)?.toSet() ?: emptySet()
    }
}