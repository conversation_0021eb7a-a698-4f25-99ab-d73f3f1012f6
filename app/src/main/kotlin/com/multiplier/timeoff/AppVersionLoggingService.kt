package com.multiplier.timeoff

import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Service
import jakarta.annotation.PostConstruct

private val log = KotlinLogging.logger {}

@Service
class AppVersionLoggingService(val appConfig: AppConfig) {
    @PostConstruct
    fun init() {
        try {
            val version = appConfig.version
            withLoggingContext("appVersion" to version) {
                log.info { "Timeoff Service initialized, version $version" }
            }
        } catch (e: Exception) {
            log.warn(e) { "Timeoff Service initialized" }
        }
    }
}

@ConfigurationProperties(prefix = "platform.app")
data class AppConfig(val version: String? = "N/A")