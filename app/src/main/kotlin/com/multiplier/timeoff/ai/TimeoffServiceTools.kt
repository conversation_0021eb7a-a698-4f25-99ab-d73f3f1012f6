package com.multiplier.timeoff.ai

import com.multiplier.ai.annotations.AiField
import com.multiplier.ai.annotations.AiFunction
import com.multiplier.ai.schema.assistant.AssistantResult
import com.multiplier.ai.schema.assistant.customAssistantRequest
import com.multiplier.ai.service.sync.AssistantService
import com.multiplier.timeoff.leavecompliance.adapter.LeaveComplianceServiceAdapter
import com.multiplier.timeoff.leavecompliance.dto.AnnualLeaveEntitlement
import com.multiplier.timeoff.repository.TimeoffEntitlementDBORepository
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO
import com.multiplier.timeoff.service.TimeOffRequirementsService
import com.multiplier.timeoff.service.builder.DefinitionFilters
import com.multiplier.timeoff.service.dto.CountryLocation
import com.multiplier.timeoff.types.ContractType
import com.multiplier.timeoff.types.CountryCode
import com.multiplier.timeoff.types.CountryFilter
import com.multiplier.timeoff.types.TimeOffFixedValidation
import com.multiplier.timeoff.types.TimeOffTypeDefinition
import com.multiplier.timeoff.types.TimeOffUnit
import mu.KotlinLogging
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import com.multiplier.common.jobs.annotations.Job

private val logger = KotlinLogging.logger {}

data class TimeoffEntitlement(
    @AiField("The unique numeric ID of the timeoff entitlement")
    val id: Long,

    @AiField("The unique numeric ID of the contract this entitlement belongs to")
    val contractId: Long,

    @AiField("The type of timeoff (e.g., 'ANNUAL_LEAVE', 'SICK_LEAVE')")
    val type: String,

    @AiField("The label or display name of the timeoff type")
    val label: String,

    @AiField("The value of the entitlement (e.g., number of days)")
    val value: Double,

    @AiField("The unit of the entitlement (e.g., 'DAYS', 'HOURS')")
    val unit: String,

    @AiField("Whether this timeoff type is mandatory according to local regulations")
    val isMandatory: Boolean,
)

data class GetTimeoffEntitlementRequest(
    @AiField("The unique numeric ID of the contract to look up entitlements for")
    val contractId: String,
)

data class CountryLeaveRequirement(
    @AiField("The type of timeoff (e.g., 'ANNUAL_LEAVE', 'SICK_LEAVE')")
    val type: String,

    @AiField("The label or display name of the timeoff type")
    val label: String,

    @AiField("Whether this timeoff type is mandatory according to local regulations")
    val isRequired: Boolean,

    @AiField("The minimum value required by law (if applicable)")
    val minimumValue: Double?,

    @AiField("The default value suggested")
    val defaultValue: Double?,

    @AiField("The unit of the requirement (e.g., 'DAYS', 'HOURS')")
    val unit: String,

    @AiField("Description of the timeoff type")
    val description: String?,
)

data class GetCountryLeaveRequirementsRequest(
    @AiField("The 3-letter ISO country code to look up requirements for, e.g., USA, IND, VNM")
    val countryCode: String,
)

data class TimeoffAssistantRequest(
    val contractId: String,
)

@Component
class TimeoffServiceTools(
    private val timeoffEntitlementDBORepository: TimeoffEntitlementDBORepository,
    private val timeOffRequirementsService: TimeOffRequirementsService,
    private val leaveComplianceServiceAdapter: LeaveComplianceServiceAdapter,
    private val assistantService: AssistantService,
) {

    @Job("timeoff-compliance-assistant")
    fun timeOffAssistant(request: TimeoffAssistantRequest): Any {
        val description = """
              You are a compliance expert specializing in the timeoff domain. Your task is to verify the compliance of timeoff data for a specific contract.
  
              Please follow these guidelines:
                - The most important thing is that you need to explain the total avaliable timeoff days for each employee based on the contract and the timeoff rules.
                - Select all timeoff requests associated with the given contract.
                - Obtain all applicable timeoff rules and policies for the contract (rules may be based on country or company requirements).
                - If you want to calculate anything, let write a python script to do it and use tool. Don't calculate anything by yourself.
              
              You should answer in the format:
                
              ## Summary:
              - Overall summary of the compliance status for the given contract ID.
              ## Detailed Explanation:
              For each leave type, provide the following information:
              ### Leave Type: {leave type}
              #### Applicable Rules and Policies:
              - Total available leaves: {total available day off}, explaining how it was calculated within the rules
              - Total used: {total usages}
              - Status: {status - is good or not base on the rules}
              - Explain: {Detailed explanation of the compliance status for this leave type, why it complies or not}
        """.trimIndent()
        val result = assistantService.execute(customAssistantRequest {
            this.name = "timeoff-compliance-assistant"
            this.description = description
            this.input = "process the request this contract id ${request.contractId}"
            this.tools.addAll(
                listOf(
                    "get-timeoff-entitlement-for-contract",
                    "get-minimum-annual-leave-requirements-for-country",
                    "get-contract-by-id",
                    "get-contract-by-name",
                    "get-contracts-by-country",
                    "get-contracts-by-company-id"
                )
            )
        })
        return when (result.resultCase) {
            AssistantResult.ResultCase.SUCCESS -> {
                result.success.history.entriesList.map {
                }
                result.success.result

                mapOf(
                    "result" to result.success.result,
                    "history" to result.success.history.entriesList.map { it.valuesMap }.toList()
                )
            }

            AssistantResult.ResultCase.FAILURE ->
                mapOf(
                    "error" to result.failure.errorMessage,
                    "history" to result.failure
                )

            else -> "Unknown result case"
        }
    }

    @AiFunction(
        name = "get-timeoff-entitlement-for-contract",
        description = "Retrieve all timeoff entitlements for a specific contract using its ID. This shows what types of leave the contract has and how many days/hours are allocated."
    )
    @Transactional(readOnly = true)
    fun getTimeoffEntitlementForContract(req: GetTimeoffEntitlementRequest): List<TimeoffEntitlement> {
        logger.info { "Getting timeoff entitlements for contract ID: ${req.contractId}" }

        return try {
            val contractId = req.contractId.toLong()
            val entitlements = timeoffEntitlementDBORepository.findAllByContractId(contractId)

            if (entitlements.isEmpty()) {
                logger.info { "No timeoff entitlements found for contract ID: $contractId" }
                emptyList()
            } else {
                entitlements.map { mapToTimeoffEntitlement(it) }
            }
        } catch (e: NumberFormatException) {
            logger.error(e) { "Invalid contract ID format: ${req.contractId}" }
            emptyList()
        } catch (e: Exception) {
            logger.error(e) { "Error getting timeoff entitlements for contract ID: ${req.contractId}" }
            emptyList()
        }
    }

    private fun mapToTimeoffEntitlement(entitlement: TimeoffEntitlementDBO): TimeoffEntitlement {
        val type = entitlement.type()

        return TimeoffEntitlement(
            id = entitlement.id(),
            contractId = entitlement.contractId(),
            type = type?.key() ?: "unknown",
            label = type?.label() ?: "Unknown Type",
            value = entitlement.value() ?: 0.0,
            unit = entitlement.unit()?.name ?: TimeOffUnit.DAYS.name,
            isMandatory = entitlement.definition()?.isRequired ?: false
        )
    }

    @AiFunction(
        name = "get-minimum-annual-leave-requirements-for-country",
        description = "Get the minimum annual leave requirements and other mandatory timeoff types for a specific country based on its country code."
    )
    @Transactional(readOnly = true)
    fun getMinimumAnnualLeaveRequirementsForCountry(req: GetCountryLeaveRequirementsRequest): List<CountryLeaveRequirement> {
        logger.info { "Getting minimum annual leave requirements for country code: ${req.countryCode}" }

        return try {
            val countryCode = CountryCode.valueOf(req.countryCode.uppercase())

            // Get annual leave entitlement from leave compliance service
            val countryLocation = CountryLocation(countryCode)
            val annualEntitlements =
                leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(listOf(countryLocation))

            val annualEntitlement = annualEntitlements[countryLocation] ?: AnnualLeaveEntitlement.NONE

            // Get all country timeoff requirements
            val filters = DefinitionFilters(
                contractType = ContractType.EMPLOYEE,
                country = CountryFilter.newBuilder()
                    .code(countryCode)
                    .build()
            )

            val requirements = timeOffRequirementsService.getCountryTimeOffRequirements(filters)

            if (requirements.definitions.isEmpty()) {
                logger.info { "No timeoff requirements found for country code: ${req.countryCode}" }

                // If no requirements found but we have annual entitlement, return just that
                if (!annualEntitlement.isNone()) {
                    listOf(createAnnualLeaveRequirement(annualEntitlement))
                } else {
                    emptyList()
                }
            } else {
                requirements.definitions.map { mapToCountryLeaveRequirement(it) }
            }
        } catch (e: IllegalArgumentException) {
            logger.error(e) { "Invalid country code: ${req.countryCode}" }
            emptyList()
        } catch (e: Exception) {
            logger.error(e) { "Error getting minimum annual leave requirements for country code: ${req.countryCode}" }
            emptyList()
        }
    }

    private fun createAnnualLeaveRequirement(annualEntitlement: AnnualLeaveEntitlement): CountryLeaveRequirement {
        return CountryLeaveRequirement(
            type = "ANNUAL_LEAVE",
            label = "Annual Leave",
            isRequired = annualEntitlement.mandatory,
            minimumValue = annualEntitlement.exact,
            defaultValue = annualEntitlement.exact,
            unit = annualEntitlement.timeUnit.name,
            description = "Annual leave entitlement required by law"
        )
    }

    private fun mapToCountryLeaveRequirement(definition: TimeOffTypeDefinition): CountryLeaveRequirement {
        var minimumValue: Double? = null
        var defaultValue: Double? = null
        var unit = TimeOffUnit.DAYS.name

        definition.validation?.let { validation ->
            minimumValue = (validation as TimeOffFixedValidation).minimum
            defaultValue = validation.defaultValue
            unit = validation.unit?.name ?: TimeOffUnit.DAYS.name
        }

        return CountryLeaveRequirement(
            type = definition.type,
            label = definition.label,
            isRequired = definition.required,
            minimumValue = minimumValue,
            defaultValue = defaultValue,
            unit = unit,
            description = definition.description
        )
    }
}
