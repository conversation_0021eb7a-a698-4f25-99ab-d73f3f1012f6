package com.multiplier.timeoff

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.context.annotation.ComponentScan

@SpringBootApplication(exclude = [KafkaAutoConfiguration::class])
@ConfigurationPropertiesScan
@ComponentScan("com.multiplier")
class TimeoffServiceApplication

fun main(args: Array<String>) {
	runApplication<TimeoffServiceApplication>(*args)
}
