package com.multiplier.timeoff.integration

import com.multiplier.grpc.common.toLocalDateTime
import com.multiplier.timeoff.integration.exception.GrpcExceptionWrapper
import com.multiplier.timeoff.schema.grpc.timeoffentry.GetTimeOffEntriesByIdsAndStatusRequest
import com.multiplier.timeoff.schema.grpc.timeoffentry.GetTimeOffEntriesByStatusAndUpdatedOnRequest
import com.multiplier.timeoff.schema.grpc.timeoffentry.GetTimeOffEntriesByTimeOffIdsAndStatusRequest
import com.multiplier.timeoff.schema.grpc.timeoffentry.TimeOffEntriesResponse
import com.multiplier.timeoff.schema.grpc.timeoffentry.TimeOffEntryServiceGrpcKt
import com.multiplier.timeoff.schema.grpc.timeoffentry.timeOffEntriesResponse
import com.multiplier.timeoff.service.TimeOffEntryService
import com.multiplier.timeoff.types.TimeOffStatus
import io.grpc.Status
import kotlinx.coroutines.Dispatchers
import mu.KotlinLogging
import net.devh.boot.grpc.server.service.GrpcService

val log = KotlinLogging.logger {}

@GrpcService
class GrpcTimeOffEntryService(
    private val timeOffEntryService: TimeOffEntryService,
) : TimeOffEntryServiceGrpcKt.TimeOffEntryServiceCoroutineImplBase(coroutineContext = Dispatchers.Unconfined) {
    override suspend fun getTimeOffEntriesByTimeOffIdsAndStatus(
        request: GetTimeOffEntriesByTimeOffIdsAndStatusRequest,
    ): TimeOffEntriesResponse {
        val timeOffIds = request.timeOffIdsList.toSet()
        val statuses = request.statusList

        log.info { "Getting timeOffEntries for time off ids $timeOffIds with statuses $statuses" }

        try {
            val timeOffEntries = timeOffEntryService.getTimeOffEntriesByTimeOffIdsAndStatus(
                timeOffIds = timeOffIds,
                statuses = statuses.map { TimeOffStatus.valueOf(it.name) }.toSet(),
            )
            return timeOffEntriesResponse { this.timeOffEntries.addAll(timeOffEntries) }
        } catch (e: Exception) {
            log.error(e) { "Error getting timeOffEntries for time off ids $timeOffIds with statuses $statuses" }
            throw GrpcExceptionWrapper(Status.INTERNAL, e)
        }
    }

    override suspend fun getTimeOffEntriesByStatusAndUpdatedOn(
        request: GetTimeOffEntriesByStatusAndUpdatedOnRequest,
    ): TimeOffEntriesResponse {
        val fromExclusive = request.fromExclusive.toLocalDateTime()
        val untilInclusive = request.untilInclusive.toLocalDateTime()
        val statuses = request.statusList

        log.info { "Getting timeOffEntries from [$fromExclusive] until [$untilInclusive] with statuses $statuses" }

        try {
            val timeOffEntries = timeOffEntryService.getTimeOffEntriesByStatusAndUpdatedOn(
                updatedOnFromExclusive = fromExclusive,
                updatedOnToInclusive = untilInclusive,
                statuses = statuses.map { TimeOffStatus.valueOf(it.name) }.toSet(),
            )
            return timeOffEntriesResponse { this.timeOffEntries.addAll(timeOffEntries) }
        } catch (e: Exception) {
            log.error(e) { "Error getting timeOffEntries from [$fromExclusive] until [$untilInclusive] with statuses $statuses" }
            throw GrpcExceptionWrapper(Status.INTERNAL, e)
        }
    }

    override suspend fun getTimeOffEntriesByIdsAndStatus(
        request: GetTimeOffEntriesByIdsAndStatusRequest,
    ): TimeOffEntriesResponse {
        val timeOffEntryIds = request.idsList.toSet()
        val statuses = request.statusList

        log.info("Getting timeOffEntries for ids $timeOffEntryIds with statuses $statuses")

        try {
            val timeOffEntries = timeOffEntryService.getTimeOffEntriesByIdsAndStatus(
                timeOffEntryIds = timeOffEntryIds,
                statuses = statuses.map { TimeOffStatus.valueOf(it.name) }.toSet(),
            )
            return timeOffEntriesResponse { this.timeOffEntries.addAll(timeOffEntries) }
        } catch (e: Exception) {
            log.error(e) { "Error getting timeOffEntries for ids $timeOffEntryIds with statuses $statuses" }
            throw GrpcExceptionWrapper(Status.INTERNAL, e)
        }
    }
}
