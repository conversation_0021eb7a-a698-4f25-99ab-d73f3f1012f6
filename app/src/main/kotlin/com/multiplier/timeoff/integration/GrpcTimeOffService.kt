package com.multiplier.timeoff.integration

import com.google.protobuf.Timestamp
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractBasicInfo
import com.multiplier.timeoff.*
import com.multiplier.timeoff.MDCKeys.METHOD
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.core.common.dto.BulkTimeOffRequest
import com.multiplier.timeoff.core.common.dto.BulkTimeOffRequest.Input
import com.multiplier.timeoff.core.common.dto.BulkUpsertTimeOffResult
import com.multiplier.timeoff.core.common.dto.BulkValidateTimeOffResult
import com.multiplier.timeoff.integration.exception.GrpcExceptionWrapper
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO
import com.multiplier.timeoff.schema.*
import com.multiplier.timeoff.schema.contract.Contract
import com.multiplier.timeoff.service.*
import com.multiplier.timeoff.service.builder.DefinitionFilters
import com.multiplier.timeoff.service.bulk.BulkTimeOffService
import com.multiplier.timeoff.service.exception.EntityNotFoundException
import com.multiplier.timeoff.types.*
import com.multiplier.timeoff.types.TimeOff
import io.grpc.Status
import io.grpc.stub.StreamObserver
import mu.withLoggingContext
import net.devh.boot.grpc.server.service.GrpcService
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset

@GrpcService
class GrpcTimeOffService(
    private val requirementsSvc: TimeOffRequirementsService,
    private val timeOffService: TimeoffService,
    private val timeOffQuery: TimeoffQuery,
    private val timeOffCron: TimeoffCron,
    private val bulkTimeOffService: BulkTimeOffService,
    private val timeoffTypeService: TimeoffTypeService,
    private val contractServiceAdapter: ContractServiceAdapter,
) : TimeOffServiceGrpc.TimeOffServiceImplBase() {

    val log by logger()

    override fun getAnnualLeaveType(request: GrpcEmpty, responseObserver: StreamObserver<GrpcTimeOffType>) {
        try {
            val annualLeaveType: TimeoffTypeDBO? = timeOffService.findAnnualLeaveType()
            if (annualLeaveType == null) {
                responseObserver.onError(GrpcExceptionWrapper(Status.NOT_FOUND, EntityNotFoundException("Annual Leave Type not found. Check timeoff_type table!")))
                return
            }

            responseObserver.onNext(GrpcTimeOffType.newBuilder()
                .setId(annualLeaveType.id())
                .setKey(annualLeaveType.key())
                .build())

            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in getAnnualLeaveType: ", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getTimeOffById(request: GrpcTimeOffId, responseObserver: StreamObserver<GrpcTimeOff>) {
        try {
            val timeOff: TimeOff? = timeOffQuery.getTimeoffById(request.id)
            if (timeOff == null) {
                responseObserver.onNext(GrpcTimeOff.newBuilder().setId(-1L).build())
            } else {
                responseObserver.onNext(timeOff.toGrpc())
            }
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in getTimeOffById, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun updateTimeOffSchedule(request: GrpcEmpty, responseObserver: StreamObserver<GrpcEmpty>) {
        try {
            timeOffCron.updateTimeoffStatus()

            responseObserver.returnEmptyMessage()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in updateTimeOffSchedule: ", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }


    override fun getTimeOffEntitlements(request: GrpcContractId, responseObserver: StreamObserver<GrpcListOfTimeOffEntitlements>) {
        try {
            val entitlements = timeOffService.getTimeOffEntitlementsForContract(request.id).map { it.toGrpcType() }

            responseObserver.onNext(GrpcListOfTimeOffEntitlements.newBuilder().addAllEntitlements(entitlements).build())
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in getTimeOffEntitlements, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun bulkUpdateTimeOffEntitlements(request: GrpcBulkUpdateTimeOffEntitlementsRequest, responseObserver: StreamObserver<GrpcEmpty>) {
        try {
            val graphQLTypes = request.entitlementsList.map {
                ContractUpdateTimeOffEntitlementsInput.newBuilder()
                    .key(it.key)
                    .value(it.value)
                    .unit(TimeOffUnit.valueOf(it.unit.name))
                    .build()
            }
            timeOffService.updateTimeOffEntitlements(request.contractId, graphQLTypes)

            responseObserver.returnEmptyMessage()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in bulkUpdateTimeOffEntitlements, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun setEntitlementsToDefaultCountryRequirements(request: GrpcContractId, responseObserver: StreamObserver<GrpcEmpty>) {
        try {
            timeOffService.setEntitlementsToDefaultRequirements(request.id)

            responseObserver.returnEmptyMessage()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in setEntitlementsToDefaultCountryRequirements, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun runTimeOffAllocation(request: GrpcContractId, responseObserver: StreamObserver<GrpcEmpty>) {
        try {
            timeOffService.reRunAllocationTimeOff(request.id)

            responseObserver.returnEmptyMessage()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in runTimeOffAllocation, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun onTimeOffApprovalStatusChange(request: GrpcTimeOffApprovalStatusChangeEvent, responseObserver: StreamObserver<GrpcEmpty>) {
        try {
            timeOffService.onTimeOffApprovalStatusChange(request)
            responseObserver.returnEmptyMessage()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in onTimeOffApprovalStatusChange, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }


    override fun getDefinitions(
        request: GrpcDefinitionsRequest,
        responseObserver: StreamObserver<GrpcDefinitionsResponse>,
    ) {

        try {

            responseObserver.onNext(
                request.fromGrpc().let {
                    requirementsSvc.getCompanyTimeOffRequirements(it)
                        .toGrpc()
                }
            )
            responseObserver.onCompleted()

        } catch (e: Exception) {

            log.error("[GrpcTimeOffService] Error in getDefinitions, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    @Deprecated("migrate to getTimeOffsForPayrollCycle()")
    override fun getTimeOffsForPayroll(
        request: GetTimeOffsForPayrollRequest,
        responseObserver: StreamObserver<GetTimeOffsForPayrollResponse>,
    ) = withLoggingContext(METHOD to "getTimeOffsForPayroll") {
        log.info("Getting timeoffs for payroll")
        try {
            val timeOffs =
                timeOffService.getTimeOffsForPayroll(
                    request.contractIdsList.toSet(),
                    setOf(TimeOffStatus.TAKEN, TimeOffStatus.APPROVED),
                    request.intersectionRange.startDate.toLocalDate(),
                    request.intersectionRange.endDate.toLocalDate(),
                    request.approvedOnGreaterThanEqTo.toInstant(),
                )
            responseObserver.onNext(GetTimeOffsForPayrollResponse.newBuilder()
                .addAllTimeOffs(timeOffs)
                .build())
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in getTimeOffsForPayroll, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getTimeOffsForPayrollCycle(
        request: GetTimeOffsForPayrollCycleRequest,
        responseObserver: StreamObserver<GetTimeOffsForPayrollResponse>,
    ) = withLoggingContext(METHOD to "getTimeOffsForPayroll") {
        log.info("Getting timeoffs for payroll")
        try {
            val timeOffs =
                timeOffService.getTimeOffsForPayrollCycle(
                    request.contractIdsList.toSet(),
                    setOf(TimeOffStatus.TAKEN, TimeOffStatus.APPROVED),
                    request.timeOffRange.startDate.toLocalDate(),
                    request.timeOffRange.endDate.toLocalDate(),
                    request.approvedOnFromInclusive.toLocalDate(),
                    request.approvedOnToInclusive.toLocalDate(),
                )
            responseObserver.onNext(GetTimeOffsForPayrollResponse.newBuilder()
                .addAllTimeOffs(timeOffs)
                .build())
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in getTimeOffsForPayroll, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    /**
     * Returns all regardless of timeoff.status
     */
    override fun getTimeOffsByContractIds(request: GrpcContractIds, responseObserver: StreamObserver<GrpcTimeOffs>) {
        log.info("[getTimeOffsByContractIds] with request={}", request)
        try {
            val graphTimeOffs = timeOffQuery.getTimeOffsByContractIds(request.idsList)
            val result = GrpcTimeOffs.newBuilder()
                .addAllTimeOffs(graphTimeOffs.map { it.toGrpc() })
                .build()
            responseObserver.onNext(result)
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[GrpcTimeOffService] Error in getTimeOffsByContractIds, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun bulkValidateTimeOffUpsert(
        request: GrpcBulkTimeOffRequest,
        responseObserver: StreamObserver<GrpcBulkValidateTimeOffResponse>
    ) {
        log.info("[bulkValidateTimeOffUpsert] with request = {}", request)
        try {
            val results = bulkTimeOffService.validateBulkTimeOffs(request.fromGrpc(getEmployeeIdToContractMap(request)))
            responseObserver.onNext(results.toGrpc())
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[bulkValidateTimeOffUpsert] Error in bulkValidateTimeOffUpsert, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    private fun getEmployeeIdToContractMap(request : GrpcBulkTimeOffRequest) : Map<String, ContractBasicInfo>{
        return contractServiceAdapter.getContractsByEmployeeIds(
            request.inputsList.map { it.employeeId },
            request.companyId
        )
    }

    override fun bulkUpsertTimeOffs(
        request: GrpcBulkTimeOffRequest,
        responseObserver: StreamObserver<GrpcBulkTimeOffResponse>
    ) {
        log.info("[bulkUpsertTimeOffs] with request = {}", request)
        try {
            val results = bulkTimeOffService.bulkUpsertTimeOffs(request.fromGrpc(getEmployeeIdToContractMap(request)))
            responseObserver.onNext(results.toGrpc())
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[bulkUpsertTimeOffs] Error in bulkUpsertTimeOffs, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun setEntitlementsToDefaultRequirements(
        request: GrpcContractIds,
        responseObserver: StreamObserver<GrpcEmpty>
    ) {
        log.info("[setEntitlementsToDefaultRequirements] with request = {}", request)
        try {
            timeOffService.setEntitlementsToDefaultRequirementsBulk(request.idsList)
            responseObserver.returnEmptyMessage()
        } catch (e: Exception) {
            log.error("[setEntitlementsToDefaultRequirements] Error in setEntitlementsToDefaultRequirements, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getCompanyTimeOffTypes(
        request: GrpcCompanyTimeOffTypesRequest,
        responseObserver: StreamObserver<GrpcCompanyTimeOffTypesResponse>
    ) {
        log.info("[getCompanyTimeOffTypes] with request = {}", request)
        try {
            val results = timeoffTypeService.findTimeOffTypeDBOsForCompany(request.companyId)
            responseObserver.onNext(toGrpcCompanyTimeOffTypesResponse(results))
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[getCompanyTimeOffTypes] Error in getCompanyTimeOffTypes, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun bulkRevokeTimeOffs(
        request: GrpcBulkRevokeTimeOffRequest,
        responseObserver: StreamObserver<GrpcEmpty>
    ) {
        log.info("[bulkRevokeTimeOffsFromExternalIds] with request = {}", request)
        try {
            bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(request.companyId, request.externalTimeOffIdsList)
            responseObserver.returnEmptyMessage()
        } catch (e: Exception) {
            log.error("[bulkRevokeTimeOffsFromExternalIds] Error in bulkRevokeTimeOffsFromExternalIds, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }
}


private fun GrpcDefinitionsRequest.fromGrpc(): DefinitionFilters {

    return DefinitionFilters(
        country = this.countryCode.let {
            CountryFilter.newBuilder()
                .code(
                    CountryCode.valueOf(it.countryCode.name)
                )
                .stateCode(
                    if (it.countryStateCode.isNullOrBlank()) null else it.countryStateCode
                )
                .build()
        },
        contractType = this.contractType.let {
            if (it == Contract.GrpcContractType.UNSET_CONTRACT_TYPE) null else ContractType.valueOf(it.name)
        },
        contractTerm = this.contractTerm.let {
            if (it == Contract.GrpcContractTerm.UNSET_CONTRACT_TERM) null else ContractTerm.valueOf(it.name)
        },
        contractStatus = this.contractStatus.let {
            if (it == Contract.GrpcContractStatus.UNSET_CONTRACT_STATUS) null else ContractStatus.valueOf(it.name)
        },
        timeoffTypeId = this.timeoffTypeId.let {
            if (it <= 0) null else it
        },
        companyId = this.companyId.let {
            if (it <= 0) null else it
        }
    )
}

private fun ContractTimeOffEntitlement.toGrpcType(): GrpcTimeOffEntitlement {
    return GrpcTimeOffEntitlement.newBuilder()
        .setValue((this.value ?: -1) as Double)
        .setType(this.timeOffType.type)
        .setLabel(this.definition?.label ?: "")
        .setIsRequired(this.definition?.required ?: false)
        .setUnit(GrpcTimeOffUnit.valueOf(this.unit.name))
        .build()
}

private fun TimeOffRequirements.toGrpc(): GrpcDefinitionsResponse {

    return GrpcDefinitionsResponse.newBuilder()
        .setClause(this.clause ?: "")
        .addAllDefinitions(this.definitions.map { it.toGrpc() })
        .build()

}

private fun toGrpcCompanyTimeOffTypesResponse(types: List<TimeoffTypeDBO>): GrpcCompanyTimeOffTypesResponse {
    val (systemTimeOffTypes, companyTimeOffTypes) = types.partition { it.companyId() == null }
    return GrpcCompanyTimeOffTypesResponse.newBuilder()
        .addAllSystemTimeOffTypes(systemTimeOffTypes.map { it.toGrpc() })
        .addAllCompanyTimeOffTypes(companyTimeOffTypes.map { it.toGrpc() })
        .build()
}

private fun TimeoffTypeDBO.toGrpc(): GrpcTimeOffType {
    return GrpcTimeOffType.newBuilder()
        .setId(this.id())
        .setKey(this.key())
        .setLabel(this.label())
        .setPaidLeave(this.isPaidLeave())
        .setCompanyId(this.companyId() ?: -1)
        .setDescription(this.description() ?: "")
        .build()
}

private fun TimeOffTypeDefinition.toGrpc(): GrpcTimeOffTypeDefinition {

    return GrpcTimeOffTypeDefinition.newBuilder()
        .setTypeId(this.typeId ?: -1)
        .setType(this.type ?: "")
        .setRequired(this.required ?: false)
        .setLabel(this.label ?: "")
        .setDescription(this.description ?: "")
        .setBasis(this.basis ?: "")
        .addAllValidation(this.validation?.mapNotNull { (it as TimeOffFixedValidation).toGrpc() } ?: emptyList())
        .setConfiguration(this.configuration?.toGrpc() ?: GrpcTimeoffConfiguration.getDefaultInstance())
        .build()

}

private fun TimeOffFixedValidation.toGrpc(): GrpcTimeOffValidation {

    return GrpcTimeOffValidation.newBuilder()
        .setMinimum(this.minimum ?: -1.0)
        .setMaximum(this.maximum ?: -1.0)
        .setDefaultValue(this.defaultValue ?: -1.0)
        .setUnit(this.unit?.let { GrpcTimeOffUnit.valueOf(this.unit.name) })
        .addAllAllowedContractStatuses(
            this.allowedContractStatuses?.map { Contract.GrpcContractStatus.valueOf(it.name) } ?: emptyList()
        )
        .build()

}

fun TimeoffConfiguration.toGrpc(): GrpcTimeoffConfiguration {

    return GrpcTimeoffConfiguration.newBuilder()
        .setAllocation(this.allocation?.toGrpc() ?: GrpcAllocationConfig.getDefaultInstance())
        .setCarryForward(this.carryForward?.toGrpc() ?: GrpcCarryForwardConfig.getDefaultInstance())
        .build()
}

fun AllocationConfig.toGrpc(): GrpcAllocationConfig {

    return GrpcAllocationConfig.newBuilder()
        .setBasis(this.basis?.let { GrpcAllocationBasis.valueOf(it.name) })
        .setProrated(this.prorated ?: false)
        .build()
}

fun CarryForwardConfig.toGrpc(): GrpcCarryForwardConfig {
    return GrpcCarryForwardConfig.newBuilder()
        .setMinForEligibility(this.minForEligibility?.toGrpc() ?: GrpcCarryForwardLimit.getDefaultInstance())
        .setMaxLimit(this.minForEligibility?.toGrpc() ?: GrpcCarryForwardLimit.getDefaultInstance())
        .build()
}


fun CarryForwardLimit?.toGrpc(): GrpcCarryForwardLimit {
    return if (this == null) GrpcCarryForwardLimit.getDefaultInstance()
    else GrpcCarryForwardLimit.newBuilder()
        .setType(this.type?.let { GrpcLimitValueType.valueOf(it.name) })
        .setValue(this.value ?: -1.0)
        .setUnit(this.unit?.let { GrpcTimeOffUnit.valueOf(it.name) })
        .build()
}

fun LocalDate.toGrpcTimestamp(): Timestamp {
    val instant = this.atStartOfDay().toInstant(ZoneOffset.UTC)
    return Timestamp.newBuilder()
        .setSeconds(instant.epochSecond)
        .setNanos(instant.nano)
        .build()
}


fun Timestamp.toLocalDate(): LocalDate? {
    return if (this == Timestamp.getDefaultInstance()) null
        else LocalDateTime
        .ofEpochSecond(this.seconds, this.nanos, ZoneOffset.UTC)
        .toLocalDate()
}


fun TimeOff.toGrpc(): GrpcTimeOff =
    GrpcTimeOff.newBuilder()
        .setId(this.id)
        .setContractId(this.contract.id)
        .setStatus(GrpcTimeOffStatus.valueOf(this.status.name))
        .setStartDate(this.startDate.dateOnly.toGrpcTimestamp())
        .setEndDate(this.endDate.dateOnly.toGrpcTimestamp())
        .setStartDateOnly(this.startDate.dateOnly.toDate())
        .setEndDateOnly(this.endDate.dateOnly.toDate())
        .setStartSession(GrpcTimeOffSession.valueOf(this.startDate.session.name))
        .setEndSession(GrpcTimeOffSession.valueOf(this.endDate.session.name))
        .setNoOfDays(this.noOfDays)
        .setDescription(if (this.description != null) this.description else "")
        .setTimeOffType(GrpcTimeOffType.newBuilder()
            .setKey(this.type.key)
            .build())
        .build()


private fun GrpcBulkTimeOffInput.fromGrpc(employeeIdToContractMap: Map<String, ContractBasicInfo>) : Input {

    return this.let {
        Input.builder()
            .externalTimeOffId(it.externalTimeOffId)
            .contractId(employeeIdToContractMap[it.employeeId]?.contractId)
            .type(it.type)
            .noOfDays(it.noOfDays)
            .startDate(it.startDate)
            .endDate(it.endDate)
            .description(it.description)
            .build()
    }
}

private fun GrpcBulkTimeOffRequest.fromGrpc(employeeIdToContractMap : Map<String, ContractBasicInfo>): BulkTimeOffRequest {
    return this.let {
        BulkTimeOffRequest.builder()
            .companyId(it.companyId)
            .inputs(it.inputsList.mapNotNull { item -> item.fromGrpc(employeeIdToContractMap) }.toList())
            .build()
    }
}

private fun BulkValidateTimeOffResult.toGrpc(): GrpcBulkValidateTimeOffResponse {
    return this.let { result ->
        GrpcBulkValidateTimeOffResponse.newBuilder()
            .setSuccess(result.isSuccess)
            .addAllItems(result.items.mapNotNull { it.toGrpc() })
            .build()
    }
}

private fun BulkValidateTimeOffResult.BulkValidateTimeOffResultItem.toGrpc(): GrpcBulkValidateTimeOffResultItem {
    return this.let {
        GrpcBulkValidateTimeOffResultItem.newBuilder()
            .setExternalTimeOffId(it.externalTimeOffId)
            .addAllErrors(it.errors)
            .build()
    }
}

private fun BulkUpsertTimeOffResult.toGrpc(): GrpcBulkTimeOffResponse {
    return this.let { result ->
        GrpcBulkTimeOffResponse.newBuilder()
            .setSuccess(result.isSuccess)
            .setMessage(result.message)
            .addAllItems(result.items.mapNotNull { it.toGrpc() })
            .build()
    }
}

private fun BulkUpsertTimeOffResult.Item.toGrpc(): GrpcBulkResponseItem {
    val builder = GrpcBulkResponseItem.newBuilder()
        .setExternalTimeOffId(externalTimeOffId.orEmpty())

    if (timeOffId != null) {
        builder.setTimeOffId(timeOffId)
    }

    if (errors != null) {
        builder.addAllErrors(errors)
    }

    return builder.build()
}
