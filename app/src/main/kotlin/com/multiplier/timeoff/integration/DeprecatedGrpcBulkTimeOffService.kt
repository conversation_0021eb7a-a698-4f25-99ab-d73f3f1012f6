package com.multiplier.timeoff.integration

import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsResponse
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkResponse
import com.multiplier.timeoff.schema.BulkUploadApiGrpc
import io.grpc.stub.StreamObserver
import net.devh.boot.grpc.server.service.GrpcService

@GrpcService
@Deprecated("Use GrpcBulkTimeOffService instead")
class DeprecatedGrpcBulkTimeOffService(
        private val grpcBulkTimeOffService: GrpcBulkTimeOffService,
) : BulkUploadApiGrpc.BulkUploadApiImplBase() {
    override fun getFieldRequirements(
            request: FieldRequirementsRequest,
            responseObserver: StreamObserver<FieldRequirementsResponse>
    ) = grpcBulkTimeOffService.getFieldRequirements(request, responseObserver)

    override fun bulkValidateUpsertInput(
            request: ValidateUpsertInputBulkRequest,
            responseObserver: StreamObserver<ValidateUpsertInputBulkResponse>
    ) = grpcBulkTimeOffService.bulkValidateUpsertInput(request, responseObserver)

    override fun bulkUpsert(
            request: UpsertBulkRequest,
            responseObserver: StreamObserver<UpsertBulkResponse>
    ) = grpcBulkTimeOffService.bulkUpsert(request, responseObserver)
}