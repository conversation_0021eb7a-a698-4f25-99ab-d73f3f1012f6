package com.multiplier.timeoff.integration

import com.multiplier.grpc.common.bulkupload.v1.BulkDataRequest
import com.multiplier.grpc.common.bulkupload.v1.BulkDataResponse
import com.multiplier.grpc.common.bulkupload.v1.BulkUploadServiceGrpc
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsResponse
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.bulkDataResponse
import com.multiplier.timeoff.integration.exception.GrpcExceptionWrapper
import com.multiplier.timeoff.service.bulk.BulkTimeOffServiceV2
import io.grpc.Status
import io.grpc.stub.StreamObserver
import net.devh.boot.grpc.server.service.GrpcService

@GrpcService
class GrpcBulkTimeOffService(
        private val bulkTimeOffServiceV2: BulkTimeOffServiceV2,
) : BulkUploadServiceGrpc.BulkUploadServiceImplBase() {
    override fun getFieldRequirements(
            request: FieldRequirementsRequest,
            responseObserver: StreamObserver<FieldRequirementsResponse>
    ) {
        log.info("[getFieldRequirements] with request = {}", request)
        try {
            val results = bulkTimeOffServiceV2.getFieldRequirements(request)
            responseObserver.onNext(results)
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[getFieldRequirements] Error in getFieldRequirements, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getFieldData(
            request: BulkDataRequest,
            responseObserver: StreamObserver<BulkDataResponse>
    ) {
        log.info("[getFieldData] with request = {}", request)
        responseObserver.onNext(bulkDataResponse {})
        responseObserver.onCompleted()
    }

    override fun bulkValidateUpsertInput(
            request: ValidateUpsertInputBulkRequest,
            responseObserver: StreamObserver<ValidateUpsertInputBulkResponse>
    ) {
        log.info("[bulkValidateUpsertInput] with request = {}", request)
        try {
            val results = bulkTimeOffServiceV2.validateBulkTimeOffs(request)
            responseObserver.onNext(results)
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[bulkValidateUpsertInput] Error in bulkValidateUpsertInput, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun bulkUpsert(
            request: UpsertBulkRequest,
            responseObserver: StreamObserver<UpsertBulkResponse>
    ) {
        log.info("[bulkUpsert] with request = {}", request)
        try {
            val results = bulkTimeOffServiceV2.bulkUpsertTimeOffs(request)
            responseObserver.onNext(results)
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.error("[bulkUpsert] Error in bulkUpsert, request: $request", e)
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }
}