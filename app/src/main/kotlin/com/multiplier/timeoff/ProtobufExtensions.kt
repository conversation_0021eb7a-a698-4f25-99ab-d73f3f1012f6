package com.multiplier.timeoff

import com.google.protobuf.Timestamp
import com.google.type.Date
import com.multiplier.timeoff.schema.GrpcEmpty
import io.grpc.stub.StreamObserver
import java.time.*


fun LocalDateTime.toDate(): Date {
    return Date.newBuilder()
        .setYear(this.year)
        .setMonth(this.monthValue)
        .setDay(this.dayOfMonth)
        .build()
}

fun LocalDate.toDate(): Date {
    return Date.newBuilder()
        .setYear(this.year)
        .setMonth(this.monthValue)
        .setDay(this.dayOfMonth)
        .build()
}

fun Date.toLocalDate(): LocalDate {
    return LocalDate.of(this.year, this.month, this.day)
}

fun Timestamp.toInstant(): Instant? =
    if (this == Timestamp.getDefaultInstance()) null
    else Instant.ofEpochSecond(this.seconds, this.nanos.toLong())

fun StreamObserver<GrpcEmpty>.returnEmptyMessage() {
    this.onNext(GrpcEmpty.newBuilder().build())
    this.onCompleted()
}