package com.multiplier.timeoff.repository.model

import com.multiplier.timeoff.core.constant.Database
import com.multiplier.timeoff.repository.model.DefinitionEntity
import com.multiplier.timeoff.repository.model.TimeOffDefinitionEntity
import jakarta.persistence.*


@Entity
@Table(
    schema = Database.SCHEMA,
    name = Database.Table.CountryDefinition.TABLE_NAME,
    uniqueConstraints = [
        UniqueConstraint(
            name = Database.Table.CountryDefinition.Constraint.UQ_DEFINITION,
            columnNames = [
                Database.Table.CountryDefinition.Column.TYPE_ID,
                Database.Table.CountryDefinition.Column.DEFINITION_ID
            ]
        )
    ]
)
class CountryDefinitionEntity(
    id: Long? = null,
    typeId: Long? = null,
    definition: DefinitionEntity? = null,
) : TimeOffDefinitionEntity(
    id,
    typeId,
    definition,
)

