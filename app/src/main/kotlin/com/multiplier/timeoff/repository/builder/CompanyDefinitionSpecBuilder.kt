package com.multiplier.timeoff.repository.builder;

import com.multiplier.timeoff.core.common.builder.SpecificationBuilder
import com.multiplier.timeoff.repository.filters.CompanyDefinitionFilter
import com.multiplier.timeoff.repository.model.CompanyDefinitionEntity
import com.multiplier.timeoff.repository.model.DefinitionEntity
import com.multiplier.timeoff.types.EntityType
import com.multiplier.timeoff.types.TimeOffTypeDefinitionStatus
import lombok.extern.slf4j.Slf4j
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Component


const val ENTITY_ID = "entityId"
const val ENTITY_TYPE = "entityType"
const val COMPANY_ID = "companyId"
const val DEFINITION = "definition"
const val DEFINITION_STATUS = "status"


@Slf4j(topic = "CompanyDefinitionSpecBuilder")
@Component
class CompanyDefinitionSpecBuilder : SpecificationBuilder<CompanyDefinitionEntity, List<CompanyDefinitionFilter>> {

    override fun build(filters: List<CompanyDefinitionFilter>): Specification<CompanyDefinitionEntity> {
        return Specification { root, _, builder ->
            if (filters.isEmpty()) {
                return@Specification builder.disjunction() // Return "false" condition (matches nothing)
            }

            // Create an OR predicate to combine multiple filters
            var combinedPredicate = builder.disjunction()

            for (filter in filters) {
                // entityType, entityId, and companyId should be non-null
                var singleFilterPredicate = builder.conjunction()
                singleFilterPredicate = builder.and(singleFilterPredicate, builder.equal(root.get<EntityType>(ENTITY_TYPE), filter.entityType))
                singleFilterPredicate = builder.and(singleFilterPredicate, builder.equal(root.get<Long>(ENTITY_ID), filter.entityId))
                if (filter.companyId != null) {
                    singleFilterPredicate = builder.and(singleFilterPredicate, builder.equal(root.get<Long>(COMPANY_ID), filter.companyId))
                }

                // combine filters with OR operation
                combinedPredicate = builder.or(combinedPredicate, singleFilterPredicate)
            }

            val definitionJoin = root.join<CompanyDefinitionEntity, DefinitionEntity>(DEFINITION)
            combinedPredicate = builder.and(combinedPredicate, builder.notEqual(definitionJoin.get<TimeOffTypeDefinitionStatus>(DEFINITION_STATUS), TimeOffTypeDefinitionStatus.DELETED))

            combinedPredicate
        }
    }
}
