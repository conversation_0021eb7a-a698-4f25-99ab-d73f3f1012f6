package com.multiplier.timeoff.repository.model

import com.multiplier.timeoff.core.constant.Database
import com.multiplier.timeoff.types.EntityType
import jakarta.persistence.*
import lombok.Builder
import org.hibernate.annotations.BatchSize
import org.hibernate.envers.Audited

@MappedSuperclass
abstract class TimeOffDefinitionEntity(
    id: Long?,

    @Column(name = Database.Table.CompanyDefinition.Column.TYPE_ID)
    var typeId: Long?,

    @JoinColumn(nullable = false, name = Database.Table.CompanyDefinition.Column.DEFINITION_ID)
    @ManyToOne(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    var definition: DefinitionEntity? = null,

    ) : AuditableBaseEntityKt(id)

@Entity
@Table(
    schema = Database.SCHEMA,
    name = Database.Table.CompanyDefinition.TABLE_NAME,
    uniqueConstraints = [
        UniqueConstraint(
            name = Database.Table.CompanyDefinition.Constraint.UQ_DEFINITION,
            columnNames = [
                Database.Table.CompanyDefinition.Column.TYPE_ID,
                Database.Table.CompanyDefinition.Column.DEFINITION_ID,
                Database.Table.CompanyDefinition.Column.COMPANY_ID
            ]
        )
    ]
)
@Audited
class CompanyDefinitionEntity(
    id: Long? = null,
    typeId: Long? = null,
    definition: DefinitionEntity? = null,

    @Column(name = Database.Table.CompanyDefinition.Column.COMPANY_ID)
    var companyId: Long? = null,

    @Enumerated(EnumType.STRING)
    var entityType: EntityType? = null,

    var entityId: Long? = null,

    @OneToMany(mappedBy = "companyDefinition", fetch = FetchType.LAZY, cascade = [CascadeType.ALL], orphanRemoval = true)
    @Builder.Default
    @BatchSize(size = 500)
    var rules: MutableList<TimeOffDefinitionRuleDBO>? = mutableListOf(),
) : TimeOffDefinitionEntity(
    id,
    typeId,
    definition,
)

