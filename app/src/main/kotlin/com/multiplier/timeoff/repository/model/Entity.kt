package com.multiplier.timeoff.repository.model

import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime
import jakarta.persistence.*

@MappedSuperclass
abstract class BaseEntityKt(
    @field:Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    open var id: Long? = null
)

@MappedSuperclass
@EntityListeners(AuditingEntityListener::class)
abstract class AuditableBaseEntityKt(
    id: Long? = null,

    @Column(name = "created_by")
    @CreatedBy
    var createdBy: Long? = null,

    @Column(name = "created_on")
    @CreatedDate
    var createdOn: LocalDateTime? = null,

    @Column(name = "updated_by")
    @LastModifiedBy
    var updatedBy: Long? = null,

    @Column(name = "updated_on")
    @LastModifiedDate
    var updatedOn: LocalDateTime? = null,
): BaseEntityKt(id)