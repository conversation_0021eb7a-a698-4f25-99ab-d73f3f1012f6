package com.multiplier.timeoff.repository

import com.multiplier.timeoff.repository.model.CountryDefinitionEntity
import com.multiplier.timeoff.types.CountryCode
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query

interface CountryDefinitionRepository : JpaRepository<CountryDefinitionEntity, Long>,
    JpaSpecificationExecutor<CountryDefinitionEntity> {

    @Query("""
        SELECT cd FROM CountryDefinitionEntity cd 
        WHERE cd.definition.countryCode = :countryCode 
        AND COALESCE(cd.definition.stateCode, '') = :stateCode 
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllByCountryAndState(
        countryCode : CountryCode,
        stateCode: String
    ): List<CountryDefinitionEntity>

    @Query("""
        SELECT cd FROM CountryDefinitionEntity cd 
        WHERE cd.definition.countryCode IS NULL
        AND (cd.definition.stateCode IS NULL OR cd.definition.stateCode = '')
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllDefaultDefinitions(): List<CountryDefinitionEntity>

    @Query("""
        SELECT cd FROM CountryDefinitionEntity cd 
        WHERE (cd.definition.countryCode IS NULL AND (cd.definition.stateCode IS NULL OR cd.definition.stateCode = '')
        OR cd.definition.countryCode IN :countryCodes)
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllDefaultAndByCountryIn(
        countryCodes: Set<CountryCode>
    ): List<CountryDefinitionEntity>

    @Query("""
        SELECT cd FROM CountryDefinitionEntity cd 
        WHERE cd.definition.id IN :definitionIds
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllByDefinitionIdIn(
        definitionIds : Set<Long>
    ): List<CountryDefinitionEntity>




}