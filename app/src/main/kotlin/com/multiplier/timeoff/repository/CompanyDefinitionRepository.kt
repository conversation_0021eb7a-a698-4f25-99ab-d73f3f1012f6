package com.multiplier.timeoff.repository

import com.multiplier.timeoff.repository.model.CompanyDefinitionEntity
import com.multiplier.timeoff.types.EntityType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import java.util.*

interface CompanyDefinitionRepository : JpaRepository<CompanyDefinitionEntity, Long>,
    JpaSpecificationExecutor<CompanyDefinitionEntity> {

    @Query("""
        SELECT COUNT(cd) FROM CompanyDefinitionEntity cd 
        WHERE cd.typeId = :typeId 
        AND cd.definition.status != 'DELETED'
        """
    )
    fun countNonDeletedByTypeId(typeId: Long): Long

    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.typeId IN :typeIds 
        AND cd.companyId = :companyId 
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllNonDeletedByTypeIdInAndCompanyId(
        typeIds: Collection<Long>,
        companyId: Long
    ): List<CompanyDefinitionEntity>

    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.typeId IN :typeIds 
        AND cd.companyId IN :companyIds 
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllNonDeletedByTypeIdInAndCompanyIdIn(
        typeIds: Collection<Long>,
        companyIds: Collection<Long>
    ): List<CompanyDefinitionEntity>

    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.companyId = :companyId 
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllNonDeletedByCompanyId(companyId: Long): List<CompanyDefinitionEntity>


    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.companyId = :companyId 
        AND cd.definition.id IN :definitionIds 
        AND cd.definition.status != 'DELETED'
    """
    )
    fun findAllNonDeletedByDefinitionIdInAndCompanyId(@Param("definitionIds") definitionIds: Collection<Long>, @Param("companyId") companyId: Long): List<CompanyDefinitionEntity>

    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.companyId = :companyId 
        AND cd.entityId IN :entityIds 
        AND cd.definition.status != 'DELETED'
    """
    )
    fun findAllNonDeletedByCompanyIdAndEntityIdIn(@Param("companyId") companyId: Long, @Param("entityIds") entityIds: Collection<Long>): List<CompanyDefinitionEntity>


    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.companyId = :companyId 
        AND cd.definition.id = :definitionId 
        AND cd.definition.status != 'DELETED'
    """
    )
    fun findNonDeletedByDefinitionIdAndCompanyId(definitionId: Long, companyId: Long): Optional<CompanyDefinitionEntity>


    @Query("""
        SELECT COUNT(cd) FROM CompanyDefinitionEntity cd 
        WHERE cd.companyId != :companyId 
        AND cd.definition.id = :definitionId 
        AND cd.definition.status != 'DELETED'
    """
    )
    fun countNonDeletedByDefinitionIdAndCompanyIdNot(definitionId: Long, companyId: Long): Long

    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.companyId IN :companyIds
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllNonDeletedByCompanyIdIn(companyIds: Collection<Long>): List<CompanyDefinitionEntity>

    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.definition.id IN :definitionIds
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findByDefinitionIdIn(definitionIds: Collection<Long>): List<CompanyDefinitionEntity>

    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.definition.id = :definitionId
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findByDefinitionId(definitionId: Long): Optional<CompanyDefinitionEntity>

    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.entityId IS NULL
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllByEntityIdIsNull(): List<CompanyDefinitionEntity>

    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.entityId IS NULL
        AND cd.companyId IN :companyIds
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllByEntityIdIsNullAndCompanyIdIn(companyIds: Collection<Long>): List<CompanyDefinitionEntity>

    @Query("""
        SELECT cd FROM CompanyDefinitionEntity cd 
        WHERE cd.companyId = :companyId 
        AND cd.entityId = :entityId
        AND cd.entityType = :entityType
        AND cd.definition.status != 'DELETED'
        """
    )
    fun findAllNonDeletedByCompanyIdAndEntityIdAndEntityType(companyId: Long, entityId: Long, entityType: EntityType): List<CompanyDefinitionEntity>


}