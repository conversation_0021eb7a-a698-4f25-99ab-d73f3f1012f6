package com.multiplier.timeoff.repository.model

import com.multiplier.timeoff.types.TimeOffUnit
import jakarta.persistence.*



class TimeoffDefinitionValidationEntity(
    var defaultValue: Double? = null,
    var minimumValue: Double? = null,
    val maximumValue: Double? = null,
    @Enumerated(EnumType.STRING) var unit: TimeOffUnit? = null,
    val allowedContractStatuses: List<String> = listOf(),
    var isUnlimitedLeavesAllowed: Boolean? = false,
)