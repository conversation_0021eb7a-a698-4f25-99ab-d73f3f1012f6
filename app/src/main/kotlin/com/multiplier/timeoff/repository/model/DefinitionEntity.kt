package com.multiplier.timeoff.repository.model

import com.multiplier.timeoff.core.common.constant.ColumnDefinition
import com.multiplier.timeoff.core.constant.Database
import com.multiplier.timeoff.types.CountryCode
import com.multiplier.timeoff.types.TimeOffTypeDefinitionStatus
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType
import lombok.experimental.SuperBuilder
import org.hibernate.annotations.BatchSize
import org.hibernate.annotations.Type
import org.hibernate.envers.Audited
import jakarta.persistence.*
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes

@Audited
@Entity
@SuperBuilder
@BatchSize(size = 200)
@Table(
    schema = Database.SCHEMA,
    name = Database.Table.Definition.TABLE_NAME
)
class DefinitionEntity(
    id: Long? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = Database.Table.Definition.Column.COUNTRY_CODE)
    var countryCode: CountryCode? = null,

    @Column(name = Database.Table.Definition.Column.STATE_CODE)
    var stateCode: String? = null,

    @Column(nullable = false)
    var isRequired: Boolean = false,

    var description: String? = null,

    var clause: String? = null,

    var basis: String? = null,

    @JdbcTypeCode(SqlTypes.JSON)
    @Type(JsonBinaryType::class)
    @Column(columnDefinition = ColumnDefinition.JSONB)
    var validations: Set<TimeoffDefinitionValidationEntity> = emptySet(),

    @JdbcTypeCode(SqlTypes.JSON)
    @Type(JsonBinaryType::class)    @Column(columnDefinition = ColumnDefinition.JSONB)
    var configurations: TimeoffDefinitionConfigEntity? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = Database.Table.Definition.Column.STATUS)
    var status: TimeOffTypeDefinitionStatus? = TimeOffTypeDefinitionStatus.ACTIVE,

    @Column(name = Database.Table.Definition.Column.NAME)
    var name: String? = null

    ) : AuditableBaseEntityKt(id)
