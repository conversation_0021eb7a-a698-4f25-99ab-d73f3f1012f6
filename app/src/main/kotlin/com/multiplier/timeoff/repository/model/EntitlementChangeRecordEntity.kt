package com.multiplier.timeoff.repository.model

import com.multiplier.timeoff.core.constant.Database
import java.time.LocalDate
import jakarta.persistence.*

enum class EntitlementChangeCategory {
    NULL_CATEGORY,
    ALLOCATION,
    CARRY_FORWARD,
    EXPIRATION,
    ENCASHMENT,
}

@Entity
@Table(
    schema = Database.SCHEMA,
    name = Database.Table.EntitlementChangeRecord.TABLE_NAME
)
class EntitlementChangeRecordEntity(
    id: Long? = null,

    @Column(name = Database.Table.EntitlementChangeRecord.Column.TYPE_ID)
    val typeId: Long = 0,

    val contractId: Long = 0,

    @Enumerated(EnumType.STRING)
    val category: EntitlementChangeCategory = EntitlementChangeCategory.NULL_CATEGORY,

    var count: Double = 0.0,

    val validFrom: LocalDate? = null,
    val validToInclusive: LocalDate? = null,

    @Column(name = Database.Table.EntitlementChangeRecord.Column.REF_ID)
    val refId: Long? = null,

) : AuditableBaseEntityKt(id)
