package com.multiplier.timeoff.repository.model

import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.multiplier.timeoff.types.TimeOffUnit


@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
class TimeoffDefinitionConfigEntity @JsonCreator constructor(
    @JsonProperty("allocationConfig") val allocationConfig: AllocationConfigEntity?,
    @JsonProperty("carryForwardConfig") val carryForwardConfig: CarryForwardConfigEntity?,
    @JsonProperty("futureLeaveConfig") val futureLeaveConfig: FutureLeaveConfigEntity = FutureLeaveConfigEntity(),
) {
    // Constructor for backward compatibility with existing code
    constructor(allocationConfig: AllocationConfigEntity?, carryForwardConfig: CarryForwardConfigEntity?) : this(allocationConfig, carryForwardConfig, FutureLeaveConfigEntity())
}

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
class AllocationConfigEntity @JsonCreator constructor(
    @JsonProperty("basis") val basis: String? = null,
    @JsonProperty("prorated") val prorated: Boolean = false,
)


@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
class CarryForwardConfigEntity @JsonCreator constructor(
    @JsonProperty("enabled") val enabled: Boolean = false,
    @JsonProperty("minForEligibility") val minForEligibility: CarryForwardLimitEntity?,
    @JsonProperty("maxLimit") val maxLimit: CarryForwardLimitEntity?,
    @JsonProperty("expiry") val expiry: CarryForwardExpiryEntity?,
)

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
class FutureLeaveConfigEntity @JsonCreator constructor(
    @JsonProperty("enabled") val enabled: Boolean = false,
    @JsonProperty("allowedYears") val allowedYears: Int = 1,
    @JsonProperty("lapsableLeaveConfig") val lapsableLeaveConfig: LapsableLeaveConfigEntity = LapsableLeaveConfigEntity(),
)

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
class LapsableLeaveConfigEntity @JsonCreator constructor(
    @JsonProperty("enabled") val enabled: Boolean = false,
    @JsonProperty("expiry") val expiry: LapsableExpiryEntity = LapsableExpiryEntity(),
)

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
class LapsableExpiryEntity @JsonCreator constructor(
    @JsonProperty("value") val value: Double? = 1.0,
    @JsonProperty("unit") val unit: TimeOffUnit? = TimeOffUnit.YEARS
)

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
class CarryForwardLimitEntity @JsonCreator constructor(
    @JsonProperty("type") val type: CarryForwardLimitValueType?,
    @JsonProperty("value") val value: Double?,
    @JsonProperty("unit") val unit: TimeOffUnit? = null,
)

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY, isGetterVisibility = JsonAutoDetect.Visibility.NONE)
class CarryForwardExpiryEntity @JsonCreator constructor(
    @JsonProperty("value") val value: Double?,
    @JsonProperty("unit") val unit: TimeOffUnit? = null,
)

enum class CarryForwardLimitValueType {
    FIXED,
    PERCENTAGE
}
