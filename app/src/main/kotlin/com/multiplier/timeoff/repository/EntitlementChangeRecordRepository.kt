package com.multiplier.timeoff.repository

import com.multiplier.timeoff.repository.model.EntitlementChangeCategory
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import java.time.LocalDate

interface EntitlementChangeRecordRepository : JpaRepository<EntitlementChangeRecordEntity, Long>,
    JpaSpecificationExecutor<EntitlementChangeRecordEntity> {

    @Query("""
        SELECT cr from EntitlementChangeRecordEntity cr
        JOIN TimeoffTypeDBO tt ON tt.id = cr.typeId
        WHERE cr.contractId = :contractId
        AND LOWER(tt.key) = LOWER(:type)
        AND cr.category IN :categories
        """
    )
    fun findAllByTypeAndContractIdAndCategoryIn(
        type: String,
        contractId: Long,
        categories: Set<EntitlementChangeCategory>
    ): List<EntitlementChangeRecordEntity>

    fun findAllByTypeIdAndContractIdAndCategoryIn(
        typeId: Long,
        contractId: Long,
        categories: Set<EntitlementChangeCategory>
    ): List<EntitlementChangeRecordEntity>

    @Query("""
        SELECT cr from EntitlementChangeRecordEntity cr
        JOIN TimeoffTypeDBO tt ON tt.id = cr.typeId
        WHERE cr.contractId IN :contractIds
        AND tt.id IN :typeIds
        AND cr.category IN :categories
        """
    )
    fun findAllByTypeInAndContractIdInAndCategoryIn(
        typeIds: Collection<Long>,
        contractIds: Collection<Long>,
        categories: Set<EntitlementChangeCategory>
    ): List<EntitlementChangeRecordEntity>

    fun findByTypeIdAndContractIdAndCategoryAndValidFromAndValidToInclusive(
        typeId: Long,
        contractId: Long,
        category: EntitlementChangeCategory,
        validFrom: LocalDate,
        validToInclusive: LocalDate
    ): EntitlementChangeRecordEntity?

    fun findAllByCategoryInAndRefIdIn(categories: Set<EntitlementChangeCategory>, ids: Set<Long>): List<EntitlementChangeRecordEntity>

    @Query("SELECT e.refId FROM EntitlementChangeRecordEntity e WHERE e.refId IN :refIds")
    fun findAllRefIdsIn(refIds: List<Long>): List<Long>

    @Query("""
        SELECT cr FROM EntitlementChangeRecordEntity cr
        WHERE cr.count > 0
        AND cr.validToInclusive < :expiryDate
        """)
    fun findByPositiveCountAndExpiredBefore(expiryDate: LocalDate): List<EntitlementChangeRecordEntity>

    @Query("""
        SELECT cr FROM EntitlementChangeRecordEntity cr
        WHERE cr.count > 0
        AND cr.validToInclusive < :expiryDate
        AND cr.contractId IN :contractIds
        """)
    fun findByPositiveCountAndExpiredBeforeAndContractIdIn(expiryDate: LocalDate, contractIds: Set<Long>): List<EntitlementChangeRecordEntity>
    fun findAllByContractIdIn(contractIds: Set<Long>): List<EntitlementChangeRecordEntity>
}