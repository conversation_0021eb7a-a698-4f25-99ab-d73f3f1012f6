package com.multiplier.timeoff.processor

import com.google.common.collect.ImmutableList
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity
import com.multiplier.timeoff.repository.model.TimeoffDBO
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import kotlin.math.min

internal class BalanceStore(
    allocationRecords: Collection<EntitlementChangeRecordEntity>,
    deductionRecords: Collection<EntitlementChangeRecordEntity>,
    timeoffs: Collection<TimeoffDBO>
) {

    private lateinit var balanceChangeEvents: List<BalanceChangeEvent>
    private lateinit var balanceEntries: List<BalanceEntry>

    init {
        init(allocationRecords, deductionRecords, timeoffs)
    }

    private fun init(
        allocationRecords: Collection<EntitlementChangeRecordEntity>,
        deductionRecords: Collection<EntitlementChangeRecordEntity>,
        timeoffs: Collection<TimeoffDBO>
    ) {
        initBalanceEntries(allocationRecords)
        initBalanceChangeEvents(deductionRecords, timeoffs)
        calculateRemainingBalance()
    }

    private fun initBalanceEntries(allocationRecords: Collection<EntitlementChangeRecordEntity>) {
        balanceEntries = allocationRecords.map { BalanceEntry(
            it.id!!,
            it.validFrom!!,
            it.validToInclusive ?: LocalDate.MAX,
            it.count
        ) }
    }

    private fun initBalanceChangeEvents(deductionRecords: Collection<EntitlementChangeRecordEntity>, timeoffs: Collection<TimeoffDBO>) {
        val tempList = mutableListOf<BalanceChangeEvent>()
        for (deductionRecord in deductionRecords) {
            tempList.add(BalanceChangeEvent(deductionRecord.createdOn!!,deductionRecord))
        }
        timeoffs.forEach { tempList.add(BalanceChangeEvent(it.createdOn(), it)) }
        tempList.sortBy { it.timestamp }

        balanceChangeEvents = ImmutableList.copyOf(tempList)
    }

    private fun calculateRemainingBalance() {
        balanceChangeEvents.forEach { updateBalanceOnChangeEvent(it) }
    }

    private fun updateBalanceOnChangeEvent(changeEvent: BalanceChangeEvent) {
        if (changeEvent.value is EntitlementChangeRecordEntity) {
            balanceEntries.find { entry -> entry.changeRecordId == changeEvent.value.refId }
                ?.apply {
                    this.count -= changeEvent.value.count
                }
        } else if (changeEvent.value is TimeoffDBO) {
            deductTimeoffFromAvailableBalances(changeEvent.value.noOfDays(), changeEvent.value.startDate(), changeEvent.value.endDate())
        }
    }

    /**
     * Deducts requested timeoff from available balances. The function returns 0.0 if the whole requested `noOfDays` can be
     * deducted completely from available balances, otherwise it returns the number of days which can't be deducted (available
     * balances can't accommodate these dates).
     */
    fun deductTimeoffFromAvailableBalances(noOfDays: Double, startDate: LocalDate, endDate: LocalDate): Double {
        // get all balance entries which validity range intersect with the timeoff range
        // order these entries by chronological order of `validToInclusive`
        // deduct the balance from the first sorted entry (first to-be-expired entry)
        val relatedBalanceEntries = balanceEntries.filter {
            it.count > 0 && it.validFrom <= endDate && it.validToInclusive >= startDate
        }.sortedBy { it.validToInclusive }.toMutableList()
        var remainingDays = noOfDays
        while (relatedBalanceEntries.isNotEmpty() && remainingDays > 0) {
            val remainingBalanceFromStartDate = startDate.until(relatedBalanceEntries.first().validToInclusive, ChronoUnit.DAYS).toDouble().plus(1)
            val canBeAppliedDays = min(
                relatedBalanceEntries.first().count,
                remainingBalanceFromStartDate
            )

            if (canBeAppliedDays >= remainingDays) {
                relatedBalanceEntries.first().count -= remainingDays
                remainingDays = 0.0
            }
            else {
                remainingDays -= canBeAppliedDays
                relatedBalanceEntries.first().count -= canBeAppliedDays
                relatedBalanceEntries.removeFirst()
            }
        }

        return remainingDays
    }

    override fun toString(): String {
        return """ID     Valid From          Valid To        Count
${balanceEntries.joinToString(separator = "\n")}
Total:                                     ${balanceEntries.sumOf { it.count }}"""
    }

    class BalanceChangeEvent(
        val timestamp: LocalDateTime,
        val value: Any
    )

    class BalanceEntry(
        val changeRecordId: Long,
        val validFrom: LocalDate,
        val validToInclusive: LocalDate,
        var count: Double
    ) {
        override fun toString(): String {
            return "$changeRecordId     $validFrom          $validToInclusive       $count"
        }
    }
}