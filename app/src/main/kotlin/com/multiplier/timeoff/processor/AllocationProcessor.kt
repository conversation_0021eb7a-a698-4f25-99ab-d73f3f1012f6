package com.multiplier.timeoff.processor

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.repository.EntitlementChangeRecordRepository
import com.multiplier.timeoff.repository.model.EntitlementChangeCategory
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO
import com.multiplier.timeoff.service.TimeoffSummaryService
import com.multiplier.timeoff.types.TimeOffAllocationInput
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class AllocationProcessor(
    private val timeoffSummaryService: TimeoffSummaryService,
    private val entitlementChangeRecordRepository: EntitlementChangeRecordRepository,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val featureFlagService: FeatureFlagService
) {

    private val log by logger()

    fun allocateTimeoffBalances(input: TimeOffAllocationInput?) {
        for (expiredSummary in getExpiredSummaries(input)) {
            try {
                val successorSummary = allocateTimeoffBalance(expiredSummary) ?: continue
                recordAllocationEvent(successorSummary)
            }
            catch (e: Exception) {
                log.error("Failed to allocate timeoff balance for expired summary id = {}", expiredSummary.id, e)
            }
        }
    }

    private fun getExpiredSummaries(input: TimeOffAllocationInput?): List<TimeoffSummaryDBO> {
        val expiryDate = input?.expiryDate ?: LocalDate.now()
        val nonExpiredSummaries = if (input?.companyIds?.isNotEmpty() == true) {
            getAllExpiredSummariesForContractsInGivenCompanies(expiryDate, input.companyIds)
        } else {
            getAllExpiredSummaries(expiryDate)
        }
        log.info("Found {} timeoff summaries which have been expired before {}", nonExpiredSummaries.size, expiryDate)

        return nonExpiredSummaries
    }

    private fun getAllExpiredSummariesForContractsInGivenCompanies(expiryDate: LocalDate, companyIds: List<Long>): List<TimeoffSummaryDBO> {
        val contractIds = contractServiceAdapter.getContractIdsByCompanyIds(companyIds)
        val nonDeletedNonEndedContracts = contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(contractIds)
        val carryForwardExpiryEnabledContractIds =
            getCarryForwardExpiryEnabledContracts(nonDeletedNonEndedContracts).map { it.id }
        if (carryForwardExpiryEnabledContractIds.isNotEmpty()) {
            return timeoffSummaryService.getLatestSummariesExpiredBeforeForContracts(
                expiryDate,
                carryForwardExpiryEnabledContractIds
            )
        }

        return emptyList()
    }

    private fun getAllExpiredSummaries(expiryDate: LocalDate): List<TimeoffSummaryDBO> {
        val summaries = timeoffSummaryService.getLatestSummariesExpiredBefore(expiryDate)
        val nonDeletedNonEndedContracts =
            contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(summaries.map { it.contractId() })
        val carryForwardExpiryEnabledContractIds =
            getCarryForwardExpiryEnabledContracts(nonDeletedNonEndedContracts).map { it.id }

        return summaries.filter { carryForwardExpiryEnabledContractIds.contains(it.contractId()) }
    }

    private fun allocateTimeoffBalance(expiredSummary: TimeoffSummaryDBO): TimeoffSummaryDBO? {
        try {
            val newSummary = timeoffSummaryService.reallocateTimeoff(expiredSummary)
            if (newSummary != null) {
                log.info("New timeoff balance allocated, id = {}", newSummary.id)
            }
            else {
                log.warn("Allocation for expired summary id = {} doesn't happen, checkout TimeoffSummaryService logs for more info", expiredSummary.id)
            }

            return newSummary
        } catch (e: Exception) {
            log.error("Failed to allocate timeoff balance for expired summary id = {}", expiredSummary.id, e)
        }

        return null
    }

    private fun recordAllocationEvent(successorSummary: TimeoffSummaryDBO) {
        val changeRecord = EntitlementChangeRecordEntity(
            typeId = successorSummary.typeId(),
            contractId = successorSummary.contractId(),
            category = EntitlementChangeCategory.ALLOCATION,
            count = successorSummary.allocatedCount(),
            validFrom = successorSummary.periodStart(),
            validToInclusive = successorSummary.periodEnd()
        )
        entitlementChangeRecordRepository.save(changeRecord)
    }

    fun backfillAllocationRecords(input: TimeOffAllocationInput?) {
        for (nonExpiredSummary in getNonExpiredSummaries(input)) {
            try {
                recordAllocationEventIfNotYetDone(nonExpiredSummary)
            }
            catch (e: Exception) {
                log.error("Failed to back fill allocation record for summary id = {}", nonExpiredSummary.id, e)
            }
        }
    }

    private fun recordAllocationEventIfNotYetDone(nonExpiredSummary: TimeoffSummaryDBO) {
        val changeRecord =
            entitlementChangeRecordRepository.findByTypeIdAndContractIdAndCategoryAndValidFromAndValidToInclusive(
                nonExpiredSummary.typeId(),
                nonExpiredSummary.contractId(),
                EntitlementChangeCategory.ALLOCATION,
                nonExpiredSummary.periodStart(),
                nonExpiredSummary.periodEnd()
            )
        if (changeRecord != null) {
            return
        }
        recordAllocationEvent(nonExpiredSummary)
    }

    private fun getNonExpiredSummaries(input: TimeOffAllocationInput?): List<TimeoffSummaryDBO> {
        val expiryDate = input?.expiryDate ?: LocalDate.now()
        val nonExpiredSummaries = if (input?.companyIds?.isNotEmpty() == true) {
            getAllNonExpiredSummariesForContractsInGivenCompanies(expiryDate, input.companyIds)
        } else {
            getAllNonExpiredSummaries(expiryDate)
        }
        log.info("Found {} timeoff summaries which are not expired on or after {}", nonExpiredSummaries.size, expiryDate)

        return nonExpiredSummaries
    }

    private fun getAllNonExpiredSummariesForContractsInGivenCompanies(expiryDate: LocalDate, companyIds: List<Long>): List<TimeoffSummaryDBO> {
        val contractIds = contractServiceAdapter.getContractIdsByCompanyIds(companyIds)
        val nonDeletedNonEndedContracts = contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(contractIds)
        val carryForwardExpiryEnabledContractIds =
            getCarryForwardExpiryEnabledContracts(nonDeletedNonEndedContracts).map { it.id }
        if (carryForwardExpiryEnabledContractIds.isNotEmpty()) {
            return timeoffSummaryService.getSummariesNotExpiredOnOrAfterForContracts(
                expiryDate,
                carryForwardExpiryEnabledContractIds
            )
        }

        return emptyList()
    }

    private fun getAllNonExpiredSummaries(expiryDate: LocalDate): List<TimeoffSummaryDBO> {
        val summaries = timeoffSummaryService.getSummariesNotExpiredOnOrAfter(expiryDate)
        val nonDeletedNonEndedContracts =
            contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(summaries.map { it.contractId() })
        val carryForwardExpiryEnabledContractIds =
            getCarryForwardExpiryEnabledContracts(nonDeletedNonEndedContracts).map { it.id }

        return summaries.filter { carryForwardExpiryEnabledContractIds.contains(it.contractId()) }
    }

    private fun getCarryForwardExpiryEnabledContracts(contracts: List<Contract>): List<Contract> {
        return contracts.filter { isCarryForwardExpiryEnabled(it.companyId) }
    }

    private fun isCarryForwardExpiryEnabled(companyId: Long): Boolean {
        return featureFlagService.feature(
            FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY,
            mapOf("company" to companyId)
        ).on
    }
}