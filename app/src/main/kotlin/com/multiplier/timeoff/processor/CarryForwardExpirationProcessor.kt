package com.multiplier.timeoff.processor

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.repository.EntitlementChangeRecordRepository
import com.multiplier.timeoff.repository.model.EntitlementChangeCategory
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity
import com.multiplier.timeoff.service.TimeoffSummaryService
import com.multiplier.timeoff.types.TimeOffAllocationInput
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class CarryForwardExpirationProcessor(
    private val entitlementChangeRecordRepository: EntitlementChangeRecordRepository,
    private val timeoffSummaryService: TimeoffSummaryService,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val featureFlagService: FeatureFlagService
) {

    private val log by logger()

    fun expireCarryForwardTimeoffBalances(input: TimeOffAllocationInput?) {
        for (carryForwardRecord in getExpiredCarryForwardRecords(input)) {
            try {
                expireCarryForwardTimeoffBalance(carryForwardRecord)
            }
            catch (e: Exception) {
                log.error("Failed to expire carry forward timeoff balance of record id = {}", carryForwardRecord.id)
            }
        }
    }

    private fun expireCarryForwardTimeoffBalance(carryForwardRecord: EntitlementChangeRecordEntity) {
        try {
            timeoffSummaryService.deductCarryForwardLeave(carryForwardRecord.refId!!, carryForwardRecord.count)
        } catch (e: Exception) {
            log.error("Failed to deduct carry forward leave from summary id = {}, count = {}", carryForwardRecord.refId, carryForwardRecord.count)
            return
        }

        entitlementChangeRecordRepository.save(
            EntitlementChangeRecordEntity(
                typeId = carryForwardRecord.typeId,
                contractId = carryForwardRecord.contractId,
                category = EntitlementChangeCategory.EXPIRATION,
                count = carryForwardRecord.count,
                refId = carryForwardRecord.id
            )
        )
    }

    private fun getExpiredCarryForwardRecords(input: TimeOffAllocationInput?): List<EntitlementChangeRecordEntity> {
        val expiryDate = input?.expiryDate ?: LocalDate.now()
        val expiredCarryForwardRecords = if (input?.companyIds?.isNotEmpty() == true) {
            getAllExpiredCarryForwardRecordsForContractsInGivenCompanies(expiryDate, input.companyIds)
        } else {
            getAllExpiredCarryForwardRecords(expiryDate)
        }
        log.info("Found {} carry forward records which have been expired before {}", expiredCarryForwardRecords.size, expiryDate)

        return expiredCarryForwardRecords
    }

    private fun getAllExpiredCarryForwardRecordsForContractsInGivenCompanies(
        expiryDate: LocalDate,
        companyIds: List<Long>
    ): List<EntitlementChangeRecordEntity> {
        val contractIds = contractServiceAdapter.getContractIdsByCompanyIds(companyIds)
        val nonDeletedNonEndedContracts = contractServiceAdapter.getNonDeletedNonEndedContractsByIds(contractIds)
        val carryForwardEnabledContracts = getCarryForwardExpiryEnabledContracts(nonDeletedNonEndedContracts)
        if (carryForwardEnabledContracts.isNotEmpty()) {
            return entitlementChangeRecordRepository.findByPositiveCountAndExpiredBeforeAndContractIdIn(expiryDate, carryForwardEnabledContracts.keys)
        }

        return emptyList()
    }

    private fun getAllExpiredCarryForwardRecords(expiryDate: LocalDate): List<EntitlementChangeRecordEntity> {
        val expiredCarryForwardRecords = entitlementChangeRecordRepository.findByPositiveCountAndExpiredBefore(expiryDate)
        val nonDeletedNonEndedContracts =
            contractServiceAdapter.getNonDeletedNonEndedContractsByIds(expiredCarryForwardRecords.map { it.contractId })
        val carryForwardEnabledContracts = getCarryForwardExpiryEnabledContracts(nonDeletedNonEndedContracts)

        return expiredCarryForwardRecords.filter { carryForwardEnabledContracts.containsKey(it.contractId) }
    }

    private fun getCarryForwardExpiryEnabledContracts(contracts: List<ContractOuterClass.Contract>): Map<Long, ContractOuterClass.Contract> {
        return contracts.filter { isCarryForwardExpiryEnabled(it.companyId) }.associateBy { it.id }
    }

    private fun isCarryForwardExpiryEnabled(companyId: Long): Boolean {
        return featureFlagService.feature(
            FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY,
            mapOf("company" to companyId)
        ).on
    }
}