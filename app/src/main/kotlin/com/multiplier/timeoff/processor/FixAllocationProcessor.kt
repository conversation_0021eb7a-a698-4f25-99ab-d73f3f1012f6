package com.multiplier.timeoff.processor

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.repository.TimeoffEntitlementDBORepository
import com.multiplier.timeoff.repository.TimeoffSummaryRepository
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO
import com.multiplier.timeoff.service.CarryForwardLeaveService
import com.multiplier.timeoff.service.TimeOffConfigurationService
import com.multiplier.timeoff.service.TimeoffSummaryService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate

@Service
class FixAllocationProcessor(
    private val summaryRepository: TimeoffSummaryRepository,
    private val entitlementDBORepository: TimeoffEntitlementDBORepository,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val timeOffConfigurationService: TimeOffConfigurationService,
    private val timeoffSummaryService: TimeoffSummaryService,
    private val carryForwardLeaveService: CarryForwardLeaveService
) {
    private val log by logger()

    @Transactional
    fun fixTimeoffAllocations(summaryIds: List<Long>, dryRun: Boolean) {
        for (summaryId in summaryIds) {
            try {
                fixTimeoffAllocation(summaryId, dryRun)
            }
            catch (e: Exception) {
                log.error("Failed to fix timeoff allocation for summaryID = {}", summaryId, e)
            }
        }
    }

    private fun fixTimeoffAllocation(summaryId: Long, dryRun: Boolean) {
        val summaryDBOOpt = summaryRepository.findById(summaryId)
        if (summaryDBOOpt.isEmpty) {
            log.warn("Timeoff summary with ID = {} not found", summaryId)
            return
        }

        val summaryDBO = summaryDBOOpt.get()
        val contract = contractServiceAdapter.findContractByContractId(summaryDBO.contractId())
        if (contract.status == ContractOuterClass.ContractStatus.ENDED
            || contract.status == ContractOuterClass.ContractStatus.DELETED) {
            log.info("Skip fixing timeoff allocation, contract with ID = {} is ENDED or DELETED", contract.id)
            return
        }

        log.info("Trying to fix timeoff allocation for summaryID = {}, contractID = {}", summaryId, summaryDBO.contractId())
        val entitlementOpt = entitlementDBORepository.findByContractIdAndTypeId(summaryDBO.contractId(), summaryDBO.typeId())
        if (entitlementOpt.isEmpty) {
            log.warn("Entitlement for typeId = {} and contractId = {} not found", summaryDBO.typeId(), summaryDBO.contractId())
            return
        }
        var mutableSummaryDBO = clone(summaryDBO)
        mutableSummaryDBO = timeOffConfigurationService.fixTimeoffAllocation(mutableSummaryDBO, entitlementOpt.get(), contract)
        if (mutableSummaryDBO != null) {
            mutableSummaryDBO = timeoffSummaryService.updateSummary(mutableSummaryDBO)
            if (!dryRun) {
                summaryDBO.allocatedCount(mutableSummaryDBO.allocatedCount())
                summaryDBO.carriedCount(mutableSummaryDBO.carriedCount())
                summaryDBO.totalEntitledCount(mutableSummaryDBO.totalEntitledCount())
                summaryDBO.takenCount(mutableSummaryDBO.takenCount())
                summaryDBO.pendingCount(mutableSummaryDBO.pendingCount())
                log.info("Fix timeoff allocation completed, summaryID = {}", summaryDBO.id)
            }
            else {
                log.info("Fix timeoff allocation dry run output: summaryID = {}, allocatedCount = {}, carriedCount = {}, totalEntitledCount = {}, takenCount = {}, pendingCount = {}",
                    mutableSummaryDBO.id,
                    mutableSummaryDBO.allocatedCount(),
                    mutableSummaryDBO.carriedCount(),
                    mutableSummaryDBO.totalEntitledCount(),
                    mutableSummaryDBO.takenCount(),
                    mutableSummaryDBO.pendingCount()
                )
            }

            if (summaryExpired(mutableSummaryDBO)) {
                fixCarryForward(mutableSummaryDBO, entitlementOpt.get(), contract, dryRun)
            }
        }
    }

    private fun summaryExpired(summaryDBO: TimeoffSummaryDBO) =
        summaryDBO.periodEnd().isBefore(LocalDate.now())

    private fun fixCarryForward(
        expiredSummaryDBO: TimeoffSummaryDBO,
        entitlement: TimeoffEntitlementDBO,
        contract: ContractOuterClass.Contract,
        dryRun: Boolean
    ) {
        val successorSummaryDBO = timeoffSummaryService.getSuccessor(expiredSummaryDBO)
        if (successorSummaryDBO == null) {
            log.info("Skip fixing carry forward, no successor found for summaryID = {}", expiredSummaryDBO.id)
            return
        }
        var mutableSummaryDBO = clone(successorSummaryDBO)
        carryForwardLeaveService.carryForwardLeaves(entitlement, expiredSummaryDBO, mutableSummaryDBO, contract)
        mutableSummaryDBO = timeoffSummaryService.updateSummary(mutableSummaryDBO)
        if (!dryRun) {
            successorSummaryDBO.allocatedCount(mutableSummaryDBO.allocatedCount())
            successorSummaryDBO.carriedCount(mutableSummaryDBO.carriedCount())
            successorSummaryDBO.totalEntitledCount(mutableSummaryDBO.totalEntitledCount())
            successorSummaryDBO.takenCount(mutableSummaryDBO.takenCount())
            successorSummaryDBO.pendingCount(mutableSummaryDBO.pendingCount())
            log.info("Fix carry forward completed, summaryID = {}", expiredSummaryDBO.id)
        }
        else {
            log.info("Fix carry forward dry run output: summaryID = {}, allocatedCount = {}, carriedCount = {}, totalEntitledCount = {}, takenCount = {}, pendingCount = {}",
                mutableSummaryDBO.id,
                mutableSummaryDBO.allocatedCount(),
                mutableSummaryDBO.carriedCount(),
                mutableSummaryDBO.totalEntitledCount(),
                mutableSummaryDBO.takenCount(),
                mutableSummaryDBO.pendingCount()
            )
        }
    }

    /**
     * This method is used to return a cloned summary, the purpose is to support dry run mode, where update to the summary doesn't
     * get saved into the database automatically because of @Transactional annotation.
     */
    private fun clone(summary: TimeoffSummaryDBO): TimeoffSummaryDBO {
        val clonedSummary = TimeoffSummaryDBO(
            summary.typeId(),
            summary.contractId(),
            summary.periodStart(),
            summary.periodEnd(),
            summary.totalEntitledCount(),
            summary.allocatedCount(),
            summary.carriedCount(),
            summary.takenCount(),
            summary.pendingCount(),
            summary.timeoffType(),
            summary.status(),
            summary.carryForwardCount(),
            summary.carryForwardExpiredCount(),
            summary.usedFromAllocatedCount(),
            summary.usedFromCarryForwardCount(),
            summary.usedFromLapsableCount(),
            summary.usedFromNextCycleCarryForwardCount()
        )
        clonedSummary.id(summary.id)

        return clonedSummary
    }
}