package com.multiplier.timeoff.processor

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractBasicInfo
import com.multiplier.timeoff.adapters.HolidayServiceAdapter
import com.multiplier.timeoff.core.common.db.AuditUser
import com.multiplier.timeoff.core.common.dto.TimeOffUsageSummaryDTO
import com.multiplier.timeoff.core.common.dto.WorkshiftDTO
import com.multiplier.timeoff.core.common.exceptionhandler.MPLError
import com.multiplier.timeoff.core.common.exceptionhandler.MPLErrorType
import com.multiplier.timeoff.core.util.TimeOffUtil
import com.multiplier.timeoff.core.util.WorkshiftUtil
import com.multiplier.timeoff.exception.TimeoffValidationException
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.repository.EntitlementChangeRecordRepository
import com.multiplier.timeoff.repository.TimeoffRepository
import com.multiplier.timeoff.repository.TimeoffTypeRepository
import com.multiplier.timeoff.repository.model.EntitlementChangeCategory
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity
import com.multiplier.timeoff.repository.model.TimeoffDBO
import com.multiplier.timeoff.repository.model.TimeoffEntryDBO
import com.multiplier.timeoff.service.TimeoffCalendarService
import com.multiplier.timeoff.types.*
import org.springframework.stereotype.Service
import java.time.LocalDate
import kotlin.math.floor

/**
 * This class provides methods to validate if a timeoff request can be taken or not, e.g.: the user has enough timeoff balance.
 */
@Service
class TimeoffValidator(
    private val entitlementChangeRecordRepository: EntitlementChangeRecordRepository,
    private val timeoffRepository: TimeoffRepository,
    private val timeoffTypeRepository: TimeoffTypeRepository,
    private val holidayServiceAdapter: HolidayServiceAdapter,
    private val timeoffCalendarService: TimeoffCalendarService,
    private val featureFlagService: FeatureFlagService,
    private val currentUser: CurrentUser,
) {

    private val log by logger()

    companion object {
        val ALLOCATION_CATEGORIES = setOf(
            EntitlementChangeCategory.ALLOCATION,
            EntitlementChangeCategory.CARRY_FORWARD
        )

        val DEDUCTION_CATEGORIES = setOf(
            EntitlementChangeCategory.ENCASHMENT
        )

        val PENDING_OR_TAKEN_STATUSES = setOf(
            TimeOffStatus.APPROVAL_IN_PROGRESS,
            TimeOffStatus.APPROVED,
            TimeOffStatus.TAKEN
        )
    }

    /**
     * Validation flow for timeoffCreateV2 request
     */
    fun preValidateTimeoffCreateV2(
        contract: ContractBasicInfo,
        input: TimeOffCreateInputV2,
        workshift: WorkshiftDTO,
        timeoffDBO: TimeoffDBO)
    {
        // contract validation
        throwIfContractIsNotValid(contract)

        // input validation
        throwIfTimeoffTypeIdIsNotValid(input.typeId, contract)
        throwIfTimeoffStartDateIsNotValid(input.startDate, contract, workshift)
        throwIfTimeoffEndDateIsNotValid(input.endDate, contract, workshift)
        throwIfStartAndEndDateIsNotValid(input.startDate, input.endDate)
        throwIfTimeoffInputIsOverlapped(input.startDate, input.endDate, contract.contractId)

        // validation for existing timeoffs (submitting draft)
        throwIfAccessDeniedForExistingTimeoff(timeoffDBO, input, contract)
    }

    private fun throwIfAccessDeniedForExistingTimeoff(
        timeoffDBO: TimeoffDBO,
        createInput: TimeOffCreateInputV2,
        contract: ContractBasicInfo
    ) {
        // Skip validation for new timeoffs
        createInput.timeOffId ?: return

        if (timeoffDBO.status() != TimeOffStatus.DRAFT) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0525_INVALID_TIMEOFF_STATUS_TO_SUBMIT, mapOf("timeoffId" to timeoffDBO.id(), "timeoffStatus" to timeoffDBO.status().name)))
        }
        // Validate based on user experience
        when (currentUser.context?.experience) {
            TimeOffUtil.MEMBER_EXP -> throwIfMemberCannotSubmitDraftTimeoff(timeoffDBO, contract)
            TimeOffUtil.COMPANY_EXP -> throwIfCompanyUserCannotSubmitDraftTimeoff(timeoffDBO)
            else -> throw TimeoffValidationException(MPLError(MPLErrorType.MPL0527_UNAUTHORIZED_EXPERIENCE_TO_SUBMIT_TIMEOFFS, mapOf("experience" to currentUser.context?.experience.toString())))
        }
    }

    private fun throwIfMemberCannotSubmitDraftTimeoff(timeoffDBO: TimeoffDBO, contract: ContractBasicInfo) {
        when {
            isCreatedByUndefined(timeoffDBO) -> throwIfTimeoffNotBelongsToMe(timeoffDBO, contract)
            timeoffDBO.createdByInfo() == null -> throwIfCreatedByIsNotMe(timeoffDBO)
            else -> throwIfCreatedByInfoIsNotMe(timeoffDBO.createdByInfo()!!, timeoffDBO)
        }
    }


    private fun throwIfCompanyUserCannotSubmitDraftTimeoff(timeoffDBO: TimeoffDBO) {
        if (isCreatedByUndefined(timeoffDBO)) return

        val createdByInfo = timeoffDBO.createdByInfo()

        when {
            // No created_by_info, check only created_by
            createdByInfo == null -> {
                if (timeoffDBO.createdBy() != currentUser.context?.id) {
                    throw TimeoffValidationException(MPLError(
                        MPLErrorType.MPL0526_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_COMPANY,
                        mapOf("timeoffId" to timeoffDBO.id())
                    ))
                }
            }
            // Has created_by_info, check both userId and experience
            TimeOffUtil.MEMBER_EXP == createdByInfo.experience || createdByInfo.userId != currentUser.context?.id -> {
                throw TimeoffValidationException(MPLError(
                    MPLErrorType.MPL0526_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_COMPANY,
                    mapOf("timeoffId" to timeoffDBO.id())
                ))
            }
        }

    }

    private fun throwIfTimeoffNotBelongsToMe(timeoffDBO: TimeoffDBO, contract: ContractBasicInfo) {
        if (timeoffDBO.contractId() != contract.contractId) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0525_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_MEMBER, mapOf("timeoffId" to timeoffDBO.id())))
        }
    }

    private fun throwIfCreatedByInfoIsNotMe(createdByInfo: AuditUser, timeoffDBO: TimeoffDBO) {
        // this timeoff has `created_by_info` != null, comparing both userId and experience
        if (TimeOffUtil.COMPANY_EXP == createdByInfo.experience || createdByInfo.userId != currentUser.context?.id) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0525_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_MEMBER, mapOf("timeoffId" to timeoffDBO.id())))
        }
    }

    private fun throwIfCreatedByIsNotMe(timeoffDBO: TimeoffDBO) {
        if (timeoffDBO.createdBy() != currentUser.context?.id) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0525_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_MEMBER, mapOf("timeoffId" to timeoffDBO.id())))
        }
    }

    private fun isCreatedByUndefined(timeoffDBO: TimeoffDBO): Boolean {
        return timeoffDBO.createdBy() == null || timeoffDBO.createdBy() == -1L
    }

    fun throwIfBalanceIsInsufficient(input: TimeOffCreateInput, contract: Contract) {
        if (!isCarryForwardExpiryEnabled(contract.companyId)) {
            return
        }

        val requestedEndDate = input.endDate?.dateOnly ?: input.startDate.dateOnly.plusDays(floor(input.noOfDays).toLong())
        log.info("Validating if balance is sufficient for accommodating timeoff request of ${input.noOfDays} days from ${input.startDate.dateOnly} to $requestedEndDate")

        val allocationRecords = entitlementChangeRecordRepository.findAllByTypeAndContractIdAndCategoryIn(input.type, contract.id, ALLOCATION_CATEGORIES)
        val deductionRecords = entitlementChangeRecordRepository.findAllByCategoryInAndRefIdIn(DEDUCTION_CATEGORIES, allocationRecords.map { it.id!! }.toSet())
        val timeoffs = timeoffRepository.findAllByTypeAndContractIdAndStatusIn(input.type, contract.id, PENDING_OR_TAKEN_STATUSES)
        val balanceStore = BalanceStore(allocationRecords, deductionRecords, timeoffs)
        log.info("In-memory balance data before validation:\n$balanceStore")

        val unableToAccommodateCount = balanceStore.deductTimeoffFromAvailableBalances(input.noOfDays, input.startDate.dateOnly, requestedEndDate)
        log.info("In-memory balance data after applying input request:\n$balanceStore")

        if (unableToAccommodateCount > 0) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0500_INSUFFICIENT_BALANCE))
        }
    }

    fun throwIfBalanceIsInsufficient(usageSummaryDTO: TimeOffUsageSummaryDTO) {
        if (usageSummaryDTO.currentSummaryBalance <= 0 || usageSummaryDTO.nextSummaryBalance <=0) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0500_INSUFFICIENT_BALANCE))
        }
    }

    fun throwIfBalanceIsInsufficient(input: TimeOffCreateInputV2, noOfDays: Double, contract: ContractBasicInfo) {
        if (!isCarryForwardExpiryEnabled(contract.companyId)) {
            return
        }

        log.info(
            "Validating if balance is sufficient for accommodating timeoff request from ${
                input.startDate
                    .dateOnly
            } to ${input.endDate?.dateOnly}"
        )

        val allocationRecords = entitlementChangeRecordRepository.findAllByTypeIdAndContractIdAndCategoryIn(
            input
                .typeId, contract.contractId, ALLOCATION_CATEGORIES
        )
        val deductionRecords = entitlementChangeRecordRepository.findAllByCategoryInAndRefIdIn(
            DEDUCTION_CATEGORIES,
            allocationRecords.map { it.id!! }.toSet()
        )
        val timeoffs = timeoffRepository.findAllByTypeIdAndContractIdAndStatusIn(
            input.typeId, contract.contractId,
            PENDING_OR_TAKEN_STATUSES
        )
        val balanceStore = BalanceStore(allocationRecords, deductionRecords, timeoffs)
        log.info("In-memory balance data before validation:\n$balanceStore")

        val unableToAccommodateCount = balanceStore.deductTimeoffFromAvailableBalances(
            noOfDays, input.startDate.dateOnly,
            input.endDate?.dateOnly!!
        )
        log.info("In-memory balance data after applying input request:\n$balanceStore")

        if (unableToAccommodateCount > 0) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0500_INSUFFICIENT_BALANCE))
        }
    }

    fun isBalanceSufficient(
        startDate: LocalDate,
        endDate: LocalDate,
        noOfDays: Double,
        allocationRecords: List<EntitlementChangeRecordEntity>,
        deductionRecords: List<EntitlementChangeRecordEntity>,
        timeoffs: List<TimeoffDBO>,
    ): Boolean {

        log.info("Validating if balance is sufficient for accommodating timeoff request of $noOfDays days from $startDate to $endDate")

        val balanceStore = BalanceStore(allocationRecords, deductionRecords, timeoffs)
        log.info("In-memory balance data before validation:\n$balanceStore")

        val unableToAccommodateCount = balanceStore.deductTimeoffFromAvailableBalances(noOfDays, startDate, endDate)
        log.info("In-memory balance data after applying input request:\n$balanceStore")

        return unableToAccommodateCount <= 0
    }

    private fun isCarryForwardExpiryEnabled(companyId: Long): Boolean {
        return featureFlagService.feature(
            FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY,
            mapOf("company" to companyId)
        ).on
    }

    fun throwIfTimeoffBreakdownInputIsNotValid(
        input: TimeOffBreakdownInput, contract: ContractBasicInfo, workshift: WorkshiftDTO
    ): Boolean {
        throwIfTimeoffStartDateIsNotValid(input.startDate, contract, workshift)
        throwIfTimeoffEndDateIsNotValid(input.endDate, contract, workshift)
        throwIfStartAndEndDateIsNotValid(input.startDate, input.endDate)
        throwIfTimeoffInputIsOverlapped(input.startDate, input.endDate, contract.contractId)
        return true
    }

    private fun throwIfTimeoffInputIsOverlapped(
        startDate: TimeOffDateInput, endDate: TimeOffDateInput, contractId: Long
    ): Boolean {
        val inputStartSession = startDate.session
        val inputEndSession = endDate.session
        val inputStartDate = startDate.dateOnly
        val inputEndDate = endDate.dateOnly

        val overlappingTimeoffs = timeoffRepository.findAllTimeoffsWithinRangeByContractId(
            contractId, PENDING_OR_TAKEN_STATUSES, inputStartDate, inputEndDate
        )

        val overlappingTimeoffEntryList = overlappingTimeoffs.flatMap { it.timeoffEntries() }

        for (timeoffEntry in overlappingTimeoffEntryList) {
            if (timeoffEntry.date() == inputStartDate) {
                validateOverlappingSession(timeoffEntry, inputStartSession)
            }
            if (timeoffEntry.date() == inputEndDate) {
                validateOverlappingSession(timeoffEntry, inputEndSession)
            }
            if (timeoffEntry.date().isAfter(inputStartDate) && timeoffEntry.date().isBefore(inputEndDate)) {
                throw TimeoffValidationException(MPLError(MPLErrorType.MPL0516_INVALID_TIMEOFF_OVERLAPPING))
            }
        }

        return true
    }

    private fun validateOverlappingSession(
        timeoffEntry: TimeoffEntryDBO,
        session: TimeOffSession
    ) {
        if (timeoffEntry.session() == TimeOffSession.FULL_DAY) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0516_INVALID_TIMEOFF_OVERLAPPING))
        }
        if (timeoffEntry.session() == session) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0516_INVALID_TIMEOFF_OVERLAPPING))
        }
    }

    private fun throwIfTimeoffTypeIdIsNotValid(typeId: Long, contract: ContractBasicInfo): Boolean {
        if (!timeoffTypeRepository.existsByTypeIdAndCompanyId(typeId, contract.companyId)) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0501_INVALID_TIMEOFF_TYPE))
        }
        return true
    }

    private fun throwIfTimeoffStartDateIsNotValid(
        startDate: TimeOffDateInput, contract: ContractBasicInfo, workshift: WorkshiftDTO
    ):
            Boolean {
        if (startDate.dateOnly == null) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0507_INVALID_TIMEOFF_START_DATE))
        }

        if (startDate.session == null) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0519_INVALID_TIMEOFF_START_SESSION))
        }

        val contractStartDate = LocalDate.of(contract.startOn.year, contract.startOn.month, contract.startOn.day)
        if (startDate.dateOnly.isBefore(contractStartDate)) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0502_INVALID_TIMEOFF_START_DATE_CONTRACT_START_DATE))
        }

        if (contract.lastWorkingDay.year != 0) {
            val contractEndDate = LocalDate.of(contract.lastWorkingDay.year, contract.lastWorkingDay.month, contract.lastWorkingDay.day)
            if (startDate.dateOnly.isAfter(contractEndDate)) {
                throw TimeoffValidationException(MPLError(MPLErrorType.MPL0503_INVALID_TIMEOFF_START_DATE_CONTRACT_END_DATE))
            }
        }

        val holidays = holidayServiceAdapter.getHolidays(
            setOf(contract.contractId), startDate.dateOnly.year, startDate.dateOnly.monthValue
        )
        val isStartDateAHoliday = holidays.any { startDate.dateOnly.isEqual(LocalDate.of(it.year, it.month, it.date)) }
        if (isStartDateAHoliday) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0509_INVALID_TIMEOFF_START_DATE_HOLIDAY))
        }

        val calendarStartAndEndDate = timeoffCalendarService.getTimeOffCalendarStartDateAndEndDateForContractId(
            contract.contractId
        )
        if (startDate.dateOnly.isBefore(calendarStartAndEndDate.left) || startDate.dateOnly.isAfter(
                calendarStartAndEndDate.right
            )
        ) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0513_INVALID_TIMEOFF_START_DATE_SUMMARY_PERIOD))
        }

        if (WorkshiftUtil.isRestDay(startDate.dateOnly, workshift)) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0521_INVALID_TIMEOFF_START_DATE_REST_DAY))
        }

        return true
    }

    private fun throwIfTimeoffEndDateIsNotValid(
        endDate: TimeOffDateInput, contract: ContractBasicInfo, workshift: WorkshiftDTO
    ):
            Boolean {
        if (endDate.dateOnly == null) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0508_INVALID_TIMEOFF_END_DATE))
        }

        if (endDate.session == null) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0520_INVALID_TIMEOFF_END_SESSION))
        }

        val contractStartDate = LocalDate.of(contract.startOn.year, contract.startOn.month, contract.startOn.day)
        if (endDate.dateOnly.isBefore(contractStartDate)) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0504_INVALID_TIMEOFF_END_DATE_CONTRACT_START_DATE))
        }

        if (contract.lastWorkingDay.year != 0) {
            val contractEndDate = LocalDate.of(contract.lastWorkingDay.year, contract.lastWorkingDay.month, contract.lastWorkingDay.day)
            if (endDate.dateOnly.isAfter(contractEndDate)) {
                throw TimeoffValidationException(MPLError(MPLErrorType.MPL0505_INVALID_TIMEOFF_END_DATE_CONTRACT_END_DATE))
            }
        }

        // check if the end date is a holiday
        val holidays = holidayServiceAdapter.getHolidays(
            setOf(contract.contractId), endDate.dateOnly.year, endDate
                .dateOnly.monthValue
        )
        val isEndDateAHoliday = holidays.any { endDate.dateOnly.isEqual(LocalDate.of(it.year, it.month, it.date)) }
        if (isEndDateAHoliday) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0510_INVALID_TIMEOFF_END_DATE_HOLIDAY))
        }

        // check if the end date is between calendar summary start and end dates
        val calendarStartAndEndDate = timeoffCalendarService.getTimeOffCalendarStartDateAndEndDateForContractId(
            contract.contractId
        )
        if (endDate.dateOnly.isBefore(calendarStartAndEndDate.left) || endDate.dateOnly.isAfter(calendarStartAndEndDate.right)) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0514_INVALID_TIMEOFF_END_DATE_SUMMARY_PERIOD))
        }

        if (WorkshiftUtil.isRestDay(endDate.dateOnly, workshift)) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0522_INVALID_TIMEOFF_END_DATE_REST_DAY))
        }

        return true
    }

    private fun throwIfStartAndEndDateIsNotValid(startDate: TimeOffDateInput, endDate: TimeOffDateInput) {
        if (startDate.dateOnly.isAfter(endDate.dateOnly)) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0507_INVALID_TIMEOFF_START_DATE))
        }
        if (startDate.dateOnly.equals(endDate.dateOnly)) {
            if (startDate.session.equals(TimeOffSession.AFTERNOON) && endDate.session.equals(TimeOffSession.MORNING)) {
                throw TimeoffValidationException(MPLError(MPLErrorType.MPL0518_INVALID_TIMEOFF_START_DATE_SESSION))
            }
        }
    }



    fun throwIfContractIsNotValid(contract: ContractBasicInfo): Boolean {
        throwIfContractHasNotStarted(contract)
        throwIfContractIsNotActive(contract)
        return true
    }

    private fun throwIfContractIsNotActive(contract: ContractBasicInfo): Boolean {
        if (!contract.status.equals(ContractOuterClass.ContractStatus.ACTIVE)) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0517_INVALID_TIMEOFF_CONTRACT_STATUS))
        }
        return true
    }

    private fun throwIfContractHasNotStarted(contract: ContractBasicInfo): Boolean {
        if (!contract.started && !isStartedLegacy(contract)) {
            throw TimeoffValidationException(MPLError(MPLErrorType.MPL0515_INVALID_TIMEOFF_CONTRACT_NOT_STARTED))
        }
        return true
    }

    private fun isStartedLegacy(contract: ContractBasicInfo): Boolean {
        return contract.status == ContractOuterClass.ContractStatus.ACTIVE || contract.status == ContractOuterClass.ContractStatus.OFFBOARDING
    }
}
