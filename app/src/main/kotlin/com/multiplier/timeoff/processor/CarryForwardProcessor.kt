package com.multiplier.timeoff.processor

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.repository.EntitlementChangeRecordRepository
import com.multiplier.timeoff.repository.model.EntitlementChangeCategory
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO
import com.multiplier.timeoff.service.CarryForwardLeaveService
import com.multiplier.timeoff.service.TimeoffSummaryService
import com.multiplier.timeoff.service.dto.CarryForwardResult
import com.multiplier.timeoff.types.CarryForwardConfig
import com.multiplier.timeoff.types.TimeOffAllocationInput
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.util.*

@Service
class CarryForwardProcessor(
    private val timeoffSummaryService: TimeoffSummaryService,
    private val carryForwardLeaveService: CarryForwardLeaveService,
    private val entitlementChangeRecordRepository: EntitlementChangeRecordRepository,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val featureFlagService: FeatureFlagService
) {

    private val log by logger()
    private lateinit var carryForwardEnabledContracts: Map<Long, Contract>

    fun carryForwardTimeoffBalances(input: TimeOffAllocationInput?) {
        for (nonExpiredSummary in getNonExpiredSummariesWhichCarryForwardNotRecorded(input)) {
            try {
                carryForwardTimeoffBalances(nonExpiredSummary)
            }
            catch (e: Exception) {
                log.error("Failed to carry forward timeoff balance for summary id = {}", nonExpiredSummary.id, e)
            }
        }
    }

    private fun carryForwardTimeoffBalances(nonExpiredSummary: TimeoffSummaryDBO) {
        val predecessorSummary = timeoffSummaryService.getPredecessor(nonExpiredSummary) ?: return
        val carryForwardResult: CarryForwardResult?
        try {
            carryForwardResult = timeoffSummaryService.carryForwardLeaves(
                predecessorSummary,
                nonExpiredSummary,
                carryForwardEnabledContracts[nonExpiredSummary.contractId()]
            ) ?: return
        } catch (e: Exception) {
            log.error("Failed to carry forward timeoff balance for summary id = {} and its predecessor id = {}", nonExpiredSummary.id, predecessorSummary.id, e)
            return
        }

        val changeRecord = EntitlementChangeRecordEntity(
            typeId = nonExpiredSummary.typeId(),
            contractId = nonExpiredSummary.contractId(),
            category = EntitlementChangeCategory.CARRY_FORWARD,
            count = carryForwardResult.carriedCount,
            validFrom = nonExpiredSummary.periodStart(),
            validToInclusive = carryForwardResult.lastValidDate,
            refId = nonExpiredSummary.id
        )
        entitlementChangeRecordRepository.save(changeRecord)
    }

    private fun getNonExpiredSummariesWhichCarryForwardNotRecorded(input: TimeOffAllocationInput?): List<TimeoffSummaryDBO> {
        val expiryDate = input?.expiryDate ?: LocalDate.now()
        val nonExpiredSummaries = if (input?.companyIds?.isNotEmpty() == true) {
            getAllNonExpiredSummariesForContractsInGivenCompanies(expiryDate, input.companyIds)
        } else {
            getAllNonExpiredSummaries(expiryDate)
        }
        val carryForwardNotRecordedSummaries = getCarryForwardNotRecordedSummaries(nonExpiredSummaries)
        log.info("Found {} non-expired timeoff after {} for which carry forward is not recorded", carryForwardNotRecordedSummaries.size, expiryDate)

        return carryForwardNotRecordedSummaries
    }

    private fun getAllNonExpiredSummariesForContractsInGivenCompanies(expiryDate: LocalDate, companyIds: List<Long>): List<TimeoffSummaryDBO> {
        val contractIds = contractServiceAdapter.getContractIdsByCompanyIds(companyIds)
        val nonDeletedNonEndedContracts = contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(contractIds)
        carryForwardEnabledContracts = getCarryForwardExpiryEnabledContracts(nonDeletedNonEndedContracts)
        if (carryForwardEnabledContracts.isNotEmpty()) {
            return timeoffSummaryService.getSummariesNotExpiredOnOrAfterForContracts(
                expiryDate,
                carryForwardEnabledContracts.keys
            )
        }

        return emptyList()
    }

    private fun getAllNonExpiredSummaries(expiryDate: LocalDate): List<TimeoffSummaryDBO> {
        val summaries = timeoffSummaryService.getSummariesNotExpiredOnOrAfter(expiryDate)
        val nonDeletedNonEndedContracts = contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(summaries.map { it.contractId() })
        carryForwardEnabledContracts = getCarryForwardExpiryEnabledContracts(nonDeletedNonEndedContracts)

        return summaries.filter { carryForwardEnabledContracts.containsKey(it.contractId()) }
    }

    private fun getCarryForwardExpiryEnabledContracts(contracts: List<Contract>): Map<Long, Contract> {
        return contracts.filter { isCarryForwardExpiryEnabled(it.companyId) }.associateBy { it.id }
    }

    private fun isCarryForwardExpiryEnabled(companyId: Long): Boolean {
        return featureFlagService.feature(
            FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY,
            mapOf("company" to companyId)
        ).on
    }

    private fun getCarryForwardNotRecordedSummaries(nonExpiredSummaries: List<TimeoffSummaryDBO>): List<TimeoffSummaryDBO> {
        val carriedSummaryIds = entitlementChangeRecordRepository.findAllRefIdsIn(nonExpiredSummaries.map { it.id })

        return nonExpiredSummaries.filterNot { carriedSummaryIds.contains(it.id) }
    }

    fun backfillCarryForwardRecords(input: TimeOffAllocationInput?) {
        val nonExpiredSummaries = getNonExpiredSummariesWhichCarryForwardNotRecorded(input)
        val summariesWithCarryForwardLeave = nonExpiredSummaries.filter { it.carriedCount()?.let { count -> count > 0 } ?: false }
        for (summaryWithCarryForwardLeave in summariesWithCarryForwardLeave) {
            try {
                recordCarryForwardEvent(summaryWithCarryForwardLeave)
            }
            catch (e: Exception) {
                log.error("Failed to record carry forward event for summary id = {}", summaryWithCarryForwardLeave.id, e)
            }
        }
    }

    private fun recordCarryForwardEvent(summaryWithCarryForwardLeave: TimeoffSummaryDBO) {
        val config: Optional<CarryForwardConfig> = carryForwardLeaveService.getCarryForwardConfig(
            carryForwardEnabledContracts[summaryWithCarryForwardLeave.contractId()],
            summaryWithCarryForwardLeave.typeId()
        )

        if (config.isEmpty) {
            log.warn("Skip backfilling carry forward event for summary id = {}, contract id = {}: carry forward config not found",
                summaryWithCarryForwardLeave.typeId(),
                summaryWithCarryForwardLeave.contractId()
            )
            return
        }

        val changeRecord = EntitlementChangeRecordEntity(
            typeId = summaryWithCarryForwardLeave.typeId(),
            contractId = summaryWithCarryForwardLeave.contractId(),
            category = EntitlementChangeCategory.CARRY_FORWARD,
            count = summaryWithCarryForwardLeave.carriedCount(),
            validFrom = summaryWithCarryForwardLeave.periodStart(),
            validToInclusive = carryForwardLeaveService.getLastValidDate(summaryWithCarryForwardLeave.periodStart(), config.get().expiry),
            refId = summaryWithCarryForwardLeave.id
        )
        entitlementChangeRecordRepository.save(changeRecord)
    }
}