package com.multiplier.timeoff.leavecompliance.adapter

import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.grpc.common.time.v2.TimeUnitOuterClass.TimeUnit
import com.multiplier.leave.compliance.schema.LeaveComplianceRequest
import com.multiplier.leave.compliance.schema.LeaveComplianceServiceGrpc
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.leavecompliance.dto.AnnualLeaveEntitlement
import com.multiplier.timeoff.service.dto.CountryLocation
import com.multiplier.timeoff.types.CountryCode
import com.multiplier.timeoff.types.TimeOffUnit
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service
import com.multiplier.grpc.common.country.v2.Country.CountryCode as GrpcCountryCode
import com.multiplier.leave.compliance.schema.CountryLocation as GrpcCountryLocation

fun interface LeaveComplianceServiceAdapter {
    fun getAnnualEntitlementByCountryLocation(
        countryLocations: Collection<CountryLocation>
    ): Map<CountryLocation, AnnualLeaveEntitlement>
}

@Service
class DefaultLeaveComplianceServiceClient(
    private val featureFlagService: FeatureFlagService,
) : LeaveComplianceServiceAdapter {

    private val log = KotlinLogging.logger {}

    @GrpcClient("leave-compliance-service")
    private lateinit var stub: LeaveComplianceServiceGrpc.LeaveComplianceServiceBlockingStub

    override fun getAnnualEntitlementByCountryLocation(
        countryLocations: Collection<CountryLocation>
    ): Map<CountryLocation, AnnualLeaveEntitlement> {
        val validLocations = filterCountryWithLeaveComplianceEnabled(countryLocations)
        if (validLocations.isEmpty()) return emptyMap()
        log.info { "Loading country annual entitlements from leave compliance service for countries: $validLocations" }
        val request = LeaveComplianceRequest.newBuilder()
            .addAllLocations(validLocations.map { it.toGrpc() })
            .build()
        return stub.getCountryAnnualEntitlements(request)
            .countryAnnualEntitlementList
            .associate {
                val state = if (it.hasState()) { it.state } else { null }
                val countryLocation = buildCountryLocation(it.countryCode, state)
                val annualEntitlement = AnnualLeaveEntitlement(
                    from = it.from,
                    to = it.to,
                    timeUnit = it.timeUnit.toTimeOffUnit(),
                    mandatory = it.mandatory
                )
                countryLocation to annualEntitlement
            }
    }

    private fun filterCountryWithLeaveComplianceEnabled(countryLocations: Collection<CountryLocation>) =
        countryLocations.filter {
            featureFlagService.isOn(
                FeatureFlags.TIME_OFF_LEAVE_COMPLIANCE_ENABLED,
                mapOf("country" to it.countryCode.name)
            )
        }.toSet()
}

private fun CountryLocation.toGrpc(): GrpcCountryLocation {
    val builder = GrpcCountryLocation.newBuilder()
        .setCountryCode(this.countryCode.toGrpcCountryCode())
    this.stateCode?.let { builder.setState(it) }
    return builder.build()
}

private fun CountryCode.toGrpcCountryCode() =
    try {
        GrpcCountryCode.valueOf("COUNTRY_CODE_${this.name}")
    } catch (e: IllegalArgumentException) {
        throw IllegalArgumentException("Invalid country code: $this", e)
    }

private fun TimeUnit.toTimeOffUnit() =
    try {
        TimeOffUnit.valueOf(this.name.substringAfter("TIME_UNIT_"))
    } catch (e: IllegalArgumentException) {
        throw IllegalArgumentException("Invalid time unit: $this", e)
    }

private fun buildCountryLocation(countryCode: Country.CountryCode, state: String? = null) =
    try {
        val country = CountryCode.valueOf(countryCode.name.substringAfter("COUNTRY_CODE_"))
        CountryLocation(country, state)
    } catch (e: IllegalArgumentException) {
        throw IllegalArgumentException("Invalid country code: $countryCode", e)
    }