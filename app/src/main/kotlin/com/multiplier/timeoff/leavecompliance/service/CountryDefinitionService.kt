package com.multiplier.timeoff.leavecompliance.service

import com.multiplier.timeoff.leavecompliance.adapter.LeaveComplianceServiceAdapter
import com.multiplier.timeoff.leavecompliance.dto.AnnualLeaveEntitlement
import com.multiplier.timeoff.repository.CountryDefinitionRepository
import com.multiplier.timeoff.repository.model.CountryDefinitionEntity
import com.multiplier.timeoff.repository.model.DefinitionEntity
import com.multiplier.timeoff.repository.model.TimeOffDefinitionEntity
import com.multiplier.timeoff.repository.model.TimeoffDefinitionValidationEntity
import com.multiplier.timeoff.service.TimeoffTypeService
import com.multiplier.timeoff.service.dto.CountryLocation
import com.multiplier.timeoff.types.CountryCode
import org.springframework.stereotype.Service

@Service
class CountryDefinitionService(
    private val timeoffTypeService: TimeoffTypeService,
    private val leaveComplianceServiceAdapter: LeaveComplianceServiceAdapter,
    private val countryDefinitionRepository: CountryDefinitionRepository,
) {

    companion object {
        private const val KEY_ANNUAL = "annual";
    }

    fun getCountryLocationToCountryDefinitionsMap(
        countryLocations: Collection<CountryLocation>
    ): Map<CountryLocation, List<CountryDefinitionEntity>> {
        val uniqueCountryLocations = countryLocations.toSet()
        val countryLocationToCountryDefinitions =
            getCountryLocationToCountryDefinitionsMapFromTimeOff(uniqueCountryLocations)
        val typeIdToKey =
            getTypeIdToKeyMap(countryLocationToCountryDefinitions.values.flatten().mapNotNull { it.typeId })
        val countryLocationToAnnualEntitlement =
            leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(uniqueCountryLocations)
        return uniqueCountryLocations.associateWith {
            mergeLegacyDefsWithComplianceDefs(
                countryLocationToCountryDefinitions.getValue(it),
                countryLocationToAnnualEntitlement[it],
                typeIdToKey
            )
        }
    }

    fun getAllCountryDefinitionsByDefinitionIds(definitionIds: Collection<Long>): List<CountryDefinitionEntity> {
        return countryDefinitionRepository.findAllByDefinitionIdIn(definitionIds.toSet())
    }

    fun getAllCountryDefinitionsByCountry(
        countryCode: CountryCode,
        stateCode: String? = null
    ): List<CountryDefinitionEntity> {
        val countryDefinitions = if (stateCode == null) getAllCountryDefinitionsByCountry(countryCode)
        else getAllCountryDefinitionsByCountryAndState(countryCode, stateCode)
        if (countryDefinitions.isEmpty()) return emptyList()

        val typeIdToKey = getTypeIdToKeyMap(countryDefinitions.mapNotNull { it.typeId })
        val countryLocation = CountryLocation(countryCode, stateCode)
        val locationToAnnualEntitlement =
            leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(listOf(countryLocation))
        return mergeLegacyDefsWithComplianceDefs(
            countryDefinitions,
            locationToAnnualEntitlement[countryLocation],
            typeIdToKey
        )
    }

    private fun getAllCountryDefinitionsByCountryAndState(
        countryCode: CountryCode,
        stateCode: String
    ): List<CountryDefinitionEntity> {
        val countryDefinitions = countryDefinitionRepository.findAllByCountryAndState(countryCode, stateCode)
        return countryDefinitions.ifEmpty { getAllCountryDefinitionsByCountry(countryCode) }
    }

    private fun getAllCountryDefinitionsByCountry(countryCode: CountryCode): List<CountryDefinitionEntity> {
        val countryDefinitions = countryDefinitionRepository.findAllByCountryAndState(countryCode, "")
        if (countryDefinitions.isNotEmpty()) return countryDefinitions
        return countryDefinitionRepository.findAllDefaultDefinitions()
    }

    private fun getCountryLocationToCountryDefinitionsMapFromTimeOff(
        countryLocations: Collection<CountryLocation>
    ): Map<CountryLocation, List<CountryDefinitionEntity>> {
        val countryCodes = countryLocations.map { it.countryCode }.toSet()
        val countryAndDefaultDefinitions = countryDefinitionRepository.findAllDefaultAndByCountryIn(countryCodes)

        // This is the definitions that apply to all countries
        val defaultDefinitions = countryAndDefaultDefinitions
            .filter { it.definition != null && it.definition!!.countryCode == null }

        // This is the definitions for each country
        val countryCodeToCountryDefinitions = countryAndDefaultDefinitions
            .filter { it.definition != null && it.definition!!.countryCode != null && it.definition!!.stateCode.isNullOrBlank() }
            .groupBy { it.definition!!.countryCode!! }

        // This is the definitions for each location (country + state)
        val countryLocationToCountryDefinitions = countryAndDefaultDefinitions
            .filter { it.definition != null && it.definition!!.countryCode != null }
            .groupBy { getCountryLocation(it)!! }

        return countryLocations.associateWith { location ->
            // If definitions for the given location (country + state) does not exist,
            // then return definitions for the given country
            // If definitions for the given country does not exist,
            // then return definitions that apply to all countries
            countryLocationToCountryDefinitions[location]
                ?: countryCodeToCountryDefinitions[location.countryCode]
                ?: defaultDefinitions
        }
    }

    private fun mergeLegacyDefsWithComplianceDefs(
        legacyCountryDefinitions: List<CountryDefinitionEntity>,
        annualEntitlement: AnnualLeaveEntitlement?,
        typeIdToKey: Map<Long, String>
    ): List<CountryDefinitionEntity> {
        if (annualEntitlement == null) return legacyCountryDefinitions
        val (annualDefinitions, nonAnnualDefinitions) = legacyCountryDefinitions.partition {
            KEY_ANNUAL.equals(typeIdToKey[it.typeId], true)
        }
        return nonAnnualDefinitions + annualDefinitions.map { mergeAnnualDefinition(it, annualEntitlement) }
    }

    private fun mergeAnnualDefinition(
        legacyCountryDefinition: CountryDefinitionEntity,
        annualEntitlement: AnnualLeaveEntitlement,
    ): CountryDefinitionEntity {
        return CountryDefinitionEntity(
            id = legacyCountryDefinition.id,
            typeId = legacyCountryDefinition.typeId,
            definition = mergeDefinition(
                legacyCountryDefinition.definition!!,
                annualEntitlement
            ),
        )
    }

    private fun mergeDefinition(
        originalDefinition: DefinitionEntity,
        annualEntitlement: AnnualLeaveEntitlement,
    ): DefinitionEntity {
        // If the annual leave entitlement is null or the exact value is null, we cannot rely on it.
        // Then fallback to the original definition
        if (annualEntitlement.isNone() || annualEntitlement.exact == null) return originalDefinition

        // Some fields are not available in the leave compliance service,
        // so we need to copy them from the original definition in order to support backward compatibility
        return DefinitionEntity(
            id = originalDefinition.id,
            countryCode = originalDefinition.countryCode,
            stateCode = originalDefinition.stateCode,
            isRequired = annualEntitlement.mandatory,
            description = originalDefinition.description,
            clause = originalDefinition.clause,
            basis = originalDefinition.basis,
            configurations = originalDefinition.configurations,
            status = originalDefinition.status,
            name = originalDefinition.name,
            validations = setOf(
                TimeoffDefinitionValidationEntity(
                    defaultValue = annualEntitlement.exact,
                    minimumValue = annualEntitlement.exact,
                    maximumValue = null, // there is no max value for annual leave
                    unit = annualEntitlement.timeUnit,
                    allowedContractStatuses = originalDefinition.validations.map { it.allowedContractStatuses }.flatten(),
                    isUnlimitedLeavesAllowed = originalDefinition.validations.map { it.isUnlimitedLeavesAllowed }.firstOrNull() ?: false,
                )
            ),
        )
    }

    private fun getCountryLocation(timeOffDefinitionEntity: TimeOffDefinitionEntity): CountryLocation? =
        timeOffDefinitionEntity.definition?.let { getCountryLocation(it) }

    private fun getCountryLocation(definition: DefinitionEntity): CountryLocation? =
        definition.countryCode?.let { CountryLocation(it, definition.stateCode) }

    private fun getTypeIdToKeyMap(typeIds: Collection<Long>): Map<Long, String> {
        val types = timeoffTypeService.findAllByIds(typeIds.toSet()).toSet()
        return types.associate { it.id to it.key }
    }
}