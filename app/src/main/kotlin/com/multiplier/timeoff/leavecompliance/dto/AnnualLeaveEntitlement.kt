package com.multiplier.timeoff.leavecompliance.dto

import com.multiplier.timeoff.types.TimeOffUnit

data class AnnualLeaveEntitlement(
    val from: Double,
    val to: Double,
    val timeUnit: TimeOffUnit,
    val mandatory: Boolean = false,
) {

    companion object {
        val NONE = AnnualLeaveEntitlement(
            from = 0.0,
            to = 0.0,
            timeUnit = TimeOffUnit.DAYS
        )
    }

    val exact: Double? = if (from == to) from else null

    fun isNone() = this == NONE
}
