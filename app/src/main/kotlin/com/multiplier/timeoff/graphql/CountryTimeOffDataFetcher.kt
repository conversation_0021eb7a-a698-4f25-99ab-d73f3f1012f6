package com.multiplier.timeoff.graphql

import com.multiplier.timeoff.DgsConstants
import com.multiplier.timeoff.service.TimeOffRequirementsService
import com.multiplier.timeoff.service.builder.mapper.mapToDefinitionFilters
import com.multiplier.timeoff.types.*
import com.netflix.graphql.dgs.*

@DgsComponent
class CountryTimeOffDataFetcher(
    private val definitionSvc: TimeOffRequirementsService
) {

    @DgsEntityFetcher(name = DgsConstants.COUNTRYCOMPLIANCE.TYPE_NAME)
    fun fetchCountryCompliance(values: Map<String, Any>) = CountryCompliance().apply {
        countryCode = CountryCode.valueOf(values[DgsConstants.COUNTRYCOMPLIANCE.CountryCode] as String)
        countryState =
            values[DgsConstants.COUNTRYCOMPLIANCE.CountryState]?.let { State.newBuilder().code(it as String).build() }
    }


    @DgsData(
        parentType = DgsConstants.COUNTRYCOMPLIANCE.TYPE_NAME,
        field = DgsConstants.COUNTRYCOMPLIANCE.TimeOffRequirements
    )
    fun fetchRequirements(
        @InputArgument contractType: ContractType?,
        @InputArgument term: ContractTerm?,
        @InputArgument contractStatus: ContractStatus?,


        @InputArgument filters: TimeOffRequirementFilters?,
        dfe: DgsDataFetchingEnvironment
    ): TimeOffRequirements {

        val parent: CountryCompliance = dfe.getSource()!!

        return definitionSvc.getCountryTimeOffRequirements(
            filters.mapFrom(contractType, term, contractStatus, parent).mapToDefinitionFilters()
        )
    }
}

private fun TimeOffRequirementFilters?.mapFrom(
    contractType: ContractType?,
    term: ContractTerm?,
    contractStatus: ContractStatus?,
    countryCompliance: CountryCompliance,
): TimeOffRequirementFilters =
    this ?: TimeOffRequirementFilters.newBuilder()
        .contractType(contractType)
        .term(term)
        .contractStatus(contractStatus)
        .country(
            CountryFilter.newBuilder()
                .code(countryCompliance.countryCode)
                .stateCode(countryCompliance.countryState?.code)
                .build()

        )
        .build()