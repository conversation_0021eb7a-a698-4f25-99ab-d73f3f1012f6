package com.multiplier.timeoff.graphql

import com.multiplier.timeoff.DgsConstants
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.service.updater.DefinitionUpdateParams
import com.multiplier.timeoff.service.updater.TimeOffDefinitionUpdater
import com.multiplier.timeoff.types.TaskResponse
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import com.netflix.graphql.dgs.DgsMutation

@DgsComponent
class CountryTimeOffMutator(
    private val definitionUpdater: TimeOffDefinitionUpdater,
) {

    val log by logger()

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffUpdateCountryDefinitions)
    fun uploadCountryTimeOffDefinitions(dfe: DgsDataFetchingEnvironment): TaskResponse {

        log.info("[CountryTimeOffMutator] Uploading country timeoff definition file")

        return try {
            definitionUpdater.updateCountryDefinition(DefinitionUpdateParams(file = dfe.getArgument("file")!!, null))
            success()
        } catch (e: Exception) {
            log.error("Error saving country timeoff definitions", e)
            failure(e.message)
        }
    }
}

private fun success() =
    TaskResponse.newBuilder().success(true).message("Success").build()

private fun failure(message: String?) =
    TaskResponse.newBuilder().success(false).message(message).build()

