package com.multiplier.timeoff.graphql

import com.multiplier.timeoff.DgsConstants
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.service.updater.DefinitionUpdateParams
import com.multiplier.timeoff.service.updater.TimeOffDefinitionUpdater
import com.multiplier.timeoff.types.TaskResponse
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class CompanyTimeOffMutator(
    private val definitionUpdateService: TimeOffDefinitionUpdater,
) {

    val log by logger()

    @DgsMutation(field = DgsConstants.MUTATION.TimeOffUpdateCompanyDefinitions)
    @PreAuthorize("@me.allowed('update.operations.timeoff-definition')")
    fun uploadCompanyTimeOffDefinitions(
        @InputArgument companyId: Long?,
        dfe: DgsDataFetchingEnvironment
    ): TaskResponse {

        return TaskResponse.newBuilder()
            .success(false)
            .message("Company policy upload via excel file is deprecated. Please use the timeoff policy feature from company experience.")
            .build()
    }
}

