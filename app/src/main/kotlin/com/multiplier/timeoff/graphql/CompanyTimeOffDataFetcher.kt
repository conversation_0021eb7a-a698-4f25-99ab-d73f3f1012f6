package com.multiplier.timeoff.graphql

import com.multiplier.timeoff.DgsConstants
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.types.Company
import com.multiplier.timeoff.types.TimeOffRequirementFilters
import com.multiplier.timeoff.types.TimeOffRequirements
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsData
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import com.netflix.graphql.dgs.DgsEntityFetcher

@DgsComponent
class CompanyTimeOffDataFetcher(
) {
    val log by logger()
    @DgsEntityFetcher(name = DgsConstants.COMPANY.TYPE_NAME)
    fun fetchCompany(values: Map<String, String>) = Company().apply {
        id = values[DgsConstants.COMPANY.Id]?.toLong()
    }


    @DgsData(parentType = DgsConstants.COMPANY.TYPE_NAME, field = DgsConstants.COMPANY.TimeOffRequirements)
    fun fetchRequirements(
        filters: TimeOffRequirementFilters,
        dfe: DgsDataFetchingEnvironment
    ): TimeOffRequirements {
        log.info("This query is deprecated.")
        return TimeOffRequirements()
    }
}