package com.multiplier.timeoff.config

import com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufSerializer
import com.multiplier.timeoff.config.kafka.properties.TimeoffKafkaProperties
import com.multiplier.timeoff.kafka.proto.TimeoffEventMessageOuterClass
import com.multiplier.timeoff.logger
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.common.serialization.StringSerializer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.core.DefaultKafkaProducerFactory
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.core.ProducerFactory


const val EVENT_ID_KEY = "eventId"
const val EVENT_TIMESTAMP_KEY = "eventTimestamp"

@Configuration
class KafkaProducerConfiguration {

    val log by logger()

    @Bean
    fun timeoffEventProducerFactory(kafkaProperties: TimeoffKafkaProperties): ProducerFactory<String, TimeoffEventMessageOuterClass.TimeoffEventMessage> {
        val keySerializerClass = getClassOrDefault(kafkaProperties.keySerializer, StringSerializer::class.java)
        val valueSerializerClass = getClassOrDefault(kafkaProperties.valueSerializer, KafkaProtobufSerializer::class.java)
        val configs = mapOf<String, Any?>(
            ProducerConfig.BOOTSTRAP_SERVERS_CONFIG to kafkaProperties.bootstrapServers,
            ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG to keySerializerClass,
            ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG to valueSerializerClass,  // Enable safely ordered retries
            ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG to "true",
            ProducerConfig.ACKS_CONFIG to "all",  // Configure send blocking time and retry
            ProducerConfig.MAX_BLOCK_MS_CONFIG to kafkaProperties.maxBlockMs,
            ProducerConfig.RETRIES_CONFIG to kafkaProperties.retryCount,
            ProducerConfig.RETRY_BACKOFF_MS_CONFIG to kafkaProperties.retryBackoffMs,
            ProducerConfig.INTERCEPTOR_CLASSES_CONFIG to KafkaHeaderInterceptor::class.java.name,
            ProducerConfig.BUFFER_MEMORY_CONFIG to kafkaProperties.bufferMemory,
            ProducerConfig.BATCH_SIZE_CONFIG to kafkaProperties.batchSize,
            ProducerConfig.LINGER_MS_CONFIG to kafkaProperties.lingerMs,
        )

        return DefaultKafkaProducerFactory(configs)
    }

    @Bean
    fun timeoffEventKafkaTemplate(
        producerFactory: ProducerFactory<String, TimeoffEventMessageOuterClass.TimeoffEventMessage>,
        properties: TimeoffKafkaProperties
    ): KafkaTemplate<String, TimeoffEventMessageOuterClass.TimeoffEventMessage> {
        return KafkaTemplate(producerFactory).apply { defaultTopic = properties.topic!! }
    }

    private fun getClassOrDefault(configClass: String?, defaultClass: Class<*>): Class<*> {
        return try {
            Class.forName(configClass)
        } catch (e: ClassNotFoundException) {
            log.warn("Configured class {} is not found. Returning default class {}", configClass, defaultClass.name)
            defaultClass
        }
    }
}