package com.multiplier.timeoff.config

import org.apache.kafka.clients.producer.ProducerInterceptor
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.clients.producer.RecordMetadata
import java.util.*

class KafkaHeaderInterceptor : ProducerInterceptor<String, Any> {
    override fun onSend(record: ProducerRecord<String, Any>): ProducerRecord<String, Any> {
        return record.also {
            val eventId = UUID.randomUUID().toString()
            it.headers().add(EVENT_ID_KEY, eventId.toByteArray())

            it.headers().add(EVENT_TIMESTAMP_KEY, "${Calendar.getInstance().timeInMillis}".toByteArray())
        }
    }

    override fun configure(configs: MutableMap<String, *>?) {
        // we do nothing here
    }

    override fun onAcknowledgement(metadata: RecordMetadata?, exception: Exception?) {
        // we do nothing here
    }

    override fun close() {
        // we do nothing here
    }
}