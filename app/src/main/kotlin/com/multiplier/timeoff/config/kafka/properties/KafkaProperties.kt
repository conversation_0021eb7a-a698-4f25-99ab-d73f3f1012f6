package com.multiplier.timeoff.config.kafka.properties

open class KafkaProperties(
    var bootstrapServers: String? = null,
    var topic: String? = null,
    var keySerializer: String? = null,
    var valueSerializer: String? = null,
    var maxBlockMs: Int? = 3200,
    var retryCount: Int? = 10,
    var retryBackoffMs: Int? = 1000,
    var bufferMemory: Long? = 33554432,
    var batchSize: Int? = 16384,
    var lingerMs: Int? = 0,
) {}