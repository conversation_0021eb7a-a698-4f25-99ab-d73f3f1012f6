package com.multiplier.timeoff.config

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.timeoff.permission.AuditorAwareImpl
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.domain.AuditorAware
import org.springframework.data.jpa.repository.config.EnableJpaAuditing

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
class AuditConfig{

    @Bean
    fun auditorProvider(
        currentUser: CurrentUser
    ): AuditorAware<Long> =
        AuditorAwareImpl(currentUser)
}