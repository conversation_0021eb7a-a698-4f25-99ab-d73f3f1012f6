package com.multiplier.timeoff.grpc

import com.google.rpc.ErrorInfo
import com.multiplier.timeoff.core.common.exceptionhandler.MPLError
import com.multiplier.timeoff.core.common.exceptionhandler.MPLErrorType
import io.grpc.StatusRuntimeException

class MPLException(val mplError: MPLError) : RuntimeException(mplError.message)

fun StatusRuntimeException.toMPLException(): RuntimeException {
    val errorInfo = io.grpc.protobuf.StatusProto.fromThrowable(this)
        ?.detailsList
        ?.map { it.unpack(ErrorInfo::class.java) }
        ?.firstOrNull()
        ?: return this

    val errorType = MPLErrorType.getByCode(errorInfo.reason)
    val payload = errorInfo.metadataMap

    return MPLException(MPLError(errorType, payload as Map<String, Any>?))
}
