package com.multiplier.timeoff

import com.opencsv.CSVReader
import org.springframework.boot.autoconfigure.web.servlet.MultipartProperties
import org.springframework.web.multipart.MultipartFile
import java.io.InputStreamReader
import java.nio.charset.Charset

object Csv {
    fun readAllRows(file: MultipartFile, skipHeaderRow: Boolean = true): List<Array<String>> {
        val reader = InputStreamReader(file.inputStream, Charset.forName("UTF-8"))

        with(CSVReader(reader)) {
            return if (skipHeaderRow) {
                readAll().drop(1)
            } else {
                readAll()
            }
        }
    }
}