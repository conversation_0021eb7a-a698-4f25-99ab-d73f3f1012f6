package com.multiplier.timeoff.service

import com.multiplier.timeoff.repository.model.*
import com.multiplier.timeoff.types.ContractStatus
import com.multiplier.timeoff.types.TimeOffUnit


private val annualTimeoffConfiguration = TimeoffDefinitionConfigEntity(
    allocationConfig = AllocationConfigEntity(
        basis = "ANNUALLY",
        prorated = true,
    ),
    futureLeaveConfig = FutureLeaveConfigEntity(
        enabled = true,
        allowedYears = 1,
        lapsableLeaveConfig = LapsableLeaveConfigEntity()
    ),
    carryForwardConfig = CarryForwardConfigEntity(
        enabled = true,
        minForEligibility = CarryForwardLimitEntity(
            type = CarryForwardLimitValueType.FIXED,
            value = 0.0,
            unit = TimeOffUnit.DAYS
        ),
        maxLimit = CarryForwardLimitEntity(
            type = CarryForwardLimitValueType.FIXED,
            value = null,
            unit = TimeOffUnit.DAYS
        ),
        expiry = CarryForwardExpiryEntity(
            value = 6.0,
            unit = TimeOffUnit.MONTHS
        )
    ),
)

private val annualValidations = setOf(
    TimeoffDefinitionValidationEntity(
        unit = TimeOffUnit.DAYS,
        defaultValue = 0.0,
        maximumValue = null,
        minimumValue = 0.0,
        allowedContractStatuses = listOf(
            ContractStatus.ACTIVE.name,
            ContractStatus.ONBOARDING.name,
            ContractStatus.OFFBOARDING.name
        )
    )
)

val annualLeaveDefinition = CountryDefinitionEntity(
    typeId = 1L,
    definition = DefinitionEntity(
        configurations = annualTimeoffConfiguration,
        isRequired = true,
        description = "Annual leave/Earned Leave - HR Member",
        basis = "ANNUAL",
        validations = annualValidations,
        clause = "Please note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed.",
    )
)

private val unpaidTimeoffConfiguration = TimeoffDefinitionConfigEntity(
    allocationConfig = AllocationConfigEntity(
        basis = "ANNUALLY",
        prorated = false,
    ),
    futureLeaveConfig = FutureLeaveConfigEntity(
        enabled = true,
        allowedYears = 1,
        lapsableLeaveConfig = LapsableLeaveConfigEntity()
    ),
    carryForwardConfig = null
)

private val unpaidValidations: Set<TimeoffDefinitionValidationEntity> = setOf(
    TimeoffDefinitionValidationEntity(
        unit = TimeOffUnit.DAYS,
        defaultValue = 365.0,
        maximumValue = null,
        minimumValue = 365.0,
        allowedContractStatuses = listOf(ContractStatus.ACTIVE.name, ContractStatus.OFFBOARDING.name)
    )
)

val unpaidLeaveDefinition = CountryDefinitionEntity(
    typeId = 14L,
    definition = DefinitionEntity(
        isRequired = true,
        description = "Unpaid Leave - HR Member",
        basis = "ANNUAL",
        validations = unpaidValidations,
        configurations = unpaidTimeoffConfiguration,
        clause = "Please note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed.",
    )
)


val hrMemberTimeOffDefinitions = listOf(annualLeaveDefinition, unpaidLeaveDefinition)
