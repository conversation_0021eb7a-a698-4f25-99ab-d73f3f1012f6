package com.multiplier.timeoff.service.builder

import com.multiplier.timeoff.repository.model.CompanyDefinitionEntity
import com.multiplier.timeoff.repository.model.CountryDefinitionEntity
import com.multiplier.timeoff.repository.model.DefinitionEntity
import com.multiplier.timeoff.repository.model.TimeOffDefinitionEntity
import com.multiplier.timeoff.types.CountryFilter
import com.multiplier.timeoff.types.TimeOffTypeDefinitionStatus
import org.springframework.data.jpa.domain.Specification
import java.util.*
import jakarta.persistence.criteria.CriteriaBuilder
import jakarta.persistence.criteria.CriteriaQuery
import jakarta.persistence.criteria.Root

const val COMPANY_ID_COLUMN = "companyId"
const val DEFINITION_COLUMN = "definition"
const val TYPE_ID_COLUMN = "typeId"
const val COUNTRY_CODE_COLUMN = "countryCode"
const val COUNTRY_STATE_CODE_COLUMN = "stateCode"
const val DEFINITION_STATUS_COLUMN = "status"


fun DefinitionFilters?.toCountrySpecification(): Specification<CountryDefinitionEntity?> =
    Specification.where<CountryDefinitionEntity?>(null)
        .andIfPresent(this) { filterByDefinitions(it) }
        .andIfPresent(this?.timeoffTypeId) { filterByType(it) }

fun DefinitionFilters?.toCompanySpecification(): Specification<CompanyDefinitionEntity?> =
    Specification.where<CompanyDefinitionEntity?>(null)
        .andIfPresent(this) { filterByDefinitions(it) }
        .andIfPresent(this?.timeoffTypeId) { filterByType(it) }
        .andIfPresent(this?.companyId) { filterByCompany(it) }

private fun <Q, T> Specification<T>.andIfPresent(value: Q?, queryProvider: (value: Q) -> Specification<T>): Specification<T> =
    if (value != null) {
        this.and(queryProvider(value))
    } else {
        this
    }

private fun <TEntity> filterByDefinitions(filter : DefinitionFilters?): Specification<TEntity?> where TEntity : TimeOffDefinitionEntity{
    return Specification<TEntity?> { root: Root<TEntity?>, query: CriteriaQuery<*>?, criteriaBuilder: CriteriaBuilder ->

        val join = root.join<DefinitionEntity, TEntity>(DEFINITION_COLUMN)

        val filterByCountryCode = Optional.ofNullable(filter?.country?.code)
            .map { criteriaBuilder.equal(join.get<DefinitionEntity>(COUNTRY_CODE_COLUMN), it) }
            .orElse(criteriaBuilder.isNull(join.get<DefinitionEntity>(COUNTRY_CODE_COLUMN)))

        val filterByStateCode = criteriaBuilder.equal(
            criteriaBuilder.coalesce(join.get<DefinitionEntity>(COUNTRY_STATE_CODE_COLUMN), ""),
            filter?.country?.stateCode ?: "")

        val filterByStatusNotIn = criteriaBuilder.not(
            join.get<DefinitionEntity>(DEFINITION_STATUS_COLUMN).`in`(filter?.statusesNotIn ?: listOf(TimeOffTypeDefinitionStatus.DELETED))
        )

        criteriaBuilder.and(filterByCountryCode, filterByStateCode, filterByStatusNotIn)
    }
}

private fun filterByCompany(value: Long): Specification<CompanyDefinitionEntity?> {

    return Specification<CompanyDefinitionEntity?> { root: Root<CompanyDefinitionEntity?>, query: CriteriaQuery<*>?, criteriaBuilder: CriteriaBuilder ->
        criteriaBuilder.equal(root.get<CompanyDefinitionEntity>(COMPANY_ID_COLUMN), value)
    }
}

fun <TEntity> filterByType(value: Long): Specification<TEntity?> where TEntity : TimeOffDefinitionEntity {

    return Specification<TEntity?> { root: Root<TEntity?>, query: CriteriaQuery<*>?, criteriaBuilder: CriteriaBuilder ->
        criteriaBuilder.equal(root.get<TEntity>(TYPE_ID_COLUMN), value)
    }
}
