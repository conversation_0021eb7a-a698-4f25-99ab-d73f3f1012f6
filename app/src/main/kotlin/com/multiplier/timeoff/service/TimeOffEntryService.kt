package com.multiplier.timeoff.service

import com.multiplier.company.schema.holiday.LegalEntityHoliday
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.country.schema.Country.GrpcCountryCode
import com.multiplier.country.schema.holiday.HolidayOuterClass.Holiday
import com.multiplier.grpc.common.toDate
import com.multiplier.grpc.common.toTimestamp
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.adapters.HolidayServiceAdapter
import com.multiplier.timeoff.core.util.TimeOffUtil
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.repository.TimeoffRepository
import com.multiplier.timeoff.repository.model.TimeoffDBO
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO
import com.multiplier.timeoff.schema.GrpcTimeOffSession
import com.multiplier.timeoff.schema.GrpcTimeOffStatus
import com.multiplier.timeoff.schema.GrpcTimeOffType
import com.multiplier.timeoff.schema.grpc.timeoffentry.GrpcTimeOffTypeInfo
import com.multiplier.timeoff.schema.grpc.timeoffentry.TimeOffEntry
import com.multiplier.timeoff.schema.grpc.timeoffentry.timeOffEntry
import com.multiplier.timeoff.schema.grpc.timeoffentry.PaidType
import com.multiplier.timeoff.types.TimeOffSession
import com.multiplier.timeoff.types.TimeOffStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.pow

private const val TIME_OFF_ENTRY_KEY_DATE_FORMAT_PATTERN: String = "yyyyMMdd"
private val TIME_OFF_ENTRY_SHIFT: Long = (10.0).pow(TIME_OFF_ENTRY_KEY_DATE_FORMAT_PATTERN.length).toLong()

@Service
class TimeOffEntryService(
    private val timeOffRepository: TimeoffRepository,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val holidayServiceAdapter: HolidayServiceAdapter,
    private val featureFlagService: FeatureFlagService,
    private val dateFormatter: DateTimeFormatter = DateTimeFormatter.ofPattern(TIME_OFF_ENTRY_KEY_DATE_FORMAT_PATTERN)
) {
    @Transactional(readOnly = true)
    fun getTimeOffEntriesByTimeOffIdsAndStatus(
        timeOffIds: Set<Long>,
        statuses: Set<TimeOffStatus>,
    ): List<TimeOffEntry> {
        val timeOffs = timeOffRepository.findAllByIdInAndStatusIn(timeOffIds, statuses)
        return getTimeOffEntries(timeOffs)
    }

    @Transactional(readOnly = true)
    fun getTimeOffEntriesByStatusAndUpdatedOn(
        updatedOnFromExclusive: LocalDateTime,
        updatedOnToInclusive: LocalDateTime,
        statuses: Set<TimeOffStatus>,
    ): List<TimeOffEntry> {
        val timeOffs = timeOffRepository.findAllByUpdatedOnAndStatusIn(
            updatedOnFromExclusive,
            updatedOnToInclusive,
            statuses
        )
        return getTimeOffEntries(timeOffs)
    }

    @Transactional(readOnly = true)
    fun getTimeOffEntriesByIdsAndStatus(
        timeOffEntryIds: Set<Long>,
        statuses: Set<TimeOffStatus>,
    ): List<TimeOffEntry> {
        val timeOffIds = timeOffEntryIds.map { it / TIME_OFF_ENTRY_SHIFT }.toSet()
        val timeOffs = timeOffRepository.findAllByIdInAndStatusIn(timeOffIds, statuses)
        return getTimeOffEntries(timeOffs).filter { it.id in timeOffEntryIds }
    }

    @Transactional(readOnly = true)
    fun getTimeOffEntries(timeOffs: List<TimeoffDBO>): List<TimeOffEntry> {
        if(timeOffs.isEmpty()) return emptyList()

        val contracts = contractServiceAdapter.getContractsByIdsAnyStatus(
            contractIds = timeOffs.map { it.contractId() }.toSet(),
            includeTest = true,
        )

        return createTimeOffEntries(contracts, timeOffs)
    }

    private fun createTimeOffEntries(contracts : List<ContractOuterClass.Contract>, timeOffs : List<TimeoffDBO>): List<TimeOffEntry> {

        val (contractMapToFetchHolidaysByEntity, contractMapToFetchHolidaysByCountry) = contracts.partition { isHolidayByEntityFeatureOn(it) }

        val idToContractMapToFetchHolidaysByEntity = contractMapToFetchHolidaysByEntity.associateBy { it.id }
        val idToContractMapToFetchHolidaysByCountry = contractMapToFetchHolidaysByCountry.associateBy { it.id }

        val (timeOffsToFetchHolidayByEntity, timeOffsToFetchHolidayByCountry) = timeOffs.partition {
            idToContractMapToFetchHolidaysByEntity.containsKey(it.contractId())
        }


        return createTimeOffEntriesByCountry(timeOffsToFetchHolidayByCountry, idToContractMapToFetchHolidaysByCountry) +
                createTimeOffEntriesByEntity(timeOffsToFetchHolidayByEntity, idToContractMapToFetchHolidaysByEntity)
    }

    private fun createTimeOffEntriesByCountry(
        timeOffs: List<TimeoffDBO>,
        idToContractMap: Map<Long, ContractOuterClass.Contract>,
    ): List<TimeOffEntry> {

        if (timeOffs.isEmpty()) return emptyList()

        val yearSet = timeOffs.flatMap { setOf(it.startDate().year, it.endDate().year) }.toSet()

        val countryCodes = idToContractMap.values.map { GrpcCountryCode.valueOf(it.country) }.toSet()
        val holidayByYearCountry = yearSet
            .map { holidayServiceAdapter.getHolidays(countryCodes, it, null, null) }
            .flatten()
            .groupBy { it.year to it.countryCode.name }

        return timeOffs.flatMap { timeOff ->
            idToContractMap[timeOff.contractId()]
                ?.let { contract ->
                    getTimeOffEntries(timeOff, contract.country, holidayByYearCountry)
                }
                ?: emptyList()
        }
    }

    private fun createTimeOffEntriesByEntity(
        timeOffs: List<TimeoffDBO>,
        idToContractMap: Map<Long, ContractOuterClass.Contract>,
    ): List<TimeOffEntry> {

        if (timeOffs.isEmpty()) return emptyList()

        val yearSet = timeOffs.flatMap { setOf(it.startDate().year, it.endDate().year) }.toSet()

        val holidaysByYearContract = yearSet
            .flatMap { holidayServiceAdapter.getHolidays(idToContractMap.keys, it, null) }
            .flatMap { holiday ->
                holiday.contractIdsList.map { contractId ->
                    (holiday.year to contractId) to holiday
                }
            }
            .groupBy({ it.first }, { it.second })

        return timeOffs.flatMap { timeOff ->
            idToContractMap[timeOff.contractId()]
                ?.let {
                    getTimeOffEntries(
                        timeOff = timeOff,
                        holidaysByYearContract = holidaysByYearContract
                    )
                }
                ?: emptyList()
        }

    }

    private fun getTimeOffEntries(
        timeOff: TimeoffDBO,
        countryCode: String,
        holidaysByYearCountry: Map<Pair<Int, String>, List<Holiday>>,
    ): List<TimeOffEntry> {
        val startDateInclusive = timeOff.startDate()
        val endDateExclusive = timeOff.endDate().plusDays(1)
        return startDateInclusive.datesUntil(endDateExclusive).toList()
            .filterNot { TimeOffUtil.WEEKENDS.contains(it.dayOfWeek) }
            .filterNot {
                val holidays = holidaysByYearCountry.getOrDefault((it.year to countryCode), listOf())
                TimeOffUtil.isCountryHolidayAPublicHoliday(it, holidays)
            }
            .map { getTimeOffEntryForDate(it, timeOff) }
    }

    private fun isHolidayByEntityFeatureOn(contract : ContractOuterClass.Contract) : Boolean {
        return featureFlagService.isOn(FeatureFlags.HOLIDAYS_BY_ENTITY, mapOf("company" to contract.companyId))
    }


    private fun getTimeOffEntries(
        timeOff: TimeoffDBO,
        holidaysByYearContract: Map<Pair<Int, Long>, List<LegalEntityHoliday.Holiday>>,
    ): List<TimeOffEntry> {
        val startDateInclusive = timeOff.startDate()
        val endDateExclusive = timeOff.endDate().plusDays(1)
        return startDateInclusive.datesUntil(endDateExclusive).toList()
            .filterNot { TimeOffUtil.WEEKENDS.contains(it.dayOfWeek) }
            .filterNot {
                val holidays = holidaysByYearContract.getOrDefault((it.year to timeOff.contractId()), emptyList())
                TimeOffUtil.isEntityHolidayAPublicHoliday(it, holidays)
            }
            .map { getTimeOffEntryForDate(it, timeOff) }
    }

    private fun getTimeOffEntryForDate(timeOffEntryDate: LocalDate, timeOff: TimeoffDBO): TimeOffEntry {
        val startSession = if (timeOffEntryDate == timeOff.startDate()) timeOff.startSession() else TimeOffSession.MORNING
        val endSession = if (timeOffEntryDate == timeOff.endDate()) timeOff.endSession() else TimeOffSession.AFTERNOON
        val compositeId = "${timeOff.id}${dateFormatter.format(timeOffEntryDate)}".toLong()
        val noOfDays = if (startSession == endSession) 0.5 else 1.0

        return timeOffEntry {
            this.id = compositeId
            this.createdBy = timeOff.createdBy()
            this.createdOn = timeOff.createdOn().toTimestamp()
            this.updatedBy = timeOff.updatedBy()
            this.updatedOn = timeOff.updatedOn().toTimestamp()
            this.contractId = timeOff.contractId()
            this.timeOffId = timeOff.id()
            this.entryDate = timeOffEntryDate.toDate()
            this.noOfDays = noOfDays
            this.status = GrpcTimeOffStatus.valueOf(timeOff.status().name)
            this.startSession = GrpcTimeOffSession.valueOf(startSession.name)
            this.endSession = GrpcTimeOffSession.valueOf(endSession.name)
            this.timeOffType = GrpcTimeOffType.newBuilder().setKey(timeOff.type().key()).build()
            this.effectiveDate = timeOffEntryDate.toDate()
            this.timeOffTypeInfo = grpcTimeOffTypeInfo(timeOff.type())
            timeOff.description()?.let { this.description = it }
            timeOff.approvedOn()?.let { this.approvedOn = it.toTimestamp() }

        }
    }

    private fun grpcTimeOffTypeInfo(timeOffType: TimeoffTypeDBO) : GrpcTimeOffTypeInfo {
        return GrpcTimeOffTypeInfo.newBuilder()
                .setId(timeOffType.id())
                .setKey(timeOffType.key() ?: "")
                .setLabel(timeOffType.label() ?: "")
                .setDescription(timeOffType.description() ?: "")
                .setCompanyId(timeOffType.companyId() ?: 0L)
                .setPaidType(getPaidType(timeOffType.isPaidLeave))
                .build()
    }

    private fun getPaidType(isPaid: Boolean): PaidType {
        return if (isPaid) PaidType.PAID else PaidType.UNPAID
    }
}
