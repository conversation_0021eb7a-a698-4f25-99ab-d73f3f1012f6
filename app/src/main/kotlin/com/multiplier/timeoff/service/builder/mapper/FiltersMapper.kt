package com.multiplier.timeoff.service.builder.mapper;

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.timeoff.service.builder.DefinitionFilters
import com.multiplier.timeoff.types.*


fun TimeOffRequirementFilters.mapToDefinitionFilters(): DefinitionFilters =
    DefinitionFilters(
        contractType = this.contractType,
        contractTerm = this.term,
        contractStatus = this.contractStatus,
        country = this.country,
        companyId = this.companyId,
    );


fun ContractOuterClass.Contract.mapToDefinitionFilters(typeId: Long?, contractStatus: ContractOuterClass.ContractStatus? = null): DefinitionFilters =
    DefinitionFilters(
        contractType = this.type?.name?.let { if(it == ContractOuterClass.ContractType.NULL_CONTRACT_TYPE.toString()) null else  ContractType.valueOf(it) },
        contractTerm = this.term?.name?.let { if(it == ContractOuterClass.ContractTerm.NULL_CONTRACT_TERM.toString()) null else ContractTerm.valueOf(it) },
        contractStatus = contractStatus?.name?.let { if(it == ContractOuterClass.ContractStatus.NULL_CONTRACT_STATUS.toString()) null else ContractStatus.valueOf(it) },
        country = CountryFilter.newBuilder()
            .code(this.country?.let { CountryCode.valueOf(it) })
            .stateCode(this.countryStateCode)
            .build(),
        timeoffTypeId = typeId,
        companyId = this.companyId,
    )


fun ContractOuterClass.Contract.mapToDefinitionFilters(typeId: Long?): DefinitionFilters = this.mapToDefinitionFilters(typeId, null)