package com.multiplier.timeoff.service.updater

import com.multiplier.timeoff.Csv
import com.multiplier.timeoff.logger
import com.multiplier.timeoff.repository.CompanyDefinitionRepository
import com.multiplier.timeoff.repository.CountryDefinitionRepository
import com.multiplier.timeoff.repository.model.CompanyDefinitionEntity
import com.multiplier.timeoff.repository.model.TimeOffDefinitionEntity
import com.multiplier.timeoff.service.builder.DefinitionFilters
import com.multiplier.timeoff.service.builder.toCompanySpecification
import com.multiplier.timeoff.service.toCountryDefinition
import com.multiplier.timeoff.service.toTimeOffDefinitionEntity
import org.springframework.stereotype.Service
import jakarta.transaction.Transactional

@Service
class TimeOffDefinitionUpdater(
    val countryDefinitionRepo: CountryDefinitionRepository,
    val companyDefinitionRepo: CompanyDefinitionRepository,
) {
    val log by logger()

    fun updateCompanyDefinition(params: DefinitionUpdateParams) {

        val updatedDefinitions = Csv.readAllRows(params.file)
            .map {
                it.toTimeOffDefinitionEntity<CompanyDefinitionEntity>().apply { companyId = params.companyId }
            }
            .distinctBy { it.getUniqueKey() }

        val filters = DefinitionFilters(companyId = params.companyId)
        val existingDefinitions = companyDefinitionRepo.findAll(filters.toCompanySpecification())
        val upsert = setIdForExisting(updatedDefinitions, existingDefinitions)
        companyDefinitionRepo.saveAll(upsert)
        log.info("Saved ${upsert.size} company timeoff definitions.")
    }

    @Transactional
    fun updateCountryDefinition(params: DefinitionUpdateParams) {

        val updatedDefinitions = Csv.readAllRows(params.file)
            .map { it.toCountryDefinition() }
            .distinctBy { it.getUniqueKey() }

        val existingDefinitions = countryDefinitionRepo.findAll()
        val upsert = setIdForExisting(updatedDefinitions, existingDefinitions)
        countryDefinitionRepo.saveAll(upsert)
        log.info("Saved ${upsert.size} country timeoff definitions.")
    }
}

inline fun <reified T : TimeOffDefinitionEntity> setIdForExisting(
    updatedDefinitions: List<T>,
    existingDefinitions: List<T>
): List<T> {
    val existingByTypeId = existingDefinitions.groupBy { it.typeId }

    return updatedDefinitions.map { updated ->
        val existing =
            existingByTypeId[updated.typeId]?.find {
                it.definition?.countryCode == updated.definition?.countryCode
                        && (it.definition?.stateCode ?: "").equals((updated.definition?.stateCode ?: ""), true)
            }

        updated.apply {
            this.id = existing?.id
            this.definition?.id = existing?.definition?.id
            this.createdOn = existing?.createdOn
            this.createdBy = existing?.createdBy
        }
    }
}


inline fun <reified T : TimeOffDefinitionEntity> T.getUniqueKey(): String {

    return "${this.definition?.countryCode?.name ?: ""}_${this.definition?.stateCode ?: ""}_${this.typeId ?: ""}"
}