package com.multiplier.timeoff.service.builder;

import com.multiplier.timeoff.types.ContractStatus
import com.multiplier.timeoff.types.ContractTerm
import com.multiplier.timeoff.types.ContractType
import com.multiplier.timeoff.types.CountryFilter
import com.multiplier.timeoff.types.TimeOffTypeDefinitionStatus

data class DefinitionFilters(
    val contractType: ContractType? = null,
    val contractTerm: ContractTerm? = null,
    val contractStatus: ContractStatus? = null,
    val country: CountryFilter? = null,
    val timeoffTypeId: Long? = null,
    val companyId: Long? = null,
    val statusesNotIn: List<TimeOffTypeDefinitionStatus>? = listOf(TimeOffTypeDefinitionStatus.DELETED),
)