package com.multiplier.timeoff.service

import com.multiplier.timeoff.core.common.dto.TimeoffTypeDTO
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.repository.CompanyDefinitionRepository
import com.multiplier.timeoff.repository.CountryDefinitionRepository
import com.multiplier.timeoff.repository.model.*
import com.multiplier.timeoff.service.builder.DefinitionFilters
import com.multiplier.timeoff.service.builder.toCompanySpecification
import com.multiplier.timeoff.service.builder.toCountrySpecification
import com.multiplier.timeoff.types.*
import org.springframework.data.jpa.domain.Specification
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Service

private fun emptyTimeOffRequirements() = TimeOffRequirements.newBuilder().definitions(emptyList()).build()

@Service
class TimeOffRequirementsService(
    private val countryDefinitionRepository: CountryDefinitionRepository,
    private val companyDefinitionRepository: CompanyDefinitionRepository,
    private val timeOffTypeSvc: TimeoffTypeService,
) {

    fun getCompanyTimeOffRequirements(filters: DefinitionFilters): TimeOffRequirements {
        return mapToTimeOffRequirements(getCompanyTimeOffDefinitions(filters))
    }

    fun getCompanyTimeOffDefinitions(filters: DefinitionFilters): List<TimeOffDefinitionEntity> {
        val companyDefinitions =
            if (!filters.canFilterCompanyDefinitions())
                emptyList()
            else
                companyDefinitionRepository
                    .findAllWithFallbacks(filters) { it.toCompanySpecification() }
                    .filter { it.isValidForContractStatus(filters.contractStatus) }

        val ignoreIds = companyDefinitions.map { it.typeId }
        val countryDefs = getCountryTimeOffDefinitions(filters)
        val countryDefsWithoutCompanyDef = countryDefs.filter { !ignoreIds.contains(it.typeId) }

        return companyDefinitions + countryDefsWithoutCompanyDef
    }

    fun getCountryTimeOffRequirements(filters: DefinitionFilters): TimeOffRequirements {

        return mapToTimeOffRequirements(getCountryTimeOffDefinitions(filters))
    }


    private fun getCountryTimeOffDefinitions(filters: DefinitionFilters): List<CountryDefinitionEntity> {

        val contractType = filters.contractType ?: ContractType.EMPLOYEE

        if (contractType == ContractType.FREELANCER || contractType == ContractType.CONTRACTOR) {
            return emptyList()
        }

        if (contractType == ContractType.HR_MEMBER) {
            return hrMemberTimeOffDefinitions
        }

        return countryDefinitionRepository
            .findAllWithFallbacks(filters) { it.toCountrySpecification() }
            .filter { it.isValidForContractStatus(filters.contractStatus) }
    }

    private fun mapToTimeOffRequirements(definitions: List<TimeOffDefinitionEntity>): TimeOffRequirements {
        if (definitions.isEmpty()) {
            return emptyTimeOffRequirements()
        }

        val typeIds = definitions.map(TimeOffDefinitionEntity::typeId).toSet()
        val timeOffTypesById: Map<Long, TimeoffTypeDTO> = timeOffTypeSvc.findAllByIds(typeIds).associateBy { it.id }

        return TimeOffRequirements.newBuilder()
            .clause(definitions.firstOrNull { it.definition?.clause?.isNotEmpty() ?: false }?.definition?.clause)
            .definitions(definitions.mapToGraphType(timeOffTypesById))
            .build()
    }


    private fun <T : TimeOffDefinitionEntity> JpaSpecificationExecutor<T>.findAllWithFallbacks(
        filters: DefinitionFilters,
        toSpecFn: (filters: DefinitionFilters) -> Specification<T?>
    ): List<T> {
        return this.findAll(toSpecFn(filters))
            .ifEmpty {
                this.findAll(
                    toSpecFn(
                        filters.copy(
                            country = CountryFilter(
                                filters.country?.code,
                                null
                            )
                        )
                    )
                )
            }
            .ifEmpty {
                this.findAll(
                    toSpecFn(
                        filters.copy(
                            country = CountryFilter(
                                null,
                                null
                            )
                        )
                    )
                )
            }

    }
}

private fun <T : TimeOffDefinitionEntity> List<T>.mapToGraphType(timeOffTypesById: Map<Long, TimeoffTypeDTO>): List<TimeOffTypeDefinition> =
    this.mapNotNull {
        val timeOffType = timeOffTypesById[it.typeId]

        if (timeOffType == null) {
            null
        } else {
            TimeOffTypeDefinition.newBuilder()
                .id(it.definition?.id)
                .typeId(timeOffType.id)
                .type(timeOffType.key)
                .required(it.definition?.isRequired)
                .label(timeOffType.label)
                .description(it.definition?.description)
                .basis(it.definition?.basis)
                .validation(it.definition?.validations?.mapToGraphType())
                .configuration(it.definition?.configurations?.mapToGraphType())
                .updatedOn(it.updatedOn)
                .build()
        }
    }


fun CarryForwardLimitEntity.mapToGraphType(): CarryForwardLimit? = CarryForwardLimit.newBuilder()
    .type(this.type?.name?.let { LimitValueType.valueOf(it) })
    .value(this.value)
    .unit(this.unit)
    .build()

fun CarryForwardExpiryEntity.mapToGraphType(): TimeOffDuration? = TimeOffDuration.newBuilder()
    .value(this.value)
    .unit(this.unit)
    .build()

fun CarryForwardConfigEntity.mapToGraphType(): CarryForwardConfig = CarryForwardConfig.newBuilder()
    .enabled(this.enabled)
    .minForEligibility(this.minForEligibility?.mapToGraphType())
    .maxLimit(this.maxLimit?.mapToGraphType())
    .expiry(this.expiry?.mapToGraphType())
    .build()


fun AllocationConfigEntity.mapToGraphType(): AllocationConfig = AllocationConfig.newBuilder()
    .basis(this.basis?.let { AllocationBasis.valueOf(it) })
    .prorated(this.prorated)
    .build()

fun TimeoffDefinitionConfigEntity.mapToGraphType(): TimeoffConfiguration = TimeoffConfiguration.newBuilder()
    .allocation(this.allocationConfig?.mapToGraphType())
    .carryForward(this.carryForwardConfig?.mapToGraphType())
    .build()


fun Set<TimeoffDefinitionValidationEntity>.mapToGraphType(): List<TimeOffValidation> =
    this.map {
        TimeOffFixedValidation.newBuilder()
            .minimum(it.minimumValue)
            .maximum(it.maximumValue)
            .defaultValue(it.defaultValue)
            .unit(it.unit)
            .allowedContractStatuses(it.allowedContractStatuses.map { s -> ContractStatus.valueOf(s) })
            .build()
    }


fun <TEntity> TEntity.isValidForContractStatus(status: ContractStatus?): Boolean where TEntity : TimeOffDefinitionEntity {

    return if (status == null) true
    else definition?.validations
        ?.firstOrNull()
        ?.allowedContractStatuses
        ?.contains(status.toString())
        ?: true
}

private fun DefinitionFilters.canFilterCompanyDefinitions(): Boolean {

    return (
            (this.companyId != null && this.companyId > 0)
                    && this.contractType == ContractType.EMPLOYEE
            )
}
