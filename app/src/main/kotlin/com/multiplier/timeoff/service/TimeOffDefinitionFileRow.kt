package com.multiplier.timeoff.service

import com.multiplier.timeoff.repository.model.*
import com.multiplier.timeoff.types.CountryCode
import com.multiplier.timeoff.types.TimeOffUnit

inline fun <reified T : TimeOffDefinitionEntity> Array<String>.toTimeOffDefinitionEntity(): T {
    return when (T::class) {
        CountryDefinitionEntity::class -> this.toCountryDefinition() as T
        CompanyDefinitionEntity::class -> this.toCompanyDefinition() as T
        else -> throw IllegalStateException("Cannot map ${T::class}")
    }
}

fun Array<String>.toCompanyDefinition(): CompanyDefinitionEntity {
    return CompanyDefinitionEntity(
        typeId = this[3].toLongOrNull(),
        definition = this.toDefinitionEntity()
    )
}

fun Array<String>.toCountryDefinition(): CountryDefinitionEntity {
    return CountryDefinitionEntity(
        typeId = this[3].toLongOrNull(),
        definition = this.toDefinitionEntity()
    )
}

private fun Array<String>.toDefinitionEntity() = DefinitionEntity(
    countryCode = this[0].toCountryCode(),
    stateCode = this[1].toStateCode(),
    description = this[12],
    clause = this[13],
    basis = this[4].toAllocationBasis(),
    validations = setOf(
        TimeoffDefinitionValidationEntity(
            unit = this[5].toTimeOffUnit(),
            defaultValue = this[6].toDoubleOrNull(),
            minimumValue = this[7].toMinimum(),
            maximumValue = this[7].toMaximum(),
            allowedContractStatuses = this[8].toAllowedContractStatuses(),
        )
    ),
    isRequired = this[9].toBoolean(),
    configurations = TimeoffDefinitionConfigEntity(
        allocationConfig = AllocationConfigEntity(
            basis = this[10].toAllocationBasis(),
            prorated = this[11].toBoolean()
        ),
        carryForwardConfig = if (this.size < 21) null
        else this.sliceArray(14..20).toCarryForwardConfigEntity()
    )
)


private fun Array<String>.toCarryForwardConfigEntity(): CarryForwardConfigEntity? {

    var result: CarryForwardConfigEntity? = CarryForwardConfigEntity(
        enabled = this[0].ifEmpty { "FALSE" }.toBoolean(),
        minForEligibility = this.sliceArray(1..3).toCarryForwardLimit(),
        maxLimit = this.sliceArray(1..2).plus(this[4]).toCarryForwardLimit(),
        expiry = this.sliceArray(5..6).toCarryForwardExpiry(),
    )

    return result
}


private fun Array<String>.toCarryForwardLimit(): CarryForwardLimitEntity? {

    var result: CarryForwardLimitEntity? = CarryForwardLimitEntity(
        type = this[0].toLimitType(),
        unit = this[1].toTimeOffUnit(null),
        value = this[2].toLimit()
    )

    if (!result.isValid()) result = null

    return result
}


private fun Array<String>.toCarryForwardExpiry(): CarryForwardExpiryEntity? {

    return CarryForwardExpiryEntity(
        value = this[0].toLimit(),
        unit = this[1].toTimeOffUnit()
    )
}


private fun CarryForwardConfigEntity?.isValid(): Boolean {

    return this != null && (this.minForEligibility.isValid() || this.maxLimit.isValid())
}


private fun CarryForwardLimitEntity?.isValid(): Boolean {

    return this?.type != null
            && (this.unit != null)
            && (
            when (this.type) {
                (CarryForwardLimitValueType.PERCENTAGE) -> (this.value != null && this.value <= 100)
                else -> true
            })
}


private fun String.toCountryCode() = if (this.isEmpty()) null else CountryCode.valueOf(this)
private fun String.toStateCode() = this.ifEmpty { null }
private fun String.toAllocationBasis() = this.ifEmpty { "ANNUAL" }
private fun String.toTimeOffUnit(defaultUnit: TimeOffUnit? = TimeOffUnit.DAYS) =
    if (this.isEmpty()) defaultUnit else TimeOffUnit.valueOf(this)

private fun String.toMinimum() = if (this.isEmpty()) 0.0 else this.split(":")[0].toLimit()
private fun String.toMaximum() = if (this.isEmpty()) null else this.split(":")[1].toLimit()
private fun String.toAllowedContractStatuses() = this.split(",")

private fun String.toLimit(): Double? = if (this == "-1") null else this.toDoubleOrNull()
private fun String.toLimitType(): CarryForwardLimitValueType? =
    if (this.isBlank()) null else CarryForwardLimitValueType.valueOf(this)



