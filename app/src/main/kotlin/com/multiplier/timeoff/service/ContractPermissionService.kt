package com.multiplier.timeoff.service

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import org.springframework.stereotype.Service

@Service
class ContractPermissionService(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val currentUser: CurrentUser,
) {

    fun contractBelongsToCompanyOfCurrentUser(contractId: Long): Boolean {
        val companyId = currentUser.context?.scopes?.companyId ?: return false

        return contractServiceAdapter.getAllContractsForCompany(companyId)
            .any { it.id == contractId }
    }
}