package com.multiplier.timeoff.adapters

import com.multiplier.core.schema.notifications.Email
import com.multiplier.core.schema.notifications.EmailServiceGrpc
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface EmailServiceAdapter {
    fun send(email: Email.EmailRequest)
}

@Service
class DefaultEmailServiceAdapter : EmailServiceAdapter {

    @GrpcClient("core-service")
    private lateinit var stub: EmailServiceGrpc.EmailServiceBlockingStub

    override fun send(email: Email.EmailRequest) {
        stub.sendEmail(email)
    }
}