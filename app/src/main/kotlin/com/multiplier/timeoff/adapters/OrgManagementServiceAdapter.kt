package com.multiplier.timeoff.adapters

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.orgmanagement.schema.Manager
import com.multiplier.orgmanagement.schema.Manager.OrgDirectoryRequest
import com.multiplier.orgmanagement.schema.Manager.OrgDirectoryResponse
import com.multiplier.orgmanagement.schema.Manager.RuleCondition
import com.multiplier.orgmanagement.schema.Manager.RuleInput
import com.multiplier.orgmanagement.schema.ManagerServiceGrpc
import com.multiplier.timeoff.repository.model.TimeOffDefinitionRuleDBO
import com.multiplier.timeoff.types.RuleType
import net.devh.boot.grpc.client.inject.GrpcClient
import org.apache.commons.collections.CollectionUtils
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import kotlin.streams.toList

interface OrgManagementServiceAdapter {

    fun findContractIdsForRules(rules : List<TimeOffDefinitionRuleDBO>, companyId : Long) : List<Long>

}

@Service
@Lazy
class DefaultOrgManagementServiceClient(
    private val currentUser: CurrentUser
) : OrgManagementServiceAdapter {

    @GrpcClient("org-management-service")
    private lateinit var stub: ManagerServiceGrpc.ManagerServiceBlockingStub;

    override fun findContractIdsForRules(rules: List<TimeOffDefinitionRuleDBO>, companyId: Long): List<Long> {
        if (CollectionUtils.isEmpty(rules)) {
            return emptyList();
        }
        log.info("calling org-management-service to get contract ids for rules with company id : {}", companyId);
        val req = OrgDirectoryRequest.newBuilder()
            .setCompanyId(companyId)
            .setRuleInput(mapToGrpcRule(rules))
            .build();
        return getContractIds(stub.getOrgDirectory(req))
    }

    private fun mapToGrpcRule(rules : List<TimeOffDefinitionRuleDBO>) : RuleInput {

        if (rules.any { it.ruleType() == RuleType.ALL }) {
            // If rules have a rule_type = ALL rule, ignore all other rules
            return RuleInput.newBuilder()
                .setType(Manager.RuleInputType.valueOf(RuleType.ALL.name))
                .build()
        }
        val conditions = rules.mapNotNull { mapToGrpcCondition(it) }

        return RuleInput.newBuilder()
            .setType(Manager.RuleInputType.BY_CONDITION)
            .addAllConditions(conditions)
            .build()
    }

    private fun mapToGrpcCondition(rule : TimeOffDefinitionRuleDBO) : RuleCondition? {
        if (rule.ruleType() != RuleType.BY_CONDITION) {
            return null
        }
        return RuleCondition.newBuilder()
            .setKey(Manager.RuleKey.valueOf(rule.conditionKey().name))
            .setOperator(Manager.RuleOperator.valueOf(rule.conditionOperator().name))
            .addAllValues(rule.conditionValues().toList())
            .build()
    }

    private fun getContractIds(response : OrgDirectoryResponse) : List<Long> {
        return response.contractIdsList + response.managersList.mapNotNull { it.contractId }
    }

}