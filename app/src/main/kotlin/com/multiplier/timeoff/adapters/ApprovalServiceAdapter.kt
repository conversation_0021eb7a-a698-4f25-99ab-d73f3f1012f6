package com.multiplier.timeoff.adapters

import com.multiplier.core.schema.platform.Approval.*
import com.multiplier.core.schema.platform.ApprovalServiceGrpc
import com.multiplier.timeoff.MDCKeys
import com.multiplier.timeoff.toInstant
import mu.KotlinLogging
import mu.withLoggingContext
import net.devh.boot.grpc.client.inject.GrpcClient
import org.apache.commons.collections.CollectionUtils
import org.springframework.stereotype.Service
import java.time.Instant

val log = KotlinLogging.logger {}

interface ApprovalServiceAdapter {
    fun getManagedContractIds(userId: Long): List<Long>?
    fun getAssignedItems(userId: Long): ListOfIdsResponse?
    fun canApproveItem(userId: Long, itemId: Long): CanApproveItemResponse
    fun canApproveItems(personId: Long, itemIds: List<Long>): Map<Long, Boolean>
    fun getItemApprovedBy(itemID: Long): IdResponse
    fun startApproval(itemID: Long): TaskResponse
    fun autoApproveItem(itemId: Long): TaskResponse

    fun getApprovedOnForItems(itemIds: List<Long>) : Map<Long, Instant?>
    fun bulkStartApprovalAndAutoApprove(companyId: Long, itemIdToContractIdMap : Map<Long, Long>)
}

@Service
class DefaultApporvalServiceClient : ApprovalServiceAdapter {

    @GrpcClient("core-service")
    private lateinit var stub: ApprovalServiceGrpc.ApprovalServiceBlockingStub

    override fun getManagedContractIds(userId: Long): List<Long>? {
        val req = GetManagedContractIdsRequest.newBuilder()
                .setUserId(userId)
                .setCategory(ApprovalCategory.TIME_OFF)
                .build()

        return stub.getManagedContractIds(req).idsList
    }

    override fun getAssignedItems(userId: Long): ListOfIdsResponse? {

        val req = GetAssignedItemIdsRequest.newBuilder()
                .setUserId(userId)
                .setCategory(ApprovalCategory.TIME_OFF)
                .build()

        return stub.getAssignedItems(req)
    }

    override fun canApproveItem(userId: Long, itemId: Long): CanApproveItemResponse {
        val req = CanApproveItemRequest.newBuilder()
                .setUserId(userId)
                .setItemId(itemId)
                .setCategory(ApprovalCategory.TIME_OFF)
                .build()

        return stub.canApproveItem(req)
    }

    override fun canApproveItems(personId: Long, itemIds: List<Long>): Map<Long, Boolean> {
        if (CollectionUtils.isEmpty(itemIds)) {
            log.info("[canApproveItems] itemIds is empty, returning an empty map")
            return emptyMap()
        }
        val request = CanApproveItemListRequest.newBuilder()
            .setPersonId(personId)
            .addAllItemIds(itemIds)
            .setCategory(ApprovalCategory.TIME_OFF)
            .build()
        return stub.canApproveItems(request).idToCanApproveMapMap
    }

    /**
     * -1 may be returned to indicate "null"
     */
    override fun getItemApprovedBy(itemID: Long): IdResponse {
        val req = GetItemApprovedByRequest.newBuilder()
                .setItemId(itemID)
                .setCategory(ApprovalCategory.TIME_OFF)
                .build()

        return stub.getItemApprovedBy(req)
    }

    override fun startApproval(itemID: Long): TaskResponse {
        val req = StartApprovalRequest.newBuilder()
                .setItemId(itemID)
                .setCategory(ApprovalCategory.TIME_OFF)
                .build()

        return stub.startApproval(req)
    }

    override fun autoApproveItem(itemId: Long): TaskResponse {
        val req = AutoApproveItemRequest.newBuilder()
                .setItemId(itemId)
                .setCategory(ApprovalCategory.TIME_OFF)
                .build()

        return stub.autoApproveItem(req)
    }

    override fun getApprovedOnForItems(itemIds: List<Long>): Map<Long, Instant?> = withLoggingContext(
        MDCKeys.METHOD to "getApprovedOnForItems"
    ) {
        log.info { "Calling grpc method getApprovedOnForItems with ${itemIds.size} itemIds" }

        val req = GetApprovedOnForItemsRequest.newBuilder()
            .setCategory(ApprovalCategory.TIME_OFF)
            .addAllIds(itemIds)
            .build()

        val resp = stub.getApprovedOnForItems(req)

        log.info { "Received response with ${resp.idToTimestampCount} entries" }

        return resp.idToTimestampMap
            .mapValues { it.value.toInstant() }
    }

    override fun bulkStartApprovalAndAutoApprove(companyId: Long, itemIdToContractIdMap: Map<Long, Long>) {
        log.info("Calling grpc method bulkStartApprovalAndAutoApprove with companyId={}, itemIdToContractIdMap: {}", companyId, itemIdToContractIdMap)
        val req = BulkStartApprovalAndAutoApproveRequest.newBuilder()
            .setCategory(ApprovalCategory.TIME_OFF)
            .setCompanyId(companyId)
            .putAllItemIdToContractId(itemIdToContractIdMap)
            .build()
        stub.bulkStartApprovalAndAutoApprove(req);
    }
}