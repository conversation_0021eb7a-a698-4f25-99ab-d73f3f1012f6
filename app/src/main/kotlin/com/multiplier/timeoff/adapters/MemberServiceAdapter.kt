package com.multiplier.timeoff.adapters

import com.multiplier.member.schema.*
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface MemberServiceAdapter {
    fun getMember(id: Long): Member
    fun getMemberIdByUserId(userId: Long): Long
    fun getMembers(memberIds: Collection<Long>): List<Member>
}

@Service
class DefaultMemberServiceClient : MemberServiceAdapter {

    @GrpcClient("member-service")
    private lateinit var stub: MemberServiceGrpc.MemberServiceBlockingStub

    override fun getMember(id: Long): Member {
        val req = MemberIdRequest.newBuilder()
            .setMemberId(id)
            .build()

        return stub.getMember(req)
    }

    override fun getMemberIdByUserId(userId: Long): Long {
        val req = UserId.newBuilder()
            .setUserId(userId)
            .build()

        return stub.getMemberIdByUserId(req).memberId
    }

    override fun getMembers(memberIds: Collection<Long>): List<Member> {
        val req = MemberIdsRequest.newBuilder()
            .addAllMemberId(memberIds)
            .build()

        return stub.getMembers(req).membersList
    }
}