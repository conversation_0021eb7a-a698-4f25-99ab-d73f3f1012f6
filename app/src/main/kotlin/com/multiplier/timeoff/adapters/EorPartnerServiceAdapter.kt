package com.multiplier.timeoff.adapters

import com.multiplier.core.schema.partner.EORPartnerOuterClass.EORPartner
import com.multiplier.core.schema.partner.EORPartnerOuterClass.GetEORPartnerByIdsRequest
import com.multiplier.core.schema.partner.EORPartnerServiceGrpc
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface EorPartnerServiceAdapter {
    fun getEorPartnersByIds(eorPartnerIds: Collection<Long>): List<EORPartner>
}

@Service
class DefaultEorPartnerServiceAdapter(
) : EorPartnerServiceAdapter {

    @GrpcClient("core-service")
    private lateinit var stub: EORPartnerServiceGrpc.EORPartnerServiceBlockingStub

        override fun getEorPartnersByIds(eorPartnerIds: Collection<Long>): List<EORPartner> {
            if (eorPartnerIds.isEmpty()) return emptyList()
            log.info { "Fetching EOR Partners by ids (size: ${eorPartnerIds.size})"}
            val request = GetEORPartnerByIdsRequest.newBuilder()
                .addAllIds(eorPartnerIds)
                .build()
            return stub.getEORPartnerByIds(request).partnersList
        }
}