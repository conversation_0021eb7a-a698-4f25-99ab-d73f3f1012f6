package com.multiplier.timeoff.adapters

import com.multiplier.company.schema.holiday.LegalEntityHoliday
import com.multiplier.company.schema.holiday.LegalEntityHoliday.Holiday
import com.multiplier.company.schema.holiday.LegalEntityHolidayServiceGrpc
import com.multiplier.country.schema.Country
import com.multiplier.country.schema.holiday.HolidayOuterClass
import com.multiplier.country.schema.holiday.HolidayServiceGrpc
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface HolidayServiceAdapter {
    fun getHolidays(countryCodes: Set<Country.GrpcCountryCode>, year: Int, month: Int?, day: Int?): List<HolidayOuterClass.Holiday>
    fun getHolidays(contractIds: Set<Long>, year: Int, month: Int?): List<Holiday>
}

@Service
class DefaultHolidayServiceClient : HolidayServiceAdapter {

    @GrpcClient("country-service")
    private lateinit var stub: HolidayServiceGrpc.HolidayServiceBlockingStub

    @GrpcClient("company-service")
    private lateinit var legalEntityHolidayServiceStub: LegalEntityHolidayServiceGrpc.LegalEntityHolidayServiceBlockingStub

    override fun getHolidays(
        countryCodes: Set<Country.GrpcCountryCode>,
        year: Int,
        month: Int?,
        day: Int?
    ): List<HolidayOuterClass.Holiday> {
        val req = HolidayOuterClass.GetHolidaysRequest.newBuilder()
            .addAllCountryCode(countryCodes)
            .setYear(year)
            .setMonth(month ?: -1)
            .setDay(day ?: -1)
            .build()

        return stub.getHolidays(req).holidaysList
    }

    override fun getHolidays(contractIds: Set<Long>, year: Int, month: Int?): List<Holiday> {
        log.info { "[getHolidays] Getting holidays for contractIds: $contractIds, year: $year, month: $month"}
        val holidays = legalEntityHolidayServiceStub.getHolidays(toGrpcRequest(contractIds, year, month)).holidaysList
        log.info { "[getHolidays] Received ${holidays.size} holidays for contractIds: $contractIds, year: $year, month: $month"}
        return holidays
    }

    private fun toGrpcRequest(contractIds: Set<Long>, year: Int, month: Int?): LegalEntityHoliday.GetHolidaysRequest {
        val builder = LegalEntityHoliday.GetHolidaysRequest.newBuilder()
            .addAllContractIds(contractIds)
            .setYear(year)
        month?.let { builder.month = it }
        return builder.build()
    }
}