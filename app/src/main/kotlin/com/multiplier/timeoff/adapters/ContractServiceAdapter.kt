package com.multiplier.timeoff.adapters

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.contract.schema.contract.ContractOuterClass.*
import com.multiplier.contract.schema.contract.ContractServiceGrpc
import com.multiplier.contract.schema.contract.getContractsByIdsRequest
import com.multiplier.timeoff.core.util.TimeOffUtil
import net.devh.boot.grpc.client.inject.GrpcClient
import org.apache.commons.collections.CollectionUtils
import org.springframework.stereotype.Service

interface ContractServiceAdapter {
    fun findContractByContractId(contractId: Long): Contract
    fun getContractByIdAnyStatus(contractId: Long): Contract
    fun getContractsByIdsAnyStatus(contractIds: Collection<Long>): List<Contract>
    fun getContractsByIdsAnyStatus(contractIds: Collection<Long>, includeTest: Boolean): List<Contract>
    fun findContractByMemberId(memberId: Long): Contract?
    fun getActiveContractsForCompany(companyId: Long): List<Contract>
    fun getWorkEmailByContractId(contractId: Long): String
    fun getAllContractsForCompany(companyId: Long): List<Contract>
    fun getAllStartedContractsForCompany(companyId: Long): List<Contract>
    fun getContractIdsByCompanyIds(companyIds: Collection<Long>): List<Long>
    fun getNonDeletedNonEndedContractsByIds(contractIds: Collection<Long>): List<Contract>
    fun getNonDeletedNonEndedContractsByIdsInChunks(contractIds: List<Long>): List<Contract>
    fun getContractIdsMatchingFilters(filters: ContractFilters) : List<Long>
    fun getContractIdToCompanyIdMap(contractIds : Collection<Long>): Map<Long, Long>
    fun getContractsByEmployeeIds(employeeIds : Collection<String>, companyId : Long): Map<String, ContractBasicInfo>
    fun getContractMemberEmailsByContractIds(contractIds: Set<Long>): Map<Long, String>
    fun getStartedContractIdsByCompanyId(companyId: Long): List<Long>
    fun getEntityIdByContractId(contractId: Long): Long
    fun getBasicContractsByIds(contractIds: Set<Long>): Map<Long,ContractBasicInfo>
    fun getBasicContractById(contractId: Long): ContractBasicInfo
}

@Service
class DefaultContractServiceClient(
    private val currentUser: CurrentUser
) : ContractServiceAdapter {

    @GrpcClient("contract-service")
    private lateinit var stub: ContractServiceGrpc.ContractServiceBlockingStub

    override fun getAllContractsForCompany(companyId: Long): List<Contract> {
        val req = CompanyIdRequest.newBuilder()
            .setId(companyId)
            .build()

        return stub.getContractsForCompany(req).contractsList
    }

    override fun getAllStartedContractsForCompany(companyId: Long): List<Contract> {
        return getAllContractsForCompany(companyId).filter { it.started }
    }

    override fun findContractByContractId(contractId: Long): Contract {
        val req = GetContractByIdRequest.newBuilder()
            .setId(contractId)
            .build()

        return stub.getContractById(req)
    }

    override fun getContractByIdAnyStatus(contractId: Long): Contract {
        val req = GetContractByIdAnyStatusRequest.newBuilder()
            .setId(contractId)
            .build()

        return stub.getContractByIdAnyStatus(req)
    }

    override fun getContractsByIdsAnyStatus(contractIds: Collection<Long>): List<Contract> {
        val req = getContractsByIdsRequest {
            this.id += contractIds
        }

        return stub.getContractsByIdsAnyStatus(req).contractsList
    }

    override fun getContractsByIdsAnyStatus(contractIds: Collection<Long>, includeTest: Boolean): List<Contract> {
        val req = getContractsByIdsRequest {
            this.id += contractIds
            this.includeTest = includeTest
        }

        return stub.getContractsByIdsAnyStatus(req).contractsList
    }

    override fun findContractByMemberId(memberId: Long): Contract? {
        val req = GetContractByMemberIdRequest.newBuilder()
            .setId(memberId)
            .build()

        return stub.getContractByMemberId(req)
    }

    override fun getActiveContractsForCompany(companyId: Long): List<Contract> {
        val req = GetActiveContractsForCompanyRequest.newBuilder()
            .setId(companyId)
            .build()

        return stub.getActiveContractsForCompany(req).contractsList
    }

    override fun getWorkEmailByContractId(contractId: Long): String {
        val req = GetWorkEmailByContractIdRequest.newBuilder()
                .setContractId(contractId)
                .build()

        return stub.getWorkEmailByContractId(req).workEmail
    }

    override fun getContractIdsByCompanyIds(companyIds: Collection<Long>): List<Long> {
        val req = GetContractIdsByCompanyIdsRequest.newBuilder()
            .addAllCompanyIds(companyIds)
            .setIsTest(currentUser.context?.scopes?.isOperationsTestUser ?: false)
            .build()

        return stub.getContractIdsByCompanyIds(req).idsList
    }

    override fun getNonDeletedNonEndedContractsByIds(contractIds: Collection<Long>): List<Contract> {
        val req = GetNonDeletedNonEndedContractsByIdsRequest.newBuilder()
            .addAllIds(contractIds)
            .build()

        return stub.getNonDeletedNonEndedContractsByIds(req).contractsList
    }

    override fun getNonDeletedNonEndedContractsByIdsInChunks(contractIds: List<Long>): List<Contract> {
        log.info("Fetching contracts by chunk for contracts size : {}", contractIds.size)
        val contractIdChunks = TimeOffUtil.splitList(contractIds)
        val contracts = mutableListOf<Contract>()
        var chunkNo = 1;
        for (chunk in contractIdChunks) {
            log.info("Fetching contracts for chunk no : {}/{} (size : {})", chunkNo, contractIdChunks.size, chunk.size)
            val req = GetNonDeletedNonEndedContractsByIdsRequest.newBuilder()
                .addAllIds(chunk)
                .build()
            val contractsForChunk = stub.getNonDeletedNonEndedContractsByIds(req).contractsList
            if (!CollectionUtils.isEmpty(contractsForChunk)) {
                contracts.addAll(contractsForChunk)
            }

            chunkNo++
        }
        return contracts
    }

    override fun getContractIdsMatchingFilters(filters: ContractFilters): List<Long> {
        return stub.getContractIdsMatchingFilters(filters).idsList
    }

    override fun getContractIdToCompanyIdMap(contractIds: Collection<Long>): Map<Long, Long> {
        val req = GetContractsByIdsRequest.newBuilder()
            .addAllId(contractIds)
            .build();
        return stub.getContractIdToCompanyIdMap(req).contractIdToCompanyIdMap
    }

    override fun getContractsByEmployeeIds(employeeIds: Collection<String>, companyId : Long): Map<String, ContractBasicInfo> {
        log.info("[getContractsByExternalIds] calling contract-service to get contract by employeeIds for company id : {}", companyId)
        val req = GetContractsByEmployeeIdsRequest.newBuilder()
            .addAllEmployeeIds(employeeIds)
            .setCompanyId(companyId)
            .build();
        return stub.getContractsForEmployeeIds(req).employeeIdToContractMap
    }

    override fun getContractMemberEmailsByContractIds(contractIds: Set<Long>): Map<Long, String> {
        val request =
            GetContractMemberEmailsByContractIdsRequest.newBuilder()
                .addAllContractId(contractIds)
                .build()
        return stub.getContractMembersEmailByContractIds(request).memberEmailByContractIdMap
    }

    override fun getStartedContractIdsByCompanyId(companyId: Long): List<Long> {
        log.info { "[getStartedContractIdsByCompanyId] Calling contract-service to get started contract ids for company id : $companyId" }
        val req = CompanyIdRequest.newBuilder()
            .setId(companyId)
            .build()
        val contractIds = stub.getStartedContractIdsByCompanyId(req).idsList
        log.info { "[getStartedContractIdsByCompanyId] Received ${contractIds.size} started contract ids for company id : $companyId" }
        return contractIds
    }

    override fun getEntityIdByContractId(contractId: Long): Long {
        log.info { "[getEntityIdByContractId] Calling contract-service to get entity id for contract id : $contractId" }
        val req = GetEntityIdByContractIdRequest.newBuilder()
            .setContractId(contractId)
            .build()
        return stub.getEntityIdByContractId(req).entityId

    }

    override fun getBasicContractsByIds(contractIds: Set<Long>): Map<Long, ContractBasicInfo> {
        log.info { "[getBasicContractsByIds] Calling contract-service to get basic contracts for contract ids : $contractIds" }
        val req = GetBasicContractsByIdsRequest.newBuilder()
            .addAllIds(contractIds)
            .build()
        return stub.getBasicContractsByIds(req).contractsList.associateBy { it.contractId }
    }

    override fun getBasicContractById(contractId: Long): ContractBasicInfo {
        log.info{"[getBasicContractById] Calling contract-service to get basic contract for contract id : $contractId"}
        val req = GetBasicContractsByIdsRequest.newBuilder()
            .addAllIds(setOf(contractId))
            .build()
        return stub.getBasicContractsByIds(req).contractsList.first()
    }
}
