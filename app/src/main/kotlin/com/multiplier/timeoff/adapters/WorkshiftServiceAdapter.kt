package com.multiplier.timeoff.adapters

import com.multiplier.contract.schema.common.Common
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.GetBasicContractsByIdsRequest
import com.multiplier.contract.schema.contract.ContractServiceGrpc
import com.multiplier.timeoff.core.common.dto.WorkshiftDTO
import com.multiplier.timeoff.core.common.dto.WorkshiftDTO.WorkingHoursDTO
import com.multiplier.timeoff.core.util.WorkshiftUtil
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service
import java.time.DayOfWeek
import java.time.LocalTime

interface WorkshiftServiceAdapter {
    fun getWorkshiftByContractId(contractId: Long): WorkshiftDTO
    fun getWorkshiftByContract(contract: ContractOuterClass.ContractBasicInfo): WorkshiftDTO
}

@Service
class DefaultWorkshiftServiceAdapter(
) : WorkshiftServiceAdapter {

    @GrpcClient("contract-service")
    private lateinit var stub: ContractServiceGrpc.ContractServiceBlockingStub


    override fun getWorkshiftByContractId(contractId: Long): WorkshiftDTO {
        log.info { "[getBasicContractById] Calling contract-service to get basic contract for contract id : $contractId" }
        val req = GetBasicContractsByIdsRequest.newBuilder().addAllIds(setOf(contractId)).build()
        val contracts = stub.getBasicContractsByIds(req).contractsList
        if (contracts.isEmpty()) {
            throw IllegalStateException("No contract found for id: $contractId")
        }
        val contractWorkshift = contracts.first().workShift;
        return mapToWorkshiftDTO(contractWorkshift);
    }

    override fun getWorkshiftByContract(contract: ContractOuterClass.ContractBasicInfo): WorkshiftDTO {
        return mapToWorkshiftDTO(contract.workShift);
    }

    private fun mapToWorkshiftDTO(contractWorkshift: ContractOuterClass.WorkShift?): WorkshiftDTO {
        if (contractWorkshift == ContractOuterClass.WorkShift.getDefaultInstance() || contractWorkshift == null) {
            return WorkshiftUtil.DEFAULT_WORKSHIFT;
        }

        return WorkshiftDTO.builder()
            .startDate(mapToDayOfWeek(contractWorkshift.startDay))
            .endDate(mapToDayOfWeek(contractWorkshift.endDay))
            .workHours(mapToWorkingHours(contractWorkshift.workingHours))
            .breakHours(mapToWorkingHours(contractWorkshift.breakHours))
            .build()
    }

    private fun mapToWorkingHours(workHours: ContractOuterClass.WorkingHours): WorkingHoursDTO {
        return WorkingHoursDTO.builder()
            .startTime(LocalTime.of(workHours.startTime.hour, workHours.startTime.minute))
            .endTime(LocalTime.of(workHours.endTime.hour, workHours.endTime.minute))
            .build()
    }

    private fun mapToDayOfWeek(day: Common.DayOfWeek): DayOfWeek {
        return when (day) {
            Common.DayOfWeek.MONDAY -> DayOfWeek.MONDAY
            Common.DayOfWeek.TUESDAY -> DayOfWeek.TUESDAY
            Common.DayOfWeek.WEDNESDAY -> DayOfWeek.WEDNESDAY
            Common.DayOfWeek.THURSDAY -> DayOfWeek.THURSDAY
            Common.DayOfWeek.FRIDAY -> DayOfWeek.FRIDAY
            Common.DayOfWeek.SATURDAY -> DayOfWeek.SATURDAY
            Common.DayOfWeek.SUNDAY -> DayOfWeek.SUNDAY
            Common.DayOfWeek.UNKNOWN_DAY_OF_WEEK -> throw IllegalArgumentException("Unknown day of week")
            Common.DayOfWeek.UNRECOGNIZED -> throw IllegalArgumentException("Unknown day of week")
        }
    }
}