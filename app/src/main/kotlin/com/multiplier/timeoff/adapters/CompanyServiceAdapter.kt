package com.multiplier.timeoff.adapters

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.Company
import com.multiplier.company.schema.grpc.CompanyOuterClass.GetCompanyRequest
import com.multiplier.company.schema.grpc.CompanyServiceGrpc
import com.multiplier.core.schema.common.Common
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface CompanyServiceAdapter {
    fun getCompany(id: Long): Company
    fun getCompanyUser(filters: CompanyUserFilters): CompanyOuterClass.CompanyUser
    fun isTestCompany(companyId: Long): Boolean
    fun getCompanyAdmins(companyId: Long): List<CompanyOuterClass.CompanyUser?>
    fun getCompanyByIds(companyIds: Collection<Long>): List<Company>
    fun getLegalEntityIdsByCompanyIds(companyIds: Collection<Long>): Map<Long, List<Long>>
    fun getLegalEntitiesByIds(legalEntityIds: Collection<Long>): List<CompanyOuterClass.LegalEntity>


}

@Service
class DefaultCompanyServiceClient(
) : CompanyServiceAdapter {

    @GrpcClient("company-service")
    private lateinit var stub: CompanyServiceGrpc.CompanyServiceBlockingStub


    override fun getCompany(id: Long): Company {
        val req = GetCompanyRequest.newBuilder()
            .setId(id)
            .build()

        return stub.getCompany(req)
    }

    override fun getCompanyUser(filters: CompanyUserFilters): CompanyOuterClass.CompanyUser {
        val req = CompanyOuterClass.CompanyUsersFiltersRequest.newBuilder()
            .setId(filters.id ?: -1)
            .setUserId(filters.userId ?: -1)
            .addAllCompanyIds(if (filters.companyId == null) emptySet() else setOf(filters.companyId))
            .build()
        return stub.getCompanyUsersBy(req)
            .itemsList
            .first()
    }

    override fun isTestCompany(companyId: Long): Boolean =
        getCompany(companyId).isTest

    override fun getCompanyAdmins(companyId: Long): List<CompanyOuterClass.CompanyUser?> {
        val req = GetCompanyRequest.newBuilder()
            .setId(companyId)
            .build()
        return mapToGraphCompanyUsers(stub.getCompanyAdmins(req));
    }

    override fun getCompanyByIds(companyIds: Collection<Long>): List<Company> {
        val req = CompanyOuterClass.GetCompaniesRequest.newBuilder()
            .addAllIds(companyIds)
            .build()
        return stub.getCompanies(req).companiesList
    }

    override fun getLegalEntityIdsByCompanyIds(companyIds: Collection<Long>): Map<Long, List<Long>> {
        val req = CompanyOuterClass.GetLegalEntityIdsByCompanyIdsRequest.newBuilder()
                .addAllCompanyIds(companyIds)
                .build()

        val result = stub.getLegalEntityIdsByCompanyIds(req)
        return mapToCompanyEntityIds(result)
    }

    override fun getLegalEntitiesByIds(legalEntityIds: Collection<Long>): List<CompanyOuterClass.LegalEntity> {
        log.info {"Calling company service to get legal entities for id list (size : ${legalEntityIds.size})"}
            val req = CompanyOuterClass.GetLegalEntitiesRequest.newBuilder()
                .addAllIds(legalEntityIds)
                .build()
            return stub.getLegalEntities(req).entitiesList
    }


    private fun mapToGraphCompanyUsers(
        companyUsers: CompanyOuterClass.CompanyUsers?
    ): List<CompanyOuterClass.CompanyUser?> {
        if (companyUsers == null) return emptyList()
        return companyUsers.usersList.map { companyUser -> mapToGraphCompanyUser(companyUser) }
    }

    private fun mapToGraphCompanyUser(companyUser: CompanyOuterClass.CompanyUser?): CompanyOuterClass.CompanyUser? {
        if (companyUser == null) {
            return null
        }
        return CompanyOuterClass.CompanyUser.newBuilder()
            .setId(companyUser.id)
            .setCompanyId(companyUser.companyId)
            .setUserId(companyUser.userId)
            .setFirstName(companyUser.firstName)
            .setLastName(companyUser.lastName)
            .addAllEmails(mapEmailAddresses(companyUser.emailsList))
            .build()
    }

    private fun mapEmailAddresses(emailAddresses: List<Common.EmailAddress>): List<Common.EmailAddress> {
        return emailAddresses.map { emailAddress -> mapEmailAddress(emailAddress) }
    }

    private fun mapEmailAddress(emailAddress: Common.EmailAddress): Common.EmailAddress {
        return Common.EmailAddress.newBuilder()
            .setEmail(emailAddress.email)
            .setType(emailAddress.type)
            .build()
    }

    private fun mapToCompanyEntityIds(result: CompanyOuterClass.GetLegalEntityIdsByCompanyIdsResponse): Map<Long, List<Long>> {
        return result.companyLegalEntityIdsList.associate { it.companyId to it.legalEntityIdsList }
    }
}

class CompanyUserFilters(
    val id: Long? = null,
    val userId: Long? = null,
    val companyId: Long? = null,
)

fun CurrentUser.toCompanyUserFilters(): CompanyUserFilters {
    return CompanyUserFilters(
        id = this.context?.scopes?.companyUserId,
        companyId = this.context?.scopes?.companyId,
        userId = this.context?.id,
    )
}