spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    hikari:
      poolName: Hikari
      auto-commit: false

platform:
  frontend:
    baseurl: https://release-app.usemultiplier.com

grpc:
  client:
    core-service:
      address: dns:///core-service.release.local.usemultiplier.com:9090
      negotiationType: TLS
    company-service:
      address: dns:///company-service.release.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    member-service:
      address: dns:///member-service.release.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    country-service:
      address: dns:///country-service.release.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    contract-service:
      address: dns:///contract-service.release.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    org-management-service:
      address: dns:///org-management-service.release.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    pigeon-service:
      address: dns:///pigeon-service.release.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS

pigeon:
  client:
    kafka:
      bootstrap-servers: b-1.kafka-cluster-rel.2cwaen.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-2.kafka-cluster-rel.2cwaen.c2.kafka.ap-southeast-1.amazonaws.com:9092


