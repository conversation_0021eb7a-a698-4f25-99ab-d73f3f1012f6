spring:
  main:
    banner-mode: OFF
    lazy-initialization: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: **************************************************************************************
    username: coredb
    password: aws-secret
    hikari:
      poolName: Hikari
      auto-commit: false
      maximum-pool-size: 1
      minimum-idle: 0
  data:
    jpa:
      repositories:
        bootstrap-mode: lazy
  devtools:
    restart:
      enabled: true
      poll-interval: 5s
      quiet-period: 4s
    livereload:
      enabled: true
platform:
  frontend:
    baseurl: https://stage-app.usemultiplier.com

external:
  core-service:
    host: core-service.staging.usemultiplier.local
    port: 8081

grpc:
  client:
    core-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    member-service:
      address: dns:///localhost:9093
      negotiationType: PLAINTEXT
    country-service:
      address: dns:///localhost:9094
      negotiationType: PLAINTEXT
    contract-service:
      address: dns:///localhost:9195
      negotiationType: PLAINTEXT
    pigeon-service:
      address: dns:///localhost:9096
      negotiationType: PLAINTEXT
  server:
    port: 9090
    security:
      enabled: false

jwt:
  public-key: MIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAmzU6eIvgeuPL9Rd/NHjqyatae8pg4AeJxMo/judIMA3n4MAxPbI6/2VboB+0sdzG+Bc13AHXNExqEFBcyMdKV8oB+yoR4uTc/qrDeTZhoDvETBLVHwfRbX4ecTA2+I0DD5huI/CAcbKdrIiLqs4h+A4wi50CJ+D+I6TBkB3jTGJsRrY=
