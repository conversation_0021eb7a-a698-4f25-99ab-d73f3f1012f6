<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231027070600-1" author="Naveen Avishka">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column [status] already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="definition" columnName="status"/>
            </not>
        </preConditions>
        <comment>add column status to definition table</comment>

        <addColumn schemaName="timeoff" tableName="definition">
            <column type="VARCHAR(255)" name="status" defaultValue="ACTIVE"/>
        </addColumn>
    </changeSet>

    <changeSet id="20231027070600-2" author="Naveen Avishka">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column [status] already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="definition_aud" columnName="status"/>
            </not>
        </preConditions>
        <comment>add column status to definition_aud table</comment>

        <addColumn schemaName="timeoff" tableName="definition_aud">
            <column type="VARCHAR(255)" name="status"/>
        </addColumn>
    </changeSet>

    <changeSet author="Naveen Avishka" id="20231027070600-3"  dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Table [timeoff_definition_rule] already exists...">
            <not>
                <tableExists schemaName="timeoff" tableName="timeoff_definition_rule"/>
            </not>
        </preConditions>
        <comment>Create table to store timeoff definition assignment rules</comment>

        <createTable schemaName="timeoff" tableName="timeoff_definition_rule"
                     remarks="Store all rules related to company definitions">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="timeoff_definition_rule_pkey"/>
            </column>
            <column name="company_definition_id" type="BIGINT"/>
            <column name="company_id" type="BIGINT"/>
            <column name="rule_type" type="VARCHAR(255)"/>
            <column name="condition_key" type="VARCHAR(255)" remarks="condition key will be populated only when rule type is BY_CONDITION, otherwise null"/>
            <column name="condition_operator" type="VARCHAR(255)" remarks="condition operator will be populated only when rule type is BY_CONDITION, otherwise null"/>
            <column name="condition_values" type="TEXT[]" remarks="condition values will be populated only when rule type is BY_CONDITION, otherwise null"/>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>

        <addForeignKeyConstraint constraintName="fk_company_definition_in_timeoff_definition_rule"
                                 baseTableSchemaName="timeoff" baseTableName="timeoff_definition_rule" baseColumnNames="company_definition_id"
                                 referencedTableSchemaName="timeoff" referencedTableName="company_definition" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" validate="true"/>

        <createIndex schemaName="timeoff" tableName="timeoff_definition_rule" indexName="timeoff_definition_rule_company_id_idx">
            <column name="company_id"/>
        </createIndex>

        <createIndex schemaName="timeoff" tableName="timeoff_definition_rule" indexName="timeoff_definition_rule_definition_id_idx">
            <column name="company_definition_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="Naveen Avishka" id="20231027070600-4"  dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Table [timeoff_definition_rule_aud] already exists...">
            <not>
                <tableExists schemaName="timeoff" tableName="timeoff_definition_rule_aud"/>
            </not>
        </preConditions>

        <createTable schemaName="timeoff" tableName="timeoff_definition_rule_aud">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_timeoff_definition_rule_aud"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_timeoff_definition_rule_aud"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="definition_id" type="BIGINT"/>
            <column name="company_id" type="BIGINT"/>
            <column name="rule_type" type="VARCHAR(255)"/>
            <column name="condition_key" type="VARCHAR(255)"/>
            <column name="condition_operator" type="VARCHAR(255)"/>
            <column name="condition_values" type="TEXT[]"/>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>

    <changeSet id="20231027070600-5" author="Naveen Avishka">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column [name] already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="definition" columnName="name"/>
            </not>
        </preConditions>
        <comment>add column name to definition table</comment>

        <addColumn schemaName="timeoff" tableName="definition">
            <column type="VARCHAR(255)" name="name"/>
        </addColumn>
    </changeSet>

    <changeSet id="20231027070600-6" author="Naveen Avishka">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column [name] already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="definition_aud" columnName="name"/>
            </not>
        </preConditions>
        <comment>add column name to definition_aud table</comment>

        <addColumn schemaName="timeoff" tableName="definition_aud">
            <column type="VARCHAR(255)" name="name"/>
        </addColumn>
    </changeSet>

    <changeSet id="20231027070600-7" author="Naveen Avishka">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column [company_definition_id] already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_definition_rule_aud" columnName="company_definition_id"/>
            </not>
        </preConditions>
        <comment>rename column definition_id to company_definition_id in timeoff_definition_rule_aud table</comment>

        <renameColumn schemaName="timeoff" tableName="timeoff_definition_rule_aud" oldColumnName="definition_id" newColumnName="company_definition_id"/>

    </changeSet>

    <changeSet id="20231027070600-8" author="Naveen Avishka">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column [definition_id] already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="entitlement" columnName="definition_id"/>
            </not>
        </preConditions>
        <comment>add definition id column to entitlement</comment>

        <addColumn schemaName="timeoff" tableName="entitlement">
            <column type="BIGINT" name="definition_id"/>
        </addColumn>

        <addForeignKeyConstraint constraintName="fk_definition_in_entitlement"
                                 baseTableSchemaName="timeoff" baseTableName="entitlement" baseColumnNames="definition_id"
                                 referencedTableSchemaName="timeoff" referencedTableName="definition" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" validate="true"/>

    </changeSet>

    <changeSet id="20231027070600-9" author="Naveen Avishka">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column [definition_id] already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="entitlement_aud" columnName="definition_id"/>
            </not>
        </preConditions>
        <comment>add definition id column to entitlement_aud</comment>

        <addColumn schemaName="timeoff" tableName="entitlement_aud">
            <column type="BIGINT" name="definition_id"/>
        </addColumn>

    </changeSet>



</databaseChangeLog>
