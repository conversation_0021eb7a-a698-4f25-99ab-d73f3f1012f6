<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240429111100-1" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <comment>Rename multiplier timeoff types</comment>
        <sql>
            UPDATE timeoff.timeoff_type
                SET label= 'Annual Leave'
            WHERE label = 'annual';

            UPDATE timeoff.timeoff_type
                SET label= 'Sick Leave'
            WHERE label = 'sick';

            UPDATE timeoff.timeoff_type
                SET label= 'Maternity Leave'
            WHERE label = 'maternity';

            UPDATE timeoff.timeoff_type
                SET label= 'Paternity Leave'
            WHERE label = 'paternity';

            UPDATE timeoff.timeoff_type
                SET label= 'Personal Leave'
            WHERE label = 'personal';

            UPDATE timeoff.timeoff_type
                SET label= 'Bereavement Leave'
            WHERE label = 'bereavement';

            UPDATE timeoff.timeoff_type
                SET label= 'Sabbatical Leave'
            WHERE label = 'sabbatical';

            UPDATE timeoff.timeoff_type
                SET label= 'Family Leave'
            WHERE label = 'family';

            UPDATE timeoff.timeoff_type
                SET label= 'Solo Parent Leave'
            WHERE label = 'solo-paraent';

            UPDATE timeoff.timeoff_type
                SET label= 'Child Care Leave'
            WHERE label = 'child-care';

            UPDATE timeoff.timeoff_type
                SET label= 'Study Leave'
            WHERE label = 'study';

            UPDATE timeoff.timeoff_type
                SET label= 'Military Leave'
            WHERE label = 'military';

            UPDATE timeoff.timeoff_type
                SET label= 'Compensatory Leave'
            WHERE label = 'compensatory';

            UPDATE timeoff.timeoff_type
                SET label= 'Unpaid Leave'
            WHERE label = 'unpaid';

            UPDATE timeoff.timeoff_type
                SET label= 'Circumcision Leave'
            WHERE label = 'circumcision';
        </sql>


    </changeSet>

</databaseChangeLog>
