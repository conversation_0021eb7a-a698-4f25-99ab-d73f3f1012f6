<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20220706000800-1" author="ThangNgo" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped: unnecessary on this DB">
            <sqlCheck expectedResult="timeoff">
                select current_database();
            </sqlCheck>
        </preConditions>
        <comment>Migrate to timeoff db</comment>
        <sql>
            -- set up sequences
            ALTER TABLE "timeoff"."entitlement"
                ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY;
            select setval('timeoff.entitlement_id_seq', (select max(id) + 100 from timeoff.entitlement));

            ALTER TABLE "timeoff"."timeoff"
                ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY;
            select setval('timeoff.timeoff_id_seq', (select max(id) + 100 from timeoff.timeoff));

            ALTER TABLE "timeoff"."timeoff_summary"
                ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY;
            select setval('timeoff.timeoff_summary_id_seq', (select max(id) + 100 from timeoff.timeoff_summary));

            ALTER TABLE "timeoff"."timeoff_type"
                ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY;
            select setval('timeoff.timeoff_type_id_seq', (select max(id) + 100 from timeoff.timeoff_type));

            -- unique
            ALTER TABLE "timeoff"."entitlement" ADD CONSTRAINT "uq_contract_entitlement" UNIQUE ("type_id", "contract_id");

            -- FK
            ALTER TABLE "timeoff"."entitlement" ADD CONSTRAINT "fk_type_in_entitlement" FOREIGN KEY ("type_id") REFERENCES "timeoff"."timeoff_type" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
            ALTER TABLE "timeoff"."timeoff" ADD CONSTRAINT "fk_timeoff_type_in_timeoff" FOREIGN KEY ("type_id") REFERENCES "timeoff"."timeoff_type" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
            ALTER TABLE "timeoff"."timeoff_summary" ADD CONSTRAINT "fk_timeoff_type_in_timeoff_summary" FOREIGN KEY ("type_id") REFERENCES "timeoff"."timeoff_type" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION;
        </sql>
    </changeSet>

    <changeSet id="20220706000800-2" author="ThangNgo" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped: unnecessary on this DB">
            <sqlCheck expectedResult="timeoff">
                select current_database();
            </sqlCheck>
        </preConditions>
        <comment>Add hibernate envers stuff</comment>
        <sql>
            CREATE SEQUENCE hibernate_sequence  INCREMENT 1  MINVALUE 1
              MAXVALUE 9223372036854775807
              START 500000
              CACHE 1;

            DROP TABLE IF EXISTS "timeoff"."revinfo";
            CREATE TABLE "timeoff"."revinfo"
            (
                "rev"      int4 NOT NULL,
                "revtstmp" int8
            )
            ;

            ALTER TABLE "timeoff"."revinfo" ADD CONSTRAINT "revinfo_pkey" PRIMARY KEY ("rev");
        </sql>
    </changeSet>

</databaseChangeLog>
