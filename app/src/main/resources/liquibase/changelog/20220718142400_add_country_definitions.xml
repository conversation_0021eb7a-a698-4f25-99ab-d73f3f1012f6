<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">


    <changeSet id="20220718142400-1" author="NickPayne" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <tableExists schemaName="timeoff" tableName="country_definition"/>
            </not>
        </preConditions>

        <comment>create country_definition table using ddl generated from core-service</comment>

        <sql>
            -- auto-generated definition
            create table country_definition
            (
                id            bigint generated by default as identity
                    constraint pk_timeoff_definition
                        primary key,
                type_id       bigint                not null,
                country_code  varchar(5),
                state_code    varchar(20),
                is_required   boolean default false not null,
                description   text,
                basis         varchar(255),
                validations   jsonb,
                created_by    bigint,
                created_on    timestamp without time zone,
                updated_by    bigint,
                updated_on    timestamp without time zone,
                configuration jsonb,
                clause        text
            );

--             no need this, the current user will be the owner by default.
--             alter table country_definition
--                 owner to timeoff;

            create unique index uq_timeoff_definition
                on country_definition (type_id, COALESCE(country_code, ''::character varying), COALESCE(state_code, ''::character varying));
        </sql>
    </changeSet>

    <changeSet id="20220718142400-2" author="ThangNgo" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped because does not exist">
            <tableExists schemaName="public" tableName="country_definition"/>
        </preConditions>

        <comment>
            Delete `country_definition` table in `public` schema in devs' local db.
            Because many devs are still using `coredb.timeoff`, the 20220718142400-1 will create the table in `public` if schena name not specified
        </comment>

        <dropTable schemaName="public" tableName="country_definition"/>
    </changeSet>

    <changeSet id="20220718142400-3" author="ThangNgo" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as already exists">
            <not>
                <tableExists schemaName="timeoff" tableName="country_definition"/>
            </not>
        </preConditions>

        <comment>
            Add missing `timeoff.` prefix before the table name.
            After this, the table is created in correct schema (timeoff).
            If already in `timeoff` schema, this will be skipped.
        </comment>

        <sql>
            -- auto-generated definition
            create table timeoff.country_definition
            (
                id            bigint generated by default as identity
                    constraint pk_timeoff_definition
                        primary key,
                type_id       bigint                not null,
                country_code  varchar(5),
                state_code    varchar(20),
                is_required   boolean default false not null,
                description   text,
                basis         varchar(255),
                validations   jsonb,
                created_by    bigint,
                created_on    timestamp without time zone,
                updated_by    bigint,
                updated_on    timestamp without time zone,
                configuration jsonb,
                clause        text
            );

            create unique index uq_timeoff_definition
                on timeoff.country_definition (type_id, COALESCE(country_code, ''::character varying), COALESCE(state_code, ''::character varying));
        </sql>
    </changeSet>

</databaseChangeLog>
