<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20220708001500-1" author="ThangNgo" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped: already exists">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff" columnName="end_date"/>
                <columnExists schemaName="timeoff" tableName="timeoff" columnName="end_session"/>
                <columnExists schemaName="timeoff" tableName="timeoff_aud" columnName="end_date"/>
                <columnExists schemaName="timeoff" tableName="timeoff_aud" columnName="end_session"/>
            </not>
        </preConditions>

        <comment>Add end_date/end_session columns - https://app.clickup.com/t/2kchkk4</comment>

        <addColumn schemaName="timeoff" tableName="timeoff">
            <column name="end_date" type="date"/>
            <column name="end_session" type="text"/>
        </addColumn>

        <addColumn schemaName="timeoff" tableName="timeoff_aud">
            <column name="end_date" type="date"/>
            <column name="end_session" type="text"/>
        </addColumn>
    </changeSet>

    <changeSet id="20220708001500-2" author="ThangNgo" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>

        <comment>Drop not null constraints from back_to_work_x columns - https://app.clickup.com/t/2kchkk4</comment>

        <dropNotNullConstraint schemaName="timeoff" tableName="timeoff" columnName="back_to_work_date"/>
        <dropNotNullConstraint schemaName="timeoff" tableName="timeoff" columnName="back_to_work_session"/>
        <dropNotNullConstraint schemaName="timeoff" tableName="timeoff_aud" columnName="back_to_work_date"/>
        <dropNotNullConstraint schemaName="timeoff" tableName="timeoff_aud" columnName="back_to_work_session"/>
    </changeSet>

    <changeSet id="20220708001500-3" author="ThangNgo" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>

        <comment>add comments on the columns - https://app.clickup.com/t/2kchkk4</comment>

        <sql>
            COMMENT ON COLUMN "timeoff"."timeoff"."back_to_work_date" IS 'deprecated. Use end_date';
            COMMENT ON COLUMN "timeoff"."timeoff"."back_to_work_session" IS 'deprecated. Use end_session';
            COMMENT ON COLUMN "timeoff"."timeoff"."end_date" IS 'replace back_to_work_date';
            COMMENT ON COLUMN "timeoff"."timeoff"."end_session" IS 'replace back_to_work_session';
        </sql>
    </changeSet>

</databaseChangeLog>
