<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <!--    Adding new columns to summary table-->
    <changeSet id="20241220144800-1" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [carry_forward_count] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary" columnName="carry_forward_count"/>
            </not>
        </preConditions>
        <comment>Add [carry_forward_count] column to summary table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary">
            <column type="double" name="carry_forward_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-2" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [carry_forward_expired_count] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary" columnName="carry_forward_expired_count"/>
            </not>
        </preConditions>
        <comment>Add [carry_forward_expired_count] column to summary table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary">
            <column type="double" name="carry_forward_expired_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-3" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [used_from_carry_forward_count] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary" columnName="used_from_carry_forward_count"/>
            </not>
        </preConditions>
        <comment>Add [used_from_carry_forward_count] column to summary table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary">
            <column type="double" name="used_from_carry_forward_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-4" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [used_from_lapsable_count] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary" columnName="used_from_lapsable_count"/>
            </not>
        </preConditions>
        <comment>Add [used_from_lapsable_count] column to summary table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary">
            <column type="double" name="used_from_lapsable_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-5" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [used_from_next_cycle_carry_forward_count] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary" columnName="used_from_next_cycle_carry_forward_count"/>
            </not>
        </preConditions>
        <comment>Add [used_from_next_cycle_carry_forward_count] column to summary table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary">
            <column type="double" name="used_from_next_cycle_carry_forward_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-6" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [used_from_allocated_count] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary" columnName="used_from_allocated_count"/>
            </not>
        </preConditions>
        <comment>Add [used_from_allocated_count] column to summary table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary">
            <column type="double" name="used_from_allocated_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-7" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [status] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary" columnName="status"/>
            </not>
        </preConditions>
        <comment>add status to timeoff_summary table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary">
            <column type="VARCHAR(255)" name="status"/>
        </addColumn>
    </changeSet>



    <changeSet id="20241220144800-8" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [carry_forward_count] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary_aud" columnName="carry_forward_count"/>
            </not>
        </preConditions>
        <comment>Add [carry_forward_count] column to summary audit table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary_aud">
            <column type="double" name="carry_forward_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-9" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [carry_forward_expired] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary_aud" columnName="carry_forward_expired_count"/>
            </not>
        </preConditions>
        <comment>Add [carry_forward_expired_count] column to summary audit table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary_aud">
            <column type="double" name="carry_forward_expired_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-10" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [used_from_carry_forward_count] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary_aud" columnName="used_from_carry_forward_count"/>
            </not>
        </preConditions>
        <comment>Add [used_from_carry_forward_count] column to summary audit table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary_aud">
            <column type="double" name="used_from_carry_forward_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-11" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [used_from_lapsable_count] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary_aud" columnName="used_from_lapsable_count"/>
            </not>
        </preConditions>
        <comment>Add [used_from_lapsable_count] column to summary audit table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary_aud">
            <column type="double" name="used_from_lapsable_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-12" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [used_from_next_cycle_carry_forward] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary_aud" columnName="used_from_next_cycle_carry_forward_count"/>
            </not>
        </preConditions>
        <comment>Add [used_from_next_cycle_carry_forward_count] column to summary audit table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary_aud">
            <column type="double" name="used_from_next_cycle_carry_forward_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-13" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [used_from_allocated_count] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary_aud" columnName="used_from_allocated_count"/>
            </not>
        </preConditions>
        <comment>Add [used_from_allocated_count] column to summary audit table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary_aud">
            <column type="double" name="used_from_allocated_count" defaultValue="0.0"/>
        </addColumn>
    </changeSet>

    <changeSet id="20241220144800-14" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [status] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary_aud" columnName="status"/>
            </not>
        </preConditions>
        <comment>add status to timeoff_summary_aud table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary_aud">
            <column type="VARCHAR(255)" name="status"/>
        </addColumn>
    </changeSet>


    <!--    Adding new table for timeoff usage break down-->
    <changeSet  id="20241220144800-15" author="Naveen"  dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Table [timeoff_usage] already exists...">
            <not>
                <tableExists schemaName="timeoff" tableName="timeoff_usage"/>
            </not>
        </preConditions>
        <comment>Create table to store timeoff usage info</comment>

        <createTable schemaName="timeoff" tableName="timeoff_usage"
                     remarks="Represent the break down of the timeoff for different timeoff summar">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="timeoff_usage_pkey"/>
            </column>
            <column name="timeoff_id" type="BIGINT"/>
            <column name="used_from_carry_forward_count" type="DOUBLE"/>
            <column name="used_from_allocated_count" type="DOUBLE"/>
            <column name="used_from_lapsable_count" type="DOUBLE"/>
            <column name="used_from_next_cycle_carry_forward_count" type="DOUBLE"/>
            <column name="used_from_next_cycle_allocated_count" type="DOUBLE"/>
            <column name="status" type="TEXT"/>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>

        <addForeignKeyConstraint constraintName="fk_timeoff_in_timeoff_usage"
                                 baseTableSchemaName="timeoff" baseTableName="timeoff_usage" baseColumnNames="timeoff_id"
                                 referencedTableSchemaName="timeoff" referencedTableName="timeoff" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" validate="true"/>

        <createIndex schemaName="timeoff" tableName="timeoff_usage" indexName="timeoff_usage_timeoff_id_idx">
            <column name="timeoff_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="20241220144800-16" author="Naveen"   dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Table [timeoff_usage_aud] already exists...">
            <not>
                <tableExists schemaName="timeoff" tableName="timeoff_usage_aud"/>
            </not>
        </preConditions>
        <comment>Create table to store timeoff usage info</comment>

        <createTable schemaName="timeoff" tableName="timeoff_usage_aud">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_timeoff_usage_aud"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_timeoff_usage_aud"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="timeoff_id" type="BIGINT"/>
            <column name="used_from_carry_forward_count" type="DOUBLE"/>
            <column name="used_from_allocated_count" type="DOUBLE"/>
            <column name="used_from_lapsable_count" type="DOUBLE"/>
            <column name="used_from_next_cycle_carry_forward_count" type="DOUBLE"/>
            <column name="used_from_next_cycle_allocated_count" type="DOUBLE"/>
            <column name="status" type="TEXT"/>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>



</databaseChangeLog>