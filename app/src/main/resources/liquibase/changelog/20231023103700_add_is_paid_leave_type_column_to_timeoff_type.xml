<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231023103700-1" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_type" columnName="is_paid_leave"/>
            </not>
        </preConditions>
        <comment>add column is_paid_leave to timeoff_type table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_type">
            <column type="BOOLEAN" name="is_paid_leave" defaultValue="FALSE"/>
        </addColumn>
    </changeSet>

    <changeSet id="20231023103700-2" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_type_aud" columnName="is_paid_leave"/>
            </not>
        </preConditions>
        <comment>add column is_paid_leave to timeoff_type_aud table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_type_aud">
            <column type="BOOLEAN" name="is_paid_leave" defaultValue="FALSE"/>
        </addColumn>
    </changeSet>

    <changeSet id="20231023103700-3" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_type" columnName="status"/>
            </not>
        </preConditions>
        <comment>add column status to timeoff_type table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_type">
            <column type="VARCHAR(255)" name="status" defaultValue="ACTIVE"/>
        </addColumn>
    </changeSet>

    <changeSet id="20231023103700-4" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_type_aud" columnName="status"/>
            </not>
        </preConditions>
        <comment>add column status to timeoff_type_aud table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_type_aud">
            <column type="VARCHAR(255)" name="status" defaultValue="ACTIVE"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
