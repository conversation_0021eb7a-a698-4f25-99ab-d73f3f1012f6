<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">


    <!-- 1. [timeoff_type] table changes, add columns...-->
    <changeSet id="20220809024031-1" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_type" columnName="company_id"/>
            </not>
        </preConditions>

        <comment>Add timeoff.timeoff_type.company_id column....</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_type">
            <column name="company_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20220809024031-2" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_type_aud" columnName="company_id"/>
            </not>
        </preConditions>

        <comment>Add timeoff.timeoff_type_aud.company_id column....</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_type_aud">
            <column name="company_id" type="BIGINT"/>
        </addColumn>
    </changeSet>

    <!-- 2. [timeoff_summary] table changes, rename...-->
    <changeSet id="20220809024031-3" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column does not exists...">
            <and>
                <columnExists schemaName="timeoff" tableName="timeoff_summary" columnName="entitled_count"/>
                <not>
                    <columnExists schemaName="timeoff" tableName="timeoff_summary" columnName="allocated_count"/>
                </not>
            </and>
        </preConditions>

        <comment>Add timeoff.timeoff_type_aud.company_id column....</comment>

        <renameColumn schemaName="timeoff" tableName="timeoff_summary" oldColumnName="entitled_count" newColumnName="allocated_count"/>
    </changeSet>

    <changeSet id="20220809024031-4" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column does not exists...">
            <and>
                <columnExists schemaName="timeoff" tableName="timeoff_summary_aud" columnName="entitled_count"/>
                <not>
                    <columnExists schemaName="timeoff" tableName="timeoff_summary_aud" columnName="allocated_count"/>
                </not>
            </and>
        </preConditions>

        <comment>Add timeoff.timeoff_type_aud.company_id column....</comment>

        <renameColumn schemaName="timeoff" tableName="timeoff_summary_aud" oldColumnName="entitled_count" newColumnName="allocated_count"/>
    </changeSet>

    <!-- 3. [timeoff_summary] table changes, add columns...-->
    <changeSet id="20220809024031-5" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column does not exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary" columnName="carried_count"/>
            </not>
        </preConditions>

        <comment>Add timeoff.timeoff_type_aud.company_id column....</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary">
            <column name="entitled_count" type="FLOAT8"/>
            <column name="carried_count" type="FLOAT8"/>
        </addColumn>
    </changeSet>

    <changeSet id="20220809024031-6" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Column does not exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_summary_aud" columnName="carried_count"/>
            </not>
        </preConditions>

        <comment>Add timeoff.timeoff_type_aud.company_id column....</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_summary_aud">
            <column name="entitled_count" type="FLOAT8"/>
            <column name="carried_count" type="FLOAT8"/>
        </addColumn>
    </changeSet>

    <!-- 4. [timeoff_summary] table data changes, updating entitled_count...-->
    <changeSet id="20220809024031-7" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>

        <comment>Add timeoff.timeoff_type_aud.company_id column....</comment>

        <sql>
            UPDATE timeoff.timeoff_summary
            SET entitled_count = COALESCE(allocated_count, 0) + COALESCE(carried_count, 0);

            UPDATE timeoff.timeoff_summary_aud
            SET entitled_count = COALESCE(allocated_count, 0) + COALESCE(carried_count, 0);
        </sql>
    </changeSet>


    <!-- 5. [definition] table creation...-->
    <changeSet id="20220809024031-8" author="thiru" dbms="postgresql">

        <validCheckSum>ANY</validCheckSum>

        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <tableExists schemaName="timeoff" tableName="definition"/>
            </not>
        </preConditions>

        <comment>[definition] table creation...</comment>

        <createTable schemaName="timeoff" tableName="definition">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_definition"/>
            </column>
            <column name="country_code" type="VARCHAR(5)"/>
            <column name="state_code" type="VARCHAR(20)"/>
            <column name="description" type="TEXT"/>
            <column name="basis" type="VARCHAR(255)"/>
            <column name="is_required" type="BOOLEAN" defaultValue="false"/>
            <column name="validations" type="JSONB"/>
            <column name="configurations" type="JSONB"/>

            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>

    <changeSet id="20220809024031-9" author="thiru" dbms="postgresql">

        <validCheckSum>ANY</validCheckSum>

        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <tableExists schemaName="timeoff" tableName="definition_aud"/>
            </not>
        </preConditions>

        <comment>[definition_aud] table creation...</comment>

        <createTable schemaName="timeoff" tableName="definition_aud">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_definition_aud"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_definition_aud"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="country_code" type="VARCHAR(5)"/>
            <column name="state_code" type="VARCHAR(20)"/>
            <column name="description" type="TEXT"/>
            <column name="basis" type="VARCHAR(255)"/>
            <column name="is_required" type="BOOLEAN" defaultValue="false"/>
            <column name="validations" type="JSONB"/>
            <column name="configurations" type="JSONB"/>

            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>


    <!-- 6. [company_definition] table creation...-->
    <changeSet id="20220809024031-10" author="thiru" dbms="postgresql">

        <validCheckSum>ANY</validCheckSum>

        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <tableExists schemaName="timeoff" tableName="company_definition"/>
            </not>
        </preConditions>

        <comment>[company_definition] table creation...</comment>

        <createTable schemaName="timeoff" tableName="company_definition">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_company_definition"/>
            </column>
            <column name="type_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="definition_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>

        <addUniqueConstraint constraintName="uq_company_definition" schemaName="timeoff" tableName="company_definition" columnNames="type_id, definition_id"/>

        <addForeignKeyConstraint constraintName="fk_type_in_company_definition"
                                 baseTableSchemaName="timeoff" baseTableName="company_definition" baseColumnNames="type_id"
                                 referencedTableSchemaName="timeoff" referencedTableName="timeoff_type" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" validate="true"/>

        <addForeignKeyConstraint constraintName="fk_definition_in_company_definition"
                                 baseTableSchemaName="timeoff" baseTableName="company_definition" baseColumnNames="definition_id"
                                 referencedTableSchemaName="timeoff" referencedTableName="definition" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" validate="true"/>
    </changeSet>

    <changeSet id="20220809024031-11" author="thiru" dbms="postgresql">

        <validCheckSum>ANY</validCheckSum>

        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <tableExists schemaName="timeoff" tableName="company_definition_aud"/>
            </not>
        </preConditions>

        <comment>[company_definition_aud] table creation...</comment>

        <createTable schemaName="timeoff" tableName="company_definition_aud">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_company_definition_aud"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_company_definition_aud"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="type_id" type="BIGINT"/>
            <column name="company_id" type="BIGINT"/>
            <column name="definition_id" type="BIGINT"/>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>


    <!-- 7. [country_definition] table changes, add columns...-->
    <changeSet id="20220809024031-12" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="country_definition" columnName="country_definition"/>
            </not>
        </preConditions>

        <comment>[country_definition] table changes, add columns...</comment>

        <addColumn schemaName="timeoff" tableName="country_definition">
            <column name="definition_id" type="BIGINT"/>
        </addColumn>

        <addUniqueConstraint constraintName="uq_country_definition" schemaName="timeoff" tableName="company_definition" columnNames="type_id, definition_id"/>

        <addForeignKeyConstraint constraintName="fk_type_in_country_definition"
                                 baseTableSchemaName="timeoff" baseTableName="country_definition" baseColumnNames="type_id"
                                 referencedTableSchemaName="timeoff" referencedTableName="timeoff_type" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" validate="true"/>

        <addForeignKeyConstraint constraintName="fk_definition_in_country_definition"
                                 baseTableSchemaName="timeoff" baseTableName="country_definition" baseColumnNames="definition_id"
                                 referencedTableSchemaName="timeoff" referencedTableName="definition" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" validate="true"/>
    </changeSet>


    <!-- 8. [definition] table changes, add missed columns...-->
    <changeSet id="20220809024031-13" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="definition" columnName="clause"/>
            </not>
        </preConditions>

        <comment>Add timeoff.definition.clause column....</comment>

        <addColumn schemaName="timeoff" tableName="definition">
            <column name="clause" type="TEXT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20220809024031-14" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="definition_aud" columnName="clause"/>
            </not>
        </preConditions>

        <comment>Add timeoff.definition_aud.clause column....</comment>

        <addColumn schemaName="timeoff" tableName="definition_aud">
            <column name="clause" type="TEXT">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- 9. [country_definition] table changes, drop columns...-->
    <changeSet id="20220809024031-15" author="thiru" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already dropped...">
            <columnExists schemaName="timeoff" tableName="country_definition" columnName="country_code"/>
        </preConditions>

        <comment>[country_definition] table changes, drop columns...</comment>

        <dropColumn schemaName="timeoff" tableName="country_definition">
            <column name="country_code"/>
            <column name="state_code"/>
            <column name="is_required"/>
            <column name="description"/>
            <column name="basis"/>
            <column name="validations"/>
            <column name="configuration"/>
            <column name="clause"/>
        </dropColumn>

        <sql>
            DROP INDEX IF EXISTS uq_timeoff_definition
        </sql>

        <dropUniqueConstraint constraintName="uq_country_definition" schemaName="timeoff" tableName="company_definition"/>
        <addUniqueConstraint constraintName="uq_country_definition" schemaName="timeoff" tableName="country_definition" columnNames="type_id, definition_id"/>

        <dropUniqueConstraint constraintName="uq_company_definition" schemaName="timeoff" tableName="company_definition"/>
        <addUniqueConstraint constraintName="uq_company_definition" schemaName="timeoff" tableName="company_definition" columnNames="type_id, definition_id, company_id"/>
    </changeSet>

</databaseChangeLog>
