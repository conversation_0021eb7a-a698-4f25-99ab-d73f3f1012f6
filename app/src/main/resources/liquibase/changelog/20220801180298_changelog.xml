<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
                    http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd
                     http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="raviwaarr (generated)" id="20220801180298-1" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <createTable schemaName="timeoff" tableName="shedlock">
            <column name="name" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="lock_until" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="locked_at" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="locked_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!-- TODO : [country_definition_aud] table creation.....  -->
</databaseChangeLog>