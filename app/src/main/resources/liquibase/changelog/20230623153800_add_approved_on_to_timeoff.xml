<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20230623153800-1" author="Saurabh Sikchi">

        <validCheckSum>ANY</validCheckSum>

        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff" columnName="approved_on"/>
            </not>
        </preConditions>

        <comment>add column approved_on to timeoff table</comment>

        <addColumn tableName="timeoff" schemaName="timeoff">
            <column type="timestamptz" name="approved_on" />
        </addColumn>

    </changeSet>

    <changeSet id="20230623153800-2" author="Saurabh Sikchi">

        <validCheckSum>ANY</validCheckSum>

        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_aud" columnName="approved_on"/>
            </not>
        </preConditions>

        <comment>add column approved_on to timeoff table</comment>

        <addColumn tableName="timeoff_aud" schemaName="timeoff">
            <column type="timestamptz" name="approved_on" />
        </addColumn>

    </changeSet>

</databaseChangeLog>
