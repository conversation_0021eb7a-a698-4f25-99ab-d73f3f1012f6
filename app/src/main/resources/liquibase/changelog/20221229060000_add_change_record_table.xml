<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <!-- 1. [entitlement_change_record] table creation...-->
    <changeSet id="20221229060000-1" author="max">

        <validCheckSum>ANY</validCheckSum>

        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Already exists...">
            <not>
                <tableExists schemaName="timeoff" tableName="entitlement_change_record"/>
            </not>
        </preConditions>

        <comment>[entitlement_change_record] table creation...</comment>

        <createTable schemaName="timeoff" tableName="entitlement_change_record">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_entitlement_change_record"/>
            </column>
            <column name="type_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="contract_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="category" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="count" type="FLOAT">
                <constraints nullable="false"/>
            </column>
            <column name="valid_from" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="valid_to_inclusive" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="ref_id" type="BIGINT"/>

            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>

        <addForeignKeyConstraint constraintName="fk_type_in_entitlement_change_record"
                                 baseTableSchemaName="timeoff" baseTableName="entitlement_change_record" baseColumnNames="type_id"
                                 referencedTableSchemaName="timeoff" referencedTableName="timeoff_type" referencedColumnNames="id"
                                 deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" validate="true"/>

    </changeSet>

</databaseChangeLog>
