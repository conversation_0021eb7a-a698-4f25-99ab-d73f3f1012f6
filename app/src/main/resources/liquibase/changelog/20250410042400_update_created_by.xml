<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20250410042400-1" author="Jithendra" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <comment>Add NOT NULL constraint to created_by column</comment>
        <addNotNullConstraint
                tableName="timeoff"
                columnName="created_by"
                schemaName="timeoff"
                columnDataType="bigint"/>
    </changeSet>

</databaseChangeLog>