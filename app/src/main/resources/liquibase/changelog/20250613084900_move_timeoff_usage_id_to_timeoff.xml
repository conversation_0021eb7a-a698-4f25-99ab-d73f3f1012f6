<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet author="Naveen" id="20250613084900-1" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. status already dropped in timeoff_usage">
            <columnExists schemaName="timeoff" tableName="timeoff_usage" columnName="status"/>
        </preConditions>
        <comment>Drop status in timeoff_usage table</comment>

        <dropColumn schemaName="timeoff" tableName="timeoff_usage">
            <column name="status"/>
        </dropColumn>
    </changeSet>

    <changeSet author="Naveen" id="20250613084900-2" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. status already dropped in timeoff_usage_aud">
            <columnExists schemaName="timeoff" tableName="timeoff_usage" columnName="status"/>
        </preConditions>
        <comment>Drop status in timeoff_usage_aud table</comment>

        <dropColumn schemaName="timeoff" tableName="timeoff_usage_aud">
            <column name="status"/>
        </dropColumn>
    </changeSet>

</databaseChangeLog>
