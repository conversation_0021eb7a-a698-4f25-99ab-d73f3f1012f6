<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240429111100-1" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. No null entries ...">
            <not>
                <sqlCheck expectedResult="0"> SELECT COUNT(*) from timeoff.timeoff WHERE created_by IS NULL</sqlCheck>
            </not>
        </preConditions>
        <comment>Set Null Created By to Default value of -1</comment>

        <sql>
            UPDATE timeoff.timeoff
            SET created_by = -1
            WHERE created_by IS NULL;
        </sql>


    </changeSet>

</databaseChangeLog>