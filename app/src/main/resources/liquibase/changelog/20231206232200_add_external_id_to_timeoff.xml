<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231206232200-1" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [external_id] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff" columnName="external_id"/>
            </not>
        </preConditions>
        <comment>add column external_id to timeoff table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff">
            <column type="TEXT" name="external_id"/>
        </addColumn>

        <createIndex schemaName="timeoff" tableName="timeoff" indexName="timeoff_external_id_idx">
            <column name="external_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="20231206232200-2" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [external_id]. Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="timeoff_aud" columnName="external_id"/>
            </not>
        </preConditions>
        <comment>add column external_id to timeoff_aud table</comment>

        <addColumn schemaName="timeoff" tableName="timeoff_aud">
            <column type="TEXT" name="external_id"/>
        </addColumn>
    </changeSet>

    <changeSet id="20231206232200-3" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <comment>add unique constraint to external_id</comment>
        <addUniqueConstraint constraintName="uq_external_id_in_timeoff" schemaName="timeoff" tableName="timeoff" columnNames="external_id"/>
    </changeSet>

</databaseChangeLog>
