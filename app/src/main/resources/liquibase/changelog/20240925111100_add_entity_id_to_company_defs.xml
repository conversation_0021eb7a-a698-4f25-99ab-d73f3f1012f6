<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240925111100-1" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [entity_type] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="company_definition" columnName="entity_type"/>
            </not>
        </preConditions>
        <comment>add entity_type to company definition table</comment>

        <addColumn schemaName="timeoff" tableName="company_definition">
            <column type="TEXT" name="entity_type"/>
        </addColumn>
    </changeSet>

    <changeSet id="20240925111100-2" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [entity_id] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="company_definition" columnName="entity_id"/>
            </not>
        </preConditions>
        <comment>add entity_id to company definition table</comment>

        <addColumn schemaName="timeoff" tableName="company_definition">
            <column type="BIGINT" name="entity_id"/>
        </addColumn>

        <createIndex schemaName="timeoff" tableName="company_definition" indexName="company_definition_entity_type_entity_id_idx">
            <column name="entity_type"/>
            <column name="entity_id"/>
        </createIndex>
    </changeSet>

    <changeSet id="20240925111100-3" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [entity_type] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="company_definition_aud" columnName="entity_type"/>
            </not>
        </preConditions>
        <comment>add entity_type to company definition audit table</comment>

        <addColumn schemaName="timeoff" tableName="company_definition_aud">
            <column type="TEXT" name="entity_type"/>
        </addColumn>
    </changeSet>

    <changeSet id="20240925111100-4" author="Naveen">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped. column [entity_id] Already exists...">
            <not>
                <columnExists schemaName="timeoff" tableName="company_definition_aud" columnName="entity_id"/>
            </not>
        </preConditions>
        <comment>add entity_id to company definition audit table</comment>

        <addColumn schemaName="timeoff" tableName="company_definition_aud">
            <column type="BIGINT" name="entity_id"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>