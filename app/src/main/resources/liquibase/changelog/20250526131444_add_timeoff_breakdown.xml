<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

  <changeSet author="Nirmal" id="20250526131444-1" dbms="postgresql">
    <validCheckSum>ANY</validCheckSum>
    <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Table [timeoff_entry] already exists...">
      <not>
        <tableExists schemaName="timeoff" tableName="timeoff_entry"/>
      </not>
    </preConditions>
    <comment>Create table to store timeoff entry details</comment>

    <createTable schemaName="timeoff" tableName="timeoff_entry"
                 remarks="Store all timeoff entry details">
      <column autoIncrement="true" name="id" type="BIGINT">
        <constraints nullable="false" primaryKey="true" primaryKeyName="timeoff_entry_pkey"/>
      </column>
      <column name="timeoff_id" type="BIGINT"/>
      <column name="timeoff_status" type="TEXT"/>
      <column name="date" type="DATE"/>
      <column name="value" type="DOUBLE"/>
      <column name="session" type="TEXT"/>
      <column name="type" type="TEXT"/>
      <column name="created_by" type="BIGINT"/>
      <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
      <column name="updated_by" type="BIGINT"/>
      <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
    </createTable>

    <addForeignKeyConstraint constraintName="fk_timeoff_id_in_timeoff_entry"
                             baseTableSchemaName="timeoff" baseTableName="timeoff_entry" baseColumnNames="timeoff_id"
                             referencedTableSchemaName="timeoff" referencedTableName="timeoff"
                             referencedColumnNames="id"
                             deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                             validate="true"/>

    <createIndex schemaName="timeoff" tableName="timeoff_entry" indexName="timeoff_entry_timeoff_id_idx">
      <column name="timeoff_id"/>
    </createIndex>

    <createIndex schemaName="timeoff" tableName="timeoff_entry" indexName="timeoff_entry_date_idx">
      <column name="date"/>
    </createIndex>
  </changeSet>

  <changeSet author="Nirmal" id="20250526131444-2" dbms="postgresql">
    <validCheckSum>ANY</validCheckSum>
    <preConditions onFail="MARK_RAN" onFailMessage="Skipped. Table [timeoff_entry_aud] already exists...">
      <not>
        <tableExists schemaName="timeoff" tableName="timeoff_entry_aud"/>
      </not>
    </preConditions>

    <createTable schemaName="timeoff" tableName="timeoff_entry_aud">
      <column autoIncrement="true" name="id" type="BIGINT">
        <constraints nullable="false" primaryKey="true" primaryKeyName="pk_timeoff_entry"/>
      </column>
      <column name="rev" type="INTEGER">
        <constraints nullable="false" primaryKey="true" primaryKeyName="pk_timeoff_entry"/>
      </column>
      <column name="revtype" type="SMALLINT"/>
      <column name="timeoff_id" type="BIGINT"/>
      <column name="timeoff_status" type="TEXT"/>
      <column name="date" type="DATE"/>
      <column name="value" type="DOUBLE"/>
      <column name="session" type="TEXT"/>
      <column name="type" type="TEXT"/>
      <column name="created_by" type="BIGINT"/>
      <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
      <column name="updated_by" type="BIGINT"/>
      <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
    </createTable>
  </changeSet>
  <changeSet id="20250526131444-3" author="nirmal" dbms="postgresql">
    <validCheckSum>ANY</validCheckSum>
    <preConditions onFail="MARK_RAN" onFailMessage="Skipped. timeoff_status already dropped...">
      <columnExists schemaName="timeoff" tableName="timeoff_entry" columnName="timeoff_status"/>
    </preConditions>

    <comment>drop timeoff_status column</comment>

    <dropColumn schemaName="timeoff" tableName="timeoff_entry">
      <column name="timeoff_status"/>
    </dropColumn>
  </changeSet>
  <changeSet id="20250526131444-4" author="nirmal" dbms="postgresql">
    <validCheckSum>ANY</validCheckSum>
    <preConditions onFail="MARK_RAN" onFailMessage="Skipped. timeoff_status already dropped in aud...">
      <columnExists schemaName="timeoff" tableName="timeoff_entry_aud" columnName="timeoff_status"/>
    </preConditions>

    <comment>drop timeoff_status column</comment>

    <dropColumn schemaName="timeoff" tableName="timeoff_entry_aud">
      <column name="timeoff_status"/>
    </dropColumn>
  </changeSet>
</databaseChangeLog>
