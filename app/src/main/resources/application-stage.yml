spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    hikari:
      poolName: Hikari
      auto-commit: false

platform:
  frontend:
    baseurl: https://stage-app.usemultiplier.com

grpc:
  client:
    core-service:
      address: dns:///core-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    company-service:
      address: ${GRPC_CLIENT_COMPANY-SERVICE_ADDRESS} # from stg-ecs-task-definition-template.json
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    member-service:
      address: dns:///member-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    country-service:
      address: dns:///country-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    contract-service:
      address: dns:///contract-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    org-management-service:
      address: dns:///org-management-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    pigeon-service:
      address: dns:///pigeon-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS

pigeon:
  client:
    kafka:
      bootstrap-servers: b-2.stgapptechappkafk.r4k9xq.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.stgapptechappkafk.r4k9xq.c5.kafka.ap-southeast-1.amazonaws.com:9092

