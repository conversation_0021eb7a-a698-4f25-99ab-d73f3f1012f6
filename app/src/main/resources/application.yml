spring:
  application:
    name: timeoff-service
  main:
    banner-mode: "off"
  liquibase:
    changeLog: classpath:liquibase/master.xml
    enabled: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ***************************************
    username: coredb
    password : password
    hikari:
      poolName: Hikari
      auto-commit: false
      max-lifetime: 1800000
      idle-timeout: 300000
      connection-timeout: 30000
      minimum-idle: 1
      maximum-pool-size: 5
      keepalive-time: 120000
  jpa:
    show-sql: false
    open-in-view: false
    properties:
      org.hibernate.envers.store_data_at_delete: true
      hibernate.format_sql=true:
      hibernate.jdbc.time_zone: UTC
      hibernate.id.new_generator_mappings: true
      hibernate.connection.provider_disables_autocommit: true
      hibernate.id.db_structure_naming_strategy: legacy
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: false
      hibernate.jdbc.batch_size: 25
      hibernate.order_inserts: true
      hibernate.order_updates: true
      hibernate.query.fail_on_pagination_over_collection_fetch: true
      hibernate.query.in_clause_parameter_padding: true
    hibernate:
      ddl-auto: none
      dialect: org.hibernate.dialect.PostgreSQLDialect
      naming:
        physical-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
        implicit-strategy: org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy

platform:
  frontend:
    baseurl: https://stage-app.usemultiplier.com
  userservice:
    system.notification.support.email: <EMAIL>
  timeoff:
    future-summary-generation:
      batch-size: 1000
    next-summary-activation:
      batch-size: 1500

    kafka:
      bootstrap-servers: localhost:9092
      topic: topic.internal.v1.timeoff
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufSerializer
      max-block-ms: 12000
      retry-count: 3
      retry-backoff-ms: 1000
      buffer-memory: 268435456 # 256MB
      batch-size: 131072 # 128kB
      linger-ms: 5

ops:
  recipient-email: <EMAIL>
  test-recipient-email: <EMAIL>
  sales-email: <EMAIL>
  msa-signer-email: <EMAIL>
  msa-sales-email: <EMAIL>
  customer-success-email: <EMAIL>
  payroll-updates-email: <EMAIL>
  member-onboarding-email: <EMAIL>
  finance-email: <EMAIL>
  eor-contract-signer-email: <EMAIL>

mpl:
  graphql:
    scalar:
      enabled: false
    error-resolver:
      enabled: false
  grpc:
    coroutine:
      enabled: false
  encryption:
    enabled: false
  classification:
    enabled: false
grpc:
  server:
    max-inbound-metadata-size: 2097152
    security:
      enabled: true
      certificate-chain: classpath:certificates/server.crt
      private-key: classpath:certificates/server.key
  client:
    leave-compliance-service:
      address: ${GRPC_CLIENT_LEAVE-COMPLIANCE-SERVICE_ADDRESS}
      negotiation-type: TLS
    authority-service:
      address: localhost:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    ai-service:
      address: ${GRPC_CLIENT_AI-SERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    access-control-service:
      address: ${GRPC_CLIENT_ACCESS-CONTROL-SERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS

pigeon:
  client:
    kafka:
      bootstrap-servers: b-2.kafka-cluster-sta.1unmw8.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-1.kafka-cluster-sta.1unmw8.c2.kafka.ap-southeast-1.amazonaws.com:9092


growthbook:
  base-url: ${GROWTHBOOK_BASEURL}
  env-key: ${GROWTHBOOK_ENVKEY}
  refresh-frequency-ms: 15000

server:
  max-http-request-header-size: 1MB
