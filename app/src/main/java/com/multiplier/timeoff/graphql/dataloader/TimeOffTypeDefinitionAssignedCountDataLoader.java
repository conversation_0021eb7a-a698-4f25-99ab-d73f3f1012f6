package com.multiplier.timeoff.graphql.dataloader;

import com.multiplier.timeoff.core.common.util.ExecutorUtil;
import com.multiplier.timeoff.service.DefinitionService;
import com.netflix.graphql.dgs.DgsDataLoader;
import lombok.RequiredArgsConstructor;
import org.dataloader.MappedBatchLoader;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;

@RequiredArgsConstructor
@DgsDataLoader(name = "timeOffPolicyAssignedEmployeeCount", maxBatchSize = 500)
public class TimeOffTypeDefinitionAssignedCountDataLoader implements MappedBatchLoader<Long, Integer> {

    private final DefinitionService definitionService;

    @Override
    public CompletionStage<Map<Long, Integer>> load(Set<Long> definitionIds) {
        Executor executor = ExecutorUtil.getNewSecurityContextExecutor();
        return CompletableFuture.supplyAsync(() -> definitionService.getDefinitionIdToAssignedEmployeeCountMap(definitionIds), executor);
    }
}
