package com.multiplier.timeoff.graphql.dataloader;

import com.multiplier.timeoff.core.common.util.ExecutorUtil;
import com.multiplier.timeoff.service.TimeoffBreakdownService;
import com.multiplier.timeoff.types.TimeOffEntry;
import com.netflix.graphql.dgs.DgsDataLoader;
import lombok.RequiredArgsConstructor;
import org.dataloader.MappedBatchLoader;

import java.util.Map;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;

@RequiredArgsConstructor
@DgsDataLoader(name = "timeOffEntries", maxBatchSize = 500)
public class TimeOffEntriesDataLoader implements MappedBatchLoader<Long, List<TimeOffEntry>> {

    private final TimeoffBreakdownService timeoffBreakdownService;

    @Override
    public CompletionStage<Map<Long, List<TimeOffEntry>>> load(Set<Long> timeOffIds) {
        Executor executor = ExecutorUtil.getNewSecurityContextExecutor();
        return CompletableFuture.supplyAsync(() -> timeoffBreakdownService.getTimeOffEntriesGroupedByTimeOffId(timeOffIds) , executor);
    }
}
