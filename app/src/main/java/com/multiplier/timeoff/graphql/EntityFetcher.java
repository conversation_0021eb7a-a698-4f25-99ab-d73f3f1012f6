package com.multiplier.timeoff.graphql;

import com.multiplier.timeoff.DgsConstants;
import com.multiplier.timeoff.service.TimeoffQuery;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsComponent;
import com.netflix.graphql.dgs.DgsEntityFetcher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
@DgsComponent
@RequiredArgsConstructor
public class EntityFetcher {

    private final TimeoffQuery timeoffQuery;

    @DgsEntityFetcher(name = DgsConstants.TIMEOFF.TYPE_NAME)
    public TimeOff fetchTimeOff(Map<String, Object> values) {
        return timeoffQuery.getTimeoffById((Long) values.get("id"));
    }

    @DgsEntityFetcher(name = DgsConstants.CONTRACT.TYPE_NAME)
    public Contract fetchContract(Map<String, Object> values) {
        Contract contract = new Contract();
        contract.setId(Long.valueOf((String) values.get("id")));
        return contract;
    }

    @DgsEntityFetcher(name = DgsConstants.COMPANY.TYPE_NAME)
    public Company fetchCompany(Map<String, Object> values) {
        Company company = new Company();
        company.setId(Long.valueOf((String) values.get("id")));
        return company;
    }

    @DgsEntityFetcher(name = DgsConstants.COMPLIANCEMULTIPLIEREOR.TYPE_NAME)
    public ComplianceMultiplierEOR fetchComplianceMultiplierEOR(Map<String, Object> values) {
        var contractId = getContractId(values);
        var contract = Contract.newBuilder().id(contractId).build();

        return ComplianceMultiplierEOR.newBuilder().contract(contract).build();
    }

    @DgsEntityFetcher(name = DgsConstants.COMPLIANCEPARTNEREOR.TYPE_NAME)
    public CompliancePartnerEOR fetchCompliance(Map<String, Object> values) {
        var contractId = getContractId(values);
        var contract = Contract.newBuilder().id(contractId).build();

        return CompliancePartnerEOR.newBuilder().contract(contract).build();
    }

    @DgsEntityFetcher(name = DgsConstants.COMPLIANCECONTRACTOR.TYPE_NAME)
    public ComplianceContractor fetchComplianceContractor(Map<String, Object> values) {
        return ComplianceContractor.newBuilder()
                .contract(Contract.newBuilder().id(getContractId(values)).build())
                .build();
    }

    @DgsEntityFetcher(name = DgsConstants.COMPLIANCEPEO.TYPE_NAME)
    public CompliancePEO fetchCompliancePEO(Map<String, Object> values) {
        var contractId = getContractId(values);
        var contract = Contract.newBuilder().id(contractId).build();

        return CompliancePEO.newBuilder().contract(contract).build();
    }

    @DgsEntityFetcher(name = DgsConstants.COMPLIANCEFREELANCE.TYPE_NAME)
    public ComplianceFreelance fetchComplianceFreelance(Map<String, Object> values) {
        var contractId = getContractId(values);
        var contract = Contract.newBuilder().id(contractId).build();

        return ComplianceFreelance.newBuilder().contract(contract).build();
    }

    @SuppressWarnings("unchecked")
    private Long getContractId(final Map<String, Object> values) {
        return Long.valueOf(((Map<String, String>) values.get("contract")).get("id"));
    }
}
