package com.multiplier.timeoff.graphql.dataloader;

import com.multiplier.timeoff.core.common.util.ExecutorUtil;
import com.multiplier.timeoff.service.TimeoffTypeService;
import com.netflix.graphql.dgs.DgsDataLoader;
import lombok.RequiredArgsConstructor;
import org.dataloader.MappedBatchLoader;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;

@RequiredArgsConstructor
@DgsDataLoader(name = "timeOffTypeUsages", maxBatchSize = 500)
public class TimeOffTypeUsageDataLoader implements MappedBatchLoader<Long, Long> {

    private final TimeoffTypeService timeoffTypeService;

    @Override
    public CompletionStage<Map<Long, Long>> load(Set<Long> keys) {
        Executor executor = ExecutorUtil.getNewSecurityContextExecutor();
        return CompletableFuture.supplyAsync(() -> timeoffTypeService.loadTimeOffTypesUsageMap(keys), executor);
    }
}
