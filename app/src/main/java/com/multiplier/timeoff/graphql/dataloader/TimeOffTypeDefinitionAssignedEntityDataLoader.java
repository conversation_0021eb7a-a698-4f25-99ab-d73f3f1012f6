package com.multiplier.timeoff.graphql.dataloader;

import com.multiplier.timeoff.core.common.util.ExecutorUtil;
import com.multiplier.timeoff.service.DefinitionService;
import com.multiplier.timeoff.types.TimeOffPolicyEntityInfo;
import com.netflix.graphql.dgs.DgsDataLoader;
import lombok.RequiredArgsConstructor;
import org.dataloader.MappedBatchLoader;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.Executor;

@RequiredArgsConstructor
@DgsDataLoader(name = "TimeOffTypeDefinitionAssignedEntity", maxBatchSize = 500)
public class TimeOffTypeDefinitionAssignedEntityDataLoader implements MappedBatchLoader<Long, TimeOffPolicyEntityInfo>{

    private final DefinitionService definitionService;

    @Override
    public CompletionStage<Map<Long, TimeOffPolicyEntityInfo>> load(Set<Long> definitionIds) {
        Executor executor = ExecutorUtil.getNewSecurityContextExecutor();
        return CompletableFuture.supplyAsync(() -> definitionService.getDefinitionIdToAssignedEntityMap(definitionIds), executor);
    }
}
