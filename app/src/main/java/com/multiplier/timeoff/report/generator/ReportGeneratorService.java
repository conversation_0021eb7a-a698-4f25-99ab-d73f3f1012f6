package com.multiplier.timeoff.report.generator;

import com.multiplier.timeoff.report.domain.ReportCategory;
import com.multiplier.timeoff.report.domain.ReportGenerationType;
import com.multiplier.timeoff.report.domain.ReportRow;
import com.multiplier.timeoff.report.domain.ReportRowCellBorderType;
import com.multiplier.timeoff.report.domain.ReportRowCellType;
import com.multiplier.timeoff.report.domain.ReportRowCellValue;
import com.multiplier.timeoff.types.DocumentReadable;
import com.opencsv.CSVWriter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReportGeneratorService {

    private final GeneratorConfigStore generatorConfigStore;

    private final Map<ReportGenerationType, BiFunction<ReportCategory, List<ReportRow>, DocumentReadable>> reportGenerators = Map.of(
            ReportGenerationType.CSV, this::generateCSVReport,
            ReportGenerationType.XLSX, this::generateXLSXReport
    );

    public DocumentReadable generateReport(ReportCategory reportCategory, List<ReportRow> fieldData) {
        ReportGenerationType reportGenerationType = generatorConfigStore.getReportGenerationType(reportCategory.toString());
        return reportGenerators.get(reportGenerationType).apply(reportCategory, fieldData);
    }

    /**
     * Generate excel report with multiple sheets.
     *
     * @param reportCategory report category. Eg: EXPENSE_REPORT, TIMEOFF_REPORT, COMPANY_PAYROLL_SUMMARY_REPORT, etc.
     * @param sheetData      map containing the sheet data.
     *                       Map key => sheet name
     *                       Map value => sheet's row data.
     * @return generated excel document.
     */
    public DocumentReadable generateMultiSheetReport(ReportCategory reportCategory, Map<String, List<ReportRow>> sheetData) {
        return generateXLSXMultiSheetReport(reportCategory, sheetData);
    }

    @SneakyThrows
    private DocumentReadable generateCSVReport(ReportCategory reportCategory, List<ReportRow> fieldData) {
        Path tempCSV = Files.createTempFile(LocalDateTime.now().toString().replace(":", "-"), ".csv");
        try(CSVWriter writer = new CSVWriter(new FileWriter(tempCSV.toString()))) {

            // Setting the headers
            List<String> reportColumns = generatorConfigStore.getColumnNames(reportCategory.toString());
            writer.writeNext(reportColumns.toArray(new String[0]));

            // Writing data for all rows to csv
            fieldData.forEach(
                    fieldDataRow -> {
                        String[] colData = new String[reportColumns.size()];
                        for (int i = 0; i < reportColumns.size(); i++) {
                            String reportColumn = reportColumns.get(i); // Getting col name
                            var rowDataMap = getRowDataMap(fieldDataRow); // Getting row data
                            ReportRowCellValue reportRowCellValue = rowDataMap.getOrDefault(reportColumn, ReportRowCellValue.builder().build()); // Getting value for col name
                            colData[i] = reportRowCellValue.getColumnValue() != null ? reportRowCellValue.getColumnValue().toString() : ""; // Setting value to csv row
                        }
                        writer.writeNext(colData); // Saving the data to csv
                    }
            );
        }

        // Getting the file data in blob
        String fileData;
        try(FileInputStream fileInputStream = new FileInputStream(tempCSV.toFile())) {
            fileData = Base64.getEncoder().encodeToString(fileInputStream.readAllBytes());
        }

        // Deleting the temp file if exists
        Files.deleteIfExists(tempCSV);

        return DocumentReadable.newBuilder()
                .blob(fileData)
                .contentType("text/csv")
                .id(System.currentTimeMillis())
                .name(generatorConfigStore.getReportName(reportCategory.toString()) + "_" + LocalDateTime.now())
                .extension("csv")
                .build();
    }

    @SneakyThrows
    private DocumentReadable generateXLSXReport(ReportCategory reportCategory, List<ReportRow> reportRows) {
        String reportName = generatorConfigStore.getReportName(reportCategory.toString());

        Path tempFile = Files.createTempFile(LocalDateTime.now().toString().replace(":", "-"), ".xlsx");

        List<String> reportColumns = generatorConfigStore.getColumnNames(reportCategory.toString());

        try(Workbook wb = new XSSFWorkbook()) {
            CreationHelper helper = wb.getCreationHelper();
            Sheet sheet = wb.createSheet(reportName);

            Row header = sheet.createRow(0);
            for (int i = 0; i < reportColumns.size(); i++) {
                String colName = reportColumns.get(i);
                header.createCell(i)
                        .setCellValue(helper.createRichTextString(colName));
            }

            for (int i = 0; i < reportRows.size(); i++) {
                Row row = sheet.createRow(i + 1); // creating a value row from 1
                var reportRow = reportRows.get(i);
                Map<String, ReportRowCellValue> cellValues = getRowDataMap(reportRow);

                for (int j = 0; j < reportColumns.size(); j++) {
                    String reportColumn = reportColumns.get(j); // Getting col name
                    ReportRowCellValue reportRowCellValue = cellValues.getOrDefault(reportColumn, ReportRowCellValue.builder().build()); // Getting value for col name
                    String cellValue = reportRowCellValue.getColumnValue() != null ? reportRowCellValue.getColumnValue().toString() : ""; // Getting value to set to row
                    var cell = row.createCell(j);
                    cell.setCellValue(helper.createRichTextString(cellValue)); // Setting cell value

                    if (reportRowCellValue.getXlBackgroundColor() != null) {
                        CellStyle style = wb.createCellStyle();
                        style.setFillForegroundColor(reportRowCellValue.getXlBackgroundColor().getIndex());
                        cell.setCellStyle(style);
                    }
                }

                if (reportRow.getXlBackgroundColor() != null) {
                    CellStyle style = wb.createCellStyle();
                    style.setFillForegroundColor(reportRow.getXlBackgroundColor().getIndex());
                    row.setRowStyle(style);
                }

            }


            // Write the output to a file
            try(FileOutputStream fileOut = new FileOutputStream(tempFile.toFile())) {
                wb.write(fileOut);
            }
        }


        // Getting the file data in blob
        String fileData;
        try(FileInputStream fileInputStream = new FileInputStream(tempFile.toFile())) {
            fileData = Base64.getEncoder().encodeToString(fileInputStream.readAllBytes());
        }

        // Deleting the temp file if exists
        Files.deleteIfExists(tempFile);


        return DocumentReadable.newBuilder()
                .blob(fileData)
                .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .id(System.currentTimeMillis())
                .name(reportName + "_" + LocalDateTime.now())
                .extension("xlsx")
                .build();
    }

    @SneakyThrows
    private DocumentReadable generateXLSXMultiSheetReport(ReportCategory reportCategory, Map<String, List<ReportRow>> sheetData) {

        try (Workbook wb = new XSSFWorkbook()) {
            CreationHelper helper = wb.getCreationHelper();

            for (Map.Entry<String, List<ReportRow>> entry : sheetData.entrySet()) {

                String sheetName = entry.getKey();
                List<ReportRow> reportRows = entry.getValue();

                Sheet sheet = wb.createSheet(sheetName);

                List<String> reportColumns = generatorConfigStore.getColumnNames(reportCategory.toString());
                reportColumns = reportColumns.isEmpty() ? extractReportColumnsFromReportRows(reportRows) : reportColumns;
                ReportRowCellBorderType defaultReportRowCellBorderType = extractDefaultBorderStyleFromReportRows(reportRows);

                Row header = sheet.createRow(0);
                for (int i = 0; i < reportColumns.size(); i++) {
                    String colName = reportColumns.get(i);
                    header.createCell(i)
                            .setCellValue(helper.createRichTextString(colName));
                    //set header styling
                    header.getCell(i).setCellStyle(getHeaderStyle(wb, generatorConfigStore.getHeaderStyle(reportCategory)));
                }

                List<Map<String, ReportRowCellValue>> allRows = getFileDataMap(reportRows);
                for (int i = 0; i < allRows.size(); i++) {
                    Row row = sheet.createRow(i + 1); // creating a value row from 1
                    var reportRow = reportRows.get(i);
                    Map<String, ReportRowCellValue> cellValues = allRows.get(i); // get all cell values for a row
                    for (int j = 0; j < reportColumns.size(); j++) { // for each column in xlsx
                        String reportColumn = reportColumns.get(j); // Getting col name
                        ReportRowCellValue reportRowCellValue = cellValues.getOrDefault(reportColumn, ReportRowCellValue.builder().reportRowCellBorderType(defaultReportRowCellBorderType).build()); // Getting value for col name // Getting value for col name
                        var cell = row.createCell(j);

                        String colFirstCellCode = CellReference.convertNumToColString(j) + "2"; //skip header
                        String colLastCellCode = CellReference.convertNumToColString(j) + sheet.getLastRowNum();

                        //Format & style each cell according to configuration
                        setFormattedCellValue(wb, cell, reportRowCellValue, reportRow.getXlBackgroundColor(), colFirstCellCode, colLastCellCode);

                        //Auto resize columns
                        sheet.autoSizeColumn(j);
                    }
                }
            }

            //Replace tag in report name
            String reportName = generatorConfigStore.getReportName(reportCategory.toString());
            String sheetName = sheetData.keySet().stream().findFirst().orElse("");
            reportName = reportName.replace("<FIRST_SHEET_NAME>", sheetName);

            // Write the output to a file
            Path tempFile = Files.createTempFile(LocalDateTime.now().toString().replace(":", "-"), ".xlsx");
            try (FileOutputStream fileOut = new FileOutputStream(tempFile.toFile())) {
                wb.write(fileOut);
            }

            // Getting the file data in blob
            String fileData;
            try (FileInputStream fileInputStream = new FileInputStream(tempFile.toFile())) {
                fileData = Base64.getEncoder().encodeToString(fileInputStream.readAllBytes());
            }

            // Deleting the temp file if exists
            Files.deleteIfExists(tempFile);

            return DocumentReadable.newBuilder()
                    .blob(fileData)
                    .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .id(System.currentTimeMillis())
                    .name(reportName + "_" + LocalDateTime.now())
                    .extension("xlsx")
                    .build();
        }
    }

    private List<String> extractReportColumnsFromReportRows(List<ReportRow> reportRows) {
        ReportRow reportRow = reportRows.stream().max(Comparator.comparingInt(a -> a.getReportRowCellValues().size())).orElse(null);
        if (reportRow == null) {
            return List.of();
        }
        return reportRow.getReportRowCellValues().stream()
                .map(ReportRowCellValue::getColumnName)
                .toList();
    }

    /**
     * Build a accessible map to get all the
     * col values for each row
     *
     * @param fieldRows all rows
     * @return all colValues for each row
     */
    private List<Map<String, ReportRowCellValue>> getFileDataMap(List<ReportRow> fieldRows) {
        return fieldRows
                .stream()
                .map(row -> row.getReportRowCellValues()
                        .stream()
                        .collect(Collectors.toMap(ReportRowCellValue::getColumnName, Function.identity(), (existing, replacement) -> replacement)))
                .toList();
    }

    private Map<String, ReportRowCellValue> getRowDataMap(ReportRow reportRow) {
        return reportRow.getReportRowCellValues()
                .stream()
                .collect(Collectors.toMap(ReportRowCellValue::getColumnName, Function.identity()));
    }

    /**
     * Method to format & style report cell
     *
     * @param wb                 Apache poi workbook
     * @param cell               Cell to format
     * @param reportRowCellValue ReportRowCellValue mapped with the cell
     * @param rowBackgroundColor Background Indexed color for the cell
     * @param colFirstCellCode   First cell position as number to get range for fomula column (Eg: A1)
     */

    protected Cell setFormattedCellValue(Workbook wb, Cell cell, ReportRowCellValue reportRowCellValue, IndexedColors rowBackgroundColor, String colFirstCellCode, String colLastCellCode) {

        CellStyle cellStyle = wb.createCellStyle();
        DataFormat format = wb.createDataFormat();
        CreationHelper helper = wb.getCreationHelper();

        //set priority for row colour if it is already set
        IndexedColors cellColor = rowBackgroundColor != null ? rowBackgroundColor : reportRowCellValue.getXlBackgroundColor();
        cellStyle = setCellBackgroundColor(cellStyle, cellColor);

        ReportRowCellType reportRowCellType = reportRowCellValue.getReportRowCellType() == null ? ReportRowCellType.RICHTEXT : reportRowCellValue.getReportRowCellType();

        switch (reportRowCellType) {
            case DOUBLE:
                setDoubleValue(cell, reportRowCellValue);
                break;
            case INTEGER:
                setIntegerValue(cell, reportRowCellValue);
                break;
            case BOOLEAN:
                setBooleanValue(cell, reportRowCellValue);
                break;
            case DATETIME:
                setDateTimeValue(cell, reportRowCellValue);
                break;
            case FORMULA:
                setFormulaValue(cell, reportRowCellValue, colFirstCellCode, colLastCellCode);
                break;
            case BLANK:
                cell.setCellValue("");
                break;
            case RICHTEXT:
                setRichTextValue(cell, reportRowCellValue, helper);
                break;
            default:
                setDefaultValue(cell, reportRowCellValue);
                break;
        }

        if (reportRowCellValue.getColumnDataFormat() != null) {
            cellStyle.setDataFormat(format.getFormat(reportRowCellValue.getColumnDataFormat()));
        }

        cellStyle.setQuotePrefixed(false);
        cellStyle = setCellBorder(cellStyle, reportRowCellValue.getReportRowCellBorderType() == null ? ReportRowCellBorderType.NONE : reportRowCellValue.getReportRowCellBorderType());
        cell.setCellStyle(cellStyle);

        return cell;
    }

    /**
     * Method to set double values for workbook cell
     *
     * @param cell               Current cell object
     * @param reportRowCellValue Report row cell value
     */

    private Cell setDoubleValue(Cell cell, ReportRowCellValue reportRowCellValue) {
        Object cellValue = reportRowCellValue.getColumnValue() != null ? reportRowCellValue.getColumnValue() : 0.D;
        cell.setCellValue(cellValue.toString().matches("\\d+\\.\\d+([eE]\\d+)?") ? Double.parseDouble(cellValue.toString()) : 0.D);
        return cell;
    }

    /**
     * Method to set Integer values for workbook cell
     *
     * @param cell               Current cell object
     * @param reportRowCellValue Report row cell value
     */

    private Cell setIntegerValue(Cell cell, ReportRowCellValue reportRowCellValue) {
        cell.setCellType(CellType.NUMERIC);
        Object cellValue = reportRowCellValue.getColumnValue() != null ? reportRowCellValue.getColumnValue() : 0;
        cell.setCellValue(cellValue.toString().matches("\\d*") ? Long.parseLong(cellValue.toString()) : 0);
        return cell;
    }

    /**
     * Method to set boolean values for workbook cell
     *
     * @param cell               Current cell object
     * @param reportRowCellValue Report row cell value
     */

    private Cell setBooleanValue(Cell cell, ReportRowCellValue reportRowCellValue) {
        Object cellValue = reportRowCellValue.getColumnValue() != null ? reportRowCellValue.getColumnValue() : 0;
        cell.setCellValue((boolean) cellValue);
        return cell;
    }

    /**
     * Method to set datetime values for workbook cell
     *
     * @param cell               Current cell object
     * @param reportRowCellValue Report row cell value
     */

    private Cell setDateTimeValue(Cell cell, ReportRowCellValue reportRowCellValue) {
        cell.setCellValue(getFormattedDate(reportRowCellValue.getColumnValue(), reportRowCellValue.getColumnDataFormat()));
        return cell;
    }

    /**
     * Method to set formular values for workbook cell
     *
     * @param cell               Current cell object
     * @param colFirstCellCode   First cell position as number to get range for fomula column (Eg: A1)
     * @param reportRowCellValue Last cell position as number to get range for fomula column (Eg: A10)
     */

    private Cell setFormulaValue(Cell cell, ReportRowCellValue reportRowCellValue, String colFirstCellCode, String colLastCellCode) {
        cell.setCellValue("");
        cell.setCellFormula(String.format(reportRowCellValue.getColumnValue().toString(), colFirstCellCode, colLastCellCode));
        return cell;
    }

    /**
     * Method to set double values for workbook cell
     *
     * @param cell               Current cell object
     * @param reportRowCellValue Report row cell value
     * @param helper             Apache poi helper instance to generate richtext values.
     */

    private Cell setRichTextValue(Cell cell, ReportRowCellValue reportRowCellValue, CreationHelper helper) {
        Object cellValue = reportRowCellValue.getColumnValue() != null ? reportRowCellValue.getColumnValue().toString() : "";
        cell.setCellValue(helper.createRichTextString(cellValue.toString()));
        return cell;
    }

    /**
     * Method to set double values for workbook cell
     *
     * @param cell               Current cell object
     * @param reportRowCellValue Report row cell value
     */

    private Cell setDefaultValue(Cell cell, ReportRowCellValue reportRowCellValue) {
        Object cellValue = reportRowCellValue.getColumnValue() != null ? reportRowCellValue.getColumnValue().toString() : "";
        cell.setCellValue(cellValue.toString());
        return cell;
    }

    /**
     * Method to create cell style for Headder row
     *
     * @param workbook Report Workbook
     * @param headerStyle Style to apply for column header
     */

    protected CellStyle getHeaderStyle(Workbook workbook, String headerStyle) {

        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        switch(headerStyle){
            case "BOLD_CENTER_BORDER_ALL":
                style = setCellBorder(style, ReportRowCellBorderType.ALL);
                font.setBold(true);
                style.setFont(font);
                style.setAlignment(HorizontalAlignment.CENTER);
                break;
            case "BOLD_CENTER_BORDER_NONE":
                style = setCellBorder(style, ReportRowCellBorderType.NONE);
                font.setBold(true);
                style.setFont(font);
                style.setAlignment(HorizontalAlignment.CENTER);
                break;
            default:
                break;
        }
        return style;
    }

    /**
     * Method to create cell style for Headder row
     *
     * @param cellStyle CellStyle to modify & apply into cell
     * @param cellColor Indexed color for the cell to apply as background
     */

    protected CellStyle setCellBackgroundColor(CellStyle cellStyle, IndexedColors cellColor) {

        if (cellColor != null) {
            cellStyle.setFillForegroundColor(cellColor.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }
        return cellStyle;
    }

    /**
     * Method to set cell borders
     *
     * @param cellStyle         CellStyle to modify & apply into cell
     * @param rowCellBorderType Cell border type
     */

    protected CellStyle setCellBorder(CellStyle cellStyle, ReportRowCellBorderType rowCellBorderType) {

        Boolean borderTop = false;
        Boolean borderRight = false;
        Boolean borderBottom = false;
        Boolean borderLeft = false;

        switch (rowCellBorderType) {
            case ALL:
                borderTop = borderRight = borderBottom = borderLeft = true;
                break;
            case LEFT_RIGHT:
                borderRight = borderLeft = true;
                break;
            case TOP_BOTTOM:
                borderTop = borderBottom = true;
                break;
            default:
                break;
        }
        return setCellBorder(cellStyle, borderTop, borderRight, borderBottom, borderLeft);
    }

    private ReportRowCellBorderType extractDefaultBorderStyleFromReportRows(List<ReportRow> reportRows) {

        ReportRowCellBorderType defaultReportRowCellBorderType =  ReportRowCellBorderType.NONE;
        ReportRow firstRow = reportRows.isEmpty() ? null : reportRows.get(0);
        return (firstRow != null && !firstRow.getReportRowCellValues().isEmpty()) ? firstRow.getReportRowCellValues().get(0).getReportRowCellBorderType() : defaultReportRowCellBorderType;
    }

    /**
     * Method to set cell borders
     *
     * @param cellStyle    CellStyle to modify & apply into cell
     * @param borderTop    flag to decide to set top border
     * @param borderRight  flag to decide to set right border
     * @param borderBottom flag to decide to set bottom border
     * @param borderLeft   flag to decide to set left border
     */

    protected CellStyle setCellBorder(CellStyle cellStyle, Boolean borderTop, Boolean borderRight, Boolean borderBottom, Boolean borderLeft) {

        if (borderTop) cellStyle.setBorderTop(BorderStyle.THIN);
        if (borderRight) cellStyle.setBorderRight(BorderStyle.THIN);
        if (borderBottom) cellStyle.setBorderBottom(BorderStyle.THIN);
        if (borderLeft) cellStyle.setBorderLeft(BorderStyle.THIN);

        return cellStyle;
    }


    private String getFormattedDate(Object columnValue, String dateFormat) {
        if (columnValue == null) {
            return "";
        }

        String inputFormat = null;
        String outputFormat = "dd-MM-yyyy";
        String formattedDate = columnValue.toString();
        dateFormat = dateFormat == null ? "" : dateFormat;

        String[] formats = dateFormat.trim().split(">");
        inputFormat = formats.length == 1 ? "" : formats[0];
        outputFormat = formats.length == 1 ? outputFormat : formats[1];

        try {
            switch (columnValue.getClass().getSimpleName()) {
                case "LocalDateTime":
                    formattedDate = ((LocalDateTime) columnValue).format(DateTimeFormatter.ofPattern(outputFormat));
                    break;
                case "LocalDate":
                    formattedDate = ((LocalDate) columnValue).format(DateTimeFormatter.ofPattern(outputFormat));
                    break;
                case "String":
                    formattedDate = LocalDate.parse(columnValue.toString(), DateTimeFormatter.ofPattern(inputFormat)).format(DateTimeFormatter.ofPattern(outputFormat));
                    break;
                default:
                    break;
            }
        } catch (DateTimeException exception) {
            log.info("[CompanyPayrollReport] Failure in converting to DateTime, InputValue = {}, ", columnValue);
        }
        return formattedDate;
    }
}
