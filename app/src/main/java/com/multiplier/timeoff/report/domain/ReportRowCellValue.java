package com.multiplier.timeoff.report.domain;

import lombok.Builder;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * Each cell value for a given row
 */
@Builder
@Data
public class ReportRowCellValue {
    private String columnName;
    private Object columnValue;
    private String columnDataFormat;
    private Object columnAlias;
    private ReportRowCellType reportRowCellType;
    private ReportRowCellBorderType reportRowCellBorderType;
    private IndexedColors xlBackgroundColor;

    public static ReportRowCellValue of(String columnName, Object columnValue) {
        return ReportRowCellValue.builder()
                .columnName(columnName)
                .columnValue(columnValue)
                .build();

    }
}
