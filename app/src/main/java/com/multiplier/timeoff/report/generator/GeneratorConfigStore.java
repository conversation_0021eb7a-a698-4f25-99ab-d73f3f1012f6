package com.multiplier.timeoff.report.generator;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.multiplier.timeoff.adapters.InvalidConfigException;
import com.multiplier.timeoff.report.domain.ReportCategory;
import com.multiplier.timeoff.types.CountryCode;
import org.springframework.stereotype.Component;
import com.multiplier.timeoff.report.domain.ReportGenerationType;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class GeneratorConfigStore {

    private final ConcurrentHashMap<String, ConcurrentHashMap<String, Object>> values;
    private final String fileName = "config/report/report-generator-config.json";

    public GeneratorConfigStore() throws IOException {
        values = new ConcurrentHashMap<>();

        try {
            InputStream json = getClass().getClassLoader().getResourceAsStream(fileName);
            List<ConcurrentHashMap<String, Object>> values = new ObjectMapper().readValue(json, new TypeReference<>() {
            });
            for (ConcurrentHashMap value : values) {
                this.values.put((String) value.get("report_category"), value);
            }
        } catch (JsonMappingException ex) {
            throw new InvalidConfigException("Wrong JSON format found in the template file " + fileName, ex);
        } catch (IllegalArgumentException ex) {
            throw new IllegalArgumentException("JSON format file not found file:" + fileName, ex);
        }
    }

    public ConcurrentHashMap<String, Object> getTemplate(String reportCategory) {
        return this.values.get(reportCategory);
    }

    public ReportGenerationType getReportGenerationType(String reportCategory) {
        ConcurrentHashMap<String, Object> reportConfig = this.values.get(reportCategory);
        if (reportConfig == null) {
            return null;
        }
        return ReportGenerationType.valueOf(reportConfig.get("type").toString());
    }

    public String getReportName(String reportCategory) {
        ConcurrentHashMap<String, Object> reportConfig = this.values.get(reportCategory);
        if (reportConfig == null) {
            return null;
        }
        return (String) reportConfig.get("report_name");
    }

    public List<String> getColumnNames(String reportCategory) {
        ConcurrentHashMap<String, Object> reportConfig = this.values.get(reportCategory);
        if (reportConfig == null) {
            return Collections.emptyList();
        }
        return (List<String>) reportConfig.get("column_names");
    }

    /**
     * Method to get country specific configurations
     *
     * @param reportCategory Categrory of report
     * @param countryCode    Country Code
     * @param countryDataKey Key to retrive configuration object
     */

    private Object getValueByCountryAndKey(ReportCategory reportCategory, CountryCode countryCode, String countryDataKey) {
        ConcurrentHashMap<String, Object> reportConfig = this.values.get(reportCategory.toString());
        if (reportConfig == null) {
            return Collections.emptyList();
        }
        List<Object> columnWrapper = (List<Object>) reportConfig.get("columns");
        return (Object) columnWrapper.stream().filter(countryColumn
                -> ((LinkedHashMap<?, ?>) countryColumn).get("country_code").equals(countryCode.toString())).map(columnList
                -> ((LinkedHashMap<?, ?>) columnList).get(countryDataKey)).findFirst().orElse(null);
    }

    /**
     * Method to get country specific configurations
     *
     * @param reportCategory Categrory of report
     * @param countryCode    Country Code
     */

    public List<Object> getColumnsByCountry(ReportCategory reportCategory, CountryCode countryCode) {
        List<Object> columns = (List<Object>) getValueByCountryAndKey(reportCategory, countryCode, "columns");
        return columns == null ? (List<Object>) getValueByCountryAndKey(reportCategory, CountryCode.PRK, "columns"): columns;
    }

    /**
     * Method to get number of freezing rows for given country
     *
     * @param reportCategory Categrory of report
     * @param countryCode    Country Code
     */

    public Integer getFreezeRowsByCountry(ReportCategory reportCategory, CountryCode countryCode) {
        Object value = getValueByCountryAndKey(reportCategory, countryCode, "freeze_rows");
        return value == null ? 0 : (int) value;
    }

    /**
     * Method to get number of freezing cols for given country
     *
     * @param reportCategory Categrory of report
     * @param countryCode    Country Code
     */

    public Integer getFreezeColsByCountry(ReportCategory reportCategory, CountryCode countryCode) {
        Object value = getValueByCountryAndKey(reportCategory, countryCode, "freeze_cols");
        return value == null ? 0 : (int) value;
    }

    /**
     * Method to get formula for given column
     *
     * @param reportCategory Categrory of report
     * @param countryCode    Country Code
     * @param columnName     column column Alias
     */

    public String getRowFormula(ReportCategory reportCategory, CountryCode countryCode, String columnName) {
        List<Object> columns = (List<Object>) getValueByCountryAndKey(reportCategory, countryCode, "columns");
        Object formula = columns.stream().filter(col -> ((LinkedHashMap<String, String>) col).get("alias").equalsIgnoreCase(columnName)).map(col -> ((LinkedHashMap<?, ?>) col).get("formula")).findFirst().orElse(null);
        return formula == null ? "" : formula.toString();
    }

    /**
     * Method to check whether summarization row is required
     *
     * @param reportCategory Categrory of report
     * @param countryCode    Country Code
     */

    public Boolean hasRowSumarization(ReportCategory reportCategory, CountryCode countryCode) {
        Object value = getValueByCountryAndKey(reportCategory, countryCode, "has_formula_row");
        return value != null && (int) value == 1;
    }

    /**
     * Method to get header style options
     *
     * @param reportCategory Categrory of report
     */

    public String getHeaderStyle(ReportCategory reportCategory) {
        ConcurrentHashMap<String, Object> reportConfig = this.values.get(reportCategory.toString());
        return (reportConfig != null && reportConfig.get("header_style") != null) ? (String) reportConfig.get("header_style") : "NONE";
    }
}
