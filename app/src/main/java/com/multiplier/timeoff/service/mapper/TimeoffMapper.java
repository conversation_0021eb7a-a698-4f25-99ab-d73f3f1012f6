package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.types.*;
import com.multiplier.timeoff.types.Contract;
import com.multiplier.timeoff.types.TimeOff;
import com.multiplier.timeoff.types.TimeOffType;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TimeoffMapper {

    default TimeOff map(TimeoffDBO source) {

        return (source == null) ? null : new TimeOff.Builder()
                .id(source.id())
                .contract(
                        new Contract.Builder()
                                .id(source.contractId())
                                .build())
                .type(
                        new TimeOffType.Builder()
                                .typeId(source.type().id())
                                .key(source.type().key())
                                .definition(
                                        new TimeOffTypeDefinition.Builder()
                                                .type(source.type().key())
                                                .typeId(source.type().id())
                                                .label(source.type().label())
                                                .build())
                                .isPaidLeave(source.type().isPaidLeave())
                                .build())
                .status(source.status())
                .noOfDays(source.noOfDays())
                .description(source.description())
                .startDate(
                        new TimeOffDate.Builder()
                                .date(source.startDate().atStartOfDay()) // keeping this only for compatibility purpose
                                .dateOnly(source.startDate()) // use this instead of `date`
                                .session(source.startSession())
                                .build())
                .endDate(
                        new TimeOffDate.Builder()
                                .date(source.endDate() != null ? source.endDate().atStartOfDay() : null) // keeping this only for compatibility purpose
                                .dateOnly(source.endDate()) // use this instead of `date`
                                .session(source.endSession())
                                .build())
                .changeReason(source.changeReason())
                .createdOn(source.createdOn())
                .updatedOn(source.updatedOn())
                .createdBy(source.createdBy())
                .updatedBy(source.updatedBy())
                .build();
    }


    List<TimeOff> map(List<TimeoffDBO> source);


    default TimeoffDBO mapFrom(TimeOffCreateInput source, TimeoffDBO target, TimeoffTypeDBO type) {

        return (source == null) ? target : target
                .type(type)
                .noOfDays(source.getNoOfDays())
                .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                .description(source.getDescription())
                .startDate(source.getStartDate().getDateOnly())
                .startSession(source.getStartDate().getSession())
                .endDate(source.getEndDate().getDateOnly())
                .endSession(source.getEndDate().getSession());
    }

    default TimeoffDBO mapFrom(TimeOffSaveAsDraftInput source, TimeoffDBO target, TimeoffTypeDBO type) {

        return (source == null) ? target : target
                .type(type)
                .noOfDays(source.getNoOfDays())
                .status(TimeOffStatus.DRAFT)
                .description(source.getDescription())
                .startDate(source.getStartDate().getDateOnly())
                .startSession(source.getStartDate().getSession())
                .endDate(source.getEndDate().getDateOnly())
                .endSession(source.getEndDate().getSession());
    }

    default TimeoffDBO mapFrom(TimeOffUpdateInput source, TimeoffDBO target, TimeoffTypeDBO type) {

        return (source == null) ? target : target
                .type(type)
                .noOfDays(source.getNoOfDays())
                .description(source.getDescription())
                .startDate(source.getStartDate().getDateOnly())
                .startSession(source.getStartDate().getSession())
                .endDate(source.getEndDate().getDateOnly())
                .endSession(source.getEndDate().getSession());
    }

    default TimeoffDBO mapToDBO(TimeOff source) {
        if (source == null) {
            return null;
        }

        TimeoffDBO target = new TimeoffDBO();

        // Set basic properties
        target.id(source.getId());
        target.contractId(source.getContract().getId());
        target.status(source.getStatus());
        target.noOfDays(source.getNoOfDays());
        target.description(source.getDescription());

        // Set dates and sessions
        if (source.getStartDate() != null) {
            target.startDate(source.getStartDate().getDateOnly());
            target.startSession(source.getStartDate().getSession());
        }

        if (source.getEndDate() != null) {
            target.endDate(source.getEndDate().getDateOnly());
            target.endSession(source.getEndDate().getSession());
        }

        // Set type information
        if (source.getType() != null) {
            TimeoffTypeDBO typeEntity = new TimeoffTypeDBO();
            typeEntity.id(source.getType().getTypeId());
            typeEntity.key(source.getType().getKey());
            typeEntity.isPaidLeave(source.getType().getIsPaidLeave());

            if (source.getType().getDefinition() != null) {
                typeEntity.label(source.getType().getDefinition().getLabel());
            }

            target.type(typeEntity);
            target.typeId(typeEntity.id());
        }

        // Set audit fields
        target.createdBy(source.getCreatedBy());
        target.createdOn(source.getCreatedOn());
        target.updatedBy(source.getUpdatedBy());
        target.updatedOn(source.getUpdatedOn());

        return target;
    }
}
