package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.core.common.dto.BulkUpsertTimeOffResult;
import com.multiplier.timeoff.core.common.dto.BulkValidateTimeOffResult;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BulkTimeOffMapper {

    BulkUpsertTimeOffResult.Item map(BulkValidateTimeOffResult.BulkValidateTimeOffResultItem item);
    List<BulkUpsertTimeOffResult.Item> map(List<BulkValidateTimeOffResult.BulkValidateTimeOffResultItem> items);
}
