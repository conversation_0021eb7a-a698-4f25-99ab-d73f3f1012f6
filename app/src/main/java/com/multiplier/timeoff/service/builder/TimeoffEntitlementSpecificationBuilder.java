package com.multiplier.timeoff.service.builder;

import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Set;

interface TimeoffEntitlementColumnNames {
    String CONTRACT_ID = "contractId";
    String DEFINITION_ID = "definitionId";
    String ID = "id";
}

@Component
public class TimeoffEntitlementSpecificationBuilder {

    public Specification<TimeoffEntitlementDBO> buildForContractIdIn(Set<Long> contractIds, Set<Long> idsNotIn) {
        var spec = Specification.<TimeoffEntitlementDBO>where(null);
        spec = CollectionUtils.isEmpty(contractIds) ? spec : spec.and((root, query, builder) -> builder.in(root.get(TimeoffEntitlementColumnNames.CONTRACT_ID)).value(contractIds));
        spec = spec.and((root, query, builder) -> builder.isNull(root.get(TimeoffEntitlementColumnNames.DEFINITION_ID)));
        spec = CollectionUtils.isEmpty(idsNotIn) ? spec : spec.and((root, query, builder) -> builder.not(root.get(TimeoffEntitlementColumnNames.ID).in(idsNotIn)));
        return spec;
    }

}
