package com.multiplier.timeoff.service.exception;

import com.multiplier.common.exception.MplBusinessException;
import com.multiplier.timeoff.core.common.exceptionhandler.MPLError;
import lombok.Getter;

@Getter
public class ValidationException extends MplBusinessException { // NOSONAR - converting all validation exceptions to this MplBusinessException
    private MPLError mplError;// NOSONAR - not declaring as final since we need to set the value later.

    public ValidationException() {
        super(TimeOffErrorCode.VALIDATION_ERROR, TimeOffErrorCode.VALIDATION_ERROR.getMessage());
    }

    public ValidationException(String message) {
        super(TimeOffErrorCode.VALIDATION_ERROR, message);
    }

    public ValidationException(String message, Throwable cause) {
        super(TimeOffErrorCode.VALIDATION_ERROR, message, cause);
    }

    public ValidationException(MPLError mplError) {
        super(TimeOffErrorCode.VALIDATION_ERROR, mplError.getMessage());
        this.mplError = mplError;
    }

}
