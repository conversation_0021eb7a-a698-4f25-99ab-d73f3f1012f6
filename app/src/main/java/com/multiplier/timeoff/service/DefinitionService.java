package com.multiplier.timeoff.service;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.member.schema.Member;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.MemberServiceAdapter;
import com.multiplier.timeoff.core.constant.Constant;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.leavecompliance.service.CountryDefinitionService;
import com.multiplier.timeoff.repository.CompanyDefinitionRepository;
import com.multiplier.timeoff.repository.TimeoffEntitlementDBORepository;
import com.multiplier.timeoff.repository.builder.CompanyDefinitionSpecBuilder;
import com.multiplier.timeoff.repository.filters.CompanyDefinitionFilter;
import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.service.dto.CountryLocation;
import com.multiplier.timeoff.service.exception.DataCorruptionException;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.TimeOffDefinitionRuleMapper;
import com.multiplier.timeoff.service.mapper.TimeOffTypeDefinitionMapper;
import com.multiplier.timeoff.service.notifications.TimeoffNotificationHelper;
import com.multiplier.timeoff.service.rules.RuleResolverInput;
import com.multiplier.timeoff.service.rules.TimeOffDefinitionRuleService;
import com.multiplier.timeoff.types.*;
import com.multiplier.timeoff.util.FunctionalUtil;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.multiplier.timeoff.core.constant.Constant.EXPERIENCE;
import static com.multiplier.timeoff.core.util.TimeOffUtil.*;


@Service
@RequiredArgsConstructor
@Slf4j(topic = "DefinitionService")
public class DefinitionService {

    private final CurrentUser currentUser;
    private final TimeoffTypeService timeoffTypeService;
    private final CountryDefinitionService countryDefinitionService;
    private final CompanyDefinitionRepository companyDefinitionRepository;
    private final TimeOffTypeDefinitionMapper timeOffTypeDefinitionMapper;
    private final ContractServiceAdapter contractServiceAdapter;
    private final TimeoffEntitlementDBORepository timeoffEntitlementDBORepository;
    private final TimeoffSummaryService timeoffSummaryService;
    private final TimeOffDefinitionRuleMapper timeOffDefinitionRuleMapper;
    private final TimeoffNotificationHelper timeoffNotificationHelper;
    private final TimeOffDefinitionRuleService timeOffDefinitionRuleService;
    private final MemberServiceAdapter memberServiceAdapter;
    private final CompanyDefinitionSpecBuilder companyDefinitionSpecBuilder;
    private final FeatureFlagService featureFlagService;
    private final CompanyServiceAdapter companyServiceAdapter;
    private final AuthorizationService authorizationService;


    @Transactional
    public TimeOffTypeDefinition createCompanyDefinition(TimeOffPolicyCreateInput timeOffPolicyCreateInput) {
        val companyId = getCompanyIdFromCurrentUser();
        val timeOffTypeDBO = timeoffTypeService.findTimeOffTypeDBOById(timeOffPolicyCreateInput.getTimeOffTypeId());
        throwIfCannotCreateCompanyDefinition(timeOffPolicyCreateInput, timeOffTypeDBO, companyId);

        CompanyDefinitionEntity companyDefinitionEntity;
        if (timeOffPolicyCreateInput.getTimeoffPolicyEntityInput() != null) {
            companyDefinitionEntity = createCompanyDefinitionWithEntity(timeOffPolicyCreateInput, companyId);
        } else {
            companyDefinitionEntity = createCompanyDefinition(timeOffPolicyCreateInput, companyId);
        }

        timeoffNotificationHelper.sendTimeoffPolicyCreatedNotification(companyDefinitionEntity.getDefinition().getId());
        return timeOffTypeDefinitionMapper.map(companyDefinitionEntity, timeOffTypeDBO);
    }

    private CompanyDefinitionEntity createCompanyDefinitionWithEntity(TimeOffPolicyCreateInput timeOffPolicyCreateInput, Long companyId) {
        val companyDefinition = buildCompanyDefinition(timeOffPolicyCreateInput, companyId);
        setEntity(timeOffPolicyCreateInput.getTimeoffPolicyEntityInput(), companyDefinition);

        val savedDefinition = companyDefinitionRepository.save(companyDefinition);

        saveEntityRule(savedDefinition, timeOffPolicyCreateInput.getTimeoffPolicyEntityInput());
        return savedDefinition;
    }

    private void setEntity(TimeOffPolicyEntityInput entityInput, CompanyDefinitionEntity companyDefinition) {
        companyDefinition.setEntityType(entityInput.getType());
        companyDefinition.setEntityId(entityInput.getId());
        if (entityInput.getType() == EntityType.EOR_PARTNER_ENTITY) {
            // If entity type is EOR Partner, then there is no entity concept
            // All EOR entities are treated  a virtual  "Multiplier Entity"
            companyDefinition.setEntityId(EOR_VIRTUAL_ENTITY_ID);
        }
    }

    private CompanyDefinitionEntity createCompanyDefinition(TimeOffPolicyCreateInput timeOffPolicyCreateInput, Long companyId) {
        val companyDefinition = buildCompanyDefinition(timeOffPolicyCreateInput, companyId);
        return companyDefinitionRepository.save(companyDefinition);
    }
    private CompanyDefinitionEntity buildCompanyDefinition(TimeOffPolicyCreateInput timeOffPolicyCreateInput, Long companyId) {
        val companyDefinition = new CompanyDefinitionEntity();
        companyDefinition.setTypeId(timeOffPolicyCreateInput.getTimeOffTypeId());
        companyDefinition.setDefinition(createDefinitionFromInput(timeOffPolicyCreateInput));
        companyDefinition.setCompanyId(companyId);
        companyDefinition.setCreatedBy(currentUser.getContext().getId());
        companyDefinition.setCreatedOn(LocalDateTime.now());

        return companyDefinition;
    }

    private void saveEntityRule(CompanyDefinitionEntity companyDefinition, TimeOffPolicyEntityInput timeoffPolicyEntityInput) {
        val conditionInput = ConditionInput.newBuilder()
                .key(ConditionKey.ENTITY)
                .operator(ConditionOperator.EQUALS)
                .values(List.of(timeoffPolicyEntityInput.getId().toString()))
                .build();
        val entityRule = createTimeOffDefinitionRuleForCondition(conditionInput, companyDefinition.getCompanyId(), companyDefinition, currentUser.getContext().getId());
        timeOffDefinitionRuleService.saveRules(List.of(entityRule));
    }

    /**
     * Convert to spec builder style once the filter has more fields
     *
     * @param filter : if ids are given, find by that ids and ignore all other filter fields
     */
    public List<TimeOffTypeDefinition> findCompanyDefinitions(CompanyTimeOffPolicyFilter filter) {
        val companyId = getCompanyIdFromCurrentUser();
        if (isFilterEmpty(filter)) {
            log.info("[findCompanyDefinitions] load all non deleted time-off definitions for company id : {}", companyId);
            return mapToGraph(findAllCompanyDefinitions(companyId));
        }

        if (!CollectionUtils.isEmpty(filter.getTimeOffPolicyIds())) {
            return mapToGraph(companyDefinitionRepository.findAllNonDeletedByDefinitionIdInAndCompanyId(filter.getTimeOffPolicyIds(), companyId));
        }

        if (CollectionUtils.isEmpty(filter.getTimeOffTypeIds()) && !CollectionUtils.isEmpty(filter.getEntityIds())) {
            return mapToGraph(companyDefinitionRepository.findAllNonDeletedByCompanyIdAndEntityIdIn(companyId, filter.getEntityIds()));
        }

        return mapToGraph(companyDefinitionRepository.findAllNonDeletedByTypeIdInAndCompanyId(filter.getTimeOffTypeIds(), companyId));
    }

    public List<CompanyDefinitionEntity> findAllCompanyDefinitions(Long companyId) {
        return companyDefinitionRepository.findAllNonDeletedByCompanyId(companyId);
    }

    public Map<Long, TimeOffDefinitionEntity> findTypeIdToAvailableDefMapForContract(@NotNull ContractOuterClass.Contract contract) {
        return findAvailableDefinitionsByContract(contract.getCompanyId(), List.of(contract)).getOrDefault(contract.getId(), Collections.emptyMap());
    }

    public List<TimeOffDefinitionEntity> findAllDefinitionsForContract(@NotNull ContractOuterClass.Contract contract) {
        log.info("[findAllDefinitionsForContract] find time off definitions for contract id : {}", contract.getId());
        if (!CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(contract.getType())) {
            log.info("[findAllDefinitionsForContract] contract id : {} : type : {} do not have time-off definitions", contract.getId(), contract.getType());
            return List.of();
        }
        val companyDefinitionsByContract = getContractIdToCDsByTypeIdMap(contract.getCompanyId(), List.of(contract));
        Map<Long, CompanyDefinitionEntity> typeToEligibleCompanyDefinitionMap = companyDefinitionsByContract.getOrDefault(contract.getId(), Collections.emptyMap());
        if (contract.getType() == ContractOuterClass.ContractType.HR_MEMBER) {
            return new ArrayList<>(typeToEligibleCompanyDefinitionMap.values());
        }

        Map<Long, CountryDefinitionEntity> typeToCountryDefinitionMap = getTypeIdToEligibleCountryDefinitionMap(contract);
        return findAllEligibleDefinitions(contract, typeToEligibleCompanyDefinitionMap, typeToCountryDefinitionMap);
    }

    /**
     * Here we find available definitions set of contracts in one single company.
     * Callers should make sure all contracts are belongs to one company
     *
     */
    public Map<Long, Map<Long, TimeOffDefinitionEntity>> findAvailableDefinitionsByContract(Long companyId, Collection<ContractOuterClass.Contract> contracts) {
        if (CollectionUtils.isEmpty(contracts)) {
            log.info("[findAvailableDefinitionsByContract] No definitions found for empty contracts list");
            return Map.of();
        }
        log.info("[findAvailableDefinitionsByContract] finding time off definitions for {} contracts", contracts.size());
        val contractIdToCDsByTypeIdMap = getContractIdToCDsByTypeIdMap(companyId, contracts);
        val eorContracts = FunctionalUtil.Companion.filter(contracts, this::isEORContract);
        val countryLocations = FunctionalUtil.Companion.mapNotNull(eorContracts, this::getCountryLocation);
        val countryLocationToCountryDefinitions = countryDefinitionService.getCountryLocationToCountryDefinitionsMap(countryLocations);
        val typeIdToCountryDefinitionsMapByContract = getTypeIdToCountryDefinitionByContract(contracts, countryLocationToCountryDefinitions);

        val definitionsByContract = getTypeIdToDefMapByContractId(contracts, contractIdToCDsByTypeIdMap, typeIdToCountryDefinitionsMapByContract);
        log.info("[findAvailableDefinitionsByContract] found time off definitions for {} contracts", definitionsByContract.size());

        return definitionsByContract;
    }

    /**
     * Only supports ops experience
     */
    public List<TimeOffTypeDefinition> getDefinitions(TimeOffPolicyFilter filter) {
        if (filter == null || CollectionUtils.isEmpty(filter.getEntityFilters())) {
            log.info("[getDefinitions] No definitions found for empty filter : {}", filter);
            return Collections.emptyList();
        }
        throwIfCurrentUserCannotAccessDefinitions();
        val filters = createCompanyDefFilters(filter);
        val companyDefinitions = companyDefinitionRepository.findAll(companyDefinitionSpecBuilder.build(filters));
        return mapToGraph(companyDefinitions);
    }

    /**
     * Only support from ops experience back-fill
     */
    @Async
    @Transactional
    public void migrateCompanyLevelDefinitionsToEntityLevel(List<Long> companyIds, boolean forAll) {
        Map<Long, List<CompanyDefinitionEntity>> cdsByCompanyId = getCompanyDefinitionsMap(companyIds, forAll);
        Map<Long, List<TimeoffEntitlementDBO>> entitlementsByDefinitionId = getEntitlementsByDefinitionId(getDefinitionIds(cdsByCompanyId.values()));
        Map<Long, ContractOuterClass.Contract> idToContractMap = getIdToContractMapIncludingTest(getContractIdsFromEntitlements(entitlementsByDefinitionId.values()));
        log.info("[migrateCompanyLevelDefinitionsToEntityLevel] Found {} companies to migrate definitions", cdsByCompanyId.size());
        int companyCount = 1;
        MigrateCompanyDefsResult result = MigrateCompanyDefsResult.builder()
                .totalCompanyDefsFound(0L)
                .totalCompanyDefsMigrated(0L)
                .totalCompanyDefsFailed(0L)
                .defToContractNotFoundIds(new HashMap<>())
                .cdsWithNoAssignments(new ArrayList<>())
                .build();
        for (Long companyId : cdsByCompanyId.keySet()) {
            val companyDefinitions = cdsByCompanyId.getOrDefault(companyId, Collections.emptyList());
            try {
                log.info("[migrateCompanyLevelDefinitionsToEntityLevel] migration process started for company id : {}", companyId);

                migrateCompanyDefinitionsForCompany(companyDefinitions, entitlementsByDefinitionId, idToContractMap, result);

                log.info("[migrateCompanyLevelDefinitionsToEntityLevel] migration process completed for company id : {} ({}/{})", companyId, companyCount, cdsByCompanyId.size());
            } catch (Exception e) {
                result.totalCompanyDefsFailed += companyDefinitions.size();
                log.error("[migrateCompanyLevelDefinitionsToEntityLevel] Error while migrating company level definitions for company id : {} ({}/{})", companyId,companyCount, cdsByCompanyId.size(), e);
            } finally {
                companyCount++;
            }
        }

        log.info("[migrateCompanyLevelDefinitionsToEntityLevel] Migration completed. Result summary :- Found = {}, Migrated = {}, Failed = {}, No assignments = {}",
                result.totalCompanyDefsFound, result.totalCompanyDefsMigrated, result.totalCompanyDefsFailed, result.cdsWithNoAssignments.size());
        if (!result.defToContractNotFoundIds.isEmpty()) {
            log.warn("Couldn't find contracts for some assigned entitlements in {} definitions", result.defToContractNotFoundIds.size());
            result.defToContractNotFoundIds.forEach((key, value) -> log.warn("company def id : {}, contract not found contract ids : {}", key, value));
        }

        if (!result.cdsWithNoAssignments.isEmpty()) {
            log.warn("There are {} company definitions with no assignments. company definition ids : {}", result.cdsWithNoAssignments.size(), result.cdsWithNoAssignments);
        }
    }

    private Set<Long> getDefinitionIds(Collection<List<CompanyDefinitionEntity>> companyDefinitions) {
        return companyDefinitions.stream()
                .flatMap(Collection::stream)
                .map(cd -> cd.getDefinition().getId())
                .collect(Collectors.toSet());
    }

    private Set<Long> getContractIdsFromEntitlements(Collection<List<TimeoffEntitlementDBO>> entitlements) {
        return entitlements.stream()
                .flatMap(Collection::stream)
                .map(TimeoffEntitlementDBO::contractId)
                .collect(Collectors.toSet());
    }

    private Map<Long, List<TimeoffEntitlementDBO>> getEntitlementsByDefinitionId(Set<Long> definitionIds) {
        return timeoffEntitlementDBORepository.findAllByDefinitionIdIn(definitionIds)
                .stream()
                .collect(Collectors.groupingBy(TimeoffEntitlementDBO::definitionId));
    }


    private Map<Long, List<CompanyDefinitionEntity>> getCompanyDefinitionsMap(List<Long> companyIds, boolean forAll) {
        if (CollectionUtils.isEmpty(companyIds) && !forAll) {
            return Collections.emptyMap();
        }
        List<CompanyDefinitionEntity> companyDefinitions = CollectionUtils.isEmpty(companyIds) ?
                companyDefinitionRepository.findAllByEntityIdIsNull():
                companyDefinitionRepository.findAllByEntityIdIsNullAndCompanyIdIn(companyIds);
        return companyDefinitions.stream().collect(Collectors.groupingBy(CompanyDefinitionEntity::getCompanyId));
    }


    private Map<Long, ContractOuterClass.Contract> getIdToContractMapIncludingTest(Set<Long> contractIds) {
        return contractServiceAdapter.getContractsByIdsAnyStatus(contractIds, true)
                .stream()
                .collect(Collectors.toMap(ContractOuterClass.Contract::getId, Function.identity()));
    }


    private void migrateCompanyDefinitionsForCompany(List<CompanyDefinitionEntity> companyDefinitions,
                                                     Map<Long, List<TimeoffEntitlementDBO>> entitlementsByCdId,
                                                     Map<Long, ContractOuterClass.Contract> idToContractMap,
                                                     MigrateCompanyDefsResult result) {
        log.info("[migrateCompanyDefinitionsForCompany] migrating {} company definitions to entity level policies :", companyDefinitions.size());
        for (CompanyDefinitionEntity companyDefinition : companyDefinitions) {
            log.info("[migrateCompanyDefinitionsForCompany] migration started for company definition id : {}", companyDefinition.getId());
            result.totalCompanyDefsFound++;

            var assignedEntitlements = entitlementsByCdId.getOrDefault(companyDefinition.getDefinition().getId(), Collections.emptyList());
            Map<Long, List<TimeoffEntitlementDBO>> assignedEntitlementsByEntity = groupEntitlementsByEntity(assignedEntitlements, idToContractMap, companyDefinition, result);
            if (CollectionUtils.isEmpty(assignedEntitlementsByEntity)) {
                log.warn("[migrateCompanyDefinitionsForCompany] company definition id : {} does not assigned to any member. Migration need a manual effect", companyDefinition.getId());
                result.cdsWithNoAssignments.add(companyDefinition.getId());
                continue;
            }
            migrateCompanyDefinitionToEntities(companyDefinition, assignedEntitlementsByEntity, result);

            log.info("[migrateCompanyDefinitionsForCompany] migration process completed for company definition id : {}", companyDefinition.getId());

        }
    }

    private void migrateCompanyDefinitionToEntities(CompanyDefinitionEntity companyDefinition, Map<Long, List<TimeoffEntitlementDBO>> assignedEntitlementsByEntity, MigrateCompanyDefsResult result) {
        val entitiesToCreateNewCompanyDefinitions = assignedEntitlementsByEntity.keySet();
        boolean isCurrentCDMigrated = false;
        int migratedEntityCount = 1;
        int totalEntitiesToMigrate = entitiesToCreateNewCompanyDefinitions.size();
        boolean isMigrationFailedForSomeEntity = false;
        log.info("[migrateCompanyDefinitionToEntities] starting migrating company definition id : {} for {} entities", companyDefinition.getId(), totalEntitiesToMigrate);
        for (Long entityId : entitiesToCreateNewCompanyDefinitions) {
            try {
                val entityType = getEntityType(entityId);
                val entitlementsForEntity = assignedEntitlementsByEntity.getOrDefault(entityId, Collections.emptyList());
                if (!isCurrentCDMigrated) {
                    // Existing company definition is migrated to the entity
                    migrateCompanyDefinitionToEntity(entityType, entityId, companyDefinition, entitlementsForEntity);
                    isCurrentCDMigrated = true;
                } else {
                    // A new company definition is created for the entity
                    migrateCompanyDefinitionToEntity(entityType, entityId, clone(companyDefinition), entitlementsForEntity);
                }
                log.info("[migrateCDForEntity] migrating company definition id : {} to entity : {} ({}/{}) completed", companyDefinition.getId(), entityId, migratedEntityCount, totalEntitiesToMigrate);
            } catch (Exception e) {
                log.error("[migrateCDForEntity] Error while migrating company definition id : {} to entity : {} ({}/{})", companyDefinition.getId(), entityId, migratedEntityCount, totalEntitiesToMigrate, e);
                isMigrationFailedForSomeEntity = true;
            } finally {
                migratedEntityCount++;
            }
        }
        if (isMigrationFailedForSomeEntity) {
            result.totalCompanyDefsFailed++;
        } else {
            result.totalCompanyDefsMigrated++;
        }
    }

    private void migrateCompanyDefinitionToEntity(EntityType entityType,
                                                  Long entityId,
                                                  CompanyDefinitionEntity companyDefinition,
                                                  List<TimeoffEntitlementDBO> assignedEntitlements) {
        companyDefinition.setEntityId(entityId);
        companyDefinition.setEntityType(entityType);
        migrateRules(entityId, companyDefinition);
        val savedCD = companyDefinitionRepository.save(companyDefinition);
        updateEntitlements(savedCD.getDefinition(), assignedEntitlements);
    }

    private void migrateRules(Long entityId, CompanyDefinitionEntity companyDefinition) {
        var ruleToMigrate = companyDefinition.getRules().stream()
                .filter(r -> r.ruleType() == RuleType.ALL)
                .findFirst()
                .orElse(findEntityRule(companyDefinition));

        if (ruleToMigrate == null) {
            // need to add a new rule for entity
            companyDefinition.getRules().add(TimeOffDefinitionRuleDBO.builder()
                    .companyId(companyDefinition.getCompanyId())
                    .companyDefinition(companyDefinition)
                    .ruleType(RuleType.BY_CONDITION)
                    .conditionKey(ConditionKey.ENTITY)
                    .conditionOperator(ConditionOperator.EQUALS)
                    .conditionValues(new String[]{String.valueOf(entityId)})
                    .build());
        } else {
            // updating existing rule for entity
            ruleToMigrate.ruleType(RuleType.BY_CONDITION);
            ruleToMigrate.conditionKey(ConditionKey.ENTITY);
            ruleToMigrate.conditionOperator(ConditionOperator.EQUALS);
            ruleToMigrate.conditionValues(new String[]{String.valueOf(entityId)});
        }
    }

    private @Nullable TimeOffDefinitionRuleDBO findEntityRule(CompanyDefinitionEntity companyDefinition) {
        return companyDefinition.getRules().stream()
                .filter( r -> r.ruleType() == RuleType.BY_CONDITION && r.conditionKey() == ConditionKey.ENTITY)
                .findFirst()
                .orElse(null);
    }

    private void updateEntitlements(DefinitionEntity newDefinitionEntity, List<TimeoffEntitlementDBO> entitlements) {
        entitlements.forEach(entitlement -> entitlement.definition(newDefinitionEntity));
        timeoffEntitlementDBORepository.saveAll(entitlements);
    }

    private EntityType getEntityType(Long key) {
        return Objects.equals(key, EOR_VIRTUAL_ENTITY_ID) ? EntityType.EOR_PARTNER_ENTITY : EntityType.COMPANY_ENTITY;
    }


    private CompanyDefinitionEntity clone(CompanyDefinitionEntity oldCompanyDefinition) {
        CompanyDefinitionEntity newCompanyDefinition = new CompanyDefinitionEntity();
        newCompanyDefinition.setCompanyId(oldCompanyDefinition.getCompanyId());
        newCompanyDefinition.setDefinition(clone(oldCompanyDefinition.getDefinition()));
        newCompanyDefinition.setTypeId(oldCompanyDefinition.getTypeId());
        newCompanyDefinition.setEntityId(oldCompanyDefinition.getEntityId());
        newCompanyDefinition.setEntityType(oldCompanyDefinition.getEntityType());
        newCompanyDefinition.setRules(clone(oldCompanyDefinition.getRules()));
        newCompanyDefinition.getRules().forEach(r -> r.companyDefinition(newCompanyDefinition));

        return newCompanyDefinition;
    }

    private List<TimeOffDefinitionRuleDBO> clone(List<TimeOffDefinitionRuleDBO> oldRules) {
        return oldRules.stream()
                .map(oldRule -> {
                    val newRule = TimeOffDefinitionRuleDBO.builder()
                            .companyId(oldRule.companyId())
                            .ruleType(oldRule.ruleType())
                            .build();
                    if (oldRule.ruleType() == RuleType.BY_CONDITION) {
                        newRule.conditionKey(oldRule.conditionKey());
                        newRule.conditionValues(oldRule.conditionValues());
                        newRule.conditionOperator(oldRule.conditionOperator());
                    }
                    return newRule;
                })
                .toList();
    }

    private DefinitionEntity clone(DefinitionEntity oldDefinition) {
        DefinitionEntity newDefinition = new DefinitionEntity();
        newDefinition.setName(oldDefinition.getName());
        newDefinition.setCountryCode(oldDefinition.getCountryCode());
        newDefinition.setStateCode(oldDefinition.getStateCode());
        newDefinition.setRequired(oldDefinition.isRequired());
        newDefinition.setDescription(oldDefinition.getDescription());
        newDefinition.setClause(oldDefinition.getClause());
        newDefinition.setBasis(oldDefinition.getBasis());
        newDefinition.setValidations(oldDefinition.getValidations());
        newDefinition.setConfigurations(oldDefinition.getConfigurations());
        newDefinition.setStatus(oldDefinition.getStatus());
        return newDefinition;
    }

    private EntityType getEntityType(ContractOuterClass.Contract assignedContract) {
        return assignedContract.getType() == ContractOuterClass.ContractType.EMPLOYEE ? EntityType.EOR_PARTNER_ENTITY : EntityType.COMPANY_ENTITY;
    }

    private List<CompanyDefinitionFilter> createCompanyDefFilters(TimeOffPolicyFilter filter) {
        return filter.getEntityFilters()
                .stream()
                .map(entityFilter -> new CompanyDefinitionFilter(entityFilter.getType(), entityFilter.getId(), null))
                .toList();
    }

    private void throwIfCurrentUserCannotAccessDefinitions() {
        if (!currentUser.getContext().getExperience().equals(EXPERIENCE.OPERATIONS_EXP)) {
            throw new ValidationException(String.format("Current user is not allowed to access definitions, user id : %s, experience : %s",
                    currentUser.getContext().getId(), currentUser.getContext().getExperience()));
        }
    }

    private Map<Long, Map<Long, CompanyDefinitionEntity>> getContractIdToCDsByTypeIdMap(Long companyId, Collection<ContractOuterClass.Contract> contracts) {
        return isEntityLevelPoliciesEnabled(companyId)
                ? getCDsByTypeIdWhenEntityPolicyFlagOn(companyId, contracts)
                : getCDsByTypeIdMapWhenEntityPolicyFlagOff(companyId, contracts);
    }

    private boolean isEntityLevelPoliciesEnabled(Long companyId) {
        return featureFlagService.isOn(FeatureFlags.ENTITY_LEVEL_POLICIES, Map.of("company", companyId));
    }

    /**
     * return type id to company definition map for each contract
     *  Ex: {contractId1 : {typeId1 : cd1, typeId2 : cd2},
     *       contractId2 : {typeId1 : cd3, typeId3 : cd4}
     *      }
     * @return
     */
    private Map<Long, Map<Long, CompanyDefinitionEntity>> getCDsByTypeIdWhenEntityPolicyFlagOn(Long companyId, Collection<ContractOuterClass.Contract> contracts) {
        Map<Boolean, Set<Long>> contractIdsByType = filterEORAndNonEORContractIds(contracts);

        Set<Long> eorContractIds = contractIdsByType.get(true);
        Set<Long> nonEorContractIds = contractIdsByType.get(false);

        // >>>>>>>>>>>> DATA LOADING START >>>>>>>>>>>>>>>>>>>>>>>>>>>
        // 1. Load contract id to entity map
        Map<Long, Long> nonEORContractIdToEntityIdMap = CollectionUtils.isEmpty(nonEorContractIds) ? Collections.emptyMap() : getContractIdToEntityIdMap(nonEorContractIds);

        // 2. Load entity id to company definition map
        Map<Long, List<CompanyDefinitionEntity>> entityIdToCDsMap = getEntityIdToCompanyDefinitionMap(companyId, eorContractIds, nonEORContractIdToEntityIdMap);


        if (CollectionUtils.isEmpty(entityIdToCDsMap)) {
            log.info("[getCDsByTypeIdWhenEntityPolicyFlagOn] No company definitions found for any contract, Hence all contracts do not eligible for any company definitions");
            return Collections.emptyMap();
        }
        // 3. load rules for company definitions
        Map<Long, List<TimeOffDefinitionRuleDBO>> companyDefinitionIdToRulesMap = getCompanyDefinitionIdToRulesMap(TimeOffUtil.flatMap(entityIdToCDsMap.values()));
        // 4. id to member map
        Map<Long, Member> idToMemberMap = getIdToMemberMap(memberIds(contracts));
        // >>>>>>>>>>>> DATA LOADING END >>>>>>>>>>>>>>>>>>>>>>>>>>>


        // create contractId to company definition map
        Map<Long, Map<Long, CompanyDefinitionEntity>> contractIdToCDsMap = new HashMap<>();

        for (ContractOuterClass.Contract contract : contracts) {
            var contractType =  contract.getType();
            var member = idToMemberMap.get(contract.getMemberId());
            if (!CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(contractType)) {
                log.info("[getCDsByTypeIdWhenEntityPolicyFlagOn] contract is not eligible for timeoff (type : {}, id : {})", contractType, contract.getId());
                continue;
            }

            if (member == null) {
                log.info("[getCDsByTypeIdWhenEntityPolicyFlagOn] member not found for contract id : {} : member Id : {}", contract.getId(), contract.getMemberId());
                continue;
            }
            var entityId = getEntityId(contract, nonEORContractIdToEntityIdMap);
            var companyDefinitionsForEntity = entityIdToCDsMap.getOrDefault(entityId, Collections.emptyList());
            var eligibleCDsMapByTypeId = groupCdsByTypeId(contract, companyDefinitionsForEntity, member, companyDefinitionIdToRulesMap, entityId);

            contractIdToCDsMap.put(contract.getId(), eligibleCDsMapByTypeId);
        }
        log.info("[getCDsByTypeIdWhenEntityPolicyFlagOn] company definition loaded for contracts size = {}", contractIdToCDsMap.size());

        return contractIdToCDsMap;
    }

    private Map<Long, List<CompanyDefinitionEntity>> getEntityIdToCompanyDefinitionMap(Long companyId, Set<Long> eorContractIds, Map<Long, Long> nonEORContractIdToEntityIdMap) {
        // create filters to load company definitions
        List<CompanyDefinitionFilter> companyDefinitionFilters = getCompanyDefinitionFilters(companyId, eorContractIds, getUniqueCompanyEntityIds(nonEORContractIdToEntityIdMap.values()));

        val companyDefinitions = companyDefinitionRepository.findAll(companyDefinitionSpecBuilder.build(companyDefinitionFilters));
        return companyDefinitions.stream()
                .filter(cd -> cd.getEntityId() != null)
                .collect(Collectors.groupingBy(CompanyDefinitionEntity::getEntityId));
    }

    private List<CompanyDefinitionFilter> getCompanyDefinitionFilters(Long companyId, Set<Long> eorContractIds, Set<Long> uniqueCompanyEntityIds) {
        List<CompanyDefinitionFilter> companyDefinitionFilters = new ArrayList<>();
        uniqueCompanyEntityIds.forEach(companyEntityId -> companyDefinitionFilters.add(new CompanyDefinitionFilter(EntityType.COMPANY_ENTITY, companyEntityId, companyId)));

        // add filter for EOR entities
        if (!CollectionUtils.isEmpty(eorContractIds)) {
            // In timeoff policies we consider all EOR contracts are belongs to virtual multiplier entity
            // Therefore, if there are any EOR contract present we add the virtual entity to filter
            companyDefinitionFilters.add(new CompanyDefinitionFilter(EntityType.EOR_PARTNER_ENTITY, EOR_VIRTUAL_ENTITY_ID, companyId));
        }
        return companyDefinitionFilters;
    }

    private Long getEntityId(ContractOuterClass.Contract contract, Map<Long, Long> contractIdToEntityIdMap) {
        if (contract.getType() == ContractOuterClass.ContractType.EMPLOYEE) {
            return EOR_VIRTUAL_ENTITY_ID; // All EOR contract are belongs to virtual multiplier entity with -1
        }
        return contractIdToEntityIdMap.get(contract.getId());
    }

    private Set<Long> getUniqueCompanyEntityIds(Collection<Long> companyEntityIds) {
        return new HashSet<>(companyEntityIds);
    }

    private Map<Boolean, Set<Long>> filterEORAndNonEORContractIds(Collection<ContractOuterClass.Contract> contracts) {
        return contracts.stream()
                .collect(Collectors.groupingBy(
                        contract -> contract.getType() == ContractOuterClass.ContractType.EMPLOYEE,
                        Collectors.mapping(ContractOuterClass.Contract::getId, Collectors.toSet())
                ));
    }

    private Map<Long, Long> getContractIdToEntityIdMap(Set<Long> contractIds) {
        return contractIds.stream()
                .collect(Collectors.toMap(
                        contractId -> contractId,
                        // TODO : FIX N+1 once bulk method is available
                        contractServiceAdapter::getEntityIdByContractId
                ));
    }

    private Map<Long, List<TimeoffEntitlementDBO>> groupEntitlementsByEntity(List<TimeoffEntitlementDBO> entitlements,
                                                                             Map<Long, ContractOuterClass.Contract> contractMap,
                                                                             CompanyDefinitionEntity companyDefinition,
                                                                             MigrateCompanyDefsResult result) {
        Map<Long, List<TimeoffEntitlementDBO>> entityToEntitlementsMap = new HashMap<>();
        for (TimeoffEntitlementDBO entitlement : entitlements) {
            var contractId = entitlement.contractId();
            var contract = contractMap.get(contractId);

            if (contract == null) {
                log.info("[groupEntitlementsByEntity] contract not found for id : {}. Hence skipping migrating policy for the contract entity", entitlement.contractId());
                result.defToContractNotFoundIds
                        .computeIfAbsent(companyDefinition.getId(), k -> new ArrayList<>())
                        .add(contractId);
                continue;
            }

            if (!CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(contract.getType())) {
                log.info("[groupEntitlementsByEntity] possible data corruption. contract (id : {}, type :{}) assigned to company definition id : {}", contract.getId(), contract.getType(), companyDefinition.getId());
                continue;
            }

            var entityId = contract.getType() == ContractOuterClass.ContractType.EMPLOYEE ? EOR_VIRTUAL_ENTITY_ID :
                    contractServiceAdapter.getEntityIdByContractId(contractId);
            if (entityId <= 0 && entityId != EOR_VIRTUAL_ENTITY_ID) {
                log.warn("[groupEntitlementsByEntity] entity id not found for contract id : {}, Hence couldn't migrate for the entity it may assigned", contractId);
                continue;
            }

            if (entityToEntitlementsMap.containsKey(entityId)) {
                entityToEntitlementsMap.get(entityId).add(entitlement);
            } else {
                entityToEntitlementsMap.put(entityId, new ArrayList<>(Collections.singletonList(entitlement)));
            }
        }
        return entityToEntitlementsMap;
    }

    private Map<Long, Map<Long, CompanyDefinitionEntity>> getCDsByTypeIdMapWhenEntityPolicyFlagOff(Long companyId, Collection<ContractOuterClass.Contract> contracts) {
        val companyDefinitions = findAllCompanyDefinitions(companyId);
        return getTypeIdToCompanyDefinitionMapByContract(contracts, companyDefinitions);
    }

    private boolean isEORContract(@Nullable ContractOuterClass.Contract contract) {
        return contract != null && contract.getType() == ContractOuterClass.ContractType.EMPLOYEE;
    }

    private @Nullable CountryLocation getCountryLocation(@Nullable ContractOuterClass.Contract contract) {
        if (contract == null || StringUtils.isBlank(contract.getCountry())) return null;
        // since the country state code provided is gRPC type, it is empty by default,
        // therefore, we trim to null to make it matching with our database
        return new CountryLocation(CountryCode.valueOf(contract.getCountry()), StringUtils.trimToNull(contract.getCountryStateCode()));
    }

    private @NotNull Map<Long, Map<Long, CountryDefinitionEntity>> getTypeIdToCountryDefinitionByContract(
            @Nullable Collection<ContractOuterClass.Contract> contracts,
            @NotNull Map<CountryLocation, List<CountryDefinitionEntity>> countryLocationToCountryDefinitions) {
        if (CollectionUtils.isEmpty(contracts)) return Collections.emptyMap();

        return contracts.stream()
                .filter(this::isValidaContractForCountryDefs)
                .collect(Collectors.toMap(
                        ContractOuterClass.Contract::getId,
                        contract -> {
                            val countryLocation = getCountryLocation(contract);
                            List<CountryDefinitionEntity> countryDefinitions = countryLocation == null
                                    ? Collections.emptyList()
                                    : countryLocationToCountryDefinitions.getOrDefault(countryLocation, Collections.emptyList());
                            return getTypeIdToCountryDefinitionMapForContract(contract, countryDefinitions);
                        }
                ));
    }


    private Map<Long, Map<Long, CompanyDefinitionEntity>> getTypeIdToCompanyDefinitionMapByContract(Collection<ContractOuterClass.Contract> contracts,
                                                                                                    List<CompanyDefinitionEntity> companyDefinitions) {
        if (CollectionUtils.isEmpty(companyDefinitions)) {
            log.info("[getTypeIdToCompanyDefinitionMapByContract] No company definitions found for any contract, Hence all contracts do not eligible for any company definitions");
            return Collections.emptyMap();
        }
        Map<Long, List<TimeOffDefinitionRuleDBO>> companyDefinitionIdToRulesMap = getCompanyDefinitionIdToRulesMap(companyDefinitions);
        Map<Long, Member> idToMemberMap = getIdToMemberMap(memberIds(contracts));

        return contracts.stream()
                .filter(c -> CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(c.getType()))
                .filter(c -> isMemberExist(c, idToMemberMap))
                .collect(Collectors.toMap(
                        ContractOuterClass.Contract::getId,
                        contract -> groupCdsByTypeId(
                                contract,
                                companyDefinitions,
                                idToMemberMap.get(contract.getMemberId()),
                                companyDefinitionIdToRulesMap,
                                null) // this is called from entity policy flag off path, hence entity id is null)
                ));
    }


    private boolean isValidaContractForCountryDefs(ContractOuterClass.Contract contract) {
        if (!CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(contract.getType())) {
            log.info("[getTypeIdToCountryDefinitionByContract] Contract id: {}, type: {} not eligible for timeoff module", contract.getId(), contract.getCountry());
            return false;
        }
        if (contract.getType() == ContractOuterClass.ContractType.HR_MEMBER) {
            log.info("[getTypeIdToCountryDefinitionByContract] Contract id: {}, is a HR MEMBER, hence not considering country definitions", contract.getId());
            return false;
        }
        return true;
    }


    private Map<Long, Map<Long, TimeOffDefinitionEntity>> getTypeIdToDefMapByContractId(Collection<ContractOuterClass.Contract> contracts,
                                                                                        Map<Long, Map<Long, CompanyDefinitionEntity>> typeIdToCompanyDefinitionsMapByContract,
                                                                                        Map<Long, Map<Long, CountryDefinitionEntity>> typeIdToCountryDefinitionsMapByContract) {
        return contracts.stream()
                .collect(Collectors.toMap(
                        ContractOuterClass.Contract::getId,
                        contract -> {
                            val typeIdToCompanyDefs = typeIdToCompanyDefinitionsMapByContract.getOrDefault(contract.getId(), Collections.emptyMap());
                            val typeIdToCountryDefs = typeIdToCountryDefinitionsMapByContract.getOrDefault(contract.getId(), Collections.emptyMap());
                            val typeIdToAvailableDefMap = getTypeIdToEligibleDefinitionMap(contract, typeIdToCompanyDefs, typeIdToCountryDefs);
                            if (contract.getType() == ContractOuterClass.ContractType.HR_MEMBER) {
                                addDefaultEntitlementsForHrMemberIfNotPresent(typeIdToAvailableDefMap);
                            }
                            return typeIdToAvailableDefMap;

                        }

                ));
    }

    private void addDefaultEntitlementsForHrMemberIfNotPresent(Map<Long, TimeOffDefinitionEntity> typeIdToAvailableDefinitionMap) {
        val defaultUnpaidDefinition = HrMemberTimeOffDefinitionKt.getUnpaidLeaveDefinition();
        if (!typeIdToAvailableDefinitionMap.containsKey(defaultUnpaidDefinition.getTypeId())) {
            // if no definition for unpaid leave -> we will add our default definition
            typeIdToAvailableDefinitionMap.put(defaultUnpaidDefinition.getTypeId(), defaultUnpaidDefinition);
        }
    }

    private boolean isMemberExist(ContractOuterClass.Contract contract, Map<Long, Member> idToMemberMap) {
        if (idToMemberMap.containsKey(contract.getMemberId())) {
            return true;
        }
        log.error("[isMemberExist] member not found for contract id : {} : member Id : {}", contract.getId(), contract.getMemberId());
        return false;
    }

    /**
     * Delete a definition for given id. Definition can be deleted only if the
     * definition is not assigned to any employee
     *
     * @param id : id of the definition to delete
     * @return deleted definition
     */
    @Transactional
    public TimeOffTypeDefinition deleteDefinition(Long id) {
        String experience = currentUser.getContext().getExperience();
        if (EXPERIENCE.COMPANY_EXP.equals(experience)) {
            return deleteDefinitionByCompanyUser(id);
        }
        if (EXPERIENCE.OPERATIONS_EXP.equals(experience)) {
            return deleteDefinitionByOpsUser(id);
        }
        throw new ValidationException(String.format("Can not delete definition id : %d for experience : %s", id, experience));
    }

    private TimeOffTypeDefinition deleteDefinitionByCompanyUser(Long id) {
        val companyDefinitionOptional = companyDefinitionRepository.findByDefinitionId(id);
        if (companyDefinitionOptional.isEmpty()) {
            log.error("[deleteDefinition] Can not find definition with id : {} to delete", id);
            return null;
        }
        val companyDefinition = companyDefinitionOptional.get();
        throwIfCompanyUserCannotDeleteDefinition(companyDefinition);
        companyDefinition.getDefinition().setStatus(TimeOffTypeDefinitionStatus.DELETED);
        val deletedDefinition = companyDefinitionRepository.save(companyDefinition);
        timeoffNotificationHelper.sendTimeoffPolicyDeletedNotification(deletedDefinition.getDefinition().getId(), deletedDefinition.getDefinition().getName());
        return mapToGraph(deletedDefinition, null);
    }

    private TimeOffTypeDefinition deleteDefinitionByOpsUser(Long id) {
        val companyDefinitionOptional = companyDefinitionRepository.findByDefinitionId(id);
        if (companyDefinitionOptional.isEmpty()) {
            log.error("[deleteDefinition] Can not find definition with id : {} to delete", id);
            return null;
        }
        val companyDefinition = companyDefinitionOptional.get();
        throwIfOpsUserCannotDeleteDefinition(companyDefinition);
        companyDefinition.getDefinition().setStatus(TimeOffTypeDefinitionStatus.DELETED);
        return mapToGraph(companyDefinitionRepository.save(companyDefinition), null);
    }

    @Transactional
    public TimeOffTypeDefinition updateDefinition(TimeOffPolicyUpdateInput input) {
        Long companyId = getCompanyIdFromCurrentUser();

        // Validate policy name (but no longer return it)
        validateInputs(input);

        if (companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(input.getId(), companyId) > 0L) {
            // if this definition mapped to multiple companies <- maybe FOR EOR cases, we do not allow to update
            throw new ValidationException(String.format(
                    "Can not update definition id : %s for company id : %d since it also belongs to other companies",
                    input.getId(), companyId
            ));
        }
        val companyDefinition = companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(input.getId(), companyId).orElse(null);
        // all inputs and company definitions are valid now. Hence, no need to check for NPEs
        throwIfCannotUpdateDefinition(input, companyId, companyDefinition);
        val definition = companyDefinition.getDefinition();
        val validation = getValidationEntity(definition);
        val definitionOldName = definition.getName();
        val oldValidation = clone(validation);
        // Get and trim policy name separately
        String policyName = input.getPolicyName().trim();
        // Set new trimmed policy name
        definition.setName(policyName);

        // update allocated count
        validation.setUnlimitedLeavesAllowed(input.getIsUnlimitedLeavesAllowed());
        val allocatedCountDefaultValue = getDefaultValue(input.getAllocatedCount(), falseIfNull(input.getIsUnlimitedLeavesAllowed()));
        val allocatedCountUnit = getUnit(input.getAllocatedCount(), falseIfNull(input.getIsUnlimitedLeavesAllowed()));
        validation.setDefaultValue(allocatedCountDefaultValue);
        validation.setUnit(allocatedCountUnit);
        validation.setMinimumValue(falseIfNull(input.getIsUnlimitedLeavesAllowed()) ? Constant.UNLIMITED_LEAVES : null);

        // update carry forward config
        definition.setConfigurations(buildTimeOffDefinitionConfigEntity(input.getCarryForwardConfig(), null, falseIfNull(input.getIsUnlimitedLeavesAllowed())));

        CompanyDefinitionEntity updatedCompanyDefinition = companyDefinitionRepository.save(companyDefinition);
        val timeOffTypeDBO = timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId());
        if (isAllocationCountUpdated(validation, oldValidation)) {
            updateEntitlementsOnPolicyUpdate(updatedCompanyDefinition, timeOffTypeDBO);
        }
        timeoffNotificationHelper.sendTimeoffPolicyUpdatedNotification(input.getId(), definitionOldName);
        return mapToGraph(updatedCompanyDefinition, timeOffTypeDBO);
    }

    private void validateInputs(TimeOffPolicyUpdateInput input) {
        if (StringUtils.isEmpty(input.getPolicyName())) {
            throw new ValidationException("Policy name is empty for policy ID: " + input.getId());
        }
    }

    public Map<Long, TimeOffPolicyEntityInfo> getDefinitionIdToAssignedEntityMap(Set<Long> definitionIds) {
        log.info("[getDefinitionIdToAssignedEntityMap] Loading assigned entity infos for definition ids (size : {})", definitionIds.size());
        if (CollectionUtils.isEmpty(definitionIds)) {
            log.info("[getDefinitionIdToAssignedEntityMap] No definitions found for empty definition ids list");
            return Collections.emptyMap();
        }

        val companyDefsByDefinitionId = getIdToCompanyDefinitionMap(definitionIds);
        val companyEntityIds = getCompanyEntityIds(companyDefsByDefinitionId.values());

        Map<Long, CompanyOuterClass.LegalEntity> idToCompanyEntityMap = getIdToLegalEntityMap(companyEntityIds);
        return definitionIds.stream()
                .map(definitionId -> {
                    val entityInfo = getEntityInfoForDefinition(definitionId, companyDefsByDefinitionId.get(definitionId), idToCompanyEntityMap);
                    return entityInfo != null ? Map.entry(definitionId, entityInfo) : null;
                })
                .filter(Objects::nonNull) // Skip null entries
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private @Nullable TimeOffPolicyEntityInfo getEntityInfoForDefinition(Long definitionId,
                                                                         CompanyDefinitionEntity companyDefinition,
                                                                         Map<Long, CompanyOuterClass.LegalEntity> idToCompanyEntityMap) {
        if (companyDefinition == null) {
            log.warn("[getEntityInfoForDefinition] definition not found for id: {}", definitionId);
            return null;
        }
        val entityType = companyDefinition.getEntityType();
        val entityId = companyDefinition.getEntityId();
        String entityName = getEntityName(entityId, entityType, idToCompanyEntityMap);
        return buildTimeOffPolicyEntityInfo(entityId, entityType, entityName);
    }

    private String getEntityName(Long entityId, @Nullable EntityType entityType, Map<Long, CompanyOuterClass.LegalEntity> idToCompanyEntityMap) {
        if (entityType == null) {
            log.warn("[getEntityName] entity type is null for entity id : {}", entityId);
            return "";
        }
        return switch (entityType) {
            case COMPANY_ENTITY -> getCompanyEntityName(entityId, idToCompanyEntityMap);
            // Hardcode name for EOR_PARTNER_ENTITY
            case EOR_PARTNER_ENTITY ->TimeOffUtil.EOR_VIRTUAL_ENTITY_NAME;
        };
    }

    private String getCompanyEntityName(Long entityId, Map<Long, CompanyOuterClass.LegalEntity> idToCompanyEntityMap) {
        val companyEntity = idToCompanyEntityMap.get(entityId);
        return companyEntity != null ? companyEntity.getLegalName() : "";
    }


    private Map<Long, CompanyDefinitionEntity> getIdToCompanyDefinitionMap(Set<Long> definitionIds) {
        return companyDefinitionRepository.findByDefinitionIdIn(definitionIds).stream()
                .collect(Collectors.toMap(cd -> cd.getDefinition().getId(), Function.identity()));
    }

    private Map<Long, CompanyOuterClass.LegalEntity> getIdToLegalEntityMap(Set<Long> companyEntityIds) {
        if (companyEntityIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return companyServiceAdapter.getLegalEntitiesByIds(companyEntityIds).stream()
                .collect(Collectors.toMap(CompanyOuterClass.LegalEntity::getId, Function.identity()));
    }

    private Set<Long> getCompanyEntityIds(Collection<CompanyDefinitionEntity> companyDefinitions) {
        return companyDefinitions.stream()
                .filter(cd -> cd.getEntityType() == EntityType.COMPANY_ENTITY)
                .map(CompanyDefinitionEntity::getEntityId)
                .collect(Collectors.toSet());
    }

    private TimeOffPolicyEntityInfo buildTimeOffPolicyEntityInfo(Long id, EntityType type, String name) {
        return TimeOffPolicyEntityInfo.newBuilder()
                .id(id)
                .type(type)
                .name(name)
                .build();
    }

    private TimeoffDefinitionValidationEntity getValidationEntity(DefinitionEntity definition) {
        return definition.getValidations().stream().findFirst().get(); // NOSONAR:validation steps guaranteed that validation entity is present
    }

    private TimeoffDefinitionValidationEntity clone(TimeoffDefinitionValidationEntity validation) {
        return new TimeoffDefinitionValidationEntity(
                validation.getDefaultValue(),
                validation.getMinimumValue(),
                validation.getMinimumValue(),
                validation.getUnit(),
                validation.getAllowedContractStatuses(),
                validation.isUnlimitedLeavesAllowed()
        );
    }

    private boolean isAllocationCountUpdated(TimeoffDefinitionValidationEntity updatedValidation, TimeoffDefinitionValidationEntity oldValidation) {
        return isAllocatedCountUnitChanged(updatedValidation, oldValidation) || isAllocatedCountValueChanged(updatedValidation, oldValidation);
    }

    private void throwIfCannotUpdateDefinition(@NotNull TimeOffPolicyUpdateInput input, Long companyId, CompanyDefinitionEntity companyDefinition) {
        if (companyDefinition == null || companyDefinition.getDefinition() == null) {
            throw new ValidationException(String.format("Can not find definition with id : %d, company id : %d to update", input.getId(), companyId));
        }

        if (CollectionUtils.isEmpty(companyDefinition.getDefinition().getValidations())) {
            throw new DataCorruptionException("validation entity not found for definition id :" + input.getId());
        }

        if (companyDefinition.getDefinition().getConfigurations() == null) {
            throw new DataCorruptionException("configuration entity not found for definition id :" + input.getId());
        }

        if (StringUtils.isEmpty(input.getPolicyName())) {
            throw new ValidationException("Can not update definition to a empty name (null or '') for id : " + input.getId());
        }

        if (falseIfNull(input.getIsUnlimitedLeavesAllowed())) {
            // when unlimited leaves set to allowed -> allocated count and carry forward config doesn't matter
            return;
        }

        validateAllocatedCount(input.getAllocatedCount());
        validateCarryForwardConfigEditAction(companyDefinition, input.getCarryForwardConfig());
        validateCarryForwardConfigUpdateInput(input.getId(), input.getCarryForwardConfig(), input.getAllocatedCount(), companyDefinition.getCompanyId());
    }

    private void validateCarryForwardConfigEditAction(@NotNull CompanyDefinitionEntity oldCompanyDefinition, CarryForwardConfigInput carryForwardConfigInput) {
        /* carry forward config not allowed to change from enable to disabled */
        val carryForwardConfig = oldCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig();
        if ((carryForwardConfig != null && carryForwardConfig.getEnabled()) && !carryForwardConfigInput.getEnabled()) {
            throw new ValidationException("Disabling carry forward leaves not permitted for already carry forward enabled company definition id:" + oldCompanyDefinition.getId());
        }
    }

    /**
     * Validate whether a policy can be assigned to the given set of users
     * The definition can be assigned to EOR contract if it only increases the existing entitlements
     */
    public TimeOffPolicyUsersValidationResult validateDefinitionAssignment(TimeOffPolicyValidateUsersInput input, DgsDataFetchingEnvironment dfe) {
        val companyId = getCompanyIdFromCurrentUser();
        List<Long> accessibleContractIds = authorizationService.filterAccessibleContractIds(dfe, input.getContractIds()).stream().toList();
        throwIfAccessDenied(accessibleContractIds, input.getContractIds(), "User tried to validate policy assignment to un-authorized contracts. policy id = " + input.getTimeOffPolicyId());

        val companyDefinition = findNonDeletedCompanyDefinitionByDefinitionIdAndCompanyId(input.getTimeOffPolicyId(), companyId);
        val alreadyAssignedEntitlements = timeoffEntitlementDBORepository.findAllByDefinitionId(getDefinitionId(companyDefinition));
        val contractIdsToUnAssign = findContractIdsToUnassign(getContractIds(alreadyAssignedEntitlements), accessibleContractIds);
        /*
         * There can be entitlement assigned for input contract ids from same leave type but from different definition
         * We need to apply the MAX(assigned entitlement value, new entitlement value)
         * */
        val contractIdToExistingEntitlementMap = getContractIdToEntitlementMap(accessibleContractIds, companyDefinition.getTypeId());
        val idToContractMap = getIdToContractMap(ListUtils.union(accessibleContractIds, contractIdsToUnAssign));

        val partitionedContracts = accessibleContractIds.stream()
                .collect(Collectors.partitioningBy(
                        cid -> isValidContractForDefinitionAssignment(companyDefinition, idToContractMap, cid, contractIdToExistingEntitlementMap.get(cid)),
                        Collectors.mapping(cid -> Contract.newBuilder().id(cid).build(), Collectors.toList())
                ));
        val invalidContractsToUnAssign = contractIdsToUnAssign.stream()
                .filter(cid -> !isValidContractToUnAssign(idToContractMap.get(cid)))
                .map(cid -> Contract.newBuilder().id(cid).build())
                .collect(Collectors.toList());
        return TimeOffPolicyUsersValidationResult.newBuilder()
                .validContractsToAssign(partitionedContracts.get(true))
                .invalidContractsToAssign(partitionedContracts.get(false))
                .invalidContractsToUnassigned(invalidContractsToUnAssign)
                .build();
    }

    private Set<Long> getContractIds(List<TimeoffEntitlementDBO> entitlements) {
        return entitlements.stream()
                .map(TimeoffEntitlementDBO::contractId)
                .collect(Collectors.toSet());
    }

    @Transactional
    public TimeOffTypeDefinition assignOrUnassignUsers(DgsDataFetchingEnvironment dfe, TimeOffPolicyAssignUsersInput input) {
        val companyId = getCompanyIdFromCurrentUser();
        throwIfInvalidInput(input, companyId);

        List<Long> accessibleContractIds = authorizationService.filterAccessibleContractIds(dfe, input.getContractIds()).stream().toList();
        throwIfAccessDenied(accessibleContractIds, input.getContractIds(), "User tried to assign policies to un-authorized contracts. policy id = " + input.getTimeOffPolicyId());

        log.info("[assignOrUnassignUsers] assigning definition id : {} for contract ids (size = {}) : {}", input.getTimeOffPolicyId(), accessibleContractIds.size(), accessibleContractIds);
        val companyDefinition = findNonDeletedCompanyDefinitionByDefinitionIdAndCompanyId(input.getTimeOffPolicyId(), companyId);
        val timeOffTypeDBO = timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId());
        val existingRulesForDefinition = timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinition.getId()));
        /* Load all entitlements set from this definition */
        val alreadyAssignedContractIdToEntitlementMap = getAlreadyAssignedEntitlementsMap(getDefinitionId(companyDefinition));
        /* Load existing entitlements for the input contract ids which comes from leave type of this definition */
        val contractIdToExistingEntitlementMap = getContractIdToEntitlementMap(accessibleContractIds, companyDefinition.getTypeId());
        val contractIdsToUnassign = findContractIdsToUnassign(alreadyAssignedContractIdToEntitlementMap.keySet(), accessibleContractIds);
        val idToContractMap = getIdToContractMap(ListUtils.union(accessibleContractIds, contractIdsToUnassign));

        unassignEntitlements(contractIdsToUnassign, idToContractMap, companyDefinition.getTypeId());
        val updatedEntitlements = assignEntitlements(Set.copyOf(accessibleContractIds), contractIdToExistingEntitlementMap, companyDefinition, idToContractMap, timeOffTypeDBO);

        deleteExistingRules(companyDefinition.getId(), existingRulesForDefinition);
        saveTimeOffDefinitionRules(input, companyId, companyDefinition);

        TimeOffTypeDefinition definition = timeOffTypeDefinitionMapper.map(companyDefinition, timeOffTypeDBO);
        saveEntitlementsAndUpdateSummary(updatedEntitlements, companyDefinition, idToContractMap);

        timeoffNotificationHelper.sendTimeoffPolicyAssignmentNotification(input.getTimeOffPolicyId(), definition.getPolicyName(), updatedEntitlements.size(), input.getTimeOffPolicyRule());
        return definition;
    }

    private void throwIfAccessDenied(List<Long> accessibleContractIds, List<Long> inputContractIds, String message) {
        if (accessibleContractIds.size() != inputContractIds.size()) {
            val accessDeniedIds = inputContractIds.stream()
                    .filter(id -> !accessibleContractIds.contains(id))
                    .toList();
            throw new ValidationException(message + " access denied ids : " + accessDeniedIds);
        }
    }

    private Map<Long, TimeoffEntitlementDBO> getAlreadyAssignedEntitlementsMap(Long definitionId) {
        return timeoffEntitlementDBORepository.findAllByDefinitionId(definitionId).stream()
                .collect(Collectors.toMap(
                        TimeoffEntitlementDBO::contractId,
                        Function.identity()
                ));
    }

    private List<Long> findContractIdsToUnassign(Set<Long> alreadyAssignContractIds, List<Long> newContractIdListToAssign) {
        // To unassign we will delete entitlement entry as well as all the summaries
        return alreadyAssignContractIds.stream()
                .filter(contractId -> !newContractIdListToAssign.contains(contractId))
                .toList();
    }

    private void deleteExistingRules(Long companyDefinitionId, List<TimeOffDefinitionRuleDBO> existingRulesForDefinition) {
        if (!CollectionUtils.isEmpty(existingRulesForDefinition)) {
            log.info("[deleteExistingRules] deleting existing rules for company definition id {}", companyDefinitionId);
            timeOffDefinitionRuleService.deleteRules(existingRulesForDefinition);
        }
    }

    private void unassignEntitlements(List<Long> contractIdsToUnassign, Map<Long, ContractOuterClass.Contract> idToContractMap, Long typeId) {
        // To unassign we will delete entitlement entry as well as all the summaries
        val validIdsToUnassign =  contractIdsToUnassign.stream()
                .filter(cid -> isValidContractToUnAssign(idToContractMap.get(cid)))
                .toList();
        validIdsToUnassign.forEach(cid -> {
            timeoffEntitlementDBORepository.deleteAllByContractIdAndTypeId(cid, typeId);
            timeoffSummaryService.deleteSummaryByContractIdAndTypeId(cid, typeId);
        });
    }

    public TimeOffPolicyAssignmentDetailsResult findDefinitionAssignmentDetails(Long definitionId) {
        val companyId = getCompanyIdFromCurrentUser();
        val companyDefinition = findNonDeletedCompanyDefinitionByDefinitionIdAndCompanyId(definitionId, companyId);
        val definitionRuleDBOs = timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinition.getId()));
        val assignedContractIds = findAssignedContractIdsForDefinition(definitionId);


        return TimeOffPolicyAssignmentDetailsResult.newBuilder()
                .rule(timeOffDefinitionRuleMapper.map(definitionRuleDBOs))
                .assignedContracts(assignedContractIds.stream()
                        .map(cid -> Contract.newBuilder().id(cid).build())
                        .collect(Collectors.toList()))
                .build();
    }

    private static boolean falseIfNull(Boolean boolVar) {
        return boolVar != null && boolVar;
    }

    private boolean isAllocatedCountValueChanged(@NotNull TimeoffDefinitionValidationEntity updatedValidation, @NotNull TimeoffDefinitionValidationEntity oldValidationEntity) {

        return Double.compare(oldValidationEntity.getDefaultValue(), updatedValidation.getDefaultValue()) != 0;
    }

    private boolean isAllocatedCountUnitChanged(@NotNull TimeoffDefinitionValidationEntity updatedValidation, @NotNull TimeoffDefinitionValidationEntity oldValidation) {
        return oldValidation.getUnit() != updatedValidation.getUnit();
    }

    private boolean isFilterEmpty(CompanyTimeOffPolicyFilter filter) {
        return filter == null || (filter.getTimeOffPolicyIds() == null && filter.getTimeOffTypeIds() == null && filter.getEntityIds() == null);
    }

    private void updateEntitlementsOnPolicyUpdate(@NotNull CompanyDefinitionEntity companyDefinition, TimeoffTypeDBO timeOffTypeDBO) {
        val contractIdToEntitlementMap = timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(companyDefinition.getDefinition().getId()))
                .stream()
                .collect(Collectors.toMap(
                                TimeoffEntitlementDBO::contractId,
                                Function.identity()));

        val assignedContractIds = contractIdToEntitlementMap.keySet();
        val idToContractMap = getIdToContractMap(assignedContractIds);

        if (!CollectionUtils.isEmpty(assignedContractIds)) {
            log.info("updating entitlements and summary for company definition id : {} and assigned contract ids : {}", companyDefinition.getId(), assignedContractIds);
            val updatedEntitlements = assignEntitlements(assignedContractIds,
                    contractIdToEntitlementMap, companyDefinition, idToContractMap, timeOffTypeDBO);
            saveEntitlementsAndUpdateSummary(updatedEntitlements, companyDefinition, idToContractMap);
        }
    }

    private void saveEntitlementsAndUpdateSummary(List<TimeoffEntitlementDBO> updatedEntitlements,
                                                  CompanyDefinitionEntity companyDefinition,
                                                  Map<Long, ContractOuterClass.Contract> idToContractMap) {
        if (!CollectionUtils.isEmpty(updatedEntitlements)) {
            timeoffEntitlementDBORepository.saveAllAndFlush(updatedEntitlements);
            timeoffSummaryService.updateSummaryOnDefinitionAssignment(updatedEntitlements, idToContractMap, companyDefinition);
        }
    }

    private Map<Long, ContractOuterClass.Contract> getIdToContractMap(Collection<Long> contractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return Map.of();
        }
        return contractServiceAdapter.getContractsByIdsAnyStatus(contractIds)
                .stream()
                .collect(Collectors.toMap(ContractOuterClass.Contract::getId, Function.identity()));
    }

    private List<TimeoffEntitlementDBO> assignEntitlements(Set<Long> contractIds,
                                                           Map<Long, TimeoffEntitlementDBO> contractIdToEntitlementMap,
                                                           @NotNull CompanyDefinitionEntity companyDefinition,
                                                           Map<Long, ContractOuterClass.Contract> idToContractMap,
                                                           TimeoffTypeDBO timeOffTypeDBO) {
        if (CollectionUtils.isEmpty(contractIds)) {
            log.info("[assignEntitlements] No contract ids to assign entitlements for company definition id : {}", companyDefinition.getId());
            return List.of();
        }
        val validation = Optional.of(companyDefinition)
                .map(CompanyDefinitionEntity::getDefinition)
                .map(DefinitionEntity::getValidations)
                .flatMap(validations -> validations.stream().findFirst())
                .orElseThrow(() -> new ValidationException(String.format(
                        "Company definition id : %d do not have validations object",
                        companyDefinition.getId())));

        return contractIds.stream()
                .filter(cid -> isValidContractForDefinitionAssignment(companyDefinition, idToContractMap, cid, contractIdToEntitlementMap.get(cid)))
                .map(cid -> {
                    var entitlementDBO = contractIdToEntitlementMap.getOrDefault(cid, TimeoffEntitlementDBO.builder().build());
                    entitlementDBO.contractId(cid);
                    entitlementDBO.type(timeOffTypeDBO);
                    entitlementDBO.value(validation.isUnlimitedLeavesAllowed() ? 365.0 : validation.getDefaultValue());
                    entitlementDBO.unit(validation.getUnit());
                    entitlementDBO.definition(companyDefinition.getDefinition());
                    return entitlementDBO;
                }).collect(Collectors.toList());
    }

    private void throwIfInvalidInput(TimeOffPolicyAssignUsersInput input, Long companyId) {
        if (input.getTimeOffPolicyRule() == null) {
            throw new IllegalArgumentException(String.format("Can not assign definition for users with empty rule for company id : %d", companyId));
        }
    }

    private List<Long> findAssignedContractIdsForDefinition(Long definitionId) {
        return timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(definitionId)).stream()
                .filter(ent -> !ent.value().equals(0.0))
                .map(TimeoffEntitlementDBO::contractId)
                .collect(Collectors.toList());
    }

    private void saveTimeOffDefinitionRules(TimeOffPolicyAssignUsersInput input, Long companyId, CompanyDefinitionEntity companyDefinition) {
        val ruleType = input.getTimeOffPolicyRule().getType();
        List<TimeOffDefinitionRuleDBO> timeOffDefinitionRules = new ArrayList<>();
        switch (ruleType) {
            case ALL:
                timeOffDefinitionRules.add(createTimeOffDefinitionRuleForAll(companyId, companyDefinition, currentUser.getContext().getId()));
                break;
            case BY_CONDITION:
                timeOffDefinitionRules = input.getTimeOffPolicyRule().getConditions().stream()
                        .map(ci -> createTimeOffDefinitionRuleForCondition(ci, companyId, companyDefinition, currentUser.getContext().getId()))
                        .collect(Collectors.toList());
                break;
            default:
                throw new ValidationException(String.format("Invalid rule type : %s for assign definition for company id : %d", ruleType, companyId));
        }
        timeOffDefinitionRuleService.saveRules(timeOffDefinitionRules);
    }

    private TimeOffDefinitionRuleDBO createTimeOffDefinitionRuleForAll(Long companyId, CompanyDefinitionEntity companyDefinition, Long createdBy) {
        return TimeOffDefinitionRuleDBO.builder()
                .companyId(companyId)
                .ruleType(RuleType.ALL)
                .companyDefinition(companyDefinition)
                .createdBy(createdBy)
                .createdOn(LocalDateTime.now())
                .build();
    }

    public CompanyDefinitionEntity findNonDeletedCompanyDefinitionByDefinitionIdAndCompanyId(Long definitionId, Long companyId) {
        return companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(definitionId, companyId)
                .orElseThrow(() -> new ValidationException(String.format("Can not find definition id: %d for company id : %d",
                        definitionId, companyId)));
    }

    public Map<Long, Integer> getDefinitionIdToAssignedEmployeeCountMap(Set<Long> definitionIds) {
        return  timeoffEntitlementDBORepository.findAllByDefinitionIdIn(definitionIds).stream()
                .filter(ent -> ent.value() != null && !ent.value().equals(0.0))
                .collect(Collectors.toMap(
                        TimeoffEntitlementDBO::definitionId,
                        ent -> 1,
                        Integer::sum
                ));

    }

    private Map<Long, TimeoffEntitlementDBO> getContractIdToEntitlementMap(List<Long> contractIds, Long typeId) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return Map.of();
        }
        /* one contract can have one entitlement for one timeoff definition*/
        return timeoffEntitlementDBORepository
                .findAllByContractIdInAndTypeId(contractIds, typeId)
                .stream()
                .collect(Collectors.toMap(
                                TimeoffEntitlementDBO::contractId,
                                Function.identity()
                        )
                );
    }

    private TimeOffDefinitionRuleDBO createTimeOffDefinitionRuleForCondition(@NotNull ConditionInput conditionInput,
                                                                            Long companyId,
                                                                            CompanyDefinitionEntity companyDefinition,
                                                                            Long createdBy) {
        return TimeOffDefinitionRuleDBO.builder()
                .ruleType(RuleType.BY_CONDITION)
                .companyId(companyId)
                .companyDefinition(companyDefinition)
                .conditionKey(conditionInput.getKey())
                .conditionOperator(conditionInput.getOperator())
                .conditionValues(conditionInput.getValues().toArray(String[]::new))
                .createdBy(createdBy)
                .createdOn(LocalDateTime.now())
                .build();
    }

    private boolean isValidContractForDefinitionAssignment(@NotNull CompanyDefinitionEntity companyDefinition, Map<Long, ContractOuterClass.Contract> idToContractMap, Long contractId, @Nullable TimeoffEntitlementDBO timeoffEntitlementDBO) {
        val contract = idToContractMap.get(contractId);
        if (contract == null) {
            log.error("[isValidContractForDefinitionAssignment] contract id {}, not found. Hence not a valid contract to assign",contractId);
            return false;
        }
        if (contract.getCompanyId() != companyDefinition.getCompanyId()) {
            log.error("[isValidContractForDefinitionAssignment] company id {}, try to validate a contract id : {}, owned by another company",
                    companyDefinition.getCompanyId(), contract.getId());
            return false;
        }
        if (contract.getType() == ContractOuterClass.ContractType.FREELANCER) {
            log.error("[isValidContractForDefinitionAssignment] contract id : {} is a freelancer. definition assignment not supported for freelancer", contract.getId());
            return false;
        }

        if (contract.getType() != ContractOuterClass.ContractType.EMPLOYEE) {
            return true; // No validations for HRIS employees
        }

        if (timeoffEntitlementDBO == null) {
            return true; // no entitlements to validate
        }
        val newEntitledValue = getEntitledValue(companyDefinition);
        if (timeoffEntitlementDBO.value() > newEntitledValue) {
            log.info("[validateDefinitionForAllContracts] can not assign definition id : {}, typeId : {}, entitled count {} since contract id : {} already has : {} {}",
                    companyDefinition.getDefinition().getId(), companyDefinition.getTypeId(), newEntitledValue, contract.getId(), timeoffEntitlementDBO.value(), timeoffEntitlementDBO.unit());
            return false;
        }
        return true;
    }

    private boolean isValidContractToUnAssign (ContractOuterClass.Contract contract) {
        return contract != null && contract.getType() != ContractOuterClass.ContractType.EMPLOYEE;
    }

    private Double getEntitledValue(TimeOffDefinitionEntity timeOffDefinition) {
        return timeOffDefinition.getDefinition().getValidations().stream()
                .findFirst()
                .map(TimeoffDefinitionValidationEntity::getDefaultValue)
                .orElseThrow(() -> new ValidationException(String.format("Can not get entitled value from definition id : %d",
                        timeOffDefinition.getDefinition().getId()))
                );
    }

    private DefinitionEntity createDefinitionFromInput(TimeOffPolicyCreateInput input) {
        DefinitionEntity definition = new DefinitionEntity();
        definition.setName(input.getPolicyName());
        definition.setCountryCode(null);
        definition.setStateCode(null);
        definition.setRequired(false);
        definition.setDescription(null);
        definition.setClause(null);
        definition.setBasis("ANNUAL");
        definition.setValidations(buildTimeOffDefinitionValidationEntity(input));
        definition.setConfigurations(buildTimeOffDefinitionConfigEntity(input.getCarryForwardConfig(), input.getFutureLeaveConfig(), falseIfNull(input.getIsUnlimitedLeavesAllowed())));
        definition.setStatus(TimeOffTypeDefinitionStatus.ACTIVE);

        return definition;
    }

    private Set<TimeoffDefinitionValidationEntity> buildTimeOffDefinitionValidationEntity(@NotNull TimeOffPolicyCreateInput input) {
        return Set.of(
                new TimeoffDefinitionValidationEntity(
                        getDefaultValue(input.getAllocatedCount(), falseIfNull(input.getIsUnlimitedLeavesAllowed())), // default value
                        falseIfNull(input.getIsUnlimitedLeavesAllowed()) ? Constant.UNLIMITED_LEAVES : null, // minimum value
                        null, // maximum value
                        getUnit(input.getAllocatedCount(), falseIfNull(input.getIsUnlimitedLeavesAllowed())),
                        List.of(ContractStatus.ONBOARDING.name(), ContractStatus.OFFBOARDING.name(), ContractStatus.ACTIVE.name()),
                        input.getIsUnlimitedLeavesAllowed() // is unlimited leaves allowed
                )
        );
    }


    private Double getDefaultValue(TimeOffDurationInput input, boolean isUnlimitedLeavesAllowed) {
        if (isUnlimitedLeavesAllowed) {
            return 365.0;
        }
        return Optional.ofNullable(input.getValue()).orElse(0.0);
    }

    private TimeOffUnit getUnit(@NotNull TimeOffDurationInput input, boolean isUnlimitedLeavesAllowed) {
        if (isUnlimitedLeavesAllowed) {
            return TimeOffUnit.DAYS;
        }
        return Optional.ofNullable(input.getUnit()).orElse(TimeOffUnit.DAYS);
    }

    private TimeoffDefinitionConfigEntity buildTimeOffDefinitionConfigEntity(@NotNull CarryForwardConfigInput carryForwardConfigInput,
                                                                             FutureLeaveConfigInput futureLeaveConfigInput,
                                                                             boolean isUnlimitedLeavesAllowed) {
        val allocationConfig = new AllocationConfigEntity("ANNUALLY", !isUnlimitedLeavesAllowed);
        val carryForwardConfig = buildCarryForwardConfigEntity(carryForwardConfigInput, isUnlimitedLeavesAllowed);
        val futureLeaveConfig = buildFutureLeaveConfigEntity(futureLeaveConfigInput);
        return new TimeoffDefinitionConfigEntity(allocationConfig, carryForwardConfig, futureLeaveConfig);
    }

    private CarryForwardConfigEntity buildCarryForwardConfigEntity(CarryForwardConfigInput input, boolean isUnlimitedLeavesAllowed) {
        return new CarryForwardConfigEntity(
                isCarryForwardEnabled(input, isUnlimitedLeavesAllowed),
                getCarryForwardMinForEligibility(input, isUnlimitedLeavesAllowed),
                getCarryForwardMaxLimit(input, isUnlimitedLeavesAllowed),
                getCarryForwardExpiry(input, isUnlimitedLeavesAllowed)
        );
    }

    private FutureLeaveConfigEntity buildFutureLeaveConfigEntity(@Nullable FutureLeaveConfigInput input) {
        if (input == null) {
            log.info("[buildFutureLeaveConfigEntity] future leave config input is null, Hence creating default future leave config");
            return new FutureLeaveConfigEntity();
        }
        val lapsableLeaveConfig = buildLapsableLeaveConfig(input);
        return new FutureLeaveConfigEntity(input.getEnabled(), input.getFutureYearsAllowed(), lapsableLeaveConfig);
    }

    private LapsableLeaveConfigEntity buildLapsableLeaveConfig(@NotNull FutureLeaveConfigInput input) {
        if (!TimeOffUtil.falseIfNull(input.getEnabled()) || input.getLapsableLeaveConfig() == null) {
            log.info("[buildLapsableLeaveConfig] future leaves config disabled. Hence disabling lapsable leaves as well : {}", input);
            // if future leaves not enabled, then lapsable leave config also should be disabled
            return new LapsableLeaveConfigEntity();
        }
        val expiry = getLapsableExpiry();
        return new LapsableLeaveConfigEntity(input.getLapsableLeaveConfig().getEnabled(), expiry);
    }

    private LapsableExpiryEntity getLapsableExpiry() {
        // We are not supporting lapsable leave expiry as of now. Hence, setting 1 year by default
        return new LapsableExpiryEntity(1.0, TimeOffUnit.YEARS);
    }

    private boolean isCarryForwardEnabled(CarryForwardConfigInput input, boolean isUnlimitedLeavesAllowed) {
        if (isUnlimitedLeavesAllowed) {
            return false;
        }
        return falseIfNull(input.getEnabled());
    }

    private @Nullable CarryForwardLimitEntity getCarryForwardMinForEligibility(CarryForwardConfigInput input, boolean isUnlimitedLeavesAllowed) {
        if (isUnlimitedLeavesAllowed || !falseIfNull(input.getEnabled())) {
            return null;
        }
        return new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 0.0, TimeOffUnit.DAYS);
    }

    private static @Nullable CarryForwardLimitEntity getCarryForwardMaxLimit(CarryForwardConfigInput input, boolean isUnlimitedLeavesAllowed) {
        if (isUnlimitedLeavesAllowed || !falseIfNull(input.getEnabled())) {
            return null;
        }
        return new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED,
                input.getMaxLimit().getValue(),
                input.getMaxLimit().getUnit());
    }

    private @Nullable CarryForwardExpiryEntity getCarryForwardExpiry(CarryForwardConfigInput input, boolean isUnlimitedLeavesAllowed) {
        if (isUnlimitedLeavesAllowed || !falseIfNull(input.getEnabled()) || !falseIfNull(input.getExpiryConfig().getEnabled())) {
            return null;
        }
        return new CarryForwardExpiryEntity(
                input.getExpiryConfig().getExpiry().getValue(),
                input.getExpiryConfig().getExpiry().getUnit());
    }

    private void throwIfOpsUserCannotDeleteDefinition(CompanyDefinitionEntity companyDefinition) {
        throwIfDefinitionHasAssignments(companyDefinition);
    }

    private void throwIfCompanyUserCannotDeleteDefinition(CompanyDefinitionEntity companyDefinition) {
        val companyId = getCompanyIdFromCurrentUser();
        if (!companyDefinition.getCompanyId().equals(companyId)) {
            throw new ValidationException(String.format("company user (user_id : %s, company_id : %s) try to delete company definition id : %s owned by another company (id : %s)",
                    currentUser.getContext().getId(), companyId, companyDefinition.getId(), companyDefinition.getCompanyId()));
        }
        throwIfDefinitionHasAssignments(companyDefinition);
    }

    private void throwIfDefinitionHasAssignments(CompanyDefinitionEntity companyDefinition) {
        val assignedContractIds = findAssignedContractIdsForDefinition(companyDefinition.getDefinition().getId());
        if (!CollectionUtils.isEmpty(assignedContractIds)) {
            throw new ValidationException(String.format("Can not delete company definition id : %d since it has active assignments.",
                    companyDefinition.getId()));
        }
    }

    private List<TimeOffTypeDefinition> mapToGraph(List<CompanyDefinitionEntity> companyDefinitions) {
        val typeIds = companyDefinitions.stream()
                .map(TimeOffDefinitionEntity::getTypeId)
                .collect(Collectors.toSet());
        val idToTimeOffTypeDBOMap = timeoffTypeService.findAllTypeDBOsByIds(typeIds)
                .stream()
                .collect(Collectors.toMap(TimeoffTypeDBO::id, Function.identity()));
        return timeOffTypeDefinitionMapper.map(companyDefinitions, idToTimeOffTypeDBOMap);
    }

    private TimeOffTypeDefinition mapToGraph(CompanyDefinitionEntity companyDefinition, @Nullable TimeoffTypeDBO timeoffTypeDBO) {
        if (timeoffTypeDBO == null) {
            timeoffTypeDBO = timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId());
        }
        return timeOffTypeDefinitionMapper.map(companyDefinition, timeoffTypeDBO);
    }

    private void throwIfCannotCreateCompanyDefinition(@NotNull TimeOffPolicyCreateInput input, @NotNull TimeoffTypeDBO timeOffTypeDBO, Long companyId) {
        throwIfDefinitionCreateInputIsInvalid(companyId, input);

        if (timeOffTypeDBO.status() == TimeOffTypeStatus.DELETED) {
            throw new ValidationException(String.format("Can not create a definition from deleted type id %d", input.getTimeOffTypeId()));
        }
        if (timeOffTypeDBO.companyId() != null && !timeOffTypeDBO.companyId().equals(companyId)) {
            throw new ValidationException(String.format("Can not create a definition for company id : %d from type id : %d, " +
                    "since type owned by company id : %d", companyId, input.getTimeOffTypeId(), timeOffTypeDBO.companyId()));
        }
    }

    private void throwIfDefinitionCreateInputIsInvalid(Long companyId, TimeOffPolicyCreateInput timeOffPolicyCreateInput) {
        if (timeOffPolicyCreateInput == null) {
            throw new ValidationException(String.format("Can not create company definition for company id : %d with timeOffPolicyCreateInput = null", companyId));
        }
        if (Objects.isNull(timeOffPolicyCreateInput.getIsUnlimitedLeavesAllowed()) || timeOffPolicyCreateInput.getIsUnlimitedLeavesAllowed().equals(false)) {
            validateAllocatedCount(timeOffPolicyCreateInput.getAllocatedCount());
        }
        validateCarryForwardConfigUpdateInput(timeOffPolicyCreateInput, companyId);
        validateFutureLeaveConfigCreationInput(timeOffPolicyCreateInput.getFutureLeaveConfig(), companyId);
    }

    private void validateAllocatedCount(TimeOffDurationInput timeOffDurationInput) {
        if (timeOffDurationInput.getUnit() == TimeOffUnit.DAYS && timeOffDurationInput.getValue() % 0.5 != 0) {
            throw new ValidationException(String.format("Allocated days should be multiple of 0.5, but given : %s", timeOffDurationInput.getValue()));
        }
        if (timeOffDurationInput.getUnit() != TimeOffUnit.DAYS && timeOffDurationInput.getValue() % 1 != 0) {
            throw new ValidationException(String.format("Allocated count should be a integer, but given : %s %s", timeOffDurationInput.getValue(), timeOffDurationInput.getUnit()));
        }
    }

    private void validateCarryForwardConfigUpdateInput(TimeOffPolicyCreateInput input, Long companyId) {
        if (input.getCarryForwardConfig() != null && input.getCarryForwardConfig().getEnabled()) {
            val allocatedCount = TimeOffUtil.getDays(input.getAllocatedCount().getValue(), input.getAllocatedCount().getUnit());
            val maxCarryForwardLeaves = TimeOffUtil.getDays(input.getCarryForwardConfig().getMaxLimit().getValue(), input.getCarryForwardConfig().getMaxLimit().getUnit());

            if (maxCarryForwardLeaves > allocatedCount) {
                throw new ValidationException(String.format("Can not create a definition for company id : %d from type id : %d, " +
                        "since given carry forward max leaves (%s) greater than allocated leaves (%s)", companyId, input.getTimeOffTypeId(), maxCarryForwardLeaves, allocatedCount));
            }
        }
    }

    private void validateFutureLeaveConfigCreationInput(FutureLeaveConfigInput futureLeaveConfigInput, Long companyId) {
        if (futureLeaveConfigInput != null
                && futureLeaveConfigInput.getEnabled()
                && futureLeaveConfigInput.getFutureYearsAllowed() > TimeOffUtil.MAX_FUTURE_LEAVES_ALLOWED) {

            throw new ValidationException(String.format(
                    "Can not create a definition for company id : %d since given max future leaves allowed years (%s) are greater than %s",
                    companyId, futureLeaveConfigInput.getFutureYearsAllowed(), TimeOffUtil.MAX_FUTURE_LEAVES_ALLOWED
            ));
        }
    }

    private void validateCarryForwardConfigUpdateInput(Long companyDefinitionId, CarryForwardConfigInput carryForwardConfigInput, TimeOffDurationInput allocationInput, Long companyId) {
        if (carryForwardConfigInput != null && carryForwardConfigInput.getEnabled()) {
            val allocatedCount = TimeOffUtil.getDays(allocationInput.getValue(), allocationInput.getUnit());
            val maxCarryForwardLeaves = TimeOffUtil.getDays(carryForwardConfigInput.getMaxLimit().getValue(), carryForwardConfigInput.getMaxLimit().getUnit());

            if (maxCarryForwardLeaves > allocatedCount) {
                throw new ValidationException(String.format("Invalid carry forward config input for definition id : %d, company id : %d, " +
                        "carry forward max leaves (%s days) greater than allocated leaves (%s days)", companyDefinitionId, companyId, maxCarryForwardLeaves, allocatedCount));
            }
        }
    }

    private List<CountryDefinitionEntity> findAllCountryDefinitions(ContractOuterClass.Contract contract) {
        if (StringUtils.isEmpty(contract.getCountry())) {
            throw new DataCorruptionException("country is not found for the contract");
        }

        CountryCode countryCode = CountryCode.valueOf(contract.getCountry());
        val stateCode = contract.hasCountryStateCode() ? contract.getCountryStateCode() : null;
        return countryDefinitionService.getAllCountryDefinitionsByCountry(countryCode, stateCode);
    }

    private Map<Long, CompanyDefinitionEntity> groupCdsByTypeId(ContractOuterClass.Contract contract,
                                                                List<CompanyDefinitionEntity> companyDefinitions,
                                                                Member member,
                                                                Map<Long, List<TimeOffDefinitionRuleDBO>> companyDefinitionIdToRulesMap,
                                                                @Nullable Long entityId) {
        if (CollectionUtils.isEmpty(companyDefinitions)) {
            log.info("[groupCdsByTypeId] No company definitions found for contract id : {}", contract.getId());
            return Map.of();
        }

        val ruleResolverInput = populateRuleResolver(contract, member, entityId);
        return companyDefinitions.stream()
                .filter(Objects::nonNull)
                .filter(cd -> isEligibleDefinitionForContract(contract, ruleResolverInput, cd, companyDefinitionIdToRulesMap))
                .collect(Collectors.toMap(
                        TimeOffDefinitionEntity::getTypeId,
                        Function.identity(),
                        this::getMostRecent));
    }

    public Map<Long, CountryDefinitionEntity> getTypeIdToEligibleCountryDefinitionMap(ContractOuterClass.Contract contract) {
        List<CountryDefinitionEntity> countryDefinitions = findAllCountryDefinitions(contract);
        return getTypeIdToCountryDefinitionMapForContract(contract, countryDefinitions);
    }

    private Map<Long, CountryDefinitionEntity> getTypeIdToCountryDefinitionMapForContract(ContractOuterClass.Contract contract, List<CountryDefinitionEntity> countryDefinitions) {
        return countryDefinitions.stream()
                .filter(this::isValidCountryDefinition)
                .filter(cd -> isAllowedContractStatus(contract, cd.getDefinition()))
                .collect(Collectors.toMap(
                        CountryDefinitionEntity::getTypeId,
                        Function.identity(),
                        (existing, replacement) ->
                                (replacement.getUpdatedOn() != null &&
                                        replacement.getUpdatedOn().isAfter(existing.getUpdatedOn())) ? replacement : existing
                ));
    }

    public Map<Long, TimeOffDefinitionEntity> findAllDefinitionsByIds(Long companyId, Set<Long> definitionIds) {
        Map<Long, CompanyDefinitionEntity> definitionIdToCompanyDefinitionMap = getDefinitionIdToCompanyDefinitionMap(companyId, definitionIds);
        Map<Long, TimeOffDefinitionEntity> definitionIdToCountryDefinitionMap = getDefinitionIdToCountryDefinitionMap(definitionIds, definitionIdToCompanyDefinitionMap);

        Map<Long, TimeOffDefinitionEntity> resultMap = new HashMap<>();
        for (Long definitionId : definitionIds) {
            if (definitionIdToCompanyDefinitionMap.containsKey(definitionId)) {
                resultMap.put(definitionId, definitionIdToCompanyDefinitionMap.get(definitionId));
            } else if (definitionIdToCountryDefinitionMap.containsKey(definitionId)) {
                resultMap.put(definitionId, definitionIdToCountryDefinitionMap.get(definitionId));
            }
        }

        return resultMap;
    }

    private Map<Long, TimeOffDefinitionEntity> getDefinitionIdToCountryDefinitionMap(Set<Long> definitionIds, Map<Long, CompanyDefinitionEntity> definitionIdToCompanyDefinitionMap) {
        val notFoundDefinitionIds = definitionIds.stream()
                .filter(definitionId -> !definitionIdToCompanyDefinitionMap.containsKey(definitionId))
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(notFoundDefinitionIds)) {
            return Collections.emptyMap();
        }
        return countryDefinitionService.getAllCountryDefinitionsByDefinitionIds(notFoundDefinitionIds)
                .stream()
                .collect(Collectors.toMap(
                        cd -> cd.getDefinition().getId(),
                        Function.identity()
                ));
    }

    private Map<Long, CompanyDefinitionEntity> getDefinitionIdToCompanyDefinitionMap(Long companyId, Set<Long> definitionIds) {
        return companyDefinitionRepository.findAllNonDeletedByDefinitionIdInAndCompanyId(definitionIds, companyId)
                .stream()
                .collect(Collectors.toMap(
                        cd -> cd.getDefinition().getId(),
                        Function.identity()
                ));
    }

    private List<TimeOffDefinitionEntity> findAllEligibleDefinitions(ContractOuterClass.Contract contract,
                                                                     Map<Long, CompanyDefinitionEntity> typeToEligibleCompanyDefinitionMap,
                                                                     Map<Long, CountryDefinitionEntity> typeToCountryDefinitionMap) {
        Set<Long> allEligibleTypes = Stream.concat(
                        typeToEligibleCompanyDefinitionMap.keySet().stream(),
                        typeToCountryDefinitionMap.keySet().stream())
                .collect(Collectors.toSet());

        List<TimeOffDefinitionEntity> eligibleDefinitions = new ArrayList<>();
        allEligibleTypes.forEach(typeId -> {
            val companyDefinition = typeToEligibleCompanyDefinitionMap.get(typeId);
            val countryDefinition = typeToCountryDefinitionMap.get(typeId);
            if (companyDefinition != null && countryDefinition != null) {
                eligibleDefinitions.add(findEligibleDefinition(contract, companyDefinition, countryDefinition));
            } else if (countryDefinition != null) {
                eligibleDefinitions.add(countryDefinition);
            } else if (companyDefinition != null) {
                eligibleDefinitions.add(companyDefinition);
            }

        });

        return eligibleDefinitions;
    }

    private Map<Long, TimeOffDefinitionEntity> getTypeIdToEligibleDefinitionMap(ContractOuterClass.Contract contract,
                                                                     Map<Long, CompanyDefinitionEntity> typeToEligibleCompanyDefinitionMap,
                                                                     Map<Long, CountryDefinitionEntity> typeToCountryDefinitionMap) {
        Set<Long> allEligibleTypes = Stream.concat(
                        typeToEligibleCompanyDefinitionMap.keySet().stream(),
                        typeToCountryDefinitionMap.keySet().stream())
                .collect(Collectors.toSet());

        Map<Long, TimeOffDefinitionEntity> eligibleDefinitions = new HashMap<>();
        allEligibleTypes.forEach(typeId -> {
            val companyDefinition = typeToEligibleCompanyDefinitionMap.get(typeId);
            val countryDefinition = typeToCountryDefinitionMap.get(typeId);
            if (companyDefinition != null && countryDefinition != null) {
                eligibleDefinitions.put(typeId, findEligibleDefinition(contract, companyDefinition, countryDefinition));
            } else if (countryDefinition != null) {
                eligibleDefinitions.put(typeId, countryDefinition);
            } else if (companyDefinition != null) {
                eligibleDefinitions.put(typeId, companyDefinition);
            }

        });

        return eligibleDefinitions;
    }

    private Map<Long, List<TimeOffDefinitionRuleDBO>> getCompanyDefinitionIdToRulesMap(Collection<CompanyDefinitionEntity> companyDefinitions) {
        if (CollectionUtils.isEmpty(companyDefinitions)) {
            return Map.of();
        }
        return timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(getCompanyDefinitionIds(companyDefinitions))
                .stream()
                .collect(Collectors.groupingBy(
                        TimeOffDefinitionRuleDBO::companyDefinitionId,
                        Collectors.toList()));
    }

    private RuleResolverInput populateRuleResolver(@NotNull ContractOuterClass.Contract contract, @NotNull Member member, @Nullable Long entityId) {
        return RuleResolverInput.builder()
                .country(contract.getCountry())
                .gender(member.getGender().name())
                .name(member.getFirstName() + " " + member.getLastName())
                .companyId(contract.getCompanyId())
                .entityId(entityId == null ? "" : String.valueOf(entityId))
                .build();
    }

    private Map<Long, Member> getIdToMemberMap(Set<Long> memberIds) {
        return memberServiceAdapter.getMembers(memberIds).stream()
                .collect(Collectors.toMap(Member::getId, Function.identity()));
    }

    private Set<Long> memberIds(Collection<ContractOuterClass.Contract> contracts) {
        return contracts.stream()
                .map(ContractOuterClass.Contract::getMemberId)
                .collect(Collectors.toSet());
    }

    private Set<Long> getCompanyDefinitionIds(Collection<CompanyDefinitionEntity> companyDefinitions) {
        return companyDefinitions.stream()
                .map(BaseEntityKt::getId)
                .collect(Collectors.toSet());
    }

    private boolean isEligibleDefinitionForContract(ContractOuterClass.Contract contract, RuleResolverInput ruleResolverInput, @NotNull CompanyDefinitionEntity companyDefinition, Map<Long, List<TimeOffDefinitionRuleDBO>> companyDefinitionIdToRulesMap) {
        if (!isAllowedContractStatus(contract, companyDefinition.getDefinition())) {
            return false;
        }
        List<TimeOffDefinitionRuleDBO> rules = companyDefinitionIdToRulesMap.getOrDefault(companyDefinition.getId(), List.of());
        return timeOffDefinitionRuleService.isContractMatchForRules(ruleResolverInput, rules);
    }

    private boolean isAllowedContractStatus(ContractOuterClass.Contract contract, DefinitionEntity definitionEntity) {
        // TODO: In legacy code, due to a bug, filtering by contract status is not working. In order to support the old behavior,
        //  we are removing the filtering by contract status. This should be fixed in the future.
//        if (definitionEntity == null) {
//            return false;
//        }
//        return definitionEntity.getValidations().stream()
//                .findFirst()
//                .map(timeoffDefinitionValidationEntity -> timeoffDefinitionValidationEntity.getAllowedContractStatuses().contains(contract.getStatus().name()))
//                .orElse(false);
        return true;
    }

    private TimeOffDefinitionEntity findEligibleDefinition(@NotNull ContractOuterClass.Contract contract, CompanyDefinitionEntity companyDefinition, CountryDefinitionEntity countryDefinition) {
        if (contract.getType() == ContractOuterClass.ContractType.EMPLOYEE) {
            val entitledCountForCompanyDefinition = getEntitledValue(companyDefinition);
            val entitledCountForCountryDefinition = getEntitledValue(countryDefinition);
            if (entitledCountForCountryDefinition > entitledCountForCompanyDefinition) {
                return countryDefinition;
            }
            return companyDefinition;
        }
        return companyDefinition;
    }

    @Async
    public void backfillRulesForExistingCompanyDefinitions(@Nullable Long companyId, boolean forAll) {
        log.info("[backfillRulesForExistingCompanyDefinitions] Started for company id : {}", companyId);
        if (companyId == null && !forAll) {
            log.info("[backfillRulesForExistingCompanyDefinitions] company id is null and forAll is false. Hence returning");
            return;
        }

        List<CompanyDefinitionEntity> companyDefinitions = companyId == null ? companyDefinitionRepository.findAll() : companyDefinitionRepository.findAllNonDeletedByCompanyId(companyId);

        val cdIdToRulesMap = timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(getIds(companyDefinitions))
                .stream()
                .collect(Collectors.groupingBy(TimeOffDefinitionRuleDBO::companyDefinitionId));
        val cdsToBackFill = companyDefinitions.stream()
                .filter(cd -> !cdIdToRulesMap.containsKey(cd.getId())) // if no rule present we need to back-fill the rule
                .filter(cd -> cd.getDefinition() != null)
                .filter(cd -> cd.getDefinition().getCountryCode() != null) // if country code is null -> it is a company definition created with hris flag on
                .collect(Collectors.toSet());

        log.info("[backfillRulesForExistingCompanyDefinitions] Found {} company definitions to back fill", cdsToBackFill.size());
        val timeoffTypesByIds = timeoffTypeService.findAllTypeDBOsByIds(getTypeIds(cdsToBackFill))
                .stream()
                .collect(Collectors.toMap(TimeoffTypeDBO::id, Function.identity()));
        val rules = cdsToBackFill.stream()
                .map(cd -> {
                    val name = getDefinitionName(timeoffTypesByIds.get(cd.getTypeId()), cd.getDefinition().getCountryCode().name());
                    cd.getDefinition().setName(name);
                    val conditionInput = ConditionInput.newBuilder()
                            .key(ConditionKey.COUNTRY)
                            .operator(ConditionOperator.EQUALS)
                            .values(List.of(cd.getDefinition().getCountryCode().name()))
                            .build();

                    return createTimeOffDefinitionRuleForCondition(conditionInput, cd.getCompanyId(), cd, -1L);
                })
                .collect(Collectors.toList());
        timeOffDefinitionRuleService.saveRules(rules);
        log.info("[backfillRulesForExistingCompanyDefinitions] Successfully back filled rules for {} company definitions", cdsToBackFill.size());

    }

    private String getDefinitionName(TimeoffTypeDBO timeoffTypeDBO, String country) {
        if (timeoffTypeDBO == null) {
            return "";
        }

        return country + " " + timeoffTypeDBO.label() + " Policy";
    }

    private Set<Long> getTypeIds(Collection<CompanyDefinitionEntity> companyDefinitions) {
        return companyDefinitions.stream()
                .map(CompanyDefinitionEntity::getTypeId)
                .collect(Collectors.toSet());
    }

    private Set<Long> getIds(List<CompanyDefinitionEntity> companyDefinitions) {
        return companyDefinitions.stream()
                .map(CompanyDefinitionEntity::getId)
                .collect(Collectors.toSet());
    }

    private boolean isValidCountryDefinition(CountryDefinitionEntity countryDefinition) {
        return countryDefinition != null && countryDefinition.getTypeId() != null;
    }

    private CompanyDefinitionEntity getMostRecent(@NotNull CompanyDefinitionEntity existingCompanyDefinition, @NotNull CompanyDefinitionEntity replacementCompanyDefinition) {
        if (existingCompanyDefinition.getUpdatedOn() != null && replacementCompanyDefinition.getUpdatedOn() != null) {
            return existingCompanyDefinition.getUpdatedOn().isAfter(replacementCompanyDefinition.getUpdatedOn()) ? existingCompanyDefinition : replacementCompanyDefinition;
        } else if (existingCompanyDefinition.getUpdatedOn() == null) {
            return replacementCompanyDefinition;
        } else {
            return existingCompanyDefinition;
        }
    }

    private Long getDefinitionId(@NotNull CompanyDefinitionEntity companyDefinition) {
        return Optional.ofNullable(companyDefinition.getDefinition()).map(DefinitionEntity::getId)
                .orElseThrow(() -> new DataCorruptionException("No definition exist for company definition id :" + companyDefinition.getId()));
    }

    private Long getCompanyIdFromCurrentUser() {
        return Optional.ofNullable(currentUser.getContext().getScopes().getCompanyId())
                .orElseThrow(() -> new ValidationException("Cannot find company id of the current user"));
    }

    @Data
    @Builder
    private static class MigrateCompanyDefsResult {
        private Long totalCompanyDefsFound;
        private Long totalCompanyDefsMigrated;
        private Long totalCompanyDefsFailed;
        private Map<Long, List<Long>> defToContractNotFoundIds;
        private List<Long> cdsWithNoAssignments;
    }
}
