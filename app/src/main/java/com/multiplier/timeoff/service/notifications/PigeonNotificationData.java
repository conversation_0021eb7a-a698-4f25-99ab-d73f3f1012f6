package com.multiplier.timeoff.service.notifications;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.Map;

@Data
@SuperBuilder(toBuilder = true)
public abstract class PigeonNotificationData {
    protected String to;
    protected String from;
    @Builder.Default
    protected Map<String, String> data = new HashMap<>();

    protected void addContext(Map<String, String> context){
        context.putAll(context);
    }
}
