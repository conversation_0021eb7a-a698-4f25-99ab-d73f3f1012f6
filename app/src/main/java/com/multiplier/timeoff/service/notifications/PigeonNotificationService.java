package com.multiplier.timeoff.service.notifications;

import com.multiplier.pigeonservice.PigeonNotificationClient;
import com.multiplier.pigeonservice.dto.PigeonEmailNotificationDTO;
import com.multiplier.pigeonservice.schema.kafka.EmailNotificationBody;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class PigeonNotificationService {

    private final PigeonNotificationClient pigeonNotificationClient;

    public String sendEmail(PigeonEmailNotificationData emailRequest){
        val body = EmailNotificationBody.newBuilder()
                .setFrom(sanitize(emailRequest.getFrom()))
                .setTo(sanitize(emailRequest.getTo()))
                .setSubject(sanitize(emailRequest.getSubject()))
                .setTemplateType(emailRequest.getType().name())
                .addAllCc(Objects.nonNull(emailRequest.getCc()) ? emailRequest.getCc() : List.of())
                .addAllBcc(Objects.nonNull(emailRequest.getBcc()) ? emailRequest.getBcc() : List.of())
                .putAllContentVariables(Objects.nonNull(emailRequest.getData()) ? emailRequest.getData() : Map.of())
                .build();
        val attachments = Optional.ofNullable(emailRequest.getAttachments())
                .orElse(new ArrayList<>());
        val dto = new PigeonEmailNotificationDTO(
                body,
                Map.of(),
                attachments
        );
        log.info("Calling pigeon service for sending email with type: {}", emailRequest.getType());
        return pigeonNotificationClient.send(dto);
    }

    private String sanitize(String value){
        if (Objects.nonNull(value)){
            return value;
        }
        return StringUtils.EMPTY;
    }
}
