package com.multiplier.timeoff.service.notifications;

import com.multiplier.pigeonservice.dto.Attachment;
import com.multiplier.timeoff.types.NotificationType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder(toBuilder = true)
public class PigeonEmailNotificationData extends PigeonNotificationData{
    private NotificationType type;
    private List<String> bcc;
    private List<String> cc;
    private String subject;
    private List<Attachment> attachments;
}
