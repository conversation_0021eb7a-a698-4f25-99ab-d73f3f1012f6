package com.multiplier.timeoff.service.notifications;

import com.google.common.annotations.VisibleForTesting;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.member.schema.Member;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.MemberServiceAdapter;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.service.exception.NotificationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
@RequiredArgsConstructor
@Slf4j
public class TimeoffNotification {

    private final ContractServiceAdapter contractServiceAdapter;
    private final CompanyServiceAdapter companyServiceAdapter;
    private final MemberServiceAdapter memberServiceAdapter;
    private final TimeoffNotificationHelper timeoffNotificationHelper;

    public void sendTimeoffCreatedEmailToApprover(TimeoffDBO timeoff, CompanyOuterClass.CompanyUser approver){
        ContractOuterClass.Contract contract = contractServiceAdapter.getContractByIdAnyStatus(timeoff.contractId());
        Member member = memberServiceAdapter.getMember(contract.getMemberId());

        timeoffNotificationHelper.sendTimeoffCreatedEmailToCompanyUsers(
                getCompanyUserEmail(approver),
                member.getFirstName(),
                member.getLastName(),
                timeoff);
    }

    public void sendTimeoffApprovedEmailToMember(TimeoffDBO timeoff){
        ContractOuterClass.Contract contract = contractServiceAdapter.getContractByIdAnyStatus(timeoff.contractId());
        CompanyOuterClass.Company company = companyServiceAdapter.getCompany(contract.getCompanyId());
        timeoffNotificationHelper.sendTimeoffApproveEmailToMember(
                getMemberEmailByContractId(contract.getId()),
                company.getDisplayName(),
                getCompanyAddress(company),
                timeoff
        );
    }

    public void sendTimeoffRejectedEmailToMember(TimeoffDBO timeoff){
        ContractOuterClass.Contract contract = contractServiceAdapter.getContractByIdAnyStatus(timeoff.contractId());
        CompanyOuterClass.Company company = companyServiceAdapter.getCompany(contract.getCompanyId());
        timeoffNotificationHelper.sendTimeoffRejectedEmailToMember(
                getMemberEmailByContractId(contract.getId()),
                company.getDisplayName(),
                getCompanyAddress(company),
                timeoff
        );
    }

    // Return company user's primary email.
    private String getCompanyUserEmail(CompanyOuterClass.CompanyUser companyUser) {
        return companyUser.getEmailsList()
                .stream()
                .filter(emailAddress -> emailAddress.getType().equalsIgnoreCase("primary")
                        || emailAddress.getType().equalsIgnoreCase("default"))
                .findFirst()
                .orElseThrow()
                .getEmail();
    }

    // return personal email or work email
    @VisibleForTesting
    String getMemberEmailByContractId(Long id) {
        return Optional.ofNullable(contractServiceAdapter.getContractMemberEmailsByContractIds(Set.of(id)).get(id))
                .orElseThrow(() -> new NotificationException("No emails found for contract id: " + id));
    }

    // Return company's address.
    @VisibleForTesting
    String getCompanyAddress(CompanyOuterClass.Company company) {
        return Optional.ofNullable(company)
                .map(CompanyOuterClass.Company::getPrimaryEntity)
                .map(CompanyOuterClass.LegalEntity::getAddress)
                .map(address -> Stream.of(
                                address.getLine1(),
                                address.getLine2(),
                                address.getStreet(),
                                address.getCity(),
                                address.getState(),
                                address.getProvince(),
                                address.getZipcode(),
                                address.getPostalCode()
                        )
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(" ")))
                .orElse(StringUtils.EMPTY);
    }

}
