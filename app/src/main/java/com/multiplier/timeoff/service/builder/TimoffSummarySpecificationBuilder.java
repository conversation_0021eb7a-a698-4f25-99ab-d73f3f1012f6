package com.multiplier.timeoff.service.builder;

import com.multiplier.timeoff.core.common.builder.SortDefinition;
import com.multiplier.timeoff.core.common.builder.SortDirection;
import com.multiplier.timeoff.core.common.builder.SpecificationBuilder;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.thymeleaf.util.ListUtils;

import java.util.List;


@Component
public class TimoffSummarySpecificationBuilder implements SpecificationBuilder<TimeoffSummaryDBO, TimeoffSummaryFilters> {

    public Specification<TimeoffSummaryDBO> build(TimeoffSummaryFilters filters) {

        var _filters = (filters != null) ? filters : new TimeoffSummaryFilters();

        var spec = Specification.<TimeoffSummaryDBO>where(null);
        spec = (_filters.contractIds() == null) ? spec : spec.and((root, query, builder) -> builder.in(root.get(SummaryColumns.CONTRACT_ID)).value(_filters.contractIds()));
        spec = (_filters.typeIds() == null) ? spec : spec.and((root, query, builder) -> builder.in(root.get(SummaryColumns.TYPE_ID)).value(_filters.typeIds()));
        spec = (_filters.fromDate() == null) ? spec : spec.and((root, query, builder) -> builder.greaterThanOrEqualTo(root.get(SummaryColumns.PERIOD_END), _filters.fromDate()));
        spec = (_filters.toDate() == null) ? spec : spec.and((root, query, builder) -> builder.lessThanOrEqualTo(root.get(SummaryColumns.PERIOD_START), _filters.toDate()));

        return spec;
    }


    public Specification<TimeoffSummaryDBO> buildSorting(Specification<TimeoffSummaryDBO> spec, List<SortDefinition> values) {

        if (spec == null || ListUtils.isEmpty(values)) {
            return spec;
        }

        return spec.and((root, query, builder) -> {
            query.orderBy(
                    values.stream()
                            .flatMap(
                                    (sorting) -> sorting.columns().stream()
                                            .map(
                                                    (column) -> SortDirection.ASCENDING.equals(sorting.direction())
                                                            ? builder.asc(root.get(column))
                                                            : builder.desc(root.get(column))))
                            .toList());
            return builder.and();
        });
    }
}