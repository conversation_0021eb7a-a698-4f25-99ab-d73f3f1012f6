package com.multiplier.timeoff.service;

import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractStatus;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.validation.TimeoffDefinitionAllocator;
import com.multiplier.timeoff.validation.TimeoffDefinitionConfigRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.time.Month;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j(topic = "[TimeoffConfigurationService]")
public class TimeOffConfigurationService {

    private final ContractServiceAdapter contractServiceAdapter;
    private final TimeoffDefinitionAllocator timeoffDefinitionAllocator;
    private final CompanyServiceAdapter companyServiceAdapter;

    public TimeoffSummaryDBO validateAndMutateSummary(TimeoffSummaryDBO summaryDBO, TimeoffEntitlementDBO entitlementDBO, boolean isAllocation) {
        try {
            log.info("Validating and mutating the time off summary for summaryID={} for contractID={}", summaryDBO.id(), entitlementDBO.contractId());
            ContractOuterClass.Contract contract = contractServiceAdapter.getContractByIdAnyStatus(entitlementDBO.contractId());

            if (contract.getStatus() == ContractStatus.ENDED || contract.getStatus() == ContractStatus.DELETED) {
                log.info("Allocation/Re-allocation is skipped for contract id = {} because contract status is {}", contract.getId(), contract.getStatus());
                return null;
            }

            val company = companyServiceAdapter.getCompany(contract.getCompanyId());
            Month companyFinancialYearEndMonth = TimeOffUtil.getFinancialEndMonth(company);
            return validateAndMutateSummary(entitlementDBO, summaryDBO, contract, companyFinancialYearEndMonth, isAllocation);

        } catch (Exception e) {
            log.warn("Validating and mutation summary failed for summaryDBOId={}, e={}", summaryDBO.id(), e.getMessage());
            return null;
        }
    }

    public List<TimeoffSummaryDBO> validateAndMutateSummaries(List<TimeoffEntitlementDBO> entitlementDBOS,
                                                              Map<String, TimeoffSummaryDBO> contractIdTypeIdToSummaryMap,
                                                              Map<Long, ContractOuterClass.Contract> idToContractMap,
                                                              boolean isAllocation) {

        val companyIdToFinancialYearEndMonth = getCompanyIdToFinancialYearEndMonthMap(idToContractMap.values());
        return entitlementDBOS.stream()
                .map( entitlementDBO -> {
                    val summary = contractIdTypeIdToSummaryMap.get(TimeOffUtil.toContractIdTypeIDKey(entitlementDBO.contractId(), entitlementDBO.type().id()));
                    val contract = idToContractMap.get(entitlementDBO.contractId());
                    if (contract == null) {
                        log.warn("[validateAndMutateSummaries] Contract not found for contractID={}", entitlementDBO.contractId());
                        return null;
                    }
                    Month companyFinancialYearEndMonth = companyIdToFinancialYearEndMonth.getOrDefault(contract.getCompanyId(), Month.DECEMBER);
                    return validateAndMutateSummary(entitlementDBO, summary, contract, companyFinancialYearEndMonth, isAllocation);
                })
                .filter(Objects::nonNull)
                .toList();
    }

    @Nullable
    private TimeoffSummaryDBO validateAndMutateSummary(TimeoffEntitlementDBO entitlementDBO,
                                                       TimeoffSummaryDBO summary,
                                                       @NotNull ContractOuterClass.Contract contract,
                                                       @NotNull Month companyFinancialYearEndMonth,
                                                       boolean isAllocation) {
        if (summary == null) {
            log.warn("[validateAndMutateSummary] Summary not found for contractID={} and typeID={}", entitlementDBO.contractId(), entitlementDBO.typeId());
            return null;
        }
        if (entitlementDBO.value() == null) {
            log.warn("[validateAndMutateSummary] Entitlement.Value is null(unlimited), skipping (MIGHT NEED FIX). summaryID={} for contractID={}", summary.id(), entitlementDBO.contractId());
            return null;
        }
        val definition = TimeOffUtil.getDefinitionForEntitlement(entitlementDBO);

        if (definition == null) {
            log.error("[validateAndMutateSummary] Definition not found for Validating and mutating the time off summary, skipping (NEED URGENT FIX). summaryID={} for contractID={}", summary.id(), entitlementDBO.contractId());
            return null;
        }

        TimeoffDefinitionConfigRequest configRequest = TimeoffDefinitionConfigRequest.of(definition,
                entitlementDBO,
                contract,
                summary,
                isAllocation,
                companyFinancialYearEndMonth);
        timeoffDefinitionAllocator.allocate(configRequest);
        return configRequest.timeoffSummaryDBO();
    }

    private Map<Long, Month> getCompanyIdToFinancialYearEndMonthMap(Collection<ContractOuterClass.Contract> contracts) {
        return companyServiceAdapter.getCompanyByIds(getCompanyIds(contracts)).stream()
                .collect(Collectors.toMap(CompanyOuterClass.Company::getId,
                        TimeOffUtil::getFinancialEndMonth));
    }

    private Set<Long> getCompanyIds(Collection<ContractOuterClass.Contract> contracts) {
        return contracts.stream()
                .map(ContractOuterClass.Contract::getCompanyId)
                .collect(Collectors.toSet());
    }

    public TimeoffSummaryDBO fixTimeoffAllocation(TimeoffSummaryDBO summaryDBO, TimeoffEntitlementDBO entitlementDBO, ContractOuterClass.Contract contract) {
        try {
            log.info("Fix timeoff allocation for summaryID={} for contractID={}", summaryDBO.id(), entitlementDBO.contractId());
            val definition = TimeOffUtil.getDefinitionForEntitlement(entitlementDBO);
            if (definition == null) {
                log.error("Timeoff definition not found, allocation fix failed for summaryID={} for contractID={}", summaryDBO.id(), entitlementDBO.contractId());
                return null;
            }

            if (entitlementDBO.value() == null) {
                log.warn("Timeoff entitlement not found, allocation fix is skipped for summaryID={} for contractID={}", summaryDBO.id(), entitlementDBO.contractId());
                return null;
            }
            val company = companyServiceAdapter.getCompany(contract.getCompanyId());
            TimeoffDefinitionConfigRequest configRequest = TimeoffDefinitionConfigRequest.of(
                    definition,
                    entitlementDBO,
                    contract,
                    summaryDBO,
                    true,
                    TimeOffUtil.getFinancialEndMonth(company));
            timeoffDefinitionAllocator.fixTimeoffAllocation(configRequest);

            return configRequest.timeoffSummaryDBO();
        } catch (Exception e) {
            log.warn("Allocation fix failed for summaryID={}, contractID={}", summaryDBO.id(), summaryDBO.contractId(), e);
            return null;
        }
    }
}
