package com.multiplier.timeoff.service.builder;

import com.multiplier.timeoff.types.TimeOffSummaryFilter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Set;


@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(fluent = true, chain = true)
public class TimeoffSummaryFilters {

    private Collection<Long> contractIds;
    private Set<Long> typeIds;
    private LocalDate fromDate;
    private LocalDate toDate;

    public static TimeoffSummaryFilters build(TimeOffSummaryFilter graphFilters) {
        var filters = new TimeoffSummaryFilters();

        if (graphFilters == null) {
            return filters;
        }

        if (graphFilters.getContractIds() != null) {
            filters.contractIds(graphFilters.getContractIds());
        }

        return filters;
    }

}
