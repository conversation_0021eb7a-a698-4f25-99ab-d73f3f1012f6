package com.multiplier.timeoff.service;


import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.member.schema.Member;
import com.multiplier.timeoff.adapters.*;
import com.multiplier.timeoff.report.domain.ReportCategory;
import com.multiplier.timeoff.report.domain.ReportRow;
import com.multiplier.timeoff.report.domain.ReportRowCellValue;
import com.multiplier.timeoff.report.generator.ReportGeneratorService;
import com.multiplier.timeoff.repository.TimeoffTypeRepository;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.types.DocumentReadable;
import com.multiplier.timeoff.types.TimeOff;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class TimeOffReportGenerationHelper {

    private final ReportGeneratorService reportGeneratorService;
    private final MemberServiceAdapter memberServiceAdapter;
    private final CompanyServiceAdapter companyServiceAdapter;
    private final ContractServiceAdapter contractSvc;
    private final TimeoffTypeRepository timeoffTypeRepository;
    private final ApprovalServiceAdapter approvalServiceAdapter;

    // Column names should be same as column names in the config report-report-generator-config.json
    private final Map<String, Function<TimeOff, Object>> colValueFunctions;

    public TimeOffReportGenerationHelper(ReportGeneratorService reportGeneratorService,
                                         MemberServiceAdapter memberServiceAdapter,
                                         CompanyServiceAdapter companyServiceAdapter,
                                         ContractServiceAdapter contractSvc,
                                         TimeoffTypeRepository timeoffTypeRepository,
                                         ApprovalServiceAdapter approvalServiceAdapter) {
        this.reportGeneratorService = reportGeneratorService;
        this.memberServiceAdapter = memberServiceAdapter;
        this.companyServiceAdapter = companyServiceAdapter;
        this.contractSvc = contractSvc;
        this.timeoffTypeRepository = timeoffTypeRepository;
        this.approvalServiceAdapter = approvalServiceAdapter;

        colValueFunctions = new HashMap<>();
        colValueFunctions.put("TimeOff Start Date", (e) -> e.getStartDate().getDateOnly() + " " + e.getStartDate().getSession());
        colValueFunctions.put("TimeOff End Date", (e) -> e.getEndDate().getDateOnly() + " " + e.getEndDate().getSession());
        colValueFunctions.put("TimeOff Status", TimeOff::getStatus);
        colValueFunctions.put("TimeOff Type", this::getTimeOffType);
        colValueFunctions.put("TimeOff Approved By", this::getApprovedBy);
    }


    private Object getApprovedBy(TimeOff timeOff) {
        return getUserNameFromCompanyUserId(approvalServiceAdapter.getItemApprovedBy(timeOff.getId()).getId());
    }

    private Object getTimeOffType(TimeOff timeOff) {
        Optional<TimeoffTypeDBO> timeOffTypeDboByKey = timeoffTypeRepository.findById(timeOff.getType().getTypeId());
        return timeOffTypeDboByKey.isPresent() ? timeOffTypeDboByKey.get().description() : "";
    }


    private Object getUserNameFromContractId(Long contractID, Map<Long, ContractOuterClass.Contract> contractMap, Map<Long, Member> membersMap) {
        if (contractID == null || !contractMap.containsKey(contractID) || !membersMap.containsKey(contractMap.get(contractID).getMemberId())) {
            return null;
        }
        return membersMap.get(contractMap.get(contractID).getMemberId()).getFullLegalName();
    }


    private Object getUserNameFromCompanyUserId(Long id) {
        if (id == null || id < 0) {
            return null;
        }

        try {
            var obj = this.companyServiceAdapter.getCompanyUser(
                    new CompanyUserFilters(id, null, null)
            );

            return obj.getFirstName() + " " + obj.getLastName();

        } catch (Exception e) {
            log.warn("[TimeOffReportGenerationHelper] Function threw exception={}", e.getMessage());
            return null;
        }
    }

    @SneakyThrows
    public DocumentReadable generateReport(List<TimeOff> timeOffForReportGen) {
        var contractsMap = this.contractSvc.getContractsByIdsAnyStatus(
                        timeOffForReportGen.stream().map(timeOff -> timeOff.getContract().getId()).collect(Collectors.toSet())
                )
                .stream()
                .collect(Collectors.toMap(ContractOuterClass.Contract::getId, Function.identity()));
        var membersMap = memberServiceAdapter.getMembers(contractsMap.values().stream().map(ContractOuterClass.Contract::getMemberId).toList())
                .stream()
                .collect(Collectors.toMap(Member::getId, Function.identity()));
        List<ReportRow> allReportRows = timeOffForReportGen.stream()
                .map(timeOff -> mapToColValues(timeOff, contractsMap, membersMap))
                .toList();
        return reportGeneratorService.generateReport(ReportCategory.TIMEOFF_REPORT, allReportRows);
    }

    private ReportRow mapToColValues(TimeOff timeOff, Map<Long, ContractOuterClass.Contract> contractsMap, Map<Long, Member> membersMap) {
        List<ReportRowCellValue> reportRowCellValues = new ArrayList<>();
        reportRowCellValues.add(
                ReportRowCellValue.builder()
                        .columnValue(getUserNameFromContractId(timeOff.getContract().getId(), contractsMap, membersMap))
                        .columnName("TimeOff For")
                        .build()
        );

        reportRowCellValues.addAll(
                colValueFunctions
                        .keySet()
                        .stream()
                        .map(k -> ReportRowCellValue.builder()
                                .columnValue(colValueFunctions.get(k).apply(timeOff))
                                .columnName(k)
                                .build())
                        .toList()
        );
        return ReportRow.builder().reportRowCellValues(reportRowCellValues).build();
    }
}
