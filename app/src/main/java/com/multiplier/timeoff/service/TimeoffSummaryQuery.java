package com.multiplier.timeoff.service;


import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.company.schema.holiday.LegalEntityHoliday;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.HolidayServiceAdapter;
import com.multiplier.timeoff.core.common.util.PageRequestHelper;
import com.multiplier.timeoff.core.common.util.PageResultHelper;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.repository.TimeoffEntitlementDBORepository;
import com.multiplier.timeoff.repository.TimeoffRepository;
import com.multiplier.timeoff.repository.TimeoffSummaryRepository;
import com.multiplier.timeoff.repository.TimeoffTypeRepository;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.service.builder.TimeoffSummaryFilters;
import com.multiplier.timeoff.service.builder.TimoffSummarySpecificationBuilder;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.TimeoffSummaryMapper;
import com.multiplier.timeoff.service.mapper.TimeoffTypeMapper;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.multiplier.timeoff.core.util.TimeOffUtil.CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS;

@Service
@RequiredArgsConstructor
@Slf4j
public class TimeoffSummaryQuery {
    private final TimeoffTypeRepository timeoffTypeRepository;

    private final TimeoffSummaryRepository repo;
    private final TimeoffEntitlementDBORepository entitlementRepository;
    private final TimoffSummarySpecificationBuilder specBuilder;
    private final TimeoffSummaryMapper mapper;
    private ContractServiceAdapter contractServiceAdapter;
    private final PageRequestHelper pageRequestHelper;
    private final CurrentUser currentUser;
    private final TimeoffTypeMapper timeoffTypeMapper;
    private final TimeoffRepository timeoffRepository;
    private final AuthorizationService authorizationService;
    private final HolidayServiceAdapter holidayServiceAdapter;

    private static final String OPERATIONS_EXP = "operations";

    @Autowired
    public void setContractServiceAdapter(ContractServiceAdapter contractServiceAdapter) {
        this.contractServiceAdapter = contractServiceAdapter;
    }

    @Transactional
    public List<TimeOffType> getAll(TimeoffSummaryFilters filters) {

        return mapper.map(
                repo.findAll(
                        specBuilder.build(filters),
                        Sort.sort(TimeoffSummaryDBO.class).by((TimeoffSummaryDBO m) -> m.timeoffType().key()).ascending()));

    }

    @Transactional
    public List<TimeOffType> getAllForCompanyInCurrentFinancialYear(Long companyId) {

        var startedContractIds = contractServiceAdapter.getAllStartedContractsForCompany(companyId).stream().map(ContractOuterClass.Contract::getId).collect(Collectors.toSet());
        // Filter to get time off summary objects of all contracts of the company in current financial year
        TimeoffSummaryFilters filters = new TimeoffSummaryFilters()
                .contractIds(startedContractIds)
                .fromDate(LocalDate.now())
                .toDate(LocalDate.now());

        var foundSummaryDBOs = repo.findAll(specBuilder.build(filters));

        return mapper.map(foundSummaryDBOs);

    }

    @Transactional
    public TimeOffSummariesResponse getAllForOperationsWithPagination(TimeOffSummaryFilter filter, PageRequest pageRequest) {
        log.info("getting all timeoff summaries for operations");

        var contractIdsFiltered = filterMatchingContractAttributes(filter);
        // keep only filtered contract ids
        if (filter.getContractIds() == null) {
            filter.setContractIds(contractIdsFiltered);
        } else {
            contractIdsFiltered.retainAll(filter.getContractIds());
            filter.setContractIds(contractIdsFiltered);
        }

        final var spec = specBuilder.build(TimeoffSummaryFilters.build(filter));
        final var springPageRequest = pageRequestHelper.toSpringPageRequestWithDefaultSort(pageRequest, "id");

        var timeOffSummaryDBOs = repo.findAll(spec, springPageRequest);

        return TimeOffSummariesResponse.newBuilder()
                .timeOffSummaries(mapper.map(timeOffSummaryDBOs.getContent()))
                .page(PageResultHelper.build(timeOffSummaryDBOs))
                .build();
    }

    public  List<TimeOffBalanceEncashment> getEncashmentBalance(TimeOffEncashmentInput input, DgsDataFetchingEnvironment dfe) {
        log.info("[getEncashmentBalance] Getting encashment balance for contractId: {} last working date : {}", input.getContractId(), input.getLastWorkingDate());

        val contract = contractServiceAdapter.getContractByIdAnyStatus(input.getContractId());
        validateContractRequirements(contract, dfe);

        return getEncashmentBalance(input.getContractId(), input.getLastWorkingDate());
    }

    private void validateContractRequirements(@Nullable ContractOuterClass.Contract contract, DgsDataFetchingEnvironment dfe) {
        if (contract == null) {
            throw new ValidationException("Cannot find contract for the given id ");
        }

        if (!CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(contract.getType())) {
            throw new ValidationException(String.format("Contract type is not eligible for timeoffs entitlements (id = %d, type = %s)", contract.getId(), contract.getType()));
        }

        validateAccess(contract, dfe);
    }

    private void validateAccess(ContractOuterClass.Contract contract, DgsDataFetchingEnvironment dfe) {
        val experience = currentUser.getContext().getExperience();
        if (OPERATIONS_EXP.equals(experience)) {
            return;
        }
        authorizationService.authorize(dfe, contract);
    }

    public List<TimeOffBalanceEncashment> getEncashmentBalance(Long contractId, LocalDate lastWorkingDate) {
        // Fetch summaries and map time off types by ID
        val currentSummaries = repo.findSummariesByContractIdAndDate(contractId, lastWorkingDate);
        val typeIdToEntitlementMap = getTypeIdToEntitlementMap(contractId);
        if (CollectionUtils.isEmpty(currentSummaries)) {
            log.info("[getEncashmentBalance] No summaries found for contractId: {} last working date : {}", contractId, lastWorkingDate);
            return Collections.emptyList();
        }

        val summariesWithTakenCount = currentSummaries.stream()
                .filter(summary -> summary.takenCount() != null && summary.takenCount() > 0)
                .toList();
        Map<Long, List<TimeoffDBO>> typeIdToTimeoffsMap = getTypeIdToTimeoffsMap(contractId, summariesWithTakenCount, lastWorkingDate);
        val holidays = holidayServiceAdapter.getHolidays(Set.of(contractId), lastWorkingDate.getYear(), null);

        val idToTimeOffTypeMap = getTimeOffTypesMap(getTypeIds(currentSummaries));

        // Calculate and collect encashment balances
        return currentSummaries.stream()
                .map(summary -> calculateEncashmentBalance(summary,
                        typeIdToEntitlementMap.get(summary.typeId()),
                        idToTimeOffTypeMap.get(summary.typeId()),
                        lastWorkingDate,
                        typeIdToTimeoffsMap,
                        holidays))
                .collect(Collectors.toList());
    }

    private Map<Long, TimeoffEntitlementDBO> getTypeIdToEntitlementMap(Long contractId) {
        return entitlementRepository.findAllByContractId(contractId).stream()
                .collect(Collectors.toMap(TimeoffEntitlementDBO::typeId, Function.identity()));
    }

    private Map<Long, List<TimeoffDBO>> getTypeIdToTimeoffsMap(Long contractId, List<TimeoffSummaryDBO> summaries, LocalDate lastWorkingDate) {
        if (summaries.isEmpty()) {
            return Collections.emptyMap();
        }

        List<Long> typeIds = summaries.stream()
                .map(TimeoffSummaryDBO::typeId)
                .toList();

        LocalDate earliestPeriodStart = summaries.stream()
                .map(TimeoffSummaryDBO::periodStart)
                .min(LocalDate::compareTo)
                .orElse(lastWorkingDate);

        List<TimeoffDBO> timeoffDBOS = timeoffRepository.findAllByContractIdTypeIdInStartDateInRangeStatusIn(
                contractId,
                typeIds,
                earliestPeriodStart,
                lastWorkingDate,
                List.of(TimeOffStatus.APPROVED, TimeOffStatus.TAKEN));

        return timeoffDBOS.stream()
                .collect(Collectors.groupingBy(TimeoffDBO::typeId));
    }

    private TimeOffBalanceEncashment calculateEncashmentBalance(TimeoffSummaryDBO summary,
                                                                TimeoffEntitlementDBO entitlementDBO,
                                                                TimeoffTypeDBO timeoffTypeDBO,
                                                                LocalDate lastWorkingDate,
                                                                Map<Long, List<TimeoffDBO>> typeIdToTimeoffsMap,
                                                                List<LegalEntityHoliday.Holiday> holidays) {
        log.info("[calculateEncashmentBalance] Calculating encashment balance for typeId: {} (summaryId: {})", summary.typeId(), summary.id());

        double allocatedCount = TimeOffUtil.zeroIfNull(TimeOffUtil.getDays(entitlementDBO.value(), entitlementDBO.unit()));
        double carriedCount = TimeOffUtil.zeroIfNull(summary.carriedCount());
        double takenCount = TimeOffUtil.zeroIfNull(calculateTakenCount(summary, lastWorkingDate, typeIdToTimeoffsMap, holidays));

        val calculatedValues = calculateEncashableBalance(
                summary.periodStart(),
                lastWorkingDate,
                allocatedCount,
                carriedCount,
                takenCount
        );

        log.info("[calculateEncashmentBalance] Encashable balance for typeId: {} (summaryId: {}) : prorated_allocated ({}) + carried({}) - taken({}) = balance({})",
                summary.typeId(), summary.id(), calculatedValues.proratedAllocatedCount, calculatedValues.carriedCount, calculatedValues.takenCount, calculatedValues.encashableBalance);

        return TimeOffBalanceEncashment.newBuilder()
                .type(timeoffTypeMapper.mapToGraph(timeoffTypeDBO))
                .totalProratedAllocatedCount(calculatedValues.proratedAllocatedCount)
                .totalCarryForwardCount(calculatedValues.carriedCount)
                .totalTakenCount(calculatedValues.takenCount)
                .encashableBalance(calculatedValues.encashableBalance)
                .build();
    }

    private Double calculateTakenCount(
            TimeoffSummaryDBO summary,
            LocalDate lastWorkingDate,
            Map<Long, List<TimeoffDBO>> typeIdToTakenTimeoffsMap,
            List<LegalEntityHoliday.Holiday> holidays) {

        if (summary.takenCount() == null || summary.takenCount() == 0) {
            return 0.0;
        }

        List<TimeoffDBO> allTakenTimeoffs = typeIdToTakenTimeoffsMap.getOrDefault(summary.typeId(), Collections.emptyList());

        List<TimeoffDBO> takenTimeoffsForSummary = allTakenTimeoffs.stream()
                .filter(timeoff -> !timeoff.startDate().isBefore(summary.periodStart()))
                .toList();

        if (takenTimeoffsForSummary.isEmpty()) {
            return 0.0;
        }

        double takenCount = 0.0;

        for (TimeoffDBO timeoffDBO : takenTimeoffsForSummary) {
            if (timeoffDBO.endDate().isAfter(lastWorkingDate)) {
                val holidayDates = TimeOffUtil.convertHolidaysToLocalDates(holidays);
                takenCount += TimeOffUtil.calculateNoOfTimeoffDays(
                        timeoffDBO.startDate(),
                        lastWorkingDate,
                        timeoffDBO.startSession(),
                        TimeOffSession.AFTERNOON,
                        holidayDates
                );
            } else {
                takenCount += timeoffDBO.noOfDays();
            }
        }

        return takenCount;
    }

    private Double getAnnualAllocatedCount(TimeoffSummaryDBO summary, TimeoffEntitlementDBO entitlementDBO) {
        if (isContractStartAfterLeaveCycle(summary.periodStart(), summary.periodEnd())) {
            return TimeOffUtil.getDays(entitlementDBO.value(), entitlementDBO.unit());
        } else {
            return summary.allocatedCount();
        }
    }

    private TimeoffEncashableBalanceCalculationResult calculateEncashableBalance(LocalDate periodStart,
                                              LocalDate lastWorkingDate,
                                              double allocatedCount,
                                              double carriedCount,
                                              double takenCount) {
        if (lastWorkingDate.isBefore(periodStart)) {
            log.info("[calculateEncashableBalance] Last working date is before period start. Returning 0.0");
            return TimeoffEncashableBalanceCalculationResult.builder()
                    .proratedAllocatedCount(0.0)
                    .carriedCount(0.0)
                    .takenCount(0.0)
                    .encashableBalance(0.0)
                    .build();
        }
        double proRatedAllocated = calculateProRatedValue(periodStart, lastWorkingDate, allocatedCount);
        double nonExpiredCarriedCount = getNonExpiredCarriedCount(carriedCount);
        double balance =  proRatedAllocated + nonExpiredCarriedCount - takenCount;

        return TimeoffEncashableBalanceCalculationResult.builder()
                .proratedAllocatedCount(proRatedAllocated)
                .carriedCount(nonExpiredCarriedCount)
                .takenCount(takenCount)
                .encashableBalance(balance)
                .build();
    }

    private boolean isContractStartAfterLeaveCycle(LocalDate periodStart, LocalDate periodEnd) {
        return !periodStart.plusYears(1).minusDays(1).equals(periodEnd);
    }

    private double getNonExpiredCarriedCount(Double carriedCount) {
        // todo: In v2 we need to consider carry forward expiration when getting
        // the carried count. For now, we are returning the carried count as is.
        return carriedCount;
    }

    private double calculateProRatedValue(LocalDate startDate, LocalDate lastWorkingDate, double value) {
        long workingDays = ChronoUnit.DAYS.between(startDate, lastWorkingDate) + 1;
        long totalDaysForSummary = 365; // Product requirement is to always divide by 365
        return TimeOffUtil.round((double) workingDays / totalDaysForSummary * value);
    }

    private Map<Long, TimeoffTypeDBO> getTimeOffTypesMap(Set<Long> typeIds) {
        return timeoffTypeRepository.findAllById(typeIds).stream()
                .collect(Collectors.toMap(TimeoffTypeDBO::id, Function.identity()));
    }

    private Set<Long> getTypeIds(List<TimeoffSummaryDBO> summaryDBOS) {
        return summaryDBOS.stream()
                .map(TimeoffSummaryDBO::typeId)
                .collect(Collectors.toSet());
    }

    private List<Long> filterMatchingContractAttributes(TimeOffSummaryFilter filter) {
        var contractFilters = buildContractFilters(filter.getContractCountry(), filter.getContractStatus(), filter.getContractType());
        return new ArrayList<>(contractServiceAdapter.getContractIdsMatchingFilters(contractFilters)); // make it mutable
    }

    private ContractOuterClass.ContractFilters buildContractFilters(CountryCode countryCode, ContractStatus contractStatus, ContractType contractType) {
        return ContractOuterClass.ContractFilters.newBuilder()
                .setCountryCode(Objects.toString(countryCode, ""))
                .setContractStatus(ContractOuterClass.ContractStatus.valueOf(Objects.toString(contractStatus, ContractOuterClass.ContractStatus.NULL_CONTRACT_STATUS.toString())))
                .setContractType(ContractOuterClass.ContractType.valueOf(Objects.toString(contractType, ContractOuterClass.ContractType.NULL_CONTRACT_TYPE.toString())))
                .setIsTest(Boolean.TRUE.equals(currentUser.getContext().getScopes().isOperationsTestUser()))
                .build();
    }

    @Data
    @Builder
    private static class TimeoffEncashableBalanceCalculationResult {
        private double proratedAllocatedCount;
        private double carriedCount;
        private double takenCount;
        private double encashableBalance;
    }
}
