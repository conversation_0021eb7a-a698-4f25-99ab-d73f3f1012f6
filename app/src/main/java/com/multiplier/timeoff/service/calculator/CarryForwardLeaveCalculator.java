package com.multiplier.timeoff.service.calculator;

import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.types.CarryForwardConfig;
import com.multiplier.timeoff.types.CarryForwardLimit;
import com.multiplier.timeoff.types.LimitValueType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.UtilityClass;
import lombok.val;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Objects;

@UtilityClass
public class CarryForwardLeaveCalculator {

    @Getter
    @Builder
    @AllArgsConstructor
    public static class CarryForwardLeaveCalculateData {

        private double totalEntitledCount;
        private double allocationCount;
        private double totalTakenCount;
        private CarryForwardConfig config;

    }

    public double calculateCarryForwardLeaves(final CarryForwardLeaveCalculateData data) {

        val balance = data.getTotalEntitledCount() - data.getTotalTakenCount();
        if (balance <= 0) {
            return 0;
        }

        return getCarryForwardCount(
                balance,
                getMinMaxCarryForwardLeaves(
                        data.getAllocationCount(),
                        data.getConfig()
                )
        );
    }

    Pair<Double, Double> getMinMaxCarryForwardLeaves(final double entitlement, CarryForwardConfig config) {

        if (Objects.isNull(config) || !config.getEnabled()) {
            return Pair.of(0D, 0D);
        }

        return Pair.of(
                calculateLeaveCount(0, entitlement, config.getMinForEligibility()),
                calculateLeaveCount(Integer.MAX_VALUE, entitlement, config.getMaxLimit())
        );
    }

    Double calculateLeaveCount(double previous, double entitlement, final CarryForwardLimit limitEntity) {

        if (limitEntity == null || limitEntity.getValue() == null || limitEntity.getType() == null) {
            return previous;
        }

        var type = limitEntity.getType();

        return (type == LimitValueType.FIXED) ? limitEntity.getValue()
                : (type == LimitValueType.PERCENTAGE) ? TimeOffUtil.round(entitlement * (limitEntity.getValue() / 100))
                : 0;
    }

    double getCarryForwardCount(double balance, final Pair<Double, Double> minMax) {

        return (balance < minMax.getLeft()) ? 0
                : (balance > minMax.getRight()) ? minMax.getRight()
                : balance;
    }

}
