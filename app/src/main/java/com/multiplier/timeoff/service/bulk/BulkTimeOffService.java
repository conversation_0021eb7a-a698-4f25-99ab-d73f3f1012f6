package com.multiplier.timeoff.service.bulk;


import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ApprovalServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.core.common.dto.BulkTimeOffDataHolder;
import com.multiplier.timeoff.core.common.dto.BulkTimeOffRequest;
import com.multiplier.timeoff.core.common.dto.BulkUpsertTimeOffResult;
import com.multiplier.timeoff.core.common.dto.BulkValidateTimeOffResult;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.kafka.TimeoffKafkaPublisher;
import com.multiplier.timeoff.processor.TimeoffValidator;
import com.multiplier.timeoff.repository.EntitlementChangeRecordRepository;
import com.multiplier.timeoff.repository.TimeoffRepository;
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.service.TimeoffSummaryService;
import com.multiplier.timeoff.service.TimeoffTypeService;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.BulkTimeOffMapper;
import com.multiplier.timeoff.service.mapper.TimeoffMapper;
import com.multiplier.timeoff.types.EntityType;
import com.multiplier.timeoff.types.TimeOffSession;
import com.multiplier.timeoff.types.TimeOffStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.multiplier.timeoff.core.util.TimeOffUtil.toContractIdTypeIDKey;
import static com.multiplier.timeoff.core.util.TimeOffUtil.toSet;
import static java.text.MessageFormat.format;


@Service
@RequiredArgsConstructor
@Slf4j(topic = "[BulkTimeOffService]")
public class BulkTimeOffService {

    private final BulkTimeOffValidator bulkTimeOffValidator;
    private final TimeoffRepository timeoffRepository;
    private final BulkTimeOffMapper bulkTimeOffMapper;
    private final TimeoffTypeService timeoffTypeService;
    private final ContractServiceAdapter contractServiceAdapter;
    private final TimeoffSummaryService timeoffSummaryService;
    private final BulkTimeOffHelper bulkTimeOffHelper;
    private final FeatureFlagService featureFlagService;
    private final EntitlementChangeRecordRepository entitlementChangeRecordRepository;
    private final CurrentUser currentUser;
    private final ApprovalServiceAdapter approvalServiceAdapter;
    private final TimeoffKafkaPublisher timeoffKafkaPublisher;
    private final TimeoffMapper timeoffMapper;
    private static final Set<TimeOffStatus> STATUSES_ALLOWED_TO_REVOKE_BY_COMPANY_USER = EnumSet.of(
            TimeOffStatus.APPROVAL_IN_PROGRESS,
            TimeOffStatus.APPROVED,
            TimeOffStatus.TAKEN,
            TimeOffStatus.REJECTED
    );

    private static final EnumSet<TimeOffStatus> STATUSES_ALLOWED_TO_REVOKE_BY_OPS = EnumSet.of(
            TimeOffStatus.APPROVAL_IN_PROGRESS,
            TimeOffStatus.APPROVED,
            TimeOffStatus.REJECTED,
            TimeOffStatus.TAKEN
    );
    public static final String COMPANY_EXP = "company";


    public BulkValidateTimeOffResult validateBulkTimeOffs(BulkTimeOffRequest req) {
        log.info("[validateBulkTimeOffs] validating bulk timeoff inputs. for company id : {}", req.getCompanyId());
        throwIfUnauthorized(req.getCompanyId());

        if (CollectionUtils.isEmpty(req.getInputs())) {
            log.info("[validateBulkTimeOffs] No inputs received to validate");
            return bulkValidateTimeOffResult(true, List.of());
        }

        BulkTimeOffDataHolder bulkTimeOffDataHolder = populateBulkTimeOffData(req);
        return validateBulkTimeOffs(req, bulkTimeOffDataHolder);
    }


    private BulkValidateTimeOffResult validateBulkTimeOffs(BulkTimeOffRequest req, BulkTimeOffDataHolder bulkTimeOffDataHolder) {
        log.info("[validateBulkTimeOffs] Bulk validate process - STARTED (company_id: {}, input size : {})", req.getCompanyId(), req.getInputs().size());

        val failedInputs = req.getInputs().stream()
                .filter(Objects::nonNull)
                .map(input -> validateInput(input, bulkTimeOffDataHolder))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        log.info("[validateBulkTimeOffs] Bulk validate process - COMPLETED (company_id: {})", req.getCompanyId());

        return bulkValidateTimeOffResult(failedInputs.isEmpty(), failedInputs);
    }

    @Transactional
    public BulkUpsertTimeOffResult bulkUpsertTimeOffs(BulkTimeOffRequest req) {
        log.info("[bulkUpsertTimeOffs] upserting {} bulk timeoffs. for company id : {}", req.getInputs().size(), req.getCompanyId());

        try {
            throwIfUnauthorized(req.getCompanyId());
            if (CollectionUtils.isEmpty(req.getInputs())) {
                log.info("[bulkUpsertTimeOffs] No inputs to upsert for company id : {}", req.getCompanyId());
                return bulkUpsertTimeOffResult(true, List.of(), "Bulk upsert process succeeded");
            }
            BulkTimeOffDataHolder bulkTimeOffDataHolder = populateBulkTimeOffData(req);
            val validationResult = validateBulkTimeOffs(req, bulkTimeOffDataHolder);
            if (!validationResult.isSuccess()) {
                log.info("[bulkUpsertTimeOffs] Bulk upsert skipped due to validation errors (company_id: {}, validation_responses: {})", req.getCompanyId(), validationResult.getItems());
                return bulkUpsertTimeOffResult(false, bulkTimeOffMapper.map(validationResult.getItems()), "Validation errors found");
            }

            log.info("[bulkUpsert] Bulk upsert process - STARTED (company_id: {})", req.getCompanyId());

            // clone of timeoffs before updating the type
            val oldTimeOffsToUpdateType = getTimeOffsToUpdateType(bulkTimeOffDataHolder.getExternalIdToExistingTimeOffMap(), req.getInputs());
            val timeOffDBOsToSave =  req.getInputs().stream()
                    .map(input -> createOrUpdateTimeOff(input, bulkTimeOffDataHolder.getIdToContractMap(), bulkTimeOffDataHolder.getExternalIdToExistingTimeOffMap(), bulkTimeOffDataHolder.getLabelToCompanyTimeOffTypeMap()))
                    .collect(Collectors.toList());
            val savedEntities = timeoffRepository.saveAllAndFlush(timeOffDBOsToSave);
            val changedTimeOffs = ListUtils.union(savedEntities, oldTimeOffsToUpdateType);// need to update summaries for all changed timeoffs
            log.info("[bulkUpsert] Bulk upsert process - updating summaries (company_id: {})", req.getCompanyId());
            timeoffSummaryService.onTimeOffChangesForBulk(changedTimeOffs);

            approvalServiceAdapter.bulkStartApprovalAndAutoApprove(req.getCompanyId(), getTimeoffIdToContractIdMap(savedEntities));

            log.info("[bulkUpsertTimeOffs] Bulk upsert process - DONE (company_id: {}, upsert_count: {})", req.getCompanyId(), savedEntities.size());

            publishKafkaMessages(savedEntities);

            return bulkUpsertTimeOffResult(true, mapToResultItems(savedEntities), "Bulk upsert process succeeded");
        } catch (Exception e) {
            log.error("[bulkUpsertTimeOffs] Bulk upsert process - FAILED (company_id: {})", req.getCompanyId(), e);
            return bulkUpsertTimeOffResult(false, List.of(), "Bulk upsert process failed due to an exception (" + e.getMessage() + ")");
        }
    }

    @Transactional
    @Async
    public void bulkRevokeTimeOffsFromExternalIds(long companyId, List<String> externalTimeOffIdsList) {
        try {
            log.info("[bulkRevokeTimeOffsFromExternalIds] bulk revoking process - STARTED (company id : {}, timeoff_count : {})", companyId, externalTimeOffIdsList.size());
            throwIfUnauthorized(companyId);

            val timeoffs = timeoffRepository.findAllByExternalIdIn(externalTimeOffIdsList);
            val contractIdToCompanyIdMap = contractServiceAdapter.getContractIdToCompanyIdMap(getContractIdsFromTimeOffs(timeoffs));

            validateTimeOffsForBulkRevoke(companyId, timeoffs, contractIdToCompanyIdMap);
            timeoffs.forEach(timeoff -> timeoff.status(TimeOffStatus.DRAFT));
            val savedTimeOfss = timeoffRepository.saveAllAndFlush(timeoffs);
            timeoffSummaryService.onTimeOffChangesForBulk(savedTimeOfss);
            publishKafkaMessages(savedTimeOfss);
            log.info("[bulkRevokeTimeOffsFromExternalIds] bulk revoking process - COMPLETED (company_id: {}, revoked_count: {})", companyId, timeoffs.size());
        } catch (Exception e) {
            log.error("[bulkRevokeTimeOffsFromExternalIds] bulk revoking process - FAILED (company_id: {})", companyId, e);
            throw e;
        }
    }

    @Async
    public void publishKafkaMessages(List<TimeoffDBO> savedTimeOfss) {
        val graphTimeOffs = timeoffMapper.map(savedTimeOfss);
        graphTimeOffs.forEach(timeoff -> timeoffKafkaPublisher.publishTimeoffUpdateEvent(timeoff));
    }

    private void validateTimeOffsForBulkRevoke(Long companyId, Collection<TimeoffDBO> timeoffDBOs, Map<Long, Long> contractIdToCompanyIdMap) {
        if (isSystemCall() || isOpsExperience()) {
            throwIfOpsUserOrSystemCannotRevokeTimeOffs(timeoffDBOs);
            return;
        }

        String experience = currentUser.getContext().getExperience();
        if (COMPANY_EXP.equals(experience)) {
            throwIfCompanyUserCannotRevokeTimeOffs(timeoffDBOs, companyId, contractIdToCompanyIdMap);
        } else {
            throw new ValidationException("Member can not revoke timeoffs in a bulk way");
        }
    }

    private void throwIfCompanyUserCannotRevokeTimeOffs(Collection<TimeoffDBO> timeoffDBOs, Long companyId, Map<Long, Long> contractIdToCompanyIdMap) {
        var invalidTimeOffs = timeoffDBOs.stream()
                .filter(timeoffDBO -> !STATUSES_ALLOWED_TO_REVOKE_BY_COMPANY_USER.contains(timeoffDBO.status()))
                .toList();
        if (!invalidTimeOffs.isEmpty()) {
            List<String> invalidTimeOffMessages = invalidTimeOffs.stream()
                    .map(timeOff -> String.format("{id: %s, status: %s}", timeOff.id(), timeOff.status()))
                    .toList();
            throw new ValidationException("Some of the given time offs cannot be processed due to their current status: " + invalidTimeOffMessages);
        }
        val companyIds = toSet(contractIdToCompanyIdMap.values());
        if (companyIds.size() > 1) {
            throw new ValidationException("Found timeoffs from multiple companies : " + companyIds);
        }
        val companyIdOfTimeOffs = companyIds.stream().findFirst().orElse(-1L);
        if (companyIdOfTimeOffs != companyId) {
            throw new ValidationException(String.format("Company user from company id %d can not revoke timeoffs of company : %s" ,
                    companyId, companyIdOfTimeOffs));
        }

    }

    private void throwIfOpsUserOrSystemCannotRevokeTimeOffs(Collection<TimeoffDBO> timeoffDBOs) {
        var invalidTimeOffs = timeoffDBOs.stream()
                .filter(timeoffDBO -> !STATUSES_ALLOWED_TO_REVOKE_BY_OPS.contains(timeoffDBO.status()))
                .toList();
        if (!invalidTimeOffs.isEmpty()) {
            List<String> invalidTimeOffMessages = invalidTimeOffs.stream()
                    .map(timeOff -> String.format("{id: %s, status: %s}", timeOff.id(), timeOff.status()))
                    .toList();
            throw new ValidationException("Some of the given time offs cannot be processed due to their current status: " + invalidTimeOffMessages);
        }
    }
    private BulkTimeOffDataHolder populateBulkTimeOffData(BulkTimeOffRequest req) {
        val companyId = req.getCompanyId();
        val bulkTimeOffs = req.getInputs();

        val contractIds = getContractIds(bulkTimeOffs);
        Map<Long, ContractOuterClass.Contract> idToContractMap = CollectionUtils.isEmpty(contractIds) ? Map.of() : contractServiceAdapter.getContractsByIdsAnyStatus(contractIds, true)
                .stream()
                .collect(Collectors.toMap(ContractOuterClass.Contract::getId, Function.identity()));

        Map<String, TimeoffTypeDBO> labelToCompanyTimeOffTypeMap = getLabelToTypeMap(companyId, req.getEntityId());
        val typeIds = getTypeIds(bulkTimeOffs, labelToCompanyTimeOffTypeMap);

        Map<String, TimeoffSummaryDBO> contractIdTypeIdToSummaryMap = Map.of();
        Map<String, List<TimeoffDBO>> contractIdTypeIdToTimeOffsMap = Map.of();
        Map<String, List<EntitlementChangeRecordEntity>> contractIdTypeIdToAllocationECRsMap = Map.of();
        List<EntitlementChangeRecordEntity> allDeductionECRs = List.of();
        Map<String, TimeoffDBO> externalIdToExistingTimeOffMap = getExternalIdToExistingTimeOffsMap(getExternalIds(bulkTimeOffs));
        if (isCarryForwardExpiryEnabled(companyId)) {
            // we need below data only if carry forward expiry enabled. Hence, not loading them for all the cases to improve the performance
            val earliestSummaryStartDate = timeoffSummaryService.getEarliestSummaryStartDate(contractIdTypeIdToSummaryMap.values());
            contractIdTypeIdToTimeOffsMap = bulkTimeOffHelper.getContractIdTypeIdToTimeOffsMap(contractIds, typeIds, earliestSummaryStartDate);
            contractIdTypeIdToAllocationECRsMap = getContractIdTypeIdToAllocationECRsMap(typeIds, contractIds);
            allDeductionECRs = entitlementChangeRecordRepository.findAllByCategoryInAndRefIdIn(TimeoffValidator.Companion.getDEDUCTION_CATEGORIES(),
                    getECRIds(contractIdTypeIdToAllocationECRsMap));
        } else {
            contractIdTypeIdToSummaryMap = getContractIdTypeIdToSummaryMap(contractIds, typeIds);
        }

        val duplicatedExternalIds = getExternalIdDuplicatedIds(bulkTimeOffs);
        val duplicatedCombinationsHashStrings = getDuplicatedCombinationsHashStrings(bulkTimeOffs);

        return BulkTimeOffDataHolder.builder()
                .companyId(companyId)
                .entityId(req.getEntityId())
                .contractIdTypeIdToSummaryMap(contractIdTypeIdToSummaryMap)
                .labelToCompanyTimeOffTypeMap(labelToCompanyTimeOffTypeMap)
                .idToContractMap(idToContractMap)
                .externalIdToExistingTimeOffMap(externalIdToExistingTimeOffMap)
                .contractIdTypeIdToTimeOffsMap(contractIdTypeIdToTimeOffsMap)
                .contractIdTypeIdToAllocationECRsMap(contractIdTypeIdToAllocationECRsMap)
                .deductionECRs(allDeductionECRs)
                .duplicatedExternalIds(duplicatedExternalIds)
                .duplicatedCombinationsHashStrings(duplicatedCombinationsHashStrings)
                .build();
    }

    private List<TimeoffDBO> getTimeOffsToUpdateType(Map<String, TimeoffDBO> externalIdToExistingTimeOffsMap, List<BulkTimeOffRequest.Input> newTimeOffInputs) {
        List<TimeoffDBO> timeOffsToUpdateType = new ArrayList<>();
        for (BulkTimeOffRequest.Input newTimeOffInput : newTimeOffInputs) {
            val existingTimeOff = externalIdToExistingTimeOffsMap.get(newTimeOffInput.getExternalTimeOffId());

            if (existingTimeOff != null && isUpdatingType(newTimeOffInput.getType(), existingTimeOff)) {
                timeOffsToUpdateType.add(copy(existingTimeOff));
            }
        }
        return timeOffsToUpdateType;
    }

    private boolean isUpdatingType(String inputType, TimeoffDBO existingTimeOff) {
        return !existingTimeOff.type().label().equals(inputType);
    }

    private Map<String, TimeoffTypeDBO> getLabelToTypeMap(Long companyId, @Nullable Long entityId) {
        if (entityId == null) {
            // called from V1 => hence load policies for company
            return timeoffTypeService.findTimeOffTypeDBOsForCompany(companyId)
                    .stream()
                    .collect(Collectors.toMap(t -> TimeOffUtil.getLabelToTimeOffTypeMapKey(t.label()), Function.identity()));

        }
        return timeoffTypeService.fetchEntityTimeOffTypes(companyId, entityId, EntityType.COMPANY_ENTITY)
                .stream()
                .collect(Collectors.toMap(t -> TimeOffUtil.getLabelToTimeOffTypeMapKey(t.label()), Function.identity()));
    }




    private Set<Long> getContractIds(Collection<BulkTimeOffRequest.Input> bulkInputs) {
        return bulkInputs.stream()
                .map(BulkTimeOffRequest.Input::getContractId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    private Set<Long> getContractIdsFromTimeOffs(Collection<TimeoffDBO> timeoffDBOs) {
        return timeoffDBOs.stream()
                .map(TimeoffDBO::contractId)
                .collect(Collectors.toSet());
    }

    private Set<Long> getTypeIds(List<BulkTimeOffRequest.Input> inputs, Map<String, TimeoffTypeDBO> labelToTypeMap) {
        Set<String> labels = inputs.stream()
                .map(BulkTimeOffRequest.Input::getType)
                .filter(type -> !StringUtils.isBlank(type))
                .collect(Collectors.toSet());
        return labels.stream()
                .map(label -> labelToTypeMap.get(TimeOffUtil.getLabelToTimeOffTypeMapKey(label)))
                .filter(Objects::nonNull)
                .map(TimeoffTypeDBO::id)
                .collect(Collectors.toSet());

    }

    private Map<String, TimeoffDBO> getExternalIdToExistingTimeOffsMap(Set<String> externalIds) {
        return timeoffRepository.findAllByExternalIdIn(externalIds).stream()
                .collect(Collectors.toMap(TimeoffDBO::externalId, Function.identity()));
    }

    private Map<String, TimeoffSummaryDBO> getContractIdTypeIdToSummaryMap(Set<Long> contractIds, Set<Long> typeIds) {
        return timeoffSummaryService.findLatestSummariesForContractIdInAndTypeIdIn(contractIds, typeIds)
                .stream()
                .collect(Collectors.toMap(
                        ts -> toContractIdTypeIDKey(ts.contractId(), ts.typeId()),
                        Function.identity()));
    }

    private Map<Long, Long> getTimeoffIdToContractIdMap(List<TimeoffDBO> timeoffDBOs) {
        return timeoffDBOs.stream()
                .collect(Collectors.toMap(TimeoffDBO::id, TimeoffDBO::contractId));
    }

    /**
     * Load all entitlement change records for given type ids and contract ids
     * Prepare and return map of contractId_type_id to list of Entitlement change records
     */
    private Map<String, List<EntitlementChangeRecordEntity>> getContractIdTypeIdToAllocationECRsMap(Set<Long> typeIds, Set<Long> contractIds) {
        return entitlementChangeRecordRepository
                .findAllByTypeInAndContractIdInAndCategoryIn(typeIds, contractIds, TimeoffValidator.Companion.getALLOCATION_CATEGORIES())
                .stream()
                .collect(Collectors.groupingBy(
                        ec -> toContractIdTypeIDKey(ec.getContractId(), ec.getTypeId()),
                        Collectors.toList()
                ));
    }

    private Set<Long> getECRIds(Map<String, List<EntitlementChangeRecordEntity>> ecrMap) {
        return ecrMap.values().stream()
                .flatMap(List::stream)
                .map(EntitlementChangeRecordEntity::getId)
                .collect(Collectors.toSet());
    }

    private Set<String> getExternalIdDuplicatedIds(List<BulkTimeOffRequest.Input> inputs) {
        return inputs.stream()
                .collect(Collectors.groupingBy(BulkTimeOffRequest.Input::getExternalTimeOffId, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    private Set<String> getDuplicatedCombinationsHashStrings(List<BulkTimeOffRequest.Input> inputs) {
        return inputs.stream()
                .collect(Collectors.groupingBy(this::generateHashString, Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    private String generateHashString(BulkTimeOffRequest.Input input) {
        return String.valueOf(
                Objects.hash(
                    input.getContractId(),
                    input.getType(),
                    input.getStartDate(),
                    input.getEndDate()
                )
        );
    }

    private BulkValidateTimeOffResult bulkValidateTimeOffResult(boolean success, List<BulkValidateTimeOffResult.BulkValidateTimeOffResultItem> items) {
        return BulkValidateTimeOffResult.builder()
                .success(success)
                .items(items)
                .build();
    }

    private BulkValidateTimeOffResult.BulkValidateTimeOffResultItem validateInput(BulkTimeOffRequest.Input input, BulkTimeOffDataHolder bulkTimeOffDataHolder) {
        if (StringUtils.isBlank(input.getExternalTimeOffId())) {
            return getBulkValidateTimeOffResult(input.getExternalTimeOffId(), List.of("External id is empty"));
        }

        if (bulkTimeOffDataHolder.getDuplicatedExternalIds().contains(input.getExternalTimeOffId())) {
            return getBulkValidateTimeOffResult(input.getExternalTimeOffId(), List.of("External id is duplicated"));
        }

        if (bulkTimeOffDataHolder.getDuplicatedCombinationsHashStrings().contains(generateHashString(input))) {
            return getBulkValidateTimeOffResult(input.getExternalTimeOffId(), List.of("Duplicated entries found for the combination of [Employee ID, Type, Start Date and End Date] with employee id: " + input.getEmployeeId()));
        }

        val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
        return errors.isEmpty() ? null : getBulkValidateTimeOffResult(input.getExternalTimeOffId(), errors);
    }

    private BulkValidateTimeOffResult.BulkValidateTimeOffResultItem getBulkValidateTimeOffResult(String externalTimeOffId, List<String> errors) {
        return BulkValidateTimeOffResult.BulkValidateTimeOffResultItem.builder()
                .externalTimeOffId(externalTimeOffId)
                .errors(errors)
                .build();
    }

    private boolean isCarryForwardExpiryEnabled(Long companyId) {
        return featureFlagService.feature(
                FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY,
                Map.of(COMPANY_EXP, companyId)
        ).on();
    }


    private BulkUpsertTimeOffResult bulkUpsertTimeOffResult(boolean succeeded, List<BulkUpsertTimeOffResult.Item> items, String message) {
        return BulkUpsertTimeOffResult.builder()
                .success(succeeded)
                .message(message)
                .items(items)
                .build();
    }

    private TimeoffDBO createOrUpdateTimeOff(BulkTimeOffRequest.Input input,
                                             Map<Long, ContractOuterClass.Contract> idToContractMap,
                                             Map<String, TimeoffDBO> externalIdToTimeOffDBOMap,
                                             Map<String, TimeoffTypeDBO> labelToTypeMap) {
        val timeOffDBO = externalIdToTimeOffDBOMap.getOrDefault(input.getExternalTimeOffId(), new TimeoffDBO());

        val startDate = toLocalDate(input.getStartDate());
        val endDate = toLocalDate(input.getEndDate());
        val noOFDays = toDouble(input.getNoOfDays());
        val contract = idToContractMap.get(input.getContractId());
        val type = labelToTypeMap.get(TimeOffUtil.getLabelToTimeOffTypeMapKey(input.getType()));
        timeOffDBO.externalId(input.getExternalTimeOffId());
        timeOffDBO.contractId(contract.getId());
        timeOffDBO.type(type);
        timeOffDBO.typeId(type.id());
        timeOffDBO.noOfDays(noOFDays);
        timeOffDBO.status(TimeOffStatus.APPROVED);
        timeOffDBO.approvedOn(Instant.now());
        timeOffDBO.description(input.getDescription());
        timeOffDBO.startDate(startDate);
        timeOffDBO.startSession(TimeOffSession.MORNING);
        timeOffDBO.endDate(endDate);
        timeOffDBO.endSession(isInteger(noOFDays) ? TimeOffSession.AFTERNOON : TimeOffSession.MORNING);
        return timeOffDBO;
    }

    private List<BulkUpsertTimeOffResult.Item> mapToResultItems(List<TimeoffDBO> timeoffDBOS) {
        return timeoffDBOS.stream()
                .map(timeoffDBO -> BulkUpsertTimeOffResult.Item.builder()
                        .externalTimeOffId(timeoffDBO.externalId())
                        .timeOffId(timeoffDBO.id())
                        .build())
                .collect(Collectors.toList());
    }

    private boolean isInteger(Double number) {
        return number % 1 == 0;
    }

    private Double toDouble(String number) {
        return Double.parseDouble(number);
    }

    private Set<String> getExternalIds(List<BulkTimeOffRequest.Input> inputs) {
        return inputs.stream()
                .map(BulkTimeOffRequest.Input::getExternalTimeOffId)
                .collect(Collectors.toSet());
    }

    private LocalDate toLocalDate(String date) {
        return BulkTimeOffHelper.parseDate(date);
    }

    private TimeoffDBO copy(TimeoffDBO timeoffDBO) {
        TimeoffDBO copy = new TimeoffDBO();
        copy.id(timeoffDBO.id);
        copy.contractId(timeoffDBO.contractId());
        copy.type(timeoffDBO.type());
        copy.typeId(timeoffDBO.typeId());
        copy.noOfDays(timeoffDBO.noOfDays());
        return copy;
    }

    private void throwIfUnauthorized(Long companyId) {
        if (isSystemCall()) {
            // we allow calls originated from system schedulers to pass
            return;
        }
        if (isOpsExperience()) {
            return;
        }
        if (!currentUser.getContext().getScopes().getCompanyId().equals(companyId)) {
            throw new AccessDeniedException(format("Access denied for user id : {0} to modify data of company id : {1}",
                    currentUser.getContext().getId(), companyId
            ));
        }
    }

    private boolean isOpsExperience() {
        return currentUser.getContext().getExperience().equals("operations");
    }

    /**
     * @return true when it looks like being called from the system/cronjob etc...
     */
    private boolean isSystemCall() {
        val ctx = currentUser.getContext();
        return ctx == null
                || ctx.getId() == null
                || ctx.getId() <= 0
                || StringUtils.isEmpty(ctx.getExperience());
    }

}
