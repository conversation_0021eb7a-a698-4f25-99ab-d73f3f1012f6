package com.multiplier.timeoff.service.builder;


import com.multiplier.timeoff.core.common.builder.SpecificationBuilder;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.types.TimeOffStatus;
import jakarta.persistence.criteria.Predicate;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

interface Columns {
    String ID = "id";
    String CONTRACT_ID = "contractId";
    String TYPE_ID = "typeId";
    String STATUS = "status";
    String START_DATE = "startDate";
    String BACK_TO_WORK_DATE = "backToWorkDate";
    String END_DATE = "endDate";
    String UPDATED_ON = "updatedOn";
}

@Component
public class TimoffSpecificationBuilder implements SpecificationBuilder<TimeoffDBO, TimeoffFilters> {

    public Specification<TimeoffDBO> build(TimeoffFilters filters) {

        var _filters = (filters != null) ? filters : new TimeoffFilters();

        var spec = Specification.<TimeoffDBO>where(null);
        spec = (_filters.ids() == null)
                ? spec
                : spec.and((root, query, builder) -> builder.in(root.get(Columns.ID)).value(_filters.ids()));
        spec = (_filters.contractIds() == null)
                ? spec
                : spec.and((root, query, builder) -> builder.in(root.get(Columns.CONTRACT_ID)).value(_filters.contractIds()));
        spec = (_filters.typeIds() == null || _filters.typeIds().size() < 1)
                ? spec
                : spec.and((root, query, builder) -> builder.in(root.get(Columns.TYPE_ID)).value(_filters.typeIds()));
        spec = (_filters.statuses() == null || _filters.statuses().size() < 1)
                ? spec.and((root, query, builder) -> builder.notEqual(root.get(Columns.STATUS), TimeOffStatus.DELETED))
                : spec.and((root, query, builder) -> builder.in(root.get(Columns.STATUS)).value(_filters.statuses()));
        spec = (_filters.startDateFrom() == null)
                ? spec
                : spec.and((root, query, builder) -> builder.greaterThanOrEqualTo(root.get(Columns.START_DATE), _filters.startDateFrom()));
        spec = (_filters.startDateUpto() == null)
                ? spec
                : spec.and((root, query, builder) -> builder.lessThanOrEqualTo(root.get(Columns.START_DATE), _filters.startDateUpto()));
        spec = (_filters.toDate() == null)
                ? spec
                : spec.and((root, query, builder) -> builder.lessThanOrEqualTo(root.get(Columns.END_DATE), _filters.toDate()));
        spec = (_filters.updatedOnFrom() == null)
                ?spec
                : spec.and(((root, query, builder) -> builder.greaterThanOrEqualTo(root.get(Columns.UPDATED_ON), _filters.updatedOnFrom())));
        spec = (_filters.updatedOnTo() == null)
                ?spec
                : spec.and(((root, query, builder) -> builder.lessThanOrEqualTo(root.get(Columns.UPDATED_ON), _filters.updatedOnTo())));

        return spec;
    }

    public Specification<TimeoffDBO> specBuilderByContractIdAndTypeId(@NotNull TimeoffFilters filters) {
        var spec = Specification.<TimeoffDBO>where(null);
        return spec.and((root, query, builder) -> {
            Predicate finalPredicate = builder.disjunction(); // Use disjunction for OR

            for (var contractIdTypeIdFilter : filters.contractIdTypeIdFilters()) {

                Predicate contractIdPredicate = builder.equal(root.get(SummaryColumns.CONTRACT_ID), contractIdTypeIdFilter.contractId());
                Predicate typeIdPredicate = builder.equal(root.get(SummaryColumns.TYPE_ID), contractIdTypeIdFilter.typeId());

                Predicate statusPredicate = CollectionUtils.isEmpty(contractIdTypeIdFilter.statuses())
                        ? builder.notEqual(root.get(Columns.STATUS), TimeOffStatus.DELETED)
                        : root.get(Columns.STATUS).in(contractIdTypeIdFilter.statuses());

                // Start date filtering
                Predicate startDatePredicate = builder.greaterThanOrEqualTo(root.get(Columns.START_DATE), contractIdTypeIdFilter.startDateFrom());

                // End date filtering
                Predicate endDatePredicate = builder.lessThanOrEqualTo(root.get(Columns.END_DATE), contractIdTypeIdFilter.toDate());

                Predicate combinedPredicate = builder.and(contractIdPredicate, typeIdPredicate, statusPredicate, startDatePredicate, endDatePredicate);
                finalPredicate = builder.or(finalPredicate, combinedPredicate);

            }

            return finalPredicate;
        });
    }
}
