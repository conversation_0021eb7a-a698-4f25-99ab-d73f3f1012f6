package com.multiplier.timeoff.service;


import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.MemberServiceAdapter;
import com.multiplier.timeoff.repository.TimeoffRepository;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.service.builder.TimeoffFilters;
import com.multiplier.timeoff.service.builder.TimoffSpecificationBuilder;
import com.multiplier.timeoff.types.DocumentReadable;
import com.multiplier.timeoff.types.TimeOffStatus;
import com.multiplier.timeoff.types.TimeOffType;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.multiplier.timeoff.report.domain.ReportCategory;
import com.multiplier.timeoff.report.domain.ReportRow;
import com.multiplier.timeoff.report.domain.ReportRowCellValue;
import com.multiplier.timeoff.report.generator.ReportGeneratorService;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TimeOffBalanceReportGenerationHelper {

    private final ReportGeneratorService reportGeneratorService;
    private final MemberServiceAdapter memberServiceAdapter;
    private final ContractServiceAdapter contractSvc;
    private final TimeoffRepository timeoffRepository;
    private final TimoffSpecificationBuilder timeoffSpecBuilder;

    // Column names should be same as column names in the config report-report-generator-config.json
    private final Map<String, Function<TimeOffType, Object>> colValueFunctions;

    public TimeOffBalanceReportGenerationHelper(ReportGeneratorService reportGeneratorService,
                                                MemberServiceAdapter memberServiceAdapter,
                                                ContractServiceAdapter contractSvc,
                                                TimeoffRepository timeoffRepository,
                                                TimoffSpecificationBuilder timeoffSpecBuilder) {
        this.colValueFunctions = new HashMap<>();
        this.reportGeneratorService = reportGeneratorService;
        this.memberServiceAdapter = memberServiceAdapter;
        this.contractSvc = contractSvc;
        this.timeoffRepository = timeoffRepository;
        this.timeoffSpecBuilder = timeoffSpecBuilder;
        colValueFunctions.put("Member Name", typeSummary -> getMemberName(typeSummary.getContract().getId()));
        colValueFunctions.put("Country", this::getContractCountry);
        colValueFunctions.put("Time off Type", typeSummary -> typeSummary.getDefinition().getDescription());
        colValueFunctions.put("Time offs applied", this::getAppliedTimeOffTotalDays);
        colValueFunctions.put("Time offs approved", this::getApprovedTimeOffTotalDays);

        colValueFunctions.put("Time offs pending for approval", TimeOffType::getPending);
        colValueFunctions.put("Time off rejected", this::getRejectedTimeOffTotalDays);
        colValueFunctions.put("Time Offs remaining", TimeOffType::getRemaining);
        colValueFunctions.put("Total Time Offs", TimeOffType::getEntitled);
    }

    @SneakyThrows
    public DocumentReadable generateReport(List<TimeOffType> timeOffForBalanceReportGen) {
        List<ReportRow> allReportRows = timeOffForBalanceReportGen.stream()
                .map(this::mapToColValues)
                .toList();
        return reportGeneratorService.generateReport(ReportCategory.TIMEOFF_BALANCE_REPORT, allReportRows);
    }

    private Object getMemberName(Long contractID) {
        if (contractID == null) {
            return null;
        }
        return memberServiceAdapter.getMember(this.contractSvc.getContractByIdAnyStatus(contractID).getMemberId()).getFullLegalName();
    }

    private Object getContractCountry(TimeOffType typeSummary) {
        Long contractId = typeSummary.getContract().getId();
        if (contractId != null) {
            ContractOuterClass.Contract contract = this.contractSvc.getContractByIdAnyStatus(contractId);
            return contract.getCountry();
        }
        return null;
    }

    private Double getRejectedTimeOffTotalDays(TimeOffType timeSummary) {
        var filter = new TimeoffFilters()
                .contractIds(Set.of(timeSummary.getContract().getId()))
                .typeIds(Set.of(timeSummary.getDefinition().getTypeId()))
                .statuses(Set.of(TimeOffStatus.REJECTED))
                .startDateFrom(timeSummary.getPeriodStart().toLocalDate())
                .toDate(timeSummary.getPeriodEnd().toLocalDate());
        var timeOffs = timeoffRepository.findAll(timeoffSpecBuilder.build(filter));
        return timeOffs.stream()
                .filter(t -> Objects.nonNull(t.noOfDays()))
                .mapToDouble(TimeoffDBO::noOfDays)
                .sum();

    }
    // status = TAKEN + APPROVED + APPROVE_IN_PROGRESS
    private Double getAppliedTimeOffTotalDays(TimeOffType timeSummary) {
        return toZeroIfNull(timeSummary.getTaken()) + toZeroIfNull(timeSummary.getPending());
    }
    // Approved = APPROVED
    private Double getApprovedTimeOffTotalDays(TimeOffType timeSummary) {
        var filter = new TimeoffFilters()
                .contractIds(Set.of(timeSummary.getContract().getId()))
                .typeIds(Set.of(timeSummary.getDefinition().getTypeId()))
                .statuses(Set.of(TimeOffStatus.APPROVED))
                .startDateFrom(timeSummary.getPeriodStart().toLocalDate())
                .toDate(timeSummary.getPeriodEnd().toLocalDate());
        var timeOffs = timeoffRepository.findAll(timeoffSpecBuilder.build(filter));
        return timeOffs.stream()
                .filter(t -> Objects.nonNull(t.noOfDays()))
                .mapToDouble(TimeoffDBO::noOfDays)
                .sum();
    }

    private ReportRow mapToColValues(TimeOffType timeSummary) {
        List<ReportRowCellValue> reportRowCellValues = colValueFunctions
                .keySet()
                .stream()
                .map(k -> ReportRowCellValue.builder()
                        .columnValue(colValueFunctions.get(k).apply(timeSummary))
                        .columnName(k)
                        .build())
                .collect(Collectors.toList());
        return ReportRow.builder().reportRowCellValues(reportRowCellValues).build();
    }

    private static double toZeroIfNull(Double d) {
        return d == null ? 0d : d;
    }
}
