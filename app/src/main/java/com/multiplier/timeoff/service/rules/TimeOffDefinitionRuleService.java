package com.multiplier.timeoff.service.rules;

import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.repository.TimeOffDefinitionRuleRepository;
import com.multiplier.timeoff.repository.model.TimeOffDefinitionRuleDBO;
import com.multiplier.timeoff.types.ConditionKey;
import com.multiplier.timeoff.types.ConditionOperator;
import com.multiplier.timeoff.types.RuleType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "TimeOffDefinitionRuleService")
public class TimeOffDefinitionRuleService {
    private final TimeOffDefinitionRuleRepository timeOffDefinitionRuleRepository;
    private final FeatureFlagService featureFlagService;
    private static final Set<ConditionKey> VALID_RULE_KEYS = Set.of(ConditionKey.COUNTRY, ConditionKey.GENDER, ConditionKey.NAME, ConditionKey.ENTITY);

    public List<TimeOffDefinitionRuleDBO> findRulesByCompanyDefinitionIdIn(Set<Long> companyDefinitionIds) {
        return timeOffDefinitionRuleRepository.findByCompanyDefinitionIdIn(companyDefinitionIds);
    }

    public boolean isContractMatchForRules(RuleResolverInput ruleResolverInput, List<TimeOffDefinitionRuleDBO> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return false; // No rules to match
        }
        if (isEntityLevelPoliciesEnabled(ruleResolverInput.getCompanyId())) {
            return isContractMatchForRulesWhenEntityPoliciesOn(ruleResolverInput, rules);
        }

        if (rules.stream().anyMatch(rule -> rule.ruleType() == RuleType.ALL)) {
            return true;
        }
        Map<ConditionKey, List<TimeOffDefinitionRuleDBO>> conditionKeyToRuleMap = rules.stream()
                .collect(Collectors.groupingBy(TimeOffDefinitionRuleDBO::conditionKey));

        if (containInvalidRules(conditionKeyToRuleMap.keySet())) {
            return false;
        }

        return isMatch(ruleResolverInput.getCountry(), conditionKeyToRuleMap.get(ConditionKey.COUNTRY))
                && isMatch(ruleResolverInput.getGender(), conditionKeyToRuleMap.get(ConditionKey.GENDER))
                && isMatch(ruleResolverInput.getName(), conditionKeyToRuleMap.get(ConditionKey.NAME));
        // when other condition keys comes need to add as AND conditions
    }

    private boolean isContractMatchForRulesWhenEntityPoliciesOn(RuleResolverInput ruleResolverInput, List<TimeOffDefinitionRuleDBO> rules) {
        Map<ConditionKey, List<TimeOffDefinitionRuleDBO>> conditionKeyToRuleMap = rules.stream()
                .collect(Collectors.groupingBy(TimeOffDefinitionRuleDBO::conditionKey));

        if (containInvalidRules(conditionKeyToRuleMap.keySet())) {
            return false;
        }

        return isMatch(ruleResolverInput.getCountry(), conditionKeyToRuleMap.get(ConditionKey.COUNTRY))
                && isMatch(ruleResolverInput.getGender(), conditionKeyToRuleMap.get(ConditionKey.GENDER))
                && isMatch(ruleResolverInput.getName(), conditionKeyToRuleMap.get(ConditionKey.NAME))
                && isMatch(ruleResolverInput.getEntityId(), conditionKeyToRuleMap.get(ConditionKey.ENTITY));
    }

    public void saveRules(List<TimeOffDefinitionRuleDBO> timeOffDefinitionRuleDBOS) {
        timeOffDefinitionRuleRepository.saveAll(timeOffDefinitionRuleDBOS);
    }

    public void deleteRules(List<TimeOffDefinitionRuleDBO> timeOffDefinitionRuleDBOS) {
        timeOffDefinitionRuleRepository.deleteAll(timeOffDefinitionRuleDBOS);
    }

    private boolean containInvalidRules(Set<ConditionKey> conditionKeys) {
        return !VALID_RULE_KEYS.containsAll(conditionKeys);
    }

    private boolean isMatch(String value, List<TimeOffDefinitionRuleDBO> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return true;
        }
        Map<ConditionOperator, List<TimeOffDefinitionRuleDBO>> operatorToRulesMap = rules.stream()
                .collect(Collectors.groupingBy(TimeOffDefinitionRuleDBO::conditionOperator));

        val notEqualRules = operatorToRulesMap.getOrDefault(ConditionOperator.NOT_EQUALS, List.of());
        val equalRules = operatorToRulesMap.getOrDefault(ConditionOperator.EQUALS, List.of());

        return !isMatchForRules(notEqualRules, value) && isMatchForRules(equalRules, value);
    }

    private boolean isMatchForRules(List<TimeOffDefinitionRuleDBO> rules, String value) {
        return rules.stream()
                .anyMatch(rule -> Arrays.stream(rule.conditionValues())
                        .anyMatch(conditionValues -> conditionValues.equalsIgnoreCase(value)));
    }

    private boolean isEntityLevelPoliciesEnabled(Long companyId) {
        return featureFlagService.isOn(FeatureFlags.ENTITY_LEVEL_POLICIES, Map.of("company", companyId));
    }

}
