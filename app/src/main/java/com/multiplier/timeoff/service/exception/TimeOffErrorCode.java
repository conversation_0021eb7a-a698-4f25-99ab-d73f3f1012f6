package com.multiplier.timeoff.service.exception;

import com.multiplier.common.exception.ErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;

@Getter
@RequiredArgsConstructor
public enum TimeOffErrorCode implements ErrorCode {

    // Keep these sorted alphabetically
    INVALID_DATA("TimeOffInvalidData", "Invalid data"),
    VALIDATION_ERROR("TimeOffValidationError", "Validation error"),
    // Keep above sorted alphabetically

    ;



    private final String code;
    private final String message;

    @NotNull
    @Override
    public String getName() {
        return getCode();
    }
}
