package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.repository.model.TimeOffDefinitionEntity;
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.service.exception.DataCorruptionException;
import com.multiplier.timeoff.types.ContractUpdateTimeOffEntitlementsInput;
import com.multiplier.timeoff.types.TimeOffFixedValidation;
import com.multiplier.timeoff.types.TimeOffTypeDefinition;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

import java.util.Map;

@Mapper(componentModel = "spring")
public interface TimeoffEntitlementMapper {

    /**
     * map to a new entity
     */
    default TimeoffEntitlementDBO mapToDBO(ContractUpdateTimeOffEntitlementsInput input,
                                           @Context Long contractID,
                                           @Context Map<String, TimeoffTypeDBO> timeoffTypeDboByKey,
                                           @Context Map<Long, TimeOffDefinitionEntity> typeIdToDefinitionMap) {
        return mapToDBO(input, null, contractID, timeoffTypeDboByKey, typeIdToDefinitionMap);
    }

    /**
     * map to an existing entity
     */
    default TimeoffEntitlementDBO mapToDBO(ContractUpdateTimeOffEntitlementsInput input,
                                           @MappingTarget TimeoffEntitlementDBO target,
                                           @Context Long contractID,
                                           @Context Map<String, TimeoffTypeDBO> timeOffTypeDBOsByKey,
                                           @Context Map<Long, TimeOffDefinitionEntity> typeIdToDefinitionMap) {
        if (input == null || timeOffTypeDBOsByKey == null) {
            return null;
        }

        if (target == null) {
            target = new TimeoffEntitlementDBO();
        }
        val type = timeOffTypeDBOsByKey.get(input.getKey());
        if (type == null) {
            throw new DataCorruptionException("No type exist for key :" + input.getKey());
        }
        val definition = typeIdToDefinitionMap.get(type.id());


        return target
                .contractId(contractID)
                .typeId(timeOffTypeDBOsByKey.get(input.getKey()).id())
                .type(timeOffTypeDBOsByKey.get(input.getKey()))
                .definition(definition != null ? definition.getDefinition() : null)
                .value(input.getValue())
                .unit(input.getUnit());
    }

    default TimeoffEntitlementDBO mapToDBO(TimeOffTypeDefinition definition,
                                           @MappingTarget TimeoffEntitlementDBO target,
                                           @Context Long contractID,
                                           @Context Map<String, TimeoffTypeDBO> timeoffTypeDboByKey) {
        if (definition == null || timeoffTypeDboByKey == null) {
            return null;
        }

        if (target == null) {
            target = new TimeoffEntitlementDBO();
        }

        var timeOffFixedValidation = CollectionUtils.isEmpty(definition.getValidation())
                ? null // data corruption?
                : (TimeOffFixedValidation) definition.getValidation().get(0);

        return target
                .contractId(contractID)
                .typeId(timeoffTypeDboByKey.containsKey(definition.getType()) ? timeoffTypeDboByKey.get(definition.getType()).id() : null)
                .type(timeoffTypeDboByKey.get(definition.getType()))
                .value(timeOffFixedValidation == null ? null : timeOffFixedValidation.getDefaultValue())
                .unit(timeOffFixedValidation == null ? null : timeOffFixedValidation.getUnit());
    }

    /**
     * to generate default entitlements based on country definitions
     */
    default TimeoffEntitlementDBO mapToDBO(TimeOffTypeDefinition definition,
                                           @Context Long contractID,
                                           @Context Map<String, TimeoffTypeDBO> timeoffTypeDboByKey) {
        return mapToDBO(definition, null, contractID, timeoffTypeDboByKey);
    }

    /**
     * to generate default entitlements based on country definitions
     */
    default TimeoffEntitlementDBO mapToDBO(TimeOffDefinitionEntity definition,
                                           @Context Long contractID,
                                           @Context Map<Long, TimeoffTypeDBO> timeoffTypeDboByKey) {
        return mapToDBO(definition, null, contractID, timeoffTypeDboByKey);
    }

    /**
     * to generate default entitlements based on country definitions
     */
    default TimeoffEntitlementDBO mapToDBO(TimeOffDefinitionEntity timeOffDefinitionEntity,
                                           @MappingTarget TimeoffEntitlementDBO target,
                                           @Context Long contractID,
                                           @Context Map<Long, TimeoffTypeDBO> timeoffTypeDboByKey) {
        if (timeOffDefinitionEntity == null) {
            return null;
        }

        val definitionEntity = timeOffDefinitionEntity.getDefinition();
        if (definitionEntity == null || timeoffTypeDboByKey == null) {
            return null;
        }

        if (target == null) {
            target = new TimeoffEntitlementDBO();
        }

        var timeOffFixedValidation = definitionEntity.getValidations().stream().findFirst().orElse(null);

        return target
                .contractId(contractID)
                .typeId(timeoffTypeDboByKey.containsKey(timeOffDefinitionEntity.getTypeId()) ? timeoffTypeDboByKey.get(timeOffDefinitionEntity.getTypeId()).id() : null)
                .definition(definitionEntity)
                .type(timeoffTypeDboByKey.get(timeOffDefinitionEntity.getTypeId()))
                .value(timeOffFixedValidation == null ? null : timeOffFixedValidation.getDefaultValue())
                .unit(timeOffFixedValidation == null ? null : timeOffFixedValidation.getUnit());
    }

    /**
     * to generate default entitlements based on country definitions
     */


}
