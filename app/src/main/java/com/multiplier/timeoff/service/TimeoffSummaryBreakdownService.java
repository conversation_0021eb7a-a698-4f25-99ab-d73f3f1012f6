package com.multiplier.timeoff.service;

import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.WorkshiftServiceAdapter;
import com.multiplier.timeoff.core.common.dto.TimeOffUsageSummaryDTO;
import com.multiplier.timeoff.core.common.exceptionhandler.MPLError;
import com.multiplier.timeoff.core.common.exceptionhandler.MPLErrorType;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.core.util.TimeoffCarryForwardUtil;
import com.multiplier.timeoff.exception.TimeoffValidationException;
import com.multiplier.timeoff.repository.TimeoffEntitlementDBORepository;
import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.service.exception.DataCorruptionException;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.TimeOffInputMapper;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "TimeoffSummaryBreakdownService")
public class TimeoffSummaryBreakdownService {
    private final AuthorizationService authorizationService;
    private final ContractServiceAdapter contractServiceAdapter;
    private final TimeoffSummaryService timeoffSummaryService;
    private final TimeoffBreakdownService timeoffBreakdownService;
    private final CarryForwardLeaveService carryForwardLeaveService;
    private final TimeoffEntitlementDBORepository entitlementDBORepository;
    private final WorkshiftServiceAdapter workshiftServiceAdapter;
    private final TimeOffInputMapper timeOffInputMapper;

    public List<TimeOffSummaryBreakdown> getSummaryBreakdown(DgsDataFetchingEnvironment dfe, TimeOffSummaryBreakdownInput input) {
        validateSummaryBreakdownInput(input);
        val contract = contractServiceAdapter.getBasicContractById(input.getContractId());
        authorizationService.authorize(dfe, contract);

        val contractId = input.getContractId();
        val typeId = input.getTypeId();
        val entitlement = entitlementDBORepository.findByContractIdAndTypeId(contractId, typeId)
                .orElseThrow(() -> new ValidationException("Entitlement not found for contract id : " + contractId + " and type id : " + typeId));
        val carryForwardConfig = getCarryForwardConfig(entitlement);

        val currentAndNextSummary = timeoffSummaryService.getCurrentAndNextSummaryByContractIdAndTypeId(contractId, typeId);


        val currentSummary = currentAndNextSummary.getFirst();
        val nextSummary = currentAndNextSummary.getSecond();

        validate(currentSummary, nextSummary, input.getTimeOffStartDate().getDateOnly(), input.getTimeOffEndDate().getDateOnly(), getFutureLeaveConfig(entitlement));

        val workShift = workshiftServiceAdapter.getWorkshiftByContractId(input.getContractId());
        val timeoffEntries = timeoffBreakdownService.calculateTimeoffEntriesAndApplyingDays(
                input.getContractId(),
                timeOffInputMapper.map(input.getTimeOffStartDate()),
                timeOffInputMapper.map(input.getTimeOffEndDate()),
                workShift
        ).getLeft();

        val currentSummaryAfterTimeoff = clone(currentSummary);
        val nextSummaryAfterTimeoff = clone(nextSummary);

        // applying timeoff
        applyTimeOff(
                entitlement,
                currentSummaryAfterTimeoff,
                nextSummaryAfterTimeoff,
                timeoffEntries,
                input.getTimeOffStartDate().getDateOnly(),
                input.getTimeOffEndDate().getDateOnly()
        );

        return getSummaryBreakdowns(currentSummary, nextSummary, currentSummaryAfterTimeoff, nextSummaryAfterTimeoff, carryForwardConfig);
    }

    /**
     * Applies a time-off entry to the current and next time-off summaries, updating them accordingly,
     * and returns a summary of the time-off usage. The time-off is validated against the applicable
     * entitlement configuration before the application.
     *
     * @param timeoffDBO The data object representing the time-off request, including contract ID, type ID,
     *                   date range, and time-off entries to be applied.
     * @param currentSummary The current time-off summary to which the time-off is applied.
     * @param nextSummary The next time-off summary, representing the subsequent period, to which the
     *                    time-off is applied when relevant.
     * @return A data transfer object summarizing the usage of time-off, including both the state before and
     *         after the application of the time-off for the current and next summaries.
     * @throws ValidationException If the entitlement configuration for the specified contract ID and type ID
     *                              is not found, or if validation of the time-off against the summaries fails.
     */
    public TimeOffUsageSummaryDTO applyAndUpdateTimeoffSummaries(
            TimeoffDBO timeoffDBO,
            TimeoffSummaryDBO currentSummary,
            TimeoffSummaryDBO nextSummary
    ) {
        val contractId = timeoffDBO.contractId();
        val typeId = timeoffDBO.typeId();
        val entitlement = entitlementDBORepository.findByContractIdAndTypeId(contractId, typeId)
                .orElseThrow(() -> new ValidationException("Entitlement not found for contract id : " + contractId + " and type id : " + typeId));

        validate(currentSummary, nextSummary, timeoffDBO.startDate(), timeoffDBO.endDate(), getFutureLeaveConfig(entitlement));

        val currentSummaryBeforeTimeoff = clone(currentSummary);
        val nextSummaryBeforeTimeoff = clone(nextSummary);

        // applying timeoff
        applyTimeOff(
                entitlement,
                currentSummary,
                nextSummary,
                timeoffDBO.timeoffEntries(),
                timeoffDBO.startDate(),
                timeoffDBO.endDate()
        );

        return getTimeoffUsageSummaryDTO(currentSummaryBeforeTimeoff, nextSummaryBeforeTimeoff,
                currentSummary, nextSummary);

    }

    private void validate(TimeoffSummaryDBO currentSummary,
                          TimeoffSummaryDBO nextSummary,
                          LocalDate timeoffStartDate,
                          LocalDate timeoffEndDate,
                          FutureLeaveConfigEntity futureLeaveConfig
                          ) {
        if (currentSummary == null) {
            throw new DataCorruptionException("Current summary is not found");
        }

        if (currentSummary.periodStart().isAfter(timeoffStartDate)) {
            throw new TimeoffValidationException(new MPLError(MPLErrorType.MPL0523_INVALID_PAST_TIMEOFF, Map.of("cutoff-date", currentSummary.periodStart())));
        }

        if (futureLeaveConfig.getEnabled() && nextSummary == null) {
            throw new DataCorruptionException("Future leaves enabled but no future leaves can be found");
        }

        if (!futureLeaveConfig.getEnabled() && timeoffEndDate.isAfter(currentSummary.periodEnd())) {
            throw new TimeoffValidationException(new MPLError(MPLErrorType.MPL0524_INVALID_FUTURE_TIMEOFF, Map.of("cutoff-date", currentSummary.periodEnd())));
        }
    }


    private void applyTimeOff(TimeoffEntitlementDBO entitlement,
                              TimeoffSummaryDBO currentSummary,
                              TimeoffSummaryDBO nextSummary,
                              List<TimeoffEntryDBO> timeoffEntries,
                              LocalDate startDate,
                              LocalDate endDate) {


        val carryForwardConfig = getCarryForwardConfig(entitlement);
        val lapsableLeaveConfig = getLapsableLeaveConfig(entitlement);

        if (isTimeoffWithinTheSummary(currentSummary, startDate, endDate)) {
            // this is a leave inside the current summary
            // Ex: summary.period_start = 2025-01-01, summary.period_end = 2025-12-31
            //     timeoff.start_date = 2025-02-01, timeoff.end_date = 2025-02-02
            applyCurrentSummaryTimeoff(timeoffEntries, currentSummary, carryForwardConfig);
        } else if (isTimeoffWithinTheSummary(nextSummary, startDate, endDate)) {
            // this is a leave inside the next summary
            // Ex: summary.period_start = 2026-01-01, summary.period_end = 2026-12-31
            //     timeoff.start_date = 2026-02-01, timeoff.end_date = 2026-02-02
            applyNextSummaryTimeOff(timeoffEntries, currentSummary, nextSummary, entitlement, carryForwardConfig, lapsableLeaveConfig);
        } else {
            applyTimeoffForOverlappingCycles(timeoffEntries, currentSummary, nextSummary, entitlement, carryForwardConfig, lapsableLeaveConfig);
        }
    }


    private void applyCurrentSummaryTimeoff(
            List<TimeoffEntryDBO> timeoffEntries,
            TimeoffSummaryDBO clonedCurrentSummary,
            CarryForwardConfigEntity carryForwardConfig
    ) {

        var requestedDays = calculateTimeoffDaysInRange(timeoffEntries, clonedCurrentSummary.periodStart(), clonedCurrentSummary.periodEnd());

        log.info("[applyCurrentSummaryTimeoff] calculating summary breakdown for timeoff in current summary (requested days = {}) ==> STARTED", requestedDays);

        // calculate timeoff days used from carry forward balance
        val usingFromCarryForwardBalance = calculateDaysUsingFromCarryForward(timeoffEntries, requestedDays, clonedCurrentSummary, carryForwardConfig);
        clonedCurrentSummary.usedFromCarryForwardCount(clonedCurrentSummary.usedFromCarryForwardCount() + usingFromCarryForwardBalance);
        requestedDays -= usingFromCarryForwardBalance;
        log.info("[applyCurrentSummaryTimeoff] USING FROM CARRY FORWARD COUNT = {} (remaining days = {})", usingFromCarryForwardBalance, requestedDays);

        // calculate timeoff days used from allocated balance
        val usingFromAllocatedBalance = requestedDays > 0 ? requestedDays : 0;
        clonedCurrentSummary.usedFromAllocatedCount(clonedCurrentSummary.usedFromAllocatedCount() + usingFromAllocatedBalance);
        requestedDays -= usingFromAllocatedBalance;
        log.info("[applyCurrentSummaryTimeoff] USING FROM ALLOCATED COUNT = {} (remaining days = {})", usingFromAllocatedBalance, requestedDays);

        log.info("[applyCurrentSummaryTimeoff] calculating summary breakdown for timeoff in current summary  ==> DONE");
    }

    private void applyNextSummaryTimeOff(
            List<TimeoffEntryDBO> timeoffEntries,
            TimeoffSummaryDBO clonedCurrentSummary,
            TimeoffSummaryDBO clonedNextSummary,
            TimeoffEntitlementDBO entitlement,
            CarryForwardConfigEntity carryForwardConfig,
            LapsableLeaveConfigEntity lapsableLeaveConfig
    ) {

        log.info("[applyNextSummaryTimeOff] summary breakdown calculation for nex cycle timeoffs => STARTED");
        var requestedDays = calculateTimeoffDaysInRange(timeoffEntries, clonedNextSummary.periodStart(), clonedNextSummary.periodEnd());

        // 1. First, use lapsable leaves from the current summary
        val usingFromLapsable = calculateDaysUsingFromLapsable(requestedDays, clonedCurrentSummary, carryForwardConfig, lapsableLeaveConfig, entitlement);
        log.info("[applyNextSummaryTimeOff] USING FROM LAPSABLE COUNT = {} (requesting days = {})", usingFromLapsable, requestedDays);
        clonedCurrentSummary.usedFromLapsableCount(clonedCurrentSummary.usedFromLapsableCount() + usingFromLapsable);
        requestedDays -= usingFromLapsable;

        // 2. Then use virtual carry forward leaves from the current summary to the next summary
        val usingFromNextCycleCarryForward = calculateDaysUsingFromNextCycleCarryForward(requestedDays, timeoffEntries, clonedCurrentSummary, clonedNextSummary, carryForwardConfig);
        log.info("[applyNextSummaryTimeOff] USING FROM NEXT CYCLE CARRY FORWARD COUNT = {} (requesting days = {})", usingFromNextCycleCarryForward, requestedDays);
        clonedCurrentSummary.usedFromNextCycleCarryForwardCount(clonedCurrentSummary.usedFromNextCycleCarryForwardCount() + usingFromNextCycleCarryForward);
        requestedDays -= usingFromNextCycleCarryForward;

        // 3. Finally, use allocated leaves in the next summary
        val timeoffDaysUsingFromNextCycleAllocated = requestedDays > 0 ? requestedDays : 0;
        log.info("[applyNextSummaryTimeOff] USING FROM NEXT CYCLE ALLOCATED COUNT = {} (requesting days = {})", timeoffDaysUsingFromNextCycleAllocated, requestedDays);
        clonedNextSummary.usedFromAllocatedCount(clonedNextSummary.usedFromAllocatedCount() + timeoffDaysUsingFromNextCycleAllocated);

        log.info("[applyNextSummaryTimeOff] summary breakdown calculation for nex cycle timeoffs => DONE");
    }

    private void applyTimeoffForOverlappingCycles(
            List<TimeoffEntryDBO> timeoffEntries,
            TimeoffSummaryDBO currentSummary,
            TimeoffSummaryDBO nextSummary,
            TimeoffEntitlementDBO entitlementDBO,
            CarryForwardConfigEntity carryForwardConfig,
            LapsableLeaveConfigEntity lapsableLeaveConfig
    ) {
        log.info("[applyTimeoffForOverlappingCycles] calculating summary breakdown for overlapping timeoff ==> STARTED");

        applyCurrentSummaryTimeoff(timeoffEntries, currentSummary, carryForwardConfig);
        applyNextSummaryTimeOff(timeoffEntries, currentSummary, nextSummary, entitlementDBO, carryForwardConfig, lapsableLeaveConfig);

        log.info("[applyTimeoffForOverlappingCycles] calculating summary breakdown for overlapping timeoff ==> DONE");

    }

    private TimeOffUsageSummaryDTO getTimeoffUsageSummaryDTO(TimeoffSummaryDBO currentSummaryBeforeTimeoff, TimeoffSummaryDBO nextSummaryBeforeTimeoff, TimeoffSummaryDBO currentSummaryAfterTimeoff, TimeoffSummaryDBO nextSummaryAfterTimeoff) {
        return TimeOffUsageSummaryDTO.builder()
                .currentSummaryBalance(currentSummaryAfterTimeoff.calculateBalance())
                .nextSummaryBalance(nextSummaryAfterTimeoff.calculateBalance())
                .usedFromAllocated(currentSummaryAfterTimeoff.usedFromAllocatedCount() - currentSummaryBeforeTimeoff.usedFromAllocatedCount())
                .usedFromCarryForward(currentSummaryAfterTimeoff.usedFromCarryForwardCount() - currentSummaryBeforeTimeoff.usedFromCarryForwardCount())
                .usedFromLapsable(currentSummaryAfterTimeoff.usedFromLapsableCount() - currentSummaryBeforeTimeoff.usedFromLapsableCount())
                .usedFromNextCycleCarryForward(currentSummaryAfterTimeoff.usedFromNextCycleCarryForwardCount() - currentSummaryBeforeTimeoff.usedFromNextCycleCarryForwardCount())
                .usedFromNextCycleAllocated(nextSummaryAfterTimeoff.usedFromAllocatedCount() - nextSummaryBeforeTimeoff.usedFromAllocatedCount())
                .build();
    }

    private List<TimeOffSummaryBreakdown> getSummaryBreakdowns(
            TimeoffSummaryDBO currentSummaryBeforeTimeoff,
            TimeoffSummaryDBO nextSummaryBeforeTimeoff,
            TimeoffSummaryDBO currentSummaryAfterTimeoff,
            TimeoffSummaryDBO nextSummaryAfterTimeoff,
            CarryForwardConfigEntity carryForwardConfig) {

        return List.of(
                TimeOffSummaryBreakdown.newBuilder()
                        .summaryStartDate(currentSummaryBeforeTimeoff.periodStart())
                        .summaryEndDate(currentSummaryBeforeTimeoff.periodEnd())
                        .entitledDays(currentSummaryBeforeTimeoff.calculateEntitledDays())
                        .applyingDays(calculateTotalApplyingDays(currentSummaryBeforeTimeoff, currentSummaryAfterTimeoff))
                        .balance(getTimeoffBalanceResponse(currentSummaryAfterTimeoff, carryForwardConfig))
                        .usedDays(getTimeoffUsedDaysResponse(currentSummaryAfterTimeoff))
                        .isCurrentSummary(true)
                        .errors(getErrors(currentSummaryAfterTimeoff))
                        .build(),
                TimeOffSummaryBreakdown.newBuilder()
                        .summaryStartDate(nextSummaryBeforeTimeoff.periodStart())
                        .summaryEndDate(nextSummaryBeforeTimeoff.periodEnd())
                        .entitledDays(nextSummaryBeforeTimeoff.calculateEntitledDays())
                        .applyingDays(calculateTotalApplyingDays(nextSummaryBeforeTimeoff, nextSummaryAfterTimeoff))
                        .balance(getTimeoffBalanceResponse(nextSummaryAfterTimeoff, carryForwardConfig))
                        .usedDays(getTimeoffUsedDaysResponse(nextSummaryAfterTimeoff))
                        .isCurrentSummary(false)
                        .errors(getErrors(nextSummaryAfterTimeoff))
                        .build()
        );

    }

    private Double calculateTotalApplyingDays(TimeoffSummaryDBO summaryBeforeTimeoff, TimeoffSummaryDBO summaryAfterTimeoff) {
        return summaryAfterTimeoff.usedFromAllocatedCount() - summaryBeforeTimeoff.usedFromAllocatedCount()
                + summaryAfterTimeoff.usedFromCarryForwardCount() - summaryBeforeTimeoff.usedFromCarryForwardCount()
                + summaryAfterTimeoff.usedFromLapsableCount() - summaryBeforeTimeoff.usedFromLapsableCount()
                + summaryAfterTimeoff.usedFromNextCycleCarryForwardCount() - summaryBeforeTimeoff.usedFromNextCycleCarryForwardCount();
    }

    private @Nullable CarryForwardConfigEntity getCarryForwardConfig(@NotNull TimeoffEntitlementDBO entitlement) {
        val definition = entitlement.definition();

        if (definition == null || definition.getConfigurations() == null) {
            log.info("Can not find definition for entitlement id : {}", entitlement.id());
            return null;
        }
        return definition.getConfigurations().getCarryForwardConfig();
    }

    private void validateSummaryBreakdownInput(TimeOffSummaryBreakdownInput input) {
        List<String> errors = new ArrayList<>();

        if (input.getContractId() <= 0) {
            errors.add("Invalid contract id : " + input.getContractId());
        }
        if (input.getTypeId() <= 0) {
            errors.add("Invalid type id : " + input.getTypeId());
        }
        validateInputDates(input, errors);
        if (!errors.isEmpty()) {
            throw new ValidationException(String.join("; ", errors));
        }
    }

    private void validateInputDates(TimeOffSummaryBreakdownInput input, List<String> errors) {
        val dateErrors = new ArrayList<String>();
        if (input.getTimeOffStartDate() == null || input.getTimeOffStartDate().getDateOnly() == null) {
            dateErrors.add("Time off start date is required.");
        }
        if (input.getTimeOffEndDate() == null || input.getTimeOffEndDate().getDateOnly() == null) {
            dateErrors.add("Time off end date is required.");
        }
        if (dateErrors.isEmpty()
                && input.getTimeOffStartDate().getDateOnly().isAfter(input.getTimeOffEndDate().getDateOnly())) {
            dateErrors.add("Start date must not be after end date. start date :" + input.getTimeOffStartDate().getDateOnly() + " end date : " + input.getTimeOffEndDate().getDateOnly());
        }
        errors.addAll(dateErrors);
    }

    private LapsableLeaveConfigEntity getLapsableLeaveConfig(@NotNull TimeoffEntitlementDBO entitlement) {
        val definition = entitlement.definition();
        if (definition == null || definition.getConfigurations() == null) {
            return new LapsableLeaveConfigEntity(); //default config
        }
        return definition.getConfigurations().getFutureLeaveConfig().getLapsableLeaveConfig();
    }

    private FutureLeaveConfigEntity getFutureLeaveConfig(@NotNull TimeoffEntitlementDBO entitlement) {
        val definition = entitlement.definition();
        if (definition == null || definition.getConfigurations() == null) {
            return new FutureLeaveConfigEntity(); //default config
        }
        return definition.getConfigurations().getFutureLeaveConfig();
    }

    private List<String> getErrors(TimeoffSummaryDBO summaryDBO) {
        if (summaryDBO.calculateBalance() < 0) {
            return List.of("Insufficient leave balance. Please reduce the number of leaves you're applying for,or get in touch with your manager");
        }
        return List.of();
    }

    /**
     * Calculate timeoff days that are using from the last summary carry forward balance
     */
    private Double calculateDaysUsingFromCarryForward(List<TimeoffEntryDBO> timeOffEntries,
                                                      Double requestedDays,
                                                      TimeoffSummaryDBO summaryDBO,
                                                      CarryForwardConfigEntity carryForwardConfig) {
        if (requestedDays <= 0) {
            return 0.0;
        }
        if (carryForwardConfig == null) {
            log.info("[calculateDaysUsingFromCarryForward] carryForwardConfig is null. Hence treating as carry forward disabled");
            return 0.0;
        }
        double carryForwardBalance = summaryDBO.calculateCarryForwardBalance();
        if (carryForwardBalance < 0) {
            log.info("[calculateDaysUsingFromCarryForward] no carry forward leaves to use : (carry forward : {}, expired : {}, used : {}",
                    summaryDBO.carryForwardCount(), summaryDBO.carryForwardExpiredCount(), summaryDBO.usedFromCarryForwardCount());
            return 0.0;
        }
        val timeoffDaysBeforeCarryForwardExpiry = calculateTimeoffDaysBeforeCFExpiry(carryForwardConfig, timeOffEntries, summaryDBO);
        return Math.min(carryForwardBalance, timeoffDaysBeforeCarryForwardExpiry);
    }


    /**
     * Calculate the next summary timeoff days that are using from the current summary-lapsable balance
     */
    private Double calculateDaysUsingFromLapsable(Double requestedDays,
                                                  TimeoffSummaryDBO summaryDBO,
                                                  CarryForwardConfigEntity carryForwardConfig,
                                                  LapsableLeaveConfigEntity lapsableLeaveConfig,
                                                  TimeoffEntitlementDBO entitlementDBO) {
        if (!lapsableLeaveConfig.getEnabled()) {
            log.info("[calculateDaysUsingFromLapsable] lapsable leaves not enabled. Hence skipping calculation");
            return 0.0;
        }
        double currentBalance = summaryDBO.calculateCurrentSummaryBalance();
        val maxCarryForwardValue = getMaximumCarryForwardCount(carryForwardConfig, entitlementDBO);
        val lapsableBalance = currentBalance - maxCarryForwardValue - summaryDBO.usedFromLapsableCount();
        log.info("[calculateDaysUsingFromLapsable] lapsable balance ({}) = current balance ({}) - max carry forward ({}) - used lapsable ({})",
                lapsableBalance, currentBalance, maxCarryForwardValue, summaryDBO.usedFromLapsableCount());
        return Math.max(0, Math.min(lapsableBalance, requestedDays));
    }

    /**
     * Calculate the next summary timeoff days that are using from the current summary carry forward balance
     */
    private Double calculateDaysUsingFromNextCycleCarryForward(Double requestedDays,
                                                               List<TimeoffEntryDBO> timeoffEntries,
                                                               TimeoffSummaryDBO currentSummary,
                                                               TimeoffSummaryDBO nextSummary,
                                                               CarryForwardConfigEntity carryForwardConfig) {
        if (requestedDays <= 0) {
            return 0.0;
        }
        if (!carryForwardConfig.getEnabled()) {
            log.info("[calculateDaysUsingFromNextCycleCarryForward] carry forward not enabled for summary id : {}", currentSummary.id());
            return 0.0;
        }

        val timeoffDaysBeforeCarryForwardExpiry = calculateTimeoffDaysBeforeNextCycleCFExpiry(
                timeoffEntries,
                nextSummary,
                carryForwardConfig
        );
        val eligibleDaysToApplyFromCarryForward = Math.min(requestedDays, timeoffDaysBeforeCarryForwardExpiry);
        log.info("[calculateDaysUsingFromNextCycleCarryForward] requesting days before carry forward expire = {}", timeoffDaysBeforeCarryForwardExpiry);
        val availableNextCycleCarryForwardBalance = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(carryForwardConfig, currentSummary);
        return Math.min(eligibleDaysToApplyFromCarryForward, availableNextCycleCarryForwardBalance);
    }



    private Double getMaximumCarryForwardCount(CarryForwardConfigEntity carryForwardConfig, TimeoffEntitlementDBO entitlementDBO) {
        if (carryForwardConfig == null || !carryForwardConfig.getEnabled()) {
            return 0.0;
        }
        val maxLimit = carryForwardConfig.getMaxLimit();
        if (maxLimit == null) {
            return Double.MAX_VALUE;
        }

        val limitValue = maxLimit.getValue();
        val limitType = maxLimit.getType();

        if (limitValue == null) {
            return 0.0;
        }

        if (limitType == CarryForwardLimitValueType.PERCENTAGE) {
            val entitledDays = TimeOffUtil.getDays(entitlementDBO.value(), entitlementDBO.unit());
            return entitledDays * (limitValue / 100);
        }

        if (limitType == CarryForwardLimitValueType.FIXED) {
            return limitValue;
        }

        return 0.0;
    }


    private boolean isTimeoffWithinTheSummary(TimeoffSummaryDBO summary, LocalDate startDate, LocalDate endDate) {
        return isDateInRange(startDate, summary.periodStart(), summary.periodEnd())
                && isDateInRange(endDate, summary.periodStart(), summary.periodEnd());
    }


    private boolean isDateInRange(LocalDate date, LocalDate rangeStart, LocalDate rangeEnd) {
        return !date.isBefore(rangeStart) && !date.isAfter(rangeEnd);
    }


    private Double calculateTimeoffDaysInRange(List<TimeoffEntryDBO> timeOffEntries, LocalDate rangeStart, LocalDate rangeEnd) {
        return timeOffEntries.stream()
                .filter(timeoffEntry -> timeoffEntry.type() == TimeOffEntryType.TIMEOFF)
                .filter(timeOffEntry -> isDateInRange(timeOffEntry.date(), rangeStart, rangeEnd))
                .map(timeOffEntry -> {
                    if (timeOffEntry.session() == TimeOffSession.FULL_DAY) {
                        return 1.0;
                    }
                    return 0.5;
                })
                .reduce(0.0, Double::sum);
    }


    private TimeOffBalance getTimeoffBalanceResponse(TimeoffSummaryDBO summary, CarryForwardConfigEntity carryForwardConfig) {
        return TimeOffBalance.newBuilder()
                .carryForwardDays(TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(carryForwardConfig, summary))
                .totalRemainingDays(summary.calculateBalance())
                .carryForwardExpiryDate(getCarryForwardExpiryDate(summary.periodEnd().plusDays(1), carryForwardConfig))
                .build();
    }


    private @Nullable LocalDate getCarryForwardExpiryDate(LocalDate periodStart, CarryForwardConfigEntity carryForwardConfig) {
        if (carryForwardConfig == null || !carryForwardConfig.getEnabled()) {
            return null;
        }
        val expiryConfig = carryForwardConfig.getExpiry();
        // if expiry not enabled => default expiry time is 1 year
        TimeOffDuration expiryDuration = TimeOffDuration.newBuilder()
                .unit(TimeOffUnit.YEARS)
                .value(1.0)
                .build();
        if (expiryConfig != null) {
            expiryDuration = TimeOffDuration.newBuilder()
                    .unit(expiryConfig.getUnit())
                    .value(expiryConfig.getValue())
                    .build();
        }
        return carryForwardLeaveService.getLastValidDate(periodStart, expiryDuration);
    }

    private TimeOffUsedDays getTimeoffUsedDaysResponse(TimeoffSummaryDBO summary) {
        return TimeOffUsedDays.newBuilder().total(calculateTotalUsedDays(summary)).allocated(summary.usedFromAllocatedCount()).carryForward(summary.usedFromCarryForwardCount()).lapsable(summary.usedFromLapsableCount()).nextCycleCarryForward(summary.usedFromNextCycleCarryForwardCount()).build();
    }

    private Double calculateTotalUsedDays(TimeoffSummaryDBO summary) {
        return summary.usedFromAllocatedCount() + summary.usedFromCarryForwardCount() + summary.usedFromLapsableCount() + summary.usedFromNextCycleCarryForwardCount();
    }


    private Double calculateTimeoffDaysBeforeCFExpiry(@NotNull CarryForwardConfigEntity carryForwardConfig,
                                                      List<TimeoffEntryDBO> timeOffEntries,
                                                      TimeoffSummaryDBO currentSummary) {
        return calculateRequestedDaysBeforeExpiry(carryForwardConfig, currentSummary, timeOffEntries);
    }

    private Double calculateTimeoffDaysBeforeNextCycleCFExpiry(List<TimeoffEntryDBO> timeOffEntries,
                                                               TimeoffSummaryDBO nextSummary,
                                                               CarryForwardConfigEntity carryForwardConfig) {
        return calculateRequestedDaysBeforeExpiry(carryForwardConfig, nextSummary, timeOffEntries);
    }

    private Double calculateRequestedDaysBeforeExpiry(CarryForwardConfigEntity carryForwardConfig,
                                                      TimeoffSummaryDBO summaryDBO,
                                                      List<TimeoffEntryDBO> timeOffEntries) {
        val carryForwardExpiryDate = getCarryForwardExpiryDate(summaryDBO.periodStart(), carryForwardConfig);
        val validToDate = carryForwardExpiryDate == null ? summaryDBO.periodEnd() : carryForwardExpiryDate;
        return calculateTimeoffDaysInRange(timeOffEntries, summaryDBO.periodStart(), validToDate);
    }

    private TimeoffSummaryDBO clone(TimeoffSummaryDBO summary) {
        return TimeoffSummaryDBO.builder().periodStart(summary.periodStart()).periodEnd(summary.periodEnd()).allocatedCount(summary.allocatedCount()).carryForwardCount(summary.carryForwardCount()).carryForwardExpiredCount(summary.carryForwardExpiredCount()).usedFromAllocatedCount(summary.usedFromAllocatedCount()).usedFromCarryForwardCount(summary.usedFromCarryForwardCount()).usedFromLapsableCount(summary.usedFromLapsableCount()).usedFromNextCycleCarryForwardCount(summary.usedFromNextCycleCarryForwardCount()).build();
    }

}
