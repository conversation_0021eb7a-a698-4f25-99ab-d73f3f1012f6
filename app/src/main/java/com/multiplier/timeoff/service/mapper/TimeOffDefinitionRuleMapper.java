package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.repository.model.TimeOffDefinitionRuleDBO;
import com.multiplier.timeoff.types.Condition;
import com.multiplier.timeoff.types.Rule;
import com.multiplier.timeoff.types.RuleType;
import lombok.val;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface TimeOffDefinitionRuleMapper {


    default Rule map(List<TimeOffDefinitionRuleDBO> timeOffDefinitionRuleDBOs) {

        if (CollectionUtils.isEmpty(timeOffDefinitionRuleDBOs)) {
            return null;
        }

        if (hasAnyAllType(timeOffDefinitionRuleDBOs)) {
            return Rule.newBuilder()
                    .type(RuleType.ALL)
                    .conditions(Collections.emptyList())
                    .build();
        }

        List<Condition> conditions = timeOffDefinitionRuleDBOs.stream()
                .map(ruleDBO -> Condition.newBuilder()
                        .key(ruleDBO.conditionKey())
                        .operator(ruleDBO.conditionOperator())
                        .values(Arrays.stream(ruleDBO.conditionValues()).collect(Collectors.toList()))
                        .build())
                .collect(Collectors.toList());

        return Rule.newBuilder()
                .type(RuleType.BY_CONDITION)
                .conditions(conditions)
                .build();
    }

    private static boolean hasAnyAllType(List<TimeOffDefinitionRuleDBO> ruleDBOs) {
        return ruleDBOs.stream()
                .anyMatch(rdb -> rdb.ruleType() == RuleType.ALL);
    }


}
