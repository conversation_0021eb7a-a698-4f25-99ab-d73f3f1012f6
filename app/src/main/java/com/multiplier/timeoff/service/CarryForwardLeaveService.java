package com.multiplier.timeoff.service;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.service.builder.mapper.FiltersMapperKt;
import com.multiplier.timeoff.service.calculator.CarryForwardLeaveCalculator;
import com.multiplier.timeoff.service.dto.CarryForwardResult;
import com.multiplier.timeoff.exception.CarryForwardException;
import com.multiplier.timeoff.types.CarryForwardConfig;
import com.multiplier.timeoff.types.TimeOffDuration;
import com.multiplier.timeoff.types.TimeOffUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "[CarryForwardLeaveService]")
public class CarryForwardLeaveService {

    private final ContractServiceAdapter contractServiceAdapter;
    private final TimeOffRequirementsService requirementsService;

    public CarryForwardResult carryForwardLeaves(TimeoffEntitlementDBO entitlement, TimeoffSummaryDBO previousSummaryDBO, TimeoffSummaryDBO newSummaryDBO, ContractOuterClass.Contract contract) {
        var config = getCarryForwardConfig(contract, previousSummaryDBO.typeId());

        if (config.isEmpty()) {
            log.warn("Valid Definition / Carry-Forward-Config is Not-Found, skipping. summaryID={} for contractID={}", previousSummaryDBO.typeId(), contract.getId());
            return null;
        }

        newSummaryDBO.carriedCount(
                CarryForwardLeaveCalculator.calculateCarryForwardLeaves(
                        CarryForwardLeaveCalculator.CarryForwardLeaveCalculateData
                                .builder()
                                .allocationCount(
                                        TimeOffUtil.round(TimeOffUtil.getDays(entitlement.value(), entitlement.unit()))
                                )
                                .totalEntitledCount(previousSummaryDBO.totalEntitledCount())
                                .totalTakenCount(
                                        previousSummaryDBO.takenCount() + previousSummaryDBO.pendingCount()
                                )
                                .config(config.get())
                                .build()
                )
        );

        return new CarryForwardResult(
                newSummaryDBO.carriedCount(),
                getLastValidDate(newSummaryDBO.periodStart(), config.get().getExpiry())
        );
    }

    public Optional<CarryForwardConfig> getCarryForwardConfig(final ContractOuterClass.Contract contract, final long typeId) {

        return requirementsService.getCompanyTimeOffRequirements(FiltersMapperKt.mapToDefinitionFilters(contract, typeId))
                .getDefinitions()
                .stream().findAny()
                .filter(m ->
                        m.getTypeId() == typeId
                                && Objects.nonNull(m.getConfiguration())
                                && Objects.nonNull(m.getConfiguration().getCarryForward())
                                && m.getConfiguration().getCarryForward().getEnabled()
                )
                .map(m -> m.getConfiguration().getCarryForward());
    }

    public LocalDate getLastValidDate(final LocalDate periodStart, final TimeOffDuration expiry) {
        if (expiry == null || expiry.getValue() == null || expiry.getUnit() == null) {
            return null;
        }

        // if expiry.value is not a whole number, we'll need convert expiry.unit to DAYS
        TimeOffUnit unit = expiry.getUnit();
        double value = expiry.getValue();
        if (value % 1 != 0) {
            // value is not a whole number
            value = TimeOffUtil.round(TimeOffUtil.getDays(value, unit));
            unit = TimeOffUnit.DAYS;
        }

        long longValue = Math.round(value);
        LocalDate expiryDate = periodStart;
        switch (unit) {
            case DAYS:
                expiryDate = expiryDate.plusDays(longValue);
                break;
            case WEEKS:
                expiryDate = expiryDate.plusWeeks(longValue);
                break;
            case MONTHS:
                expiryDate = expiryDate.plusMonths(longValue);
                break;
            case YEARS:
                expiryDate = expiryDate.plusYears(longValue);
                break;
            default:
                throw new CarryForwardException("Carry forward expiry duration unit not supported: " + unit);
        }

        return expiryDate.minusDays(1);
    }
}
