package com.multiplier.timeoff.service;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ApprovalServiceAdapter;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.core.common.db.AuditUser;
import com.multiplier.timeoff.core.common.util.PageRequestHelper;
import com.multiplier.timeoff.core.common.util.PageResultHelper;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.repository.TimeoffRepository;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.service.builder.TimeoffFilters;
import com.multiplier.timeoff.service.builder.TimoffSpecificationBuilder;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.TimeoffMapper;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.multiplier.timeoff.adapters.CompanyServiceAdapterKt.toCompanyUserFilters;
import static com.multiplier.timeoff.core.util.TimeOffUtil.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class TimeoffQuery {

    private final TimeoffRepository timeoffRepo;
    private final TimoffSpecificationBuilder timeoffSpecBuilder;
    private final TimeoffMapper mapper;
    private final PageRequestHelper pageRequestHelper;

    private final ContractServiceAdapter contractServiceAdapter;
    private final CompanyServiceAdapter companyServiceAdapter;
    private final ApprovalServiceAdapter approvalServiceAdapter;
    private final CurrentUser currentUser;
    private final FeatureFlagService featureFlagService;
    private final AuthorizationService authorizationService;

    public static final String COMPANY_EXP = "company";
    public static final String MEMBER_EXP = "member";

    /**
     * Only Member and Company can call this (check TimeoffDataFetcher.getAll())
     */
    @Transactional
    public List<TimeOff> getAll(TimeoffFilters filters) {
        // contractIds is mandatory to prevent data leak
        if (CollectionUtils.isEmpty(filters.contractIds())) {
            return Collections.emptyList();
        }

        var timeoffDBOs = timeoffRepo.findAll(
                timeoffSpecBuilder.build(filters),
                Sort.sort(TimeoffDBO.class).by((TimeoffDBO m) -> m.type().key()).ascending());

        // exclude ones which are DRAFT and not created by me (i.e. company users created on behalf of me)
        timeoffDBOs = timeoffDBOs.stream()
                .filter(timeOff -> timeOff.status() != TimeOffStatus.DRAFT
                        || isCreatedByMeInMemberView(timeOff))
                .toList();

        return mapper.map(timeoffDBOs);
    }

    /**
     * This method return all timeoffs (except for DELETED and REJECTED timeoffs) for given contract where timeoff startDate is between fromDate and toDate
     * Caller should make sure current user have access to the given contract id
     */
    public List<TimeoffDBO> findTimeoffsByContractAndDateRange(Long contractId, LocalDate fromDate, LocalDate toDate) {
        return timeoffRepo.findTimeoffsByContractAndDateRange(contractId, fromDate, toDate);
    }

    @Transactional
    public TimeOffsResponse getAllForOperationsWithPagination(TimeOffFilter filters, PageRequest pageRequest) {
        log.info("[TimeoffQuery] getAllForOperationsWithPagination");

        final var springPageRequest = pageRequestHelper.toSpringPageRequestWithDefaultSort(pageRequest, "id");
        final var spec = timeoffSpecBuilder.build(TimeoffFilters.build(filters));

        var timeoffDBOs = timeoffRepo.findAll(spec);
        timeoffDBOs = filterMatchingContractAttributes(timeoffDBOs, filters);

        // convert unpaged timeoffDBOs to paged
        Page<TimeoffDBO> page = listToPage(timeoffDBOs, springPageRequest);

        TimeOffsResponse timeOffsResponse = TimeOffsResponse.newBuilder()
                .page(PageResultHelper.build(page))
                .timeOffs(mapper.map(page.getContent()))
                .build();

        populateCounts(timeOffsResponse);

        return timeOffsResponse;
    }

    private void populateCounts(TimeOffsResponse timeOffsResponse) {

        var allCount = timeoffRepo.countAllByStatusIsNot(TimeOffStatus.DELETED);
        var pendingCount = timeoffRepo.countAllByStatusIsIn(List.of(TimeOffStatus.SUBMITTED, TimeOffStatus.APPROVAL_IN_PROGRESS));
        var approvedCount = timeoffRepo.countAllByStatusIsIn(List.of(TimeOffStatus.APPROVED));

        timeOffsResponse.setAllCount(allCount);
        timeOffsResponse.setPendingCount(pendingCount);
        timeOffsResponse.setApprovedCount(approvedCount);
    }

    /**
     * Only Company exp should call this
     */
    @Transactional
    public List<TimeOff> getAllForCompany(DgsDataFetchingEnvironment dfe, TimeoffFilters filters) {
        UserContext userContext = currentUser.getContext();
        String experience = userContext.getExperience();
        log.info("getAllForCompany. userContext: {}", userContext);
        if (!COMPANY_EXP.equals(experience)) {
            // If Ops: use the pagination query (where the top level is TimeOff, not Company) - this.getAllForOperationsWithPagination()
            // if Member: should just go to this.getAll()
            log.warn("Experience is not company ({}), likely due to the header.current-experience is member while the user is still in Team view correctly. So letting the code continue", experience);
        }

        refineFilters(filters);
        val companyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));

        val allTimeOffsForCompany = timeoffRepo.findAll(timeoffSpecBuilder.build(filters));
        val timeOffsForMe = filterTimeOffsForMe(allTimeOffsForCompany, dfe, companyUser);

        // Actionable or not Actionable
        return withIsActionable(mapper.map(timeOffsForMe), companyUser);
    }

    private List<TimeoffDBO> filterTimeOffsForMe(List<TimeoffDBO> allTimeOffs,
                                                 DgsDataFetchingEnvironment dfe,
                                                 CompanyOuterClass.CompanyUser companyUser) {
        log.info("[filterTimeOffsForMe] Found {} timeoffs for company", allTimeOffs.size());
        Map<Boolean, List<TimeoffDBO>> partitionedExpensesByDraft = allTimeOffs.stream()
                .collect(Collectors.partitioningBy(timeoffDBO -> timeoffDBO.status() == TimeOffStatus.DRAFT));

        List<TimeoffDBO> allDraftTimeoffs = partitionedExpensesByDraft.get(true);
        List<TimeoffDBO> allNonDraftTimeoffs = partitionedExpensesByDraft.get(false);
        val assignedIds = Objects.requireNonNull(approvalServiceAdapter.getAssignedItems(companyUser.getId())).getIdsList();
        val assignedTimeOffsForMe = allNonDraftTimeoffs.stream()
                .filter(timeOff -> assignedIds.contains(timeOff.id()))
                .toList();
        log.info("[filterTimeOffsForMe] Found {} assigned timeoffs for me", assignedTimeOffsForMe.size());
        val draftTimeOffsCreatedByMe = allDraftTimeoffs.stream()
                .filter(this::isCreatedByMeInTeamView)
                .toList();
        log.info("[filterTimeOffsForMe] Found {} draft timeoffs created by me", draftTimeOffsCreatedByMe.size());

        List<TimeoffDBO> allTimeOffsForMe;
        if (companyUser.getIsAdmin() || isPayrollAdmin(companyUser) || isHrAdmin(companyUser)) {
            val authorizedTimeOffs = filterAuthorizedTimeOffs(allNonDraftTimeoffs, dfe);
            log.info("[filterTimeOffsForMe] Found {} authorized timeoffs for me", authorizedTimeOffs.size());
            allTimeOffsForMe = mergeLists(authorizedTimeOffs, assignedTimeOffsForMe, draftTimeOffsCreatedByMe);
        } else {
            // we do not need to apply ABAC filter for all users except payroll admin, super admin & hr admin
            // because these users should see assigned timeoffs only regardless of the entity
            allTimeOffsForMe = mergeLists(draftTimeOffsCreatedByMe, assignedTimeOffsForMe);
        }

        log.info("[filterTimeOffsForMe] Final eligible timeoffs count for me: {}", allTimeOffsForMe.size());
        return allTimeOffsForMe;
    }


    private List<TimeoffDBO> filterAuthorizedTimeOffs(List<TimeoffDBO> allTimeoffs, DgsDataFetchingEnvironment dfe) {
        boolean abacEnabled = featureFlagService.isOn(FeatureFlags.ENABLE_ABAC, Map.of("service", "timeoff-service"));
        if (abacEnabled) {
            log.info("[filterAuthorizedTimeOffs] Applying ABAC filtering for {} timeoffs", allTimeoffs.size());
            return authorizationService.filterTimeOffs(dfe, allTimeoffs);
        }
        return allTimeoffs;
    }

    private void refineFilters(TimeoffFilters filters) {
        val companyId = Objects.requireNonNull(currentUser.getContext()).getScopes().getCompanyId();
        if (companyId == null) {
            throw new ValidationException("Cannot find the company ID from the current user");
        }
        val startedContractIds = contractServiceAdapter.getStartedContractIdsByCompanyId(companyId);
        filters.contractIds(refineContractIds(filters.contractIds(), startedContractIds));
    }

    private Set<Long> refineContractIds(Set<Long> contractIds, List<Long> startedContractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return TimeOffUtil.toSet(startedContractIds);
        }
        return contractIds.stream()
                .filter(startedContractIds::contains)
                .collect(Collectors.toSet());
    }

    public List<TimeOff> getApproveByMeTimeoffs(List<TimeOff> timeOffs) {
        var companyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));
        // currently we need to add approver timeoffs only for payroll admin
        if (!isPayrollAdmin(companyUser)) {
            return Collections.emptyList();
        }
        var assignedIds = Objects.requireNonNull(approvalServiceAdapter.getAssignedItems(companyUser.getId())).getIdsList();
        return timeOffs.stream()
                .filter(timeOff -> assignedIds.contains(timeOff.getId()))
                .collect(Collectors.toList());
    }


    private List<TimeOff> withIsActionable(List<TimeOff> timeOffs, CompanyOuterClass.CompanyUser companyUser) {
        // admin = god, only need to check status
        if (companyUser.getIsAdmin()) {
            return timeOffs.stream()
                    .peek(timeOff -> timeOff.setIsActionable(timeOff.getStatus() == TimeOffStatus.APPROVAL_IN_PROGRESS))
                    .toList();
        }

        // manager, has to check approval
        var validStatusTimeoffIds = timeOffs.stream()
                .filter(timeOff -> timeOff.getStatus() == TimeOffStatus.APPROVAL_IN_PROGRESS)
                .map(TimeOff::getId)
                .collect(Collectors.toList());
        Map<Long, Boolean> timeoffIdToApprovableMap = approvalServiceAdapter.canApproveItems(companyUser.getId(), validStatusTimeoffIds);
        timeOffs.forEach(timeOff -> timeOff.setIsActionable(timeoffIdToApprovableMap.getOrDefault(timeOff.getId(), false)));
        return timeOffs;
    }


    private boolean isCreatedByMeInTeamView(TimeoffDBO timeoffDBO) {
        return isCreatedByMeInExperience(timeoffDBO, COMPANY_EXP);
    }

    private boolean isCreatedByMeInMemberView(TimeoffDBO timeoffDBO) {
        return isCreatedByMeInExperience(timeoffDBO, MEMBER_EXP);
    }

    private boolean isCreatedByMeInExperience(TimeoffDBO timeoffDBO, String exp) {
        AuditUser createdByInfo = timeoffDBO.createdByInfo();

        // For existing timeoffs, we have nothing to know which experience the "currentUser" was in when creating this timeoff.
        // Falling back to using `created_by` only, because better accept risk than hide all existing timeoffs
        if (createdByInfo == null) {
            return Objects.equals(timeoffDBO.createdBy(), currentUser.getContext().getId());
        }

        // for new timeoffs (created after we deploy this to prod), we have enough info to get timeoffs created by "me" and in $exp view
        return exp.equals(createdByInfo.getExperience())
                && Objects.equals(createdByInfo.getUserId(), currentUser.getContext().getId());
    }


    @Transactional
    public TimeOff getTimeoffById(Long id) {
        TimeoffDBO timeoff = timeoffRepo.findById(id).orElse(null);
        return mapper.map(timeoff);
    }

    private List<TimeoffDBO> filterMatchingContractAttributes(List<TimeoffDBO> timeoffDBOs, TimeOffFilter filters) {
        final var contractFilters = buildContractFilters(timeoffDBOs.stream().map(TimeoffDBO::contractId).toList(), filters.getContractCountry(), filters.getContractStatus());
        final var filteredContractIds = contractServiceAdapter.getContractIdsMatchingFilters(contractFilters);
        // only keep timeoffs for contractIds that matched the filter
        return timeoffDBOs.stream().filter(t -> filteredContractIds.contains(t.contractId())).toList();
    }

    private ContractOuterClass.ContractFilters buildContractFilters(List<Long> contractIds, CountryCode contractCountry, ContractStatus contractStatus) {
        return ContractOuterClass.ContractFilters.newBuilder()
                .addAllContractIds(contractIds)
                .setCountryCode(Objects.toString(contractCountry, ""))
                .setContractStatus(contractStatus != null ? ContractOuterClass.ContractStatus.valueOf(contractStatus.toString()) : ContractOuterClass.ContractStatus.NULL_CONTRACT_STATUS)
                .setIsTest(Boolean.TRUE.equals(currentUser.getContext().getScopes().isOperationsTestUser()))
                .setContractType(ContractOuterClass.ContractType.NULL_CONTRACT_TYPE)
                .build();
    }

    private <T> Page<T> listToPage(List<T> list, Pageable pageable) {
        final int start = (int) pageable.getOffset();
        final int end = Math.min((start + pageable.getPageSize()), list.size());
        final List<T> pagedList = list.isEmpty() ? List.of() : list.subList(start, end);
        return new PageImpl<>(pagedList, pageable, list.size());
    }

    @NotNull
    @Transactional
    public List<TimeOff> getTimeOffsByContractIds(@NotNull Collection<Long> contractIds) {
        return mapper.map(timeoffRepo.findAllByContractIdIn(contractIds));
    }
}
