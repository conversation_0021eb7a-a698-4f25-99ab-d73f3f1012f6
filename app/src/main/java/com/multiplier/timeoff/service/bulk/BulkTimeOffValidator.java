package com.multiplier.timeoff.service.bulk;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.core.common.dto.BulkTimeOffDataHolder;
import com.multiplier.timeoff.core.common.dto.BulkTimeOffRequest;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.service.exception.BulkValidationException;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.processor.TimeoffValidator;
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.validation.constraints.NotNull;

import javax.annotation.Nullable;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.multiplier.timeoff.ProtobufExtensionsKt.toLocalDate;
import static com.multiplier.timeoff.core.util.TimeOffUtil.toContractIdTypeIDKey;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "[BulkTimeOffValidator]")
public class BulkTimeOffValidator {
    private final FeatureFlagService featureFlagService;
    private final TimeoffValidator timeoffValidator;
    public static final String UNPAID_LEAVE_TYPE = "Unpaid Leave";

    public List<String> validate(@NotNull BulkTimeOffRequest.Input input, @NotNull BulkTimeOffDataHolder bulkTimeOffDataHolder) {
        val companyId = bulkTimeOffDataHolder.getCompanyId();
        val contract = bulkTimeOffDataHolder.getIdToContractMap().get(input.getContractId());
        List<String> errors = new ArrayList<>();
        try {
            throwIfContractInvalid(contract, companyId, input.getContractId());
            throwIfContractDoesNotBelongToEntity(contract, bulkTimeOffDataHolder.getEntityId());
            validateUpsertData(input, bulkTimeOffDataHolder.getLabelToCompanyTimeOffTypeMap().keySet(), contract, errors);
            if (errors.isEmpty()) {
                validateLeaveBalance(input, bulkTimeOffDataHolder, errors);
            }
        } catch(BulkValidationException e) {
            errors.add(e.getMessage());
        }
        return errors;
    }

    private void throwIfContractInvalid(@Nullable ContractOuterClass.Contract contract, Long companyId, Long contractId) {
        if (contract == null) {
            logAndThrow("Contract not found for contract id : {}", contractId);

        }
        if (!contract.getStarted() && !isStartedLegacy(contract)) {
            logAndThrow("Contract is not started for contract id : {}", contractId);
        }

        if (contract.getCompanyId() != companyId) {
            logAndThrow("Contract id : {} belongs to company Id : {}. Hence Company user from company id : {}, can not upsert timeoffs for this employee", contractId, contract.getCompanyId(), companyId);
        }
    }

    private void throwIfContractDoesNotBelongToEntity(ContractOuterClass.Contract contract, Long entityId) {
        if (entityId == null) return;

        if (contract.getLegalEntityId() != entityId) {
            logAndThrow("Contract id : {} belongs to entity Id : {}. Hence the user from entity id : {}, can not upsert timeoffs for this employee", contract.getId(), contract.getLegalEntityId(), entityId);
        }
    }

    private void validateUpsertData(BulkTimeOffRequest.Input input, Set<String> companyTimeOffTypes, ContractOuterClass.Contract contract, List<String> errors) {
        if (!isValidTimeOffTypeInput(input, companyTimeOffTypes)) {
            errors.add("Time off type is not available in the system. It can be ONLY any of the values, mentioned above in the description");
        }

        boolean isNoOfDaysValid = isValidNoOfDaysInput(input.getNoOfDays());

        if (!isNoOfDaysValid) {
            errors.add("No of days should be greater than zero and can be ONLY in multiples of 0.5");
        }

        boolean isStartDateValid = isValidStartDate(input.getStartDate(), errors);
        boolean isEndDateValid = isValidEndDate(input.getEndDate(), errors);

        if (isStartDateValid && isEndDateValid) {
            LocalDate start = getDateFromString(input.getStartDate());
            LocalDate end = getDateFromString(input.getEndDate());
            LocalDate contractStartDate = toLocalDate(contract.getStartOn());
            if (start.isBefore(contractStartDate)) {
                errors.add("Can not add timeoffs for dates preceding the contract start date. Start date should be after the contract start date");
            }
            if (end.isBefore(contractStartDate)) {
                errors.add("Can not add timeoffs for dates preceding the contract start date. End date should be after the contract start date");
            }
            if (start.isAfter(end)) {
                errors.add("End date should be after the start date");
            } else if (isNoOfDaysValid) {
                val daysBetween = ChronoUnit.DAYS.between(start, end) + 1;
                if (daysBetween < toDouble(input.getNoOfDays())) {
                    errors.add("No of days are higher than the given period for start/end dates");
                }
            }
        }

    }

    private boolean isValidStartDate(String startDate, List<String> errors) {
        if (StringUtils.isBlank(startDate)) {
            errors.add("Start date is empty");
            return false;
        }
        if (!isValidDateInput(startDate)) {
            errors.add("Start date Should be in format of YYYY-MM-DD.");
            return false;
        }
        return true;
    }

    private boolean isValidEndDate(String endDate, List<String> errors) {
        if (StringUtils.isBlank(endDate)) {
            errors.add("End date is empty");
            return false;
        }
        if (!isValidDateInput(endDate)) {
            errors.add("End date Should be in format of YYYY-MM-DD.");
            return false;
        }
        return true;
    }

    private void validateLeaveBalance(BulkTimeOffRequest.Input input, BulkTimeOffDataHolder bulkTimeOffDataHolder, List<String> errors) {
        if (isUnpaidLeaveType(input)) {
            log.info("[validateLeaveBalance] Balance validation skipped for input id : {} since leave type is unpaid", input.getExternalTimeOffId());
            return; // no need to validate balance for unpaid leaves
        }
        if (isCarryForwardExpiryFlagOn(bulkTimeOffDataHolder.getCompanyId())) {
            if (isBalanceSufficientWhenCFExpiryFlagOn(input, bulkTimeOffDataHolder)) {
                return;
            }
        } else if (isBalanceSufficientWhenCFExpiryFlagOff(input, bulkTimeOffDataHolder)) {
            return;
        }
        errors.add("Count is greater than the available leave balance");

    }

    private boolean isBalanceSufficientWhenCFExpiryFlagOn(BulkTimeOffRequest.Input input, BulkTimeOffDataHolder bulkTimeOffDataHolder) {
        val startDate = getDateFromString(input.getStartDate());
        val endDate = getDateFromString(input.getEndDate());
        val noOfDays = toDouble(input.getNoOfDays());
        val typeId = getTimeOffTypeId(bulkTimeOffDataHolder.getLabelToCompanyTimeOffTypeMap(), input);
        val contract = bulkTimeOffDataHolder.getIdToContractMap().get(input.getContractId());
        val contractIdTypeIdKey = toContractIdTypeIDKey(contract.getId(), typeId);
        val allocationRecords = bulkTimeOffDataHolder.getContractIdTypeIdToAllocationECRsMap().getOrDefault(contractIdTypeIdKey, List.of());
        val deductionRecords = filterDeductionRecords(bulkTimeOffDataHolder.getDeductionECRs(), getECRIds(allocationRecords));
        val timeOffs = new ArrayList<>(bulkTimeOffDataHolder.getContractIdTypeIdToTimeOffsMap().getOrDefault(contractIdTypeIdKey, new ArrayList<>()));
        timeOffs.removeIf(timeoff -> timeoff.externalId() != null && timeoff.externalId().equals(input.getExternalTimeOffId()));

        return timeoffValidator.isBalanceSufficient(startDate, endDate, noOfDays, allocationRecords, deductionRecords, timeOffs);
    }

    private boolean isBalanceSufficientWhenCFExpiryFlagOff(BulkTimeOffRequest.Input input,
                                                           BulkTimeOffDataHolder bulkTimeOffDataHolder) {
        val contract = bulkTimeOffDataHolder.getIdToContractMap().get(input.getContractId());
        val contractIdTypeIdKey = toContractIdTypeIDKey(contract.getId(), getTimeOffTypeId(bulkTimeOffDataHolder.getLabelToCompanyTimeOffTypeMap(), input));
        val summary = bulkTimeOffDataHolder.getContractIdTypeIdToSummaryMap().get(contractIdTypeIdKey);
        if (summary == null) {
            log.info("can not find summary for contract id_typeId : {}", contractIdTypeIdKey);
            return false;
        }
        val existingTimeOff = bulkTimeOffDataHolder.getExternalIdToExistingTimeOffMap().get(input.getExternalTimeOffId());
        Double noOfDaysInExistingTimeOff = existingTimeOff == null ? 0.0 : existingTimeOff.noOfDays();
        Double noOfDays = toDouble(input.getNoOfDays());
        Double remainingCount = Optional.ofNullable(summary.totalEntitledCount()).orElse(0.0) - (summary.takenCount() - noOfDaysInExistingTimeOff) - summary.pendingCount();
        return remainingCount.compareTo(noOfDays) >= 0;
    }

    private boolean isUnpaidLeaveType(BulkTimeOffRequest.Input input) {
        return input.getType().equalsIgnoreCase(UNPAID_LEAVE_TYPE);
    }

    private void logAndThrow(String message, Object... args) {
        log.error(message, args);
        throw new BulkValidationException("Employee ID is invalid. Either the employee ID is not present or the employee is not activated");
    }

    private boolean isValidDateInput(String date) {
        try {
            BulkTimeOffHelper.parseDate(date);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isValidTimeOffTypeInput(BulkTimeOffRequest.Input input, Set<String> companyTimeOffTypes) {
        if (StringUtils.isBlank(input.getType())) {
            return false;
        }
        return companyTimeOffTypes.stream().anyMatch(TimeOffUtil.getLabelToTimeOffTypeMapKey(input.getType())::equals);
    }

    private boolean isValidNoOfDaysInput(String noOfDays) {
        try {
            double value = toDouble(noOfDays);
            return value > 0 && value % 0.5 == 0;
        } catch (Exception e) {
            return false;
        }
    }

    private List<Long> getECRIds(List<EntitlementChangeRecordEntity> allocationRecords) {
        return allocationRecords
                .stream()
                .map(EntitlementChangeRecordEntity::getId)
                .collect(Collectors.toList());
    }

    private List<EntitlementChangeRecordEntity> filterDeductionRecords(List<EntitlementChangeRecordEntity> deductionRecords,
                                                                 List<Long> allocationRecordIds) {
        return deductionRecords.stream()
                .filter( ecr -> allocationRecordIds.contains(ecr.getRefId()))
                .collect(Collectors.toList());
    }

    private Double toDouble(String string) {
        return Double.parseDouble(string);
    }


    private Long getTimeOffTypeId(Map<String, TimeoffTypeDBO> labelToTypeMap, BulkTimeOffRequest.Input input) {
        return labelToTypeMap.get(TimeOffUtil.getLabelToTimeOffTypeMapKey(input.getType())).id();
    }

    private boolean isCarryForwardExpiryFlagOn(Long companyId) {
        return featureFlagService.feature(
                FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY,
                Map.of("company", companyId)
        ).on();
    }

    private LocalDate getDateFromString(String date) {
        return BulkTimeOffHelper.parseDate(date);
    }


    private boolean isStartedLegacy(ContractOuterClass.Contract contract) {
        return contract.getStatus() == ContractOuterClass.ContractStatus.ACTIVE || contract.getStatus() == ContractOuterClass.ContractStatus.OFFBOARDING;
    }
}
