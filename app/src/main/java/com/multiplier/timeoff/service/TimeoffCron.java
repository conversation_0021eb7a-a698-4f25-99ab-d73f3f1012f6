package com.multiplier.timeoff.service;


import com.multiplier.timeoff.kafka.TimeoffKafkaPublisher;
import com.multiplier.timeoff.repository.TimeoffRepository;
import com.multiplier.timeoff.service.mapper.TimeoffMapper;
import com.multiplier.timeoff.types.TimeOff;
import com.multiplier.timeoff.types.TimeOffStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class TimeoffCron {

    private final TimeoffRepository timeoffRepo;
    private final TimeoffKafkaPublisher timeoffKafkaPublisher;
    private final TimeoffMapper timeoffMapper;

    @Transactional
    public void updateTimeoffStatus() {

        try {

            LocalDate aYearAgo = LocalDate.now().minusYears(1);
            LocalDate yesterday = LocalDate.now().minusDays(1);

            var timeoffList = timeoffRepo.findByStatusAndStartDateBetween(TimeOffStatus.APPROVED, aYearAgo, yesterday);
            if (CollectionUtils.isEmpty(timeoffList)) {
                log.info("[TimeoffCron] No timeoff found for update");
                return;
            }
            timeoffList.forEach(timeoff -> timeoff.status(TimeOffStatus.TAKEN));
            timeoffRepo.saveAll(timeoffList);
            List<TimeOff> timeoffs = timeoffMapper.map(timeoffList);
            timeoffs.forEach(timeoffKafkaPublisher::publishTimeoffUpdateEvent);
        } catch (Exception e) {
            log.error("[TImeoffCron] Timeoff status update process - FAILED", e);
        }
    }
}
