package com.multiplier.timeoff.service;

import com.multiplier.timeoff.core.constant.Constant;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.processor.AllocationProcessor;
import com.multiplier.timeoff.processor.CarryForwardExpirationProcessor;
import com.multiplier.timeoff.processor.CarryForwardProcessor;
import com.multiplier.timeoff.types.TimeOffAllocationInput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

import static com.multiplier.timeoff.core.constant.Constant.SCHEDULED_JOB_NAME.*;

@Component
@EnableScheduling
@Slf4j
@RequiredArgsConstructor
public class TimeoffScheduler {

    private final TimeoffCron timeoffCron;
    private final TimeoffService timeoffService;
    private final AllocationProcessor allocationProcessor;
    private final CarryForwardProcessor carryForwardProcessor;
    private final CarryForwardExpirationProcessor carryForwardExpirationProcessor;
    private final TimeoffSummaryService timeoffSummaryService;
    private final FeatureFlagService featureFlagService;

    @Scheduled(cron = "0 0 1 * * ?", zone = "GMT") // 1 AM everyday
    @SchedulerLock(name = TriggerTimeOffUpdate,
            lockAtLeastFor = Constant.SHEDLOCK_TIME_CONFIGURATION.lockAtLeastForString, // lock for at least 30 minutes, overriding the default setting here for each scheduler
            lockAtMostFor = Constant.SHEDLOCK_TIME_CONFIGURATION.lockAtMostForString)  // lock for at most 60 minutes
    protected void triggerTimeoffUpdate() {
        UUID lockId = UUID.randomUUID();
        logMessage(TriggerTimeOffUpdate, lockId, LocalDateTime.now(), "START");
        try {
            timeoffCron.updateTimeoffStatus();
        } catch (Exception ex) {
            log.error("timeoff status update log failed", ex);
        } finally {
            logMessage(TriggerTimeOffUpdate, lockId, LocalDateTime.now(), "DONE");
        }

    }

    @Scheduled(cron = "0 0 2 * * *", zone = "GMT") // 2 AM everyday
    @SchedulerLock(name = TriggerLegacyTimeoffReallocation,
            lockAtLeastFor = Constant.SHEDLOCK_TIME_CONFIGURATION.lockAtLeastForString, // lock for at least 30 minutes, overriding the default setting here for each scheduler
            lockAtMostFor = Constant.SHEDLOCK_TIME_CONFIGURATION.lockAtMostForString)  // lock for at most 60 minutes
    public void triggerLegacyTimeoffReallocation() {
        UUID lockId = UUID.randomUUID();
        logMessage(TriggerLegacyTimeoffReallocation, lockId, LocalDateTime.now(), "START");
        try {
            timeoffService.reallocateTimeoff(LocalDate.now());
        }
        catch (Exception ex) {
            log.error("Legacy timeoff reallocation failed", ex);
        } finally {
            logMessage(TriggerLegacyTimeoffReallocation, lockId, LocalDateTime.now(), "DONE");
        }
    }

    @Scheduled(cron = "0 0 2 * * *", zone = "GMT") // 2 AM everyday
    @SchedulerLock(name = TriggerTimeoffAllocation,
            lockAtLeastFor = Constant.SHEDLOCK_TIME_CONFIGURATION.lockAtLeastForString, // lock for at least 30 minutes, overriding the default setting here for each scheduler
            lockAtMostFor = Constant.SHEDLOCK_TIME_CONFIGURATION.lockAtMostForString)  // lock for at most 60 minutes
    public void triggerTimeoffAllocation() {
        UUID lockId = UUID.randomUUID();
        logMessage(TriggerTimeoffAllocation, lockId, LocalDateTime.now(), "START");
        try {
            allocationProcessor.allocateTimeoffBalances(TimeOffAllocationInput.newBuilder().expiryDate(LocalDate.now()).build());
        }
        catch (Exception ex) {
            log.error("Timeoff balance allocation failed", ex);
        } finally {
            logMessage(TriggerTimeoffAllocation, lockId, LocalDateTime.now(), "DONE");
        }
    }

    @Scheduled(cron = "0 0 5 * * *", zone = "GMT") // 5 AM everyday - 3hrs from timeoff allocation schedule
    @SchedulerLock(name = TriggerTimeoffCarryForward,
            lockAtLeastFor = Constant.SHEDLOCK_TIME_CONFIGURATION.lockAtLeastForString, // lock for at least 30 minutes, overriding the default setting here for each scheduler
            lockAtMostFor = Constant.SHEDLOCK_TIME_CONFIGURATION.lockAtMostForString)  // lock for at most 60 minutes
    public void triggerTimeoffCarryForward() {
        UUID lockId = UUID.randomUUID();
        logMessage(TriggerTimeoffCarryForward, lockId, LocalDateTime.now(), "START");

        try {
            carryForwardProcessor.carryForwardTimeoffBalances(TimeOffAllocationInput.newBuilder().expiryDate(LocalDate.now()).build());
        }
        catch (Exception ex) {
            log.error("Timeoff carry forward failed", ex);
        } finally {
            logMessage(TriggerTimeoffCarryForward, lockId, LocalDateTime.now(), "DONE");
        }
    }

    @Scheduled(cron = "0 0 1 * * *", zone = "GMT") // 1 AM everyday
    @SchedulerLock(name = TriggerCarryForwardExpiration,
            lockAtLeastFor = Constant.SHEDLOCK_TIME_CONFIGURATION.lockAtLeastForString, // lock for at least 30 minutes, overriding the default setting here for each scheduler
            lockAtMostFor = Constant.SHEDLOCK_TIME_CONFIGURATION.lockAtMostForString)  // lock for at most 60 minutes
    public void triggerCarryForwardExpiration() {
        UUID lockId = UUID.randomUUID();
        logMessage(TriggerCarryForwardExpiration, lockId, LocalDateTime.now(), "START");
        try {
            carryForwardExpirationProcessor.expireCarryForwardTimeoffBalances(TimeOffAllocationInput.newBuilder().expiryDate(LocalDate.now()).build());
        }
        catch (Exception ex) {
            log.error("Timeoff carry forward expiration failed", ex);
        } finally {
            logMessage(TriggerCarryForwardExpiration, lockId, LocalDateTime.now(), "DONE");
        }
    }

    private void logMessage(String loggerName, UUID lockId, LocalDateTime ranTime, String status) {
        log.info("[{}] scheduled job (lock id:- {}) ran at {} - {}", loggerName, lockId, ranTime, status);
    }


    @Scheduled(cron = "0 */10 * * * ?")  // Runs every 10 min, daily. (i.e. 00:00, 00:10, 00:20, 00:30, 00:40, 00:50, etc.)
    @SchedulerLock(name = GENERATE_FUTURE_TIMEOFF_SUMMARIES,
            lockAtLeastFor = Constant.SHEDLOCK_TIME_CONFIGURATION.LOCK_AT_LEAST_FOR_5_MIN, // lock for at least 5 minutes, overriding the default setting here for each scheduler.
            lockAtMostFor = Constant.SHEDLOCK_TIME_CONFIGURATION.LOCK_AT_MOST_FOR_30_MIN)  // lock for at most 30 minutes
    public void generateFutureTimeoffSummaries() {
        if (!isFeatureFlagOn("timeoff-scheduler-future-summary-generation")) {
            log.info("[generateFutureTimeoffSummaries] Future timeoff summaries generation is SKIPPED because (timeoff-scheduler-future-summary-generation) feature flag is OFF");
            return;
        }

        UUID lockId = UUID.randomUUID();

        logMessage("generateFutureTimeoffSummaries", lockId, LocalDateTime.now(), "START");
        timeoffSummaryService.generateFutureTimeoffSummaries(LocalDate.now());
        logMessage("generateFutureTimeoffSummaries", lockId, LocalDateTime.now(), "DONE");
    }

    private boolean isFeatureFlagOn(String flagName) {
        var feature = featureFlagService.feature(flagName, Map.of());
        return feature.on();
    }


    @Scheduled(cron = "0 5/10 * * * ?")  // Runs every 10 minutes starting at minute 5 of each hour, daily. (i.e., 00:05, 00:15, 00:25, etc.)
    @SchedulerLock(name = ACTIVATE_NEXT_TIMEOFF_SUMMARIES,
            lockAtLeastFor = Constant.SHEDLOCK_TIME_CONFIGURATION.LOCK_AT_LEAST_FOR_5_MIN, // lock for at least 5 minutes, overriding the default setting here for each scheduler.
            lockAtMostFor = Constant.SHEDLOCK_TIME_CONFIGURATION.LOCK_AT_MOST_FOR_30_MIN)  // lock for at most 30 minutes
    public void activateNextTimeoffSummaries() {
        if (!isFeatureFlagOn("timeoff-scheduler-next-summary-activation")) {
            log.info("[activateNextTimeoffSummaries] Next timeoff summaries activation is SKIPPED because (timeoff-scheduler-next-summary-activation) feature flag is OFF");
            return;
        }

        UUID lockId = UUID.randomUUID();

        logMessage("activateNextTimeoffSummaries", lockId, LocalDateTime.now(), "START");
        timeoffSummaryService.activateNextTimeoffSummaries(LocalDate.now());
        logMessage("activateNextTimeoffSummaries", lockId, LocalDateTime.now(), "DONE");
    }




}
