package com.multiplier.timeoff.service.bulk;

import com.multiplier.timeoff.processor.TimeoffValidator;
import com.multiplier.timeoff.repository.TimeoffRepository;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.multiplier.timeoff.core.util.TimeOffUtil.toContractIdTypeIDKey;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "[BulkTimeOffHelper]")
public class BulkTimeOffHelper {

    private final TimeoffRepository timeoffRepository;

    public static final List<DateTimeFormatter> DATE_FORMATTERS = List.of(
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy")
    );

    /**
     * Parses a date string into LocalDate using predefined formatters.
     * @param date The date string to parse.
     * @return The parsed LocalDate.
     * @throws IllegalArgumentException if the format is invalid.
     */
    public static LocalDate parseDate(String date) {
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                return LocalDate.parse(date, formatter);
            } catch (DateTimeParseException e) {
                log.debug("Failed to parse date: {} with format: {}", date, formatter);
            }
        }
        throw new IllegalArgumentException("Invalid date format: " + date);
    }

    /**
     * Load all time offs for given contract ids and type ids.
     * Data will return as a map where key is a composite key from contract id and type id ("<contractId>_<typeId>")
     * @param contractIds ; given contract ids
     * @param typeIds : given type ids
     * @param startFromDate : consider timeoffs from this date
     * @return map of time offs. List of time offs for contract and type
     */
    public Map<String, List<TimeoffDBO>> getContractIdTypeIdToTimeOffsMap(Set<Long> contractIds, Set<Long> typeIds, LocalDate startFromDate) {
        return timeoffRepository
                .findAllByTypeInAndContractIdInAndStatusInAndStartDateFrom(typeIds, contractIds, TimeoffValidator.Companion.getPENDING_OR_TAKEN_STATUSES(), startFromDate)
                .stream()
                .collect(Collectors.groupingBy(
                        t -> toContractIdTypeIDKey(t.contractId(), t.typeId()),
                        Collectors.toList()));
    }
}
