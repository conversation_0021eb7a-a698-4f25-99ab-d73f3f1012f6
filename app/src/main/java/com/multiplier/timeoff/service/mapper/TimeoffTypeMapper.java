package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.core.common.dto.TimeoffTypeDTO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.types.TimeOffTypeInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TimeoffTypeMapper {
    default TimeoffTypeDTO mapToDto(TimeoffTypeDBO sourceDBO) {
        if (sourceDBO == null) {
            return null;
        }

        return new TimeoffTypeDTO()
                .setId(sourceDBO.id())
                .setKey(sourceDBO.key())
                .setLabel(sourceDBO.label())
                .setDescription(sourceDBO.description());
    }

    default TimeOffTypeInfo mapToGraph(TimeoffTypeDBO sourceDBO) {
        if (sourceDBO == null) {
            return null;
        }
        return TimeOffTypeInfo.newBuilder()
                .typeId(sourceDBO.id())
                .type(sourceDBO.key())
                .label(sourceDBO.label())
                .description(sourceDBO.description())
                .isPaidLeave(sourceDBO.isPaidLeave())
                .status(sourceDBO.status())
                .companyId(sourceDBO.companyId())
                .createdOn(sourceDBO.createdOn())
                .updatedOn(sourceDBO.updatedOn())
                .build();
    }

    List<TimeoffTypeDTO> mapToDto(List<TimeoffTypeDBO> sourceDBOs);


    List<TimeOffTypeInfo> mapToGraph(List<TimeoffTypeDBO> timeOffTypeDBOS);
}
