package com.multiplier.timeoff.service.timeoffEntry;

import com.multiplier.timeoff.repository.TimeoffEntryRepository;
import com.multiplier.timeoff.repository.dto.TimeoffEntryWithDescription;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "TimeoffEntryServiceV2")
public class TimeoffEntryServiceV2 {
    private final TimeoffEntryRepository repository;

    /**
     * Retrieves a list of non-deleted and non-draft time off entries with type = TIMEOFF for a specific contract,
     * within a given date range.
     *
     * @param from the start date of the date range (inclusive)
     * @param to the end date of the date range (inclusive)
     * @param contractId the identifier of the contract for which time off entries are retrieved
     * @return a list of {@code TimeoffEntryDBO} objects representing the time off entries
     *         that meet the criteria
     */
    public List<TimeoffEntryWithDescription> getNonDeletedNonDraftTimeoffEntries(LocalDate from, LocalDate to, Long contractId) {
        return repository.findTimeOffEntriesWithDescriptionInDateRangeByContractId(from, to, contractId);
    }
}
