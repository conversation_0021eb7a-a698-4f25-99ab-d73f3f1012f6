package com.multiplier.timeoff.service;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.timeoff.core.common.dto.TimeoffTypeDTO;
import com.multiplier.timeoff.repository.CompanyDefinitionRepository;
import com.multiplier.timeoff.repository.TimeoffTypeRepository;
import com.multiplier.timeoff.repository.model.CompanyDefinitionEntity;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.TimeoffTypeMapper;
import com.multiplier.timeoff.service.notifications.TimeoffNotificationHelper;
import com.multiplier.timeoff.types.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.CaseUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.text.MessageFormat.format;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "TimeoffTypeService")
public class TimeoffTypeService {

    private final TimeoffTypeRepository timeoffTypeRepository;
    private final CompanyDefinitionRepository companyDefinitionRepository;
    private final TimeoffTypeMapper timeoffTypeMapper;
    private final CurrentUser currentUser;
    private final TimeoffNotificationHelper timeoffNotificationHelper;


    public List<TimeoffTypeDTO> findAllByIds(Set<Long> ids) {
        return timeoffTypeMapper.mapToDto(findAllTypeDBOsByIds(ids));
    }

    public List<TimeoffTypeDBO> findAllTypeDBOsByIds(Set<Long> ids) {
        return timeoffTypeRepository.findAllById(ids);
    }

    @Transactional
    public TimeOffTypeInfo createTimeOffType(TimeOffTypeCreateInput timeOffTypeCreateInput) {
        val companyId = getCompanyIdFromCurrentUser();
        throwIfCannotCreateTimeOffType(timeOffTypeCreateInput, companyId);

        val key = CaseUtils.toCamelCase(timeOffTypeCreateInput.getName(), false);
        val timeOffType = TimeoffTypeDBO.builder()
                .key(key)
                .label(timeOffTypeCreateInput.getName())
                .isPaidLeave(timeOffTypeCreateInput.getIsPaidLeave())
                .description(timeOffTypeCreateInput.getDescription())
                .companyId(companyId)
                .status(TimeOffTypeStatus.ACTIVE)
                .createdBy(currentUser.getContext().getId())
                .createdOn(LocalDateTime.now())
                .build();
        TimeOffTypeInfo timeOffTypeInfo = timeoffTypeMapper.mapToGraph(timeoffTypeRepository.save(timeOffType));
        timeoffNotificationHelper.sendTimeoffTypeCreatedEmail(timeOffTypeCreateInput.getName());
        return timeOffTypeInfo;
    }

    @Transactional
    public TimeOffTypeInfo updateTimeOffType(@NotNull TimeOffTypeUpdateInput timeOffTypeUpdateInput) {
        val companyId = getCompanyIdFromCurrentUser();
        val timeOffTypeDBO = timeoffTypeRepository.findByIdAndStatusNotIn(timeOffTypeUpdateInput.getTypeId(), Set.of(TimeOffTypeStatus.DELETED));
        if (timeOffTypeDBO == null) {
            log.error("No ACTIVE time-off type with id :{} to update", timeOffTypeUpdateInput.getTypeId());
            return null;
        }
        throwIfCannotUpdateTimeOffType(timeOffTypeDBO, companyId);

        String timeoffTypeOldName = timeOffTypeDBO.label();
        val inputName = timeOffTypeUpdateInput.getName();
        if (!StringUtils.isEmpty(inputName)) {
            val key = CaseUtils.toCamelCase(inputName, false);
            timeOffTypeDBO.key(key);
            timeOffTypeDBO.label(inputName);
        }
        timeOffTypeDBO.isPaidLeave(timeOffTypeUpdateInput.getIsPaidLeave());
        timeOffTypeDBO.description(timeOffTypeUpdateInput.getDescription());
        timeOffTypeDBO.updatedBy(currentUser.getContext().getId());
        timeOffTypeDBO.updatedOn(LocalDateTime.now());

        TimeoffTypeDBO timeoffTypeInfo = timeoffTypeRepository.save(timeOffTypeDBO);
        timeoffNotificationHelper.sendTimeoffTypeUpdatedEmail(timeoffTypeOldName);
        return timeoffTypeMapper.mapToGraph(timeoffTypeInfo);
    }

    @Transactional
    public TimeOffTypeInfo deleteTimeOffType(Long id) {
        val companyId = getCompanyIdFromCurrentUser();
        val timeOffType = timeoffTypeRepository.findByIdAndCompanyIdAndStatusNotIn(id, companyId, Set.of(TimeOffTypeStatus.DELETED));
        if (timeOffType == null) {
            log.error("[deleteTimeOffType] Can not find timeoff type with id : {}, company id : {} to delete", id, companyId);
            return null;
        }

        throwIfCannotDeleteTimeOffType(timeOffType, companyId);
        timeOffType.status(TimeOffTypeStatus.DELETED);
        TimeoffTypeDBO timeOffTypeInfo = timeoffTypeRepository.save(timeOffType);
        timeoffNotificationHelper.sendTimeoffTypeDeletedMail(timeOffTypeInfo.label());
        return timeoffTypeMapper.mapToGraph(timeOffTypeInfo);
    }

    /**
     * Convert to spec builder style once the filter has more fields
     *
     * @param timeOffTypeFilter if ids are given, find by that ids and ignore all other filter fields
     */
    public List<TimeOffTypeInfo> findCompanyTimeOffTypes(Long companyId, TimeOffTypeFilter timeOffTypeFilter) {
        if (timeOffTypeFilter == null) {
            log.info("[findCompanyTimeOffTypes] load all non deleted time-off types for company id : {}", companyId);
            return timeoffTypeMapper.mapToGraph(timeoffTypeRepository.findAllForCompany(companyId));
        }

        if (!CollectionUtils.isEmpty(timeOffTypeFilter.getIds())) {
            return timeoffTypeMapper.mapToGraph(timeoffTypeRepository
                    .findByIdInAndCompanyIdAndStatusNotIn(timeOffTypeFilter.getIds(), companyId, Set.of(TimeOffTypeStatus.DELETED)));

        }
        val timeOffTypes = timeoffTypeRepository.findAllByCompanyIdAndIsPaidLeaveAndStatusNotIn(companyId,
                timeOffTypeFilter.getIsPaidLeave(), Set.of(TimeOffTypeStatus.DELETED));
        return timeoffTypeMapper.mapToGraph(timeOffTypes);
    }

    public Map<Long, Long> loadTimeOffTypesUsageMap(Set<Long> typeIds) {
        log.info("[loadTimeOffTypesUsageMap] loading time-off type usages for ids : {}", typeIds);
        val companyId = getCompanyIdFromCurrentUser();
        val definitions = companyDefinitionRepository.findAllNonDeletedByTypeIdInAndCompanyId(typeIds,companyId);

        return typeIds.stream()
                .collect(Collectors.toMap(
                        typeId -> typeId,
                        typeId -> definitions.stream()
                                .filter(d -> Objects.equals(d.getTypeId(), typeId))
                                .count()
                ));
    }

    @NotNull
    public TimeoffTypeDBO findTimeOffTypeDBOById(Long id) {
        return timeoffTypeRepository.findById(id).orElseThrow(() -> new ValidationException("Can not find time off type for id : " + id));
    }

    public List<TimeoffTypeDBO> findTimeOffTypeDBOsForCompany(Long companyId) {
        throwIfUnauthorized(companyId);
        return timeoffTypeRepository.findAllForCompany(companyId);
    }

    public List<TimeoffTypeDBO> fetchEntityTimeOffTypes(Long companyId, Long entityId, EntityType entityType) {
        log.info("[fetchEntityTimeOffTypes] load all time-off types for company id : {}, entity id : {}, entity type : COMPANY_ENTITY", companyId, entityId);
        val companyDefinitions = companyDefinitionRepository.findAllNonDeletedByCompanyIdAndEntityIdAndEntityType(companyId, entityId, entityType);
        val timeOffTypeIds = getTimeOffTypeIds(companyDefinitions);

        return timeoffTypeRepository.findAllById(timeOffTypeIds);
    }

    private List<Long> getTimeOffTypeIds(List<CompanyDefinitionEntity> companyDefinitions) {
        log.info("[getTimeOffTypeIds] Extracting time-off type ids from company definitions");

        /* HR Members will have unpaid leave by default, even when there is no policy defined for unpaid leaves
         * Hence we are adding unpaid leave type by default
         */
        val defaultTypeId = HrMemberTimeOffDefinitionKt.getUnpaidLeaveDefinition().getTypeId(); // Unpaid leave

        val timeOffTypeIds = companyDefinitions.stream()
                .map(CompanyDefinitionEntity::getTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!timeOffTypeIds.contains(defaultTypeId)) {
            timeOffTypeIds.add(defaultTypeId);
        }

        return timeOffTypeIds;
    }

    private void throwIfUnauthorized(Long companyId) {
        if (isSystemCall()) {
            // we allow calls originated from system schedulers to pass
            return;
        }
        if (isOpsExperience()) {
            return;
        }
        if (!getCompanyIdFromCurrentUser().equals(companyId)) {
            throw new AccessDeniedException("Access denied for user id : %s to access/modify data of company id : %s".formatted(currentUser.getContext().getId(), companyId));
        }
    }

    private boolean isOpsExperience() {
        return currentUser.getContext().getExperience().equals("operations");
    }

    /**
     * @return true when it looks like being called from the system/cronjob etc...
     */
    private boolean isSystemCall() {
        val ctx = currentUser.getContext();
        return ctx == null
                || ctx.getId() == null
                || ctx.getId() <= 0
                || StringUtils.isEmpty(ctx.getExperience());
    }

    private Long getCompanyIdFromCurrentUser() {
        return Optional.ofNullable(currentUser.getContext().getScopes().getCompanyId())
                .orElseThrow(() -> new ValidationException("Cannot find company id of the current user"));
    }

    private void throwIfCannotCreateTimeOffType(TimeOffTypeCreateInput timeOffTypeCreateInput, Long companyId) {
        if (StringUtils.isEmpty(timeOffTypeCreateInput.getName())) {
            throw new IllegalArgumentException(format("Can not create time-off types with empty name for input: {0}, company id : {1}", timeOffTypeCreateInput, companyId));
        }

        if (timeoffTypeRepository.existsByKeyAndCompanyId(CaseUtils.toCamelCase(timeOffTypeCreateInput.getName(), false), companyId)) {
            throw new ValidationException(format("Can not create time-off type {0} for company id {1}, since its already exist", timeOffTypeCreateInput.getName(), companyId));
        }
    }

    private void throwIfCannotUpdateTimeOffType(@NotNull TimeoffTypeDBO timeoffTypeDBO, Long companyId) {
        if (timeoffTypeDBO.companyId() == null || !timeoffTypeDBO.companyId().equals(companyId)) {
            throw new ValidationException(format("Can not update time off types id:{0} of company id:{1} by company user in company id:{2}"
                    , timeoffTypeDBO.id(), timeoffTypeDBO.companyId(), companyId));
        }
    }

    /**
     * TimeOffType can not be deleted in below cases
     * 1. If time-off type belongs to all companies (i.e global timeoff type)
     * 2. If time-off type belongs to different company than current user company
     * 3. if there are existing time off policies created from this timeoff type
     *
     * @param timeOffTypeDBO : timeOffType which is validating to delete
     * @param companyId      : current user company id
     */
    private void throwIfCannotDeleteTimeOffType(@NotNull TimeoffTypeDBO timeOffTypeDBO, Long companyId) {
        if (timeOffTypeDBO.companyId() == null) {
            throw new ValidationException(format("Can not delete global time-off type id: {0}", timeOffTypeDBO.id()));
        }

        if (!timeOffTypeDBO.companyId().equals(companyId)) {
            throw new ValidationException(format("Can not delete time-off type id : {0}, company id {1} since it belongs to company id : {2}"
                    , timeOffTypeDBO.id(), companyId, timeOffTypeDBO.companyId()));
        }

        if (companyDefinitionRepository.countNonDeletedByTypeId(timeOffTypeDBO.id()) > 0) {
            throw new ValidationException(format("Can not delete time-off type id : {0} since, there are existing policies associated with it", timeOffTypeDBO.id()));
        }

    }
}
