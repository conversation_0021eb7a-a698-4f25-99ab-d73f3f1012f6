package com.multiplier.timeoff.service;

import com.multiplier.company.schema.holiday.LegalEntityHoliday;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.HolidayServiceAdapter;
import com.multiplier.timeoff.adapters.WorkshiftServiceAdapter;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.core.util.WorkshiftUtil;
import com.multiplier.timeoff.repository.dto.TimeoffEntryWithDescription;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.timeoffEntry.TimeoffEntryServiceV2;
import com.multiplier.timeoff.types.TimeOffCalendar;
import com.multiplier.timeoff.types.TimeOffCalendarEvent;
import com.multiplier.timeoff.types.TimeOffCalendarEventType;
import com.multiplier.timeoff.types.TimeOffCalendarFilter;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.multiplier.timeoff.core.util.TimeOffUtil.CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "TimeoffCalendarService")
public class TimeoffCalendarService {
    private final AuthorizationService authorizationService;
    private final ContractServiceAdapter contractServiceAdapter;
    private final TimeoffSummaryService timeoffSummaryService;
    private final HolidayServiceAdapter holidayServiceAdapter;
    private final TimeoffEntryServiceV2 timeOffEntryServiceV2;
    private final WorkshiftServiceAdapter workshiftServiceAdapter;

    @Transactional(readOnly = true)
    public TimeOffCalendar getTimeOffCalendar(DgsDataFetchingEnvironment dfe, TimeOffCalendarFilter filter) {
        validateTimeOffCalendar(dfe, filter);
        val startDate = getCalendarStartDate(filter);
        val endDate = getCalendarEndDate(filter);

        val holidays = getHolidaysForCalendar(startDate, endDate, filter.getContractId());
        val entries = timeOffEntryServiceV2.getNonDeletedNonDraftTimeoffEntries(startDate, endDate, filter.getContractId());
        val workShift = workshiftServiceAdapter.getWorkshiftByContractId(filter.getContractId());
        val restDays = WorkshiftUtil.getRestDaysListForRange(startDate, endDate, workShift);

        val calendarEvents = getCalendarEvents(holidays, entries, restDays);

        return TimeOffCalendar.newBuilder()
                .startDate(startDate)
                .endDate(endDate)
                .events(calendarEvents)
                .build();
    }

    public Pair<LocalDate, LocalDate> getTimeOffCalendarStartDateAndEndDateForContractId(Long contractId) {
        val startDate = getCalendarStartDateByContractId(contractId);
        val endDate = getCalendarEndDateByContractId(contractId);
        return Pair.of(startDate, endDate);
    }

    private void validateTimeOffCalendar(DgsDataFetchingEnvironment dfe, TimeOffCalendarFilter filter) {
        if (filter == null) {
            throw new IllegalArgumentException("Can not get timeoff calendar for null filter");
        }
        val contract = contractServiceAdapter.findContractByContractId(filter.getContractId());
        authorizationService.authorize(dfe, contract);
        if (!CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS.contains(contract.getType())) {
            throw new ValidationException(String.format("Contract type is not eligible for timeoffs (id = %d, type = %s)", contract.getId(), contract.getType()));
        }
    }

    private LocalDate getCalendarStartDate(TimeOffCalendarFilter filter) {
        if (filter.getStartDate() != null) {
            return filter.getStartDate();
        }
        return getCalendarStartDateByContractId(filter.getContractId());
    }

    private LocalDate getCalendarStartDateByContractId(Long contractId) {
        val startDate = timeoffSummaryService.getEarliestActiveSummaryStartDate(contractId);
        return getOrThrowValidDate(startDate, String.format("No timeoff summaries found for contract id : %s, " +
            "statuses : (ACTIVE, UPCOMING)", contractId));
    }

    private LocalDate getCalendarEndDate(TimeOffCalendarFilter filter) {
        if (filter.getEndDate() != null) {
            return filter.getEndDate();
        }
        return getCalendarEndDateByContractId(filter.getContractId());
    }

    private LocalDate getCalendarEndDateByContractId(Long contractId) {
        val endDate = timeoffSummaryService.getFurthestSummaryEndDate(contractId);
        return getOrThrowValidDate(endDate, String.format("No timeoff summaries found for contract id : %s, statuses " +
            ": (ACTIVE, UPCOMING)", contractId));
    }

    private LocalDate getOrThrowValidDate(@Nullable LocalDate date, String errorMessage) {
        if (date == null) {
            throw new ValidationException(errorMessage);
        }
        return date;
    }

    private List<LegalEntityHoliday.Holiday> getHolidaysForCalendar(LocalDate startDate, LocalDate endDate, Long contractId) {
        val yearsSet = getYearSet(startDate, endDate);

        // Fetch holidays for all years in the range
        return yearsSet.stream()
                .flatMap(year -> holidayServiceAdapter.getHolidays(Set.of(contractId), year, null).stream())
                .toList();
    }

    private List<Integer> getYearSet(LocalDate startDate, LocalDate endDate) {
        return startDate.getYear() <= endDate.getYear() ?
                IntStream.rangeClosed(startDate.getYear(), endDate.getYear())
                        .boxed()
                        .collect(Collectors.toList()) :
                List.of(startDate.getYear(), endDate.getYear());
    }

    private List<TimeOffCalendarEvent> getCalendarEvents(List<LegalEntityHoliday.Holiday> holidays,
                                                         List<TimeoffEntryWithDescription> timeoffEntries,
                                                         List<LocalDate> restDays) {
        List<TimeOffCalendarEvent> calendarEvents = new ArrayList<>();

        log.info("[getCalendarEvents] Adding timeoff entries to calendar (size = {})", timeoffEntries.size());
        addTimeoffEntries(calendarEvents, timeoffEntries);

        log.info("[getCalendarEvents] Adding holidays to calendar (size = {})", holidays.size());
        addHolidays(calendarEvents, holidays);

        log.info("[getCalendarEvents] Adding Rest days to calendar (size = {})", restDays.size());
        addRestDays(calendarEvents, restDays);

        return calendarEvents;
    }

    private void addTimeoffEntries(List<TimeOffCalendarEvent> calendarEvents, List<TimeoffEntryWithDescription> timeoffEntries) {
        for (TimeoffEntryWithDescription entry : timeoffEntries) {
            calendarEvents.add(
                    TimeOffCalendarEvent.newBuilder()
                            .date(entry.getDate())
                            .session(entry.getSession())
                            .description(entry.getDescription())
                            .type(TimeOffCalendarEventType.TIMEOFF)
                            .build()
            );
        }
    }

    private void addHolidays(List<TimeOffCalendarEvent> calendarEvents, List<LegalEntityHoliday.Holiday> holidays) {
        for (LegalEntityHoliday.Holiday holiday : holidays) {
            LocalDate holidayDate = LocalDate.of(holiday.getYear(), holiday.getMonth(), holiday.getDate());
            calendarEvents.add(TimeOffCalendarEvent.newBuilder()
                    .date(holidayDate)
                    .description(holiday.getName())
                    .type(TimeOffCalendarEventType.HOLIDAY)
                    .build());
        }
    }

    private void addRestDays(List<TimeOffCalendarEvent> calendarEvents, List<LocalDate> restDays) {
        for (LocalDate restDay : restDays) {
            calendarEvents.add(TimeOffCalendarEvent.newBuilder()
                    .date(restDay)
                    .type(TimeOffCalendarEventType.RESTDAY)
                    .build());
        }
    }
}
