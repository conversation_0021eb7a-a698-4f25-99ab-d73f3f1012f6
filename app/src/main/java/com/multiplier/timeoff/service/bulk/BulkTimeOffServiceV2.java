package com.multiplier.timeoff.service.bulk;

import com.multiplier.grpc.common.bulkupload.v1.*;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.core.common.dto.BulkTimeOffRequest;
import com.multiplier.timeoff.core.common.dto.BulkUpsertTimeOffResult;
import com.multiplier.timeoff.core.common.dto.BulkValidateTimeOffResult;
import com.multiplier.timeoff.core.util.TimeOffTemplateMetaData;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.service.TimeoffTypeService;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.types.EntityType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Slf4j(topic = "[BulkTimeOffServiceV2]")
public class BulkTimeOffServiceV2 {
    private final BulkTimeOffService bulkTimeOffService;
    private final TimeoffTypeService timeoffTypeService;
    private final CompanyServiceAdapter companyServiceAdapter;

    private static final String COMPANY = "company";
    private static final String ENTITY = "entity";

    public ValidateUpsertInputBulkResponse validateBulkTimeOffs(ValidateUpsertInputBulkRequest req) {
        log.info("[validateBulkTimeOffs] validating bulk time-off inputs");

        val entityId = getEntityIdFromRequest(req);
        val companyId = getCompanyIdFromRequest(req);
        validateEntityId(entityId, companyId);

        val mappedRequest = getBulkTimeOffRequest(companyId, entityId, req);

        BulkValidateTimeOffResult result = bulkTimeOffService.validateBulkTimeOffs(mappedRequest);

        return getBulkTimeOffResponse(result, mappedRequest);
    }

    public UpsertBulkResponse bulkUpsertTimeOffs(UpsertBulkRequest req) {
        log.info("[bulkUpsertTimeOffs] upserting bulk time-off inputs");

        val entityId = getEntityIdFromRequest(req);
        val companyId = getCompanyIdFromRequest(req);
        validateEntityId(entityId, companyId);

        val mappedRequest = getBulkTimeOffRequest(companyId, entityId, req);

        BulkUpsertTimeOffResult result = bulkTimeOffService.bulkUpsertTimeOffs(mappedRequest);

        return getBulkTimeOffResponse(result, mappedRequest);
    }

    public FieldRequirementsResponse getFieldRequirements(FieldRequirementsRequest request) {
        log.info("[getFieldRequirements] getting field requirements for time-off inputs request: {}", request);

        validateEntityId(request.getEntityId(), request.getCompanyId());

        val fieldRequirements = TimeOffTemplateMetaData.getFieldRequirements();
        setTimeOffTypesAllowedValues(request.getCompanyId(), request.getEntityId(), fieldRequirements);

        log.info("[getFieldRequirements] found the list of field requirements: {}", fieldRequirements);

        return FieldRequirementsResponse.newBuilder()
                .addAllFieldRequirements(fieldRequirements.values())
                .build();
    }

    private ValidateUpsertInputBulkResponse getBulkTimeOffResponse(BulkValidateTimeOffResult bulkValidateTimeOffResult, BulkTimeOffRequest request) {
        List<ValidateUpsertInputResponse> results = getValidatedBulkResponse(bulkValidateTimeOffResult, request);

        return ValidateUpsertInputBulkResponse.newBuilder()
                .addAllResults(results)
                .build();
    }

    private List<ValidateUpsertInputResponse> getValidatedBulkResponse(BulkValidateTimeOffResult bulkValidateTimeOffResult, BulkTimeOffRequest request) {
        Map<String, BulkValidateTimeOffResult.BulkValidateTimeOffResultItem> externalIdToItemMap = new HashMap<>();
        bulkValidateTimeOffResult.getItems().forEach(item -> externalIdToItemMap.put(item.getExternalTimeOffId(), item));

        return request.getInputs().stream()
                .map(i -> {
                    BulkValidateTimeOffResult.BulkValidateTimeOffResultItem foundItem = externalIdToItemMap.get(i.getExternalTimeOffId());

                    if (foundItem != null) {
                        return getFailedItemResponse(foundItem, i);
                    } else {
                        return getSuccessItemResponse(i, request.getCompanyId(), request.getEntityId());
                    }
                }).toList();
    }

    private ValidateUpsertInputResponse getFailedItemResponse(
            BulkValidateTimeOffResult.BulkValidateTimeOffResultItem foundInput,
            BulkTimeOffRequest.Input inputRequest
    ) {
        ValidateUpsertInputResponse.Builder responseBuilder = ValidateUpsertInputResponse.newBuilder()
                .setInputId(inputRequest.getInputId())
                .setSuccess(false);

        foundInput.getErrors().forEach(error -> responseBuilder.addMessages(
                ValidationMessage.newBuilder()
                        .setKey(mapErrorToKey(error))
                        .addErrors(error)
                        .build()
        ));

        return responseBuilder.build();
    }

    private static final Map<String, String> ERROR_TO_KEY_MAP = Map.of(
            "Employee ID", TimeOffTemplateMetaData.EMPLOYEE_ID,
            "Time off type", TimeOffTemplateMetaData.TIME_OFF_TYPE,
            "No of days", TimeOffTemplateMetaData.NO_OF_DAYS,
            "Start date", TimeOffTemplateMetaData.TIME_OFF_START_DATE,
            "End date", TimeOffTemplateMetaData.TIME_OFF_END_DATE,
            "External id", TimeOffTemplateMetaData.TIME_OFF_ID,
            "Count is greater", TimeOffTemplateMetaData.NO_OF_DAYS,
            "Duplicated entries", TimeOffTemplateMetaData.TIME_OFF_ID
    );

    // ToDo: Populate the key with error map; not by checking for contains key word NOSONAR
    private String mapErrorToKey(String error) {
        return ERROR_TO_KEY_MAP.entrySet().stream()
                .filter(entry -> error.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse("");
    }

    private ValidateUpsertInputResponse getSuccessItemResponse(BulkTimeOffRequest.Input inputRequest, Long companyId, Long entityId) {
        Map<String, String> dataMap = new HashMap<>();

        dataMap.put(TimeOffTemplateMetaData.COMPANY_ID, companyId.toString());
        dataMap.put(TimeOffTemplateMetaData.ENTITY_ID, entityId.toString());
        dataMap.put(TimeOffTemplateMetaData.CONTRACT_ID, inputRequest.getContractId().toString());

        dataMap.put(TimeOffTemplateMetaData.TIME_OFF_ID, inputRequest.getExternalTimeOffId());
        dataMap.put(TimeOffTemplateMetaData.TIME_OFF_TYPE, inputRequest.getType());
        dataMap.put(TimeOffTemplateMetaData.NO_OF_DAYS, inputRequest.getNoOfDays());
        dataMap.put(TimeOffTemplateMetaData.TIME_OFF_START_DATE, inputRequest.getStartDate());
        dataMap.put(TimeOffTemplateMetaData.TIME_OFF_END_DATE, inputRequest.getEndDate());

        if (inputRequest.getDescription() != null) {
            dataMap.put(TimeOffTemplateMetaData.DESCRIPTION, inputRequest.getDescription());
        }

        dataMap.put(TimeOffTemplateMetaData.EMPLOYEE_ID, inputRequest.getEmployeeId());

        if (inputRequest.getEmployeeFullName() != null) {
            dataMap.put(TimeOffTemplateMetaData.EMPLOYEE_FULL_NAME, inputRequest.getEmployeeFullName());
        }

        return ValidateUpsertInputResponse.newBuilder()
                .setInputId(inputRequest.getInputId())
                .setSuccess(true)
                .putAllValidatedInputData(dataMap)
                .build();
    }

    private Long getCompanyIdFromRequest(ValidateUpsertInputBulkRequest req) {
        log.info("[getCompanyIdFromRequest] Extracting company id from request");
        val companyIds = req.getInputsList().stream()
                .map(input -> input.getKeys().getCompanyId())
                .collect(Collectors.toSet());

        return getIdOrThrow(COMPANY, companyIds);
    }

    private Long getEntityIdFromRequest(ValidateUpsertInputBulkRequest req) {
        log.info("[getEntityIdFromRequest] Extracting entity id from request");
        val entityIds = req.getInputsList().stream()
                .map(input -> input.getKeys().getEntityId())
                .collect(Collectors.toSet());

        return getIdOrThrow(ENTITY, entityIds);
    }

    private void validateEntityId(Long entityId, Long companyId) {
        validateCompanyIdNotNullAndPositive(companyId);
        validateEntityIdNotNullAndPositive(entityId);
        validateEntityBelongsToCompany(entityId, companyId);
    }

    private void validateCompanyIdNotNullAndPositive(Long companyId) {
        if (companyId == null || companyId <= 0) {
            throw new ValidationException("[validateEntityId] Company id is not found or invalid");
        }
    }

    private void validateEntityIdNotNullAndPositive(Long entityId) {
        if (entityId == null || entityId <= 0) {
            throw new ValidationException("[validateEntityId] Entity id is not found or invalid");
        }
    }

    private void validateEntityBelongsToCompany(Long entityId, Long companyId) {
        val companyIdToEntityIdsMap = companyServiceAdapter.getLegalEntityIdsByCompanyIds(Set.of(companyId));

        Optional.ofNullable(companyIdToEntityIdsMap.get(companyId))
                .filter(entityIds -> entityIds.contains(entityId))
                .orElseThrow(() ->
                        new ValidationException(String.format("[validateEntityId] Entity id %d does not belong to company id %d", entityId, companyId)));
    }

    private BulkTimeOffRequest getBulkTimeOffRequest(Long companyId, Long entityId, ValidateUpsertInputBulkRequest req) {
        return BulkTimeOffRequest.builder()
                .companyId(companyId)
                .entityId(entityId)
                .inputs(getBulkTimeOffRequestInputs(req))
                .build();
    }

    @NotNull
    private List<BulkTimeOffRequest.Input> getBulkTimeOffRequestInputs(ValidateUpsertInputBulkRequest req) {
        return req.getInputsList().stream()
                .map(input -> {
                    val contractId = input.getKeys().getContractId();
                    val dataMap = input.getDataMap();
                    val externalId = getExternalTimeOffId(contractId, dataMap.get(TimeOffTemplateMetaData.TIME_OFF_ID));

                    return BulkTimeOffRequest.Input.builder()
                            .inputId(input.getInputId())
                            .contractId(contractId)
                            .externalTimeOffId(externalId)
                            .type(dataMap.get(TimeOffTemplateMetaData.TIME_OFF_TYPE))
                            .noOfDays(dataMap.get(TimeOffTemplateMetaData.NO_OF_DAYS))
                            .startDate(dataMap.get(TimeOffTemplateMetaData.TIME_OFF_START_DATE))
                            .endDate(dataMap.get(TimeOffTemplateMetaData.TIME_OFF_END_DATE))
                            .description(dataMap.get(TimeOffTemplateMetaData.DESCRIPTION))
                            .employeeId(dataMap.get(TimeOffTemplateMetaData.EMPLOYEE_ID))
                            .employeeFullName(dataMap.get(TimeOffTemplateMetaData.EMPLOYEE_FULL_NAME))
                            .build();
                })
                .collect(Collectors.toList());
    }

    private String getExternalTimeOffId(Long contractId, String timeOffId) {
        return Optional.ofNullable(timeOffId)
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .map(id -> TimeOffUtil.generateExternalId(contractId, id))
                .orElse("");
    }

    private Long getCompanyIdFromRequest(UpsertBulkRequest req) {
        log.info("[getCompanyIdFromRequest] Extracting company id from request");
        val companyIds = req.getInputsList().stream()
                .map(input -> {
                    var companyId = input.getDataMap().get(TimeOffTemplateMetaData.COMPANY_ID);
                    return companyId != null ? Long.parseLong(companyId) : null;
                })
                .collect(Collectors.toSet());

        return getIdOrThrow(COMPANY, companyIds);
    }

    private Long getEntityIdFromRequest(UpsertBulkRequest req) {
        log.info("[getEntityIdFromRequest] Extracting entity id from request");
        val entityIds = req.getInputsList().stream()
                .map(input -> {
                    var entityID = input.getDataMap().get(TimeOffTemplateMetaData.ENTITY_ID);
                    return entityID != null ? Long.parseLong(entityID) : null;
                })
                .collect(Collectors.toSet());

        return getIdOrThrow(ENTITY, entityIds);
    }

    private BulkTimeOffRequest getBulkTimeOffRequest(Long companyId, Long entityId, UpsertBulkRequest req) {
        return BulkTimeOffRequest.builder()
                .companyId(companyId)
                .entityId(entityId)
                .inputs(getBulkTimeOffRequestInputs(req))
                .build();
    }

    @NotNull
    private List<BulkTimeOffRequest.Input> getBulkTimeOffRequestInputs(UpsertBulkRequest req) {
        return req.getInputsList().stream()
                .map(input -> {
                    val dataMap = input.getDataMap();
                    val contractId = dataMap.get(TimeOffTemplateMetaData.CONTRACT_ID);
                    return BulkTimeOffRequest.Input.builder()
                            .inputId(input.getInputId())
                            .contractId(contractId != null ? Long.parseLong(contractId) : -1)
                            .externalTimeOffId(dataMap.get(TimeOffTemplateMetaData.TIME_OFF_ID))
                            .type(dataMap.get(TimeOffTemplateMetaData.TIME_OFF_TYPE))
                            .noOfDays(dataMap.get(TimeOffTemplateMetaData.NO_OF_DAYS))
                            .startDate(dataMap.get(TimeOffTemplateMetaData.TIME_OFF_START_DATE))
                            .endDate(dataMap.get(TimeOffTemplateMetaData.TIME_OFF_END_DATE))
                            .description(dataMap.get(TimeOffTemplateMetaData.DESCRIPTION))
                            .build();
                })
                .collect(Collectors.toList());
    }

    private UpsertBulkResponse getBulkTimeOffResponse(BulkUpsertTimeOffResult bulkUpsertTimeOffResult, BulkTimeOffRequest request) {
        Map<String, String> externalIdToInputIdMap = new HashMap<>();
        request.getInputs().forEach(input -> externalIdToInputIdMap.put(input.getExternalTimeOffId(), input.getInputId()));

        List<UpsertResponse> results = bulkUpsertTimeOffResult.getItems().stream()
                .map(item -> UpsertResponse.newBuilder()
                        .setInputId(externalIdToInputIdMap.get(item.getExternalTimeOffId()))
                        .setSuccess(bulkUpsertTimeOffResult.isSuccess())
                        .build()
                )
                .collect(Collectors.toList());

        return UpsertBulkResponse.newBuilder()
                .addAllResults(results)
                .build();
    }

    private void setTimeOffTypesAllowedValues(Long companyId, Long entityId, Map<String, FieldRequirement> fieldRequirements) {
        val timeOffTypeDBOs = timeoffTypeService.fetchEntityTimeOffTypes(companyId, entityId, EntityType.COMPANY_ENTITY);
        val timeOffTypes = timeOffTypeDBOs.stream()
                .map(TimeoffTypeDBO::label)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(timeOffTypes)) {
            FieldRequirement updatedFieldRequirement = fieldRequirements.get(TimeOffTemplateMetaData.TIME_OFF_TYPE).toBuilder()
                    .addAllAllowedValues(timeOffTypes)
                    .build();

            fieldRequirements.replace(TimeOffTemplateMetaData.TIME_OFF_TYPE, updatedFieldRequirement);
        }
    }

    private Long getIdOrThrow(String idType, Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new ValidationException("[getIdOrThrow] Could not extract time-offs from a valid " + idType + " id");
        } else if (ids.size() > 1) {
            throw new ValidationException("[getIdOrThrow] Found time-offs from multiple " + idType + " ids : " + ids);
        }

        return ids.iterator().next();
    }
}
