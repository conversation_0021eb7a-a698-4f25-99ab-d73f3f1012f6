package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.schema.GrpcTimeOffForPayroll;
import com.multiplier.timeoff.schema.GrpcTimeOffSession;
import com.multiplier.timeoff.schema.GrpcTimeOffStatus;
import com.multiplier.timeoff.schema.GrpcTimeOffType;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import static com.multiplier.timeoff.core.common.util.GrpcUtil.mapToTimestamp;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GrpcTimeoffMapper {
    default GrpcTimeOffForPayroll mapToGrpcTimeOffForPayroll(TimeoffDBO timeoffDBO, double noOfDaysWithinCycle) {
        return GrpcTimeOffForPayroll.newBuilder()
                .setId(timeoffDBO.id())
                .setContractId(timeoffDBO.contractId())
                .setStatus(GrpcTimeOffStatus.valueOf(timeoffDBO.status().name()))
                .setStartDate(mapToTimestamp(timeoffDBO.startDate()))
                .setEndDate(mapToTimestamp(timeoffDBO.endDate()))
                .setStartSession(GrpcTimeOffSession.valueOf(timeoffDBO.startSession().name()))
                .setEndSession(GrpcTimeOffSession.valueOf(timeoffDBO.endSession().name()))
                .setTotalNoOfDays(timeoffDBO.noOfDays())
                .setNoOfDaysWithinCycle(noOfDaysWithinCycle)
                .setDescription(timeoffDBO.description() != null ? timeoffDBO.description() : "")
                .setTimeOffType(GrpcTimeOffType.newBuilder()
                            .setKey(timeoffDBO.type().key())
                            .setId(timeoffDBO.type().id())
                            .setLabel(timeoffDBO.type().label())
                            .setDescription(
                                timeoffDBO.type().description() != null ? timeoffDBO.type().description() : ""
                            )
                            .setPaidLeave(timeoffDBO.type().isPaidLeave())
                        .build()
                )
                .setApprovedOn(mapToTimestamp(timeoffDBO.approvedOn()))
                .build();
    }

}
