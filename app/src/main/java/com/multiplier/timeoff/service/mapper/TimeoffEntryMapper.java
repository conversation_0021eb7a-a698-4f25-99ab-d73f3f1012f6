package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.repository.model.TimeoffEntryDBO;
import com.multiplier.timeoff.types.TimeOffEntry;
import org.mapstruct.Mapper;

import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TimeoffEntryMapper {

    default TimeOffEntry map(TimeoffEntryDBO source) {
        if (source == null) {
            return null;
        }

        return TimeOffEntry.newBuilder()
                .date(source.date())
                .value(source.value())
                .session(source.session())
                .type(source.type())
                .build();
    }

    List<TimeOffEntry> map(List<TimeoffEntryDBO> source);
}