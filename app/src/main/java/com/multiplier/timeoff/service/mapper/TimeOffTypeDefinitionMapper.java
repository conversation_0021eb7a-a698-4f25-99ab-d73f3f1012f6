package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.types.*;
import lombok.val;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Mapper(componentModel = "spring")
public interface TimeOffTypeDefinitionMapper {

    @Mapping(target = "minimum", source = "minimumValue")
    @Mapping(target = "maximum", source = "maximumValue")
    @Mapping(target = "isUnlimitedLeavesAllowed", source = "unlimitedLeavesAllowed")
    TimeOffFixedValidation map(TimeoffDefinitionValidationEntity timeoffDefinitionValidationEntity);

    @Mapping(target = "allocation", source = "allocationConfig")
    @Mapping(target = "carryForward", source = "carryForwardConfig")
    TimeoffConfiguration map(TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity);

    AllocationConfig map(AllocationConfigEntity allocationConfigEntity);

    CarryForwardConfig map(CarryForwardConfigEntity carryForwardConfigEntity);

    List<TimeOffValidation> map(Collection<TimeoffDefinitionValidationEntity> timeoffDefinitionValidationEntity);

    default TimeOffTypeDefinition map(CompanyDefinitionEntity companyDefinition, TimeoffTypeDBO timeoffTypeDBO) {
        if (companyDefinition == null) {
            return null;
        }
        val definition = companyDefinition.getDefinition();
        if (definition == null) {
            return null;
        }

        TimeOffTypeDefinition.Builder timeOffTypeDefinition = TimeOffTypeDefinition.newBuilder();

        timeOffTypeDefinition.id(definition.getId());
        timeOffTypeDefinition.type(timeoffTypeDBO.key());
        timeOffTypeDefinition.typeId(timeoffTypeDBO.id());
        timeOffTypeDefinition.required(definition.isRequired());
        timeOffTypeDefinition.label(timeoffTypeDBO.label());
        timeOffTypeDefinition.description(definition.getDescription());
        timeOffTypeDefinition.basis(definition.getBasis());
        timeOffTypeDefinition.validation(map(definition.getValidations()));
        timeOffTypeDefinition.configuration(map(definition.getConfigurations()));
        timeOffTypeDefinition.policyName(definition.getName());
        timeOffTypeDefinition.status(definition.getStatus());
        timeOffTypeDefinition.updatedOn(companyDefinition.getUpdatedOn());
        timeOffTypeDefinition.timeOffTypeIsPaidLeave(timeoffTypeDBO.isPaidLeave());

        return timeOffTypeDefinition.build();
    }

    default List<TimeOffTypeDefinition> map(List<CompanyDefinitionEntity> companyDefinitions, Map<Long, TimeoffTypeDBO> idToTimeoffTypeDBOMap) {
        if (companyDefinitions == null) {
            return null;
        }

        List<TimeOffTypeDefinition> list = new ArrayList<>(companyDefinitions.size());
        for (CompanyDefinitionEntity definitionEntity : companyDefinitions) {
            val type = idToTimeoffTypeDBOMap.get(definitionEntity.getTypeId());
            if (type != null) {
                list.add(map(definitionEntity, type));
            }
        }

        return list;
    }

    default TimeOffTypeDefinition map(DefinitionEntity definition, TimeoffTypeDBO timeoffTypeDBO) {

        if (definition == null) {
            return null;
        }

        TimeOffTypeDefinition.Builder timeOffTypeDefinition = TimeOffTypeDefinition.newBuilder();

        timeOffTypeDefinition.id(definition.getId());
        timeOffTypeDefinition.type(timeoffTypeDBO.key());
        timeOffTypeDefinition.typeId(timeoffTypeDBO.id());
        timeOffTypeDefinition.required(definition.isRequired());
        timeOffTypeDefinition.label(timeoffTypeDBO.label());
        timeOffTypeDefinition.description(definition.getDescription());
        timeOffTypeDefinition.basis(definition.getBasis());
        timeOffTypeDefinition.validation(map(definition.getValidations()));
        timeOffTypeDefinition.configuration(map(definition.getConfigurations()));
        timeOffTypeDefinition.policyName(definition.getName());
        timeOffTypeDefinition.status(definition.getStatus());
        timeOffTypeDefinition.updatedOn(definition.getUpdatedOn());

        return timeOffTypeDefinition.build();
    }
}
