package com.multiplier.timeoff.service.notifications;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.core.schema.notifications.Email;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.EmailServiceAdapter;
import com.multiplier.timeoff.core.common.constant.Keys;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.service.exception.NotificationException;
import com.multiplier.timeoff.types.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.WordUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.multiplier.timeoff.adapters.CompanyServiceAdapterKt.toCompanyUserFilters;
import static java.text.MessageFormat.format;

@Slf4j
@Component
@RequiredArgsConstructor
public class TimeoffNotificationHelper {

    private final EmailServiceAdapter emailServiceAdapter;
    private final CompanyServiceAdapter companyServiceAdapter;
    private final PigeonNotificationService pigeonNotificationService;
    private final CurrentUser currentUser;

    @Value("${platform.frontend.baseurl}")
    private String baseUrl;

    @Value("${platform.userservice.system.notification.support.email}")
    private String supportNotificationEmail;

    @Async
    public void sendTimeoffCreatedEmailToCompanyUsers(
            String recipientEmail,
            String employeeFirstName,
            String employeeLastName,
            TimeoffDBO timeoff
    ) {
        try {

            String redirectUrl = baseUrl + "/company/time-off/" + timeoff.id().toString();
            String subject = employeeFirstName.substring(0, 1).toUpperCase() + employeeFirstName.substring(1).toLowerCase() + " has added a new timeoff";
            String reasonForLeave = timeoff.description() != null ? timeoff.description() : "-";
            Map<String, String> params = Map.of(
                    "employeeFullName", (employeeFirstName + " " + employeeLastName),
                    "timeoffFromDate", timeoff.startDate().toString(),
                    "timeoffStartSession", WordUtils.capitalizeFully(timeoff.startSession().name()),
                    "timeoffEndDate", timeoff.endDate().toString(),
                    "timeoffEndSession", WordUtils.capitalizeFully(timeoff.endSession().name()),
                    "reasonForLeave", reasonForLeave,
                    "link", redirectUrl
            );

            Map<String, String> context = Map.of(
                    "from", supportNotificationEmail,
                    "to", recipientEmail,
                    "subject", subject
            );

            Email.EmailRequest email = Email.EmailRequest.newBuilder()
                    .setType(Email.EmailNotificationType.TIMEOFF_CREATED_EMAIL_TO_APPROVER)
                    .putAllContext(context)
                    .putAllTemplateData(params)
                    .build();

            emailServiceAdapter.send(email);
        } catch (Exception e) {
            String exMsg = format("Error when sending timeoff CREATED email to company users for timeoff id = {0}, exception: {1}", timeoff.id(), e.getMessage());
            throw new NotificationException(exMsg, e);
        }
    }

    public void sendTimeoffApproveEmailToMember(String recipientEmail, String companyName, String companyAddress, TimeoffDBO timeoff) {
        try {
            String redirectUrl = baseUrl + "/member/time-off/" + timeoff.id().toString() + "?tab=APPROVED";

            Map<String, String> params = Map.of(
                    "companyName", companyName,
                    "companyAddress", companyAddress,
                    "timeoffFromDate", timeoff.startDate().toString(),
                    "noOfDays", timeoff.noOfDays().toString(),
                    "link", redirectUrl
            );

            Map<String, String> context = Map.of(
                    "from", supportNotificationEmail,
                    "to", recipientEmail
            );

            Email.EmailRequest email = Email.EmailRequest.newBuilder()
                    .setType(Email.EmailNotificationType.TIMEOFF_APPROVE_EMAIL_TO_MEMBER)
                    .putAllContext(context)
                    .putAllTemplateData(params)
                    .build();

            emailServiceAdapter.send(email);
        } catch (Exception e) {
            String exMsg = format("Error when sending timeoff APPROVED email to member for timeoff id = {0}, exception: {1}", timeoff.id(), e.getMessage());
            throw new NotificationException(exMsg, e);
        }
    }

    public void sendTimeoffRejectedEmailToMember(String recipientEmail, String companyName, String companyAddress, TimeoffDBO timeoff) {
        try {
            String redirectUrl = baseUrl + "/member/time-off/" + timeoff.id().toString() + "?tab=REJECTED";

            Map<String, String> params = Map.of(
                    "companyName", companyName,
                    "companyAddress", companyAddress,
                    "rejectReason", StringUtils.trimToEmpty(timeoff.changeReason()),
                    "timeoffFromDate", timeoff.startDate().toString(),
                    "noOfDays", timeoff.noOfDays().toString(),
                    "link", redirectUrl
            );

            Map<String, String> context = Map.of(
                    "from", supportNotificationEmail,
                    "to", recipientEmail
            );

            Email.EmailRequest email = Email.EmailRequest.newBuilder()
                    .setType(Email.EmailNotificationType.TIMEOFF_REJECT_EMAIL_TO_MEMBER)
                    .putAllContext(context)
                    .putAllTemplateData(params)
                    .build();

            emailServiceAdapter.send(email);
        } catch (Exception e) {
            String exMsg = format("Error when sending timeoff REJECTED email to member for timeoff id = {0}, exception: {1}", timeoff.id(), e.getMessage());
            throw new NotificationException(exMsg, e);
        }
    }

    private String getCompanyUserEmail(CompanyOuterClass.CompanyUser companyUser) {
        val email = companyUser.getEmailsList().stream()
                .filter(e -> Keys.JSON_COLLECTION.PRIMARY.equalsIgnoreCase(e.getType()) || "default".equalsIgnoreCase(e.getType()))
                .findFirst()
                .orElse(null);
        return Objects.isNull(email) ? "" : email.getEmail();
    }

    // use this method only in the cases of single entity operations. If you call via batch operations, N+1 can happen
    public void sendTimeoffTypeCreatedEmail(String timeOffTypeName) {
        val createdByCompanyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));
        val admins = companyServiceAdapter.getCompanyAdmins(createdByCompanyUser.getCompanyId());

        admins.forEach(admin -> pigeonNotificationService.sendEmail(
                PigeonEmailNotificationData.builder()
                        .type(NotificationType.TimeoffTypeCreatedEmailToAdmin)
                        .from(supportNotificationEmail)
                        .to(getCompanyUserEmail(admin))
                        .subject(String.format("A new leave type \"%s\" has been created", timeOffTypeName))
                        .data(
                                Map.of(
                                        "recipientName", admin.getFirstName().strip(),
                                        "timeOffTypeName", timeOffTypeName,
                                        "createdBy", getUserFullName(currentUser.getContext()),
                                        "createdOn", LocalDate.now().toString()
                                )
                        )
                        .build()
        ));
    }

    // use this method only in the cases of single entity operations. If you call via batch operations, N+1 can happen
    public void sendTimeoffTypeUpdatedEmail(String timeoffTypeOldName) {
        val updatedByCompanyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));
        val admins = companyServiceAdapter.getCompanyAdmins(updatedByCompanyUser.getCompanyId());

        admins.forEach(admin -> pigeonNotificationService.sendEmail(
                PigeonEmailNotificationData.builder()
                        .type(NotificationType.TimeoffTypeUpdatedEmailToAdmin)
                        .from(supportNotificationEmail)
                        .to(getCompanyUserEmail(admin))
                        .subject(String.format("The \"%s\" leave type was edited", timeoffTypeOldName))
                        .data(
                                Map.of(
                                        "recipientName", admin.getFirstName().strip(),
                                        "timeoffTypeOldName", timeoffTypeOldName,
                                        "updatedBy", getUserFullName(currentUser.getContext()),
                                        "updatedAt", LocalDate.now().toString()
                                )
                        )
                        .build()
        ));
    }

    // use this method only in the cases of single entity operations. If you call via batch operations, N+1 can happen
    public void sendTimeoffTypeDeletedMail(String timeoffTypeName) {
        val deletedByCompanyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));
        val admins = companyServiceAdapter.getCompanyAdmins(deletedByCompanyUser.getCompanyId());

        admins.forEach(admin -> pigeonNotificationService.sendEmail(
                PigeonEmailNotificationData.builder()
                        .type(NotificationType.TimeoffTypeDeletedEmailToAdmin)
                        .from(supportNotificationEmail)
                        .to(getCompanyUserEmail(admin))
                        .subject(String.format("The \"%s\" leave type has been deleted", timeoffTypeName))
                        .data(
                                Map.of(
                                        "recipientName", admin.getFirstName().strip(),
                                        "timeOffTypeName", timeoffTypeName,
                                        "deletedBy", getUserFullName(currentUser.getContext()),
                                        "deletedOn", LocalDate.now().toString()
                                )
                        )
                        .build()
        ));
    }

    // use this method only in the cases of single entity operations. If you call via batch operations, N+1 can happen
    public void sendTimeoffPolicyCreatedNotification(Long policyId) {
        val createdByCompanyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));
        val admins = companyServiceAdapter.getCompanyAdmins(createdByCompanyUser.getCompanyId());

        admins.forEach(admin -> pigeonNotificationService.sendEmail(
                PigeonEmailNotificationData.builder()
                        .type(NotificationType.TimeoffPolicyCreatedEmailToAdmin)
                        .from(supportNotificationEmail)
                        .to(getCompanyUserEmail(admin))
                        .subject("Leave policy update! A new leave policy was created")
                        .data(
                                Map.of(
                                        "recipientName", admin.getFirstName().strip(),
                                        "link", getPolicyRedirectionUrl(policyId),
                                        "createdBy", getUserFullName(currentUser.getContext())
                                )
                        )
                        .build()
        ));
    }

    // use this method only in the cases of single entity operations. If you call via batch operations, N+1 can happen
    public void sendTimeoffPolicyDeletedNotification(Long policyId, String policyName) {
        val deletedByCompanyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));
        val admins = companyServiceAdapter.getCompanyAdmins(deletedByCompanyUser.getCompanyId());

        admins.forEach(admin -> pigeonNotificationService.sendEmail(
                PigeonEmailNotificationData.builder()
                        .type(NotificationType.TimeoffPolicyDeletedEmailToAdmin)
                        .from(supportNotificationEmail)
                        .to(getCompanyUserEmail(admin))
                        .subject("Leave policy update! A leave policy was deleted")
                        .data(
                                Map.of(
                                        "recipientName", admin.getFirstName().strip(),
                                        "leavePolicyName", policyName,
                                        "link", getPolicyRedirectionUrl(policyId),
                                        "deletedBy", getUserFullName(currentUser.getContext())
                                )
                        )
                        .build()
        ));
    }

    // use this method only in the cases of single entity operations. If you call via batch operations, N+1 can happen
    public void sendTimeoffPolicyUpdatedNotification(Long policyId, String policyName) {
        val updatedByCompanyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));
        val admins = companyServiceAdapter.getCompanyAdmins(updatedByCompanyUser.getCompanyId());

        // Ensure policy name is never null or empty
        String definitionOldName = getOldDefinitionName(policyName);

        admins.forEach(admin -> pigeonNotificationService.sendEmail(
                PigeonEmailNotificationData.builder()
                        .type(NotificationType.TimeoffPolicyUpdatedEmailToAdmin)
                        .from(supportNotificationEmail)
                        .to(getCompanyUserEmail(admin))
                        .subject("Leave policy update! A leave policy was updated")
                        .data(
                                Map.of(
                                        "recipientName", admin.getFirstName().strip(),
                                        "leavePolicyName", definitionOldName,
                                        "link", getPolicyRedirectionUrl(policyId),
                                        "updatedBy", getUserFullName(currentUser.getContext())
                                )
                        )
                        .build()
        ));
    }

    // use this method only in the cases of single entity operations. If you call via batch operations, N+1 can happen
    public void sendTimeoffPolicyAssignmentNotification(Long policyId, String leavePolicyName, int applicableEmployees, RuleInput timeOffPolicyRule) {
        val createdByCompanyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));
        val admins = companyServiceAdapter.getCompanyAdmins(createdByCompanyUser.getCompanyId());
        String applicableTo = getApplicabilityCondition(timeOffPolicyRule);

        // Use utility method for safe policy name
        String definitionOldName = getOldDefinitionName(leavePolicyName);

        admins.forEach(admin -> pigeonNotificationService.sendEmail(
                PigeonEmailNotificationData.builder()
                        .type(NotificationType.TimeoffPolicyAssignmentEmailToAdmin)
                        .from(supportNotificationEmail)
                        .to(getCompanyUserEmail(admin))
                        .subject(String.format("%s assigned to a list of employee(s)", definitionOldName))
                        .data(
                                Map.of(
                                        "recipientName", admin.getFirstName().strip(),
                                        "leavePolicyName", definitionOldName,
                                        "effectiveFrom", LocalDate.now().toString(),
                                        "applicableEmployees", String.valueOf(applicableEmployees),
                                        "link", getPolicyRedirectionUrl(policyId),
                                        "applicableTo", applicableTo
                                )
                        )
                        .build()
        ));
    }

    // Utility method to ensure policy name is never null or empty
    private @NotNull String getOldDefinitionName(String policyName) {
        return Optional.ofNullable(policyName)
                .filter(name -> !name.trim().isEmpty())
                .orElse("Unnamed Policy");
    }

    @NotNull
    private static String getApplicabilityCondition(RuleInput timeOffPolicyRule) {
        if (timeOffPolicyRule.getType() == RuleType.ALL) {
            return "All employees";
        } else if (timeOffPolicyRule.getType() == RuleType.BY_CONDITION) {
            return timeOffPolicyRule.getConditions().stream()
                    .map(
                            condition -> String.format(
                                    "%s %s %s",
                                    capitaliseFirstCharacter(condition.getKey().name()),
                                    condition.getOperator().equals(ConditionOperator.EQUALS) ? "is" : "is not",
                                    String.join(", ", condition.getValues())
                            )
                    )
                    .collect(Collectors.joining(" & "));
        } else {
            return "";
        }
    }

    @NotNull
    private static String capitaliseFirstCharacter(String s) {
        if (Objects.isNull(s) || s.isBlank()) {
            return "";
        }
        return s.substring(0, 1).toUpperCase() + s.substring(1).toLowerCase();
    }

    private String getPolicyRedirectionUrl(Long policyId) {
        return baseUrl + "/company/organization-settings/company-policies/time-off/time-off-policy/" + policyId;
    }

    private String getUserFullName(UserContext user) {
        if (Objects.nonNull(user.getFirstName()) && Objects.nonNull(user.getLastName())) {
            return user.getFirstName().strip() + " " + user.getLastName().strip();
        }
        if (Objects.nonNull(user.getFirstName())) {
            return user.getFirstName().strip();
        }
        if (Objects.nonNull(user.getLastName())) {
            return user.getLastName().strip();
        }
        return user.getUserName();
    }

}
