package com.multiplier.timeoff.service.builder;

import com.multiplier.timeoff.types.TimeOffFilter;
import com.multiplier.timeoff.types.TimeOffStatus;
import lombok.*;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(fluent = true, chain = true)
public class TimeoffFilters {

    private Set<Long> ids;
    private Set<Long> contractIds;
    private Set<Long> typeIds;
    private Set<TimeOffStatus> statuses;
    private LocalDate startDateFrom;
    private LocalDate startDateUpto;
    private LocalDate toDate;
    private LocalDateTime updatedOnFrom;
    private LocalDateTime updatedOnTo;
    private List<ContractIdTypeIdFilter> contractIdTypeIdFilters;

    public static TimeoffFilters build(TimeOffFilter graphFilter) {
        if (graphFilter == null) {
            return new TimeoffFilters();
        }

        var filters = new TimeoffFilters();

        if (graphFilter.getIds() != null) {
            filters.ids(graphFilter.getIds().stream().collect(Collectors.toUnmodifiableSet()));
        }

        if (graphFilter.getContractIds() != null) {
            filters.contractIds(graphFilter.getContractIds().stream().collect(Collectors.toUnmodifiableSet()));
        }

        if (graphFilter.getTimeOffStatus() != null) {
            filters.statuses(graphFilter.getTimeOffStatus().stream().collect(Collectors.toUnmodifiableSet()));
        }

        if (graphFilter.getStartDateRange() != null) {
            if (graphFilter.getStartDateRange().getStartDate() != null) {
                filters.startDateFrom(graphFilter.getStartDateRange().getStartDate().toLocalDate());
            }

            if (graphFilter.getStartDateRange().getEndDate() != null) {
                filters.startDateUpto(graphFilter.getStartDateRange().getEndDate().toLocalDate());
            }
        }

        return filters;
    }

    @Data
    @Builder
    public static class ContractIdTypeIdFilter {
        private @NotNull Long contractId;
        private @NotNull Long typeId;
        private @NotNull Set<TimeOffStatus> statuses;
        private @NotNull LocalDate startDateFrom;
        private @NotNull LocalDate toDate;
    }
}
