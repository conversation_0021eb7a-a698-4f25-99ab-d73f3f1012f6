package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.types.Contract;
import com.multiplier.timeoff.types.TimeOffType;
import com.multiplier.timeoff.types.TimeOffTypeDefinition;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;
import java.util.Optional;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TimeoffSummaryMapper {

    default TimeOffType map(TimeoffSummaryDBO source) {

        return (source == null) ? null : new TimeOffType.Builder()
                .key(source.timeoffType().key())
                .definition(
                        new TimeOffTypeDefinition.Builder()
                                .type(source.timeoffType().key())
                                .typeId(source.timeoffType().id())
                                .label(source.timeoffType().label())
                                .description(source.timeoffType().description())
                                .build())
                .contract(
                        new Contract.Builder()
                                .id(source.contractId())
                                .build()
                )
                .periodStart(source.periodStart().atStartOfDay())
                .periodEnd(source.periodEnd().atTime(23, 59)) // backward compatibility. Ideally FE should still work with 00:00
                .allocated(source.allocatedCount())
                .carried(source.carriedCount())
                .entitled(source.totalEntitledCount())
                .taken(source.takenCount())
                .pending(source.pendingCount())
                .remaining(
                        (source.totalEntitledCount() == null) ? null
                                : source.totalEntitledCount() - (Optional.ofNullable(source.takenCount()).orElse(0D) + Optional.ofNullable(source.pendingCount()).orElse(0D))
                )
                .build();
    }


    List<TimeOffType> map(List<TimeoffSummaryDBO> source);
}
