package com.multiplier.timeoff.service;


import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.ProtobufExtensionsKt;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.core.common.builder.SortDefinition;
import com.multiplier.timeoff.core.common.builder.SortDirection;
import com.multiplier.timeoff.core.common.db.BaseEntity;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.core.util.TimeoffCarryForwardUtil;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.repository.*;
import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.service.builder.*;
import com.multiplier.timeoff.service.bulk.BulkTimeOffHelper;
import com.multiplier.timeoff.service.dto.CarryForwardResult;
import com.multiplier.timeoff.service.exception.EntityNotFoundException;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.types.TimeOffStatus;
import com.multiplier.timeoff.types.TimeOffSummaryStatus;
import com.multiplier.timeoff.types.TimeOffUnit;
import com.multiplier.timeoff.types.UpdateTimeOffSummaryInput;
import com.multiplier.timeoff.validation.TimeoffDefinitionAllocator;
import com.multiplier.timeoff.validation.TimeoffDefinitionConfigRequest;
import kotlin.Pair;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.multiplier.timeoff.core.util.TimeOffUtil.*;
import static java.text.MessageFormat.format;


@Service
@Slf4j(topic = "[TimeoffSummaryService]")
@RequiredArgsConstructor
public class TimeoffSummaryService {

    @Value("${platform.timeoff.future-summary-generation.batch-size}")
    private int timeoffFutureSummaryGenerationBatchSize;

    @Value("${platform.timeoff.next-summary-activation.batch-size}")
    private int timeoffNextSummaryActivationBatchSize;

    private final TimeoffSummaryRepository summaryRepo;
    private final TimeoffEntitlementDBORepository entitlementDBORepository;
    private final EntitlementChangeRecordRepository entitlementChangeRecordRepository;
    private final DefinitionEntityRepository definitionEntityRepository;
    private final TimeoffRepository timeoffRepo;
    private final TimoffSummarySpecificationBuilder summarySpecBuilder;
    private final TimoffSpecificationBuilder timeoffSpecBuilder;
    private final CarryForwardLeaveService carryForwardLeavesService;
    private final TimeOffConfigurationService timeOffConfigurationService;
    private final TimeoffDefinitionAllocator timeoffDefinitionAllocator;
    private final FeatureFlagService featureFlagService;
    private final BulkTimeOffHelper bulkTimeOffHelper;
    private final CompanyServiceAdapter companyServiceAdapter;
    private final ContractServiceAdapter contractServiceAdapter;
    private final TimeoffTypeService timeoffTypeService;
    private final Clock clock;

    public TimeoffSummaryDBO updateSummary(TimeoffSummaryDBO source) {
        log.info("Update time-off summary for contractId: {}", source.contractId());

        val typeId = Optional.ofNullable(source.typeId())
                .or(() -> Optional.ofNullable(source.timeoffType()).map(TimeoffTypeDBO::id))
                .orElseThrow();
        var _filter = new TimeoffFilters()
                .contractIds(Set.of(source.contractId()))
                .typeIds(Set.of(typeId))
                .statuses(Set.of(TimeOffStatus.APPROVAL_IN_PROGRESS, TimeOffStatus.APPROVED, TimeOffStatus.TAKEN))
                .startDateFrom(source.periodStart())
                .toDate(source.periodEnd());

        var timeoffs = timeoffRepo.findAll(timeoffSpecBuilder.build(_filter));
        source
                .totalEntitledCount(Optional.ofNullable(source.allocatedCount()).orElse(0D) + Optional.ofNullable(source.carriedCount()).orElse(0D))
                .takenCount(
                        timeoffs.stream()
                                .filter(m -> !PENDING_STATUSES.contains(m.status()))
                                .map(TimeoffDBO::noOfDays)
                                .reduce(0D, Double::sum))
                .pendingCount(
                        timeoffs.stream()
                                .filter(m -> PENDING_STATUSES.contains(m.status()))
                                .map(TimeoffDBO::noOfDays)
                                .reduce(0D, Double::sum));

        return source;
    }

    public List<TimeoffSummaryDBO> findLatestSummariesForContractIdInAndTypeIdIn(Set<Long> contractIds,
                                                                                 Set<Long> typeIds) {
        return summaryRepo.findLatestSummaryForContractIdInAndTypeIdIn(contractIds, typeIds);
    }

    @Transactional
    public void updateSummaryOnDefinitionAssignment(List<TimeoffEntitlementDBO> timeOffEntitlementDBOs,
                                                    Map<Long, ContractOuterClass.Contract> idToContractMap,
                                                    @NotNull CompanyDefinitionEntity companyDefinition) {
        val partitionedEntitlements = partitionEntitlements(timeOffEntitlementDBOs, idToContractMap);
        updateSummariesWhenFutureLeaveFlagIsOff(partitionedEntitlements.get(false), idToContractMap, companyDefinition);
        updateSummariesWhenFutureLeaveFlagIsOn(partitionedEntitlements.get(true), idToContractMap, companyDefinition);
    }

    private void updateSummariesWhenFutureLeaveFlagIsOff(List<TimeoffEntitlementDBO> entitlements,
                                                         Map<Long, ContractOuterClass.Contract> idToContractMap,
                                                         @NotNull CompanyDefinitionEntity companyDefinition) {
        log.info("Future leaves disabled - Updating summaries for entitlements (size : {})", entitlements.size());
        if (CollectionUtils.isEmpty(entitlements)) {
            return;
        }
        val latestSummaries = summaryRepo.findLatestSummaryForContractIdInAndTypeId(
                getContractIds(entitlements), companyDefinition.getTypeId());
        val contractIdToSummaryMap = latestSummaries.stream()
                .collect(Collectors.toMap(TimeoffSummaryDBO::contractId, Function.identity()));

        val updatedSummaries = entitlements.stream()
                .map(entitlementDBO -> createOrUpdateSummaryFromEntitlement(entitlementDBO, idToContractMap, contractIdToSummaryMap))
                .collect(Collectors.toList());

        summaryRepo.saveAll(updatedSummaries);

    }

    private void updateSummariesWhenFutureLeaveFlagIsOn(List<TimeoffEntitlementDBO> entitlements,
                                                        Map<Long, ContractOuterClass.Contract> idToContractMap,
                                                        @NotNull CompanyDefinitionEntity companyDefinition) {
        log.info("Future leaves enabled - Updating summaries for entitlements (size : {})", entitlements.size());
        if (CollectionUtils.isEmpty(entitlements)) {
            return;
        }

        val latestSummaries = summaryRepo.findAllByContractIdInAndTypeIdAndStatusIn(
                getContractIds(entitlements),
                companyDefinition.getTypeId(),
                Set.of(TimeOffSummaryStatus.ACTIVE, TimeOffSummaryStatus.UPCOMING)
        );
        Map<Long, List<TimeoffSummaryDBO>> contractIdToSummariesMap = latestSummaries.stream()
                .collect(Collectors.groupingBy(TimeoffSummaryDBO::contractId));

        val partitionedEntitlements = entitlements.stream()
                .collect(Collectors.partitioningBy( entitlementDBO -> {
                    val summaries = contractIdToSummariesMap.getOrDefault(entitlementDBO.contractId(), List.of());
                    return CollectionUtils.isEmpty(summaries);
                }));

        // There are no existing summaries for the new entitlements (probably new policy assignment)
        // We need to generate active and upcoming summaries
        val newEntitlements = partitionedEntitlements.get(true);
        val newSummariesAndChangedRecords = generateSummariesForNewEntitlements(newEntitlements, idToContractMap, companyDefinition);

        List<TimeoffSummaryDBO> updatedSummaries = new ArrayList<>(newSummariesAndChangedRecords.getFirst());
        List<EntitlementChangeRecordEntity> entitlementChangeRecordsToSave = new ArrayList<>(newSummariesAndChangedRecords.getSecond());

        // There are existing summaries for the existing entitlements (probably policy update or shift to a different policy)
        // We need to update active and upcoming summary values
        val existingEntitlements = partitionedEntitlements.get(false);
        val existingSummariesAndChangeRecords = updateSummariesForExistingEntitlements(existingEntitlements, idToContractMap, companyDefinition, contractIdToSummariesMap);
        updatedSummaries.addAll(existingSummariesAndChangeRecords);

        summaryRepo.saveAll(updatedSummaries);
        entitlementChangeRecordRepository.saveAll(entitlementChangeRecordsToSave);

    }

    private Pair<List<TimeoffSummaryDBO>, List<EntitlementChangeRecordEntity>> generateSummariesForNewEntitlements(
            List<TimeoffEntitlementDBO> entitlements,
            Map<Long, ContractOuterClass.Contract> idToContractMap,
            @NotNull CompanyDefinitionEntity companyDefinition) {

        log.info("Generating summaries for new entitlements (size : {})", entitlements.size());
        if (CollectionUtils.isEmpty(entitlements)) {
            return new Pair<>(List.of(), List.of());
        }

        val company = companyServiceAdapter.getCompany(companyDefinition.getCompanyId());
        val timeoffType = entitlements.get(0).type(); // all entitlements are from the same type
        List<TimeoffSummaryDBO> newSummaries = new ArrayList<>();
        List<EntitlementChangeRecordEntity> entitlementChangeRecords = new ArrayList<>();

        for (var entitlement : entitlements) {
            val contract = idToContractMap.get(entitlement.contractId());
            if (contract == null) {
                throw new ValidationException(format("Can not update summary for contract id : {0}, type id : {1} since contract is not found",
                        entitlement.contractId(), entitlement.typeId()));
            }
            val changeRecordsAndSummaries = createSummariesFromContractStartOn(
                    contract,
                    TimeOffUtil.getFinancialEndMonth(company),
                    entitlement,
                    timeoffType,
                    companyDefinition.getDefinition(),
                    false // we don't need to create past expired summaries
            );

            newSummaries.addAll(changeRecordsAndSummaries.getFirst());
            entitlementChangeRecords.addAll(changeRecordsAndSummaries.getSecond());
        }

        return new Pair<>(newSummaries, entitlementChangeRecords);

    }

    private List<TimeoffSummaryDBO> updateSummariesForExistingEntitlements(List<TimeoffEntitlementDBO> existingEntitlements,
                                                                                                                      Map<Long, ContractOuterClass.Contract> idToContractMap,
                                                                                                                      @NotNull CompanyDefinitionEntity companyDefinition,
                                                                                                                      Map<Long, List<TimeoffSummaryDBO>> contractIdToSummariesMap) {
        log.info("[updateSummariesForExistingEntitlements] Updating summaries for existing entitlements (size : {})", existingEntitlements.size());
        List<TimeoffSummaryDBO> updatedSummaries = new ArrayList<>();

        for (var entitlement : existingEntitlements) {
            val contract = idToContractMap.get(entitlement.contractId());
            if (contract == null) {
                throw new ValidationException(format("Can not update summary for contract id : {0}, type id : {1} since contract is not found",
                        entitlement.contractId(), entitlement.typeId()));
            }

            val summaries = contractIdToSummariesMap.getOrDefault(contract.getId(), List.of());
            val annualAllocation = getEntitledValueInDays(entitlement);
            val allocationConfig = getAllocationConfig(companyDefinition.getDefinition());
            val newAllocationValue = allocationConfig.getProrated()
                    ? prorateAllocation(annualAllocation, entitlement.unit(), summaries.get(0).periodStart(), summaries.get(0).periodEnd())
                    : annualAllocation;

            // todo: We only update allocation value changes here.
            //  If user makes any changes to carry forward config we need to update carry forward values as well
            summaries.forEach(summary -> {
                summary.allocatedCount(newAllocationValue);
                summary.totalEntitledCount(newAllocationValue);
            });
            updatedSummaries.addAll(summaries);
        }
        return updatedSummaries;
    }

    private TimeoffSummaryDBO createOrUpdateSummaryFromEntitlement(TimeoffEntitlementDBO entitlementDBO,
                                                                   Map<Long, ContractOuterClass.Contract> idToContractMap,
                                                                   Map<Long, TimeoffSummaryDBO> contractIdToSummaryMap) {
        val contract = idToContractMap.get(entitlementDBO.contractId());
        if (contract == null) {
            throw new ValidationException(format("Can not update summary for contract id : {0}, type id : {1} since contract is not found",
                    entitlementDBO.contractId(), entitlementDBO.typeId()));
        }
        TimeoffSummaryDBO summaryDBO = contractIdToSummaryMap.get(contract.getId());
        if (summaryDBO == null || summaryDBO.periodEnd().isBefore(LocalDate.now())) {
            log.info("Creating summary. contractId={}, typeId={}, typeKey={}",contract.getId(), entitlementDBO.type().id(),
                    entitlementDBO.type().key());
            summaryDBO = new TimeoffSummaryDBO()
                    .contractId(entitlementDBO.contractId())
                    .timeoffType(entitlementDBO.type())
                    .takenCount(0D)
                    .pendingCount(0D)
                    .carriedCount(0D)
                    // ALLOCATOR is responsible for doing the below.
                    .periodStart(LocalDate.now().with(java.time.temporal.TemporalAdjusters.firstDayOfYear()))
                    .periodEnd(LocalDate.now().with(java.time.temporal.TemporalAdjusters.lastDayOfYear()));
        }
        val definition = getDefinitionForEntitlement(entitlementDBO);
        val company = companyServiceAdapter.getCompany(contract.getCompanyId());
        TimeoffDefinitionConfigRequest configRequest = TimeoffDefinitionConfigRequest.of(
                definition,
                entitlementDBO,
                contract,
                summaryDBO,
                true,
                TimeOffUtil.getFinancialEndMonth(company));
        timeoffDefinitionAllocator.allocate(configRequest);
        if (!Optional.ofNullable(definition)
                .map(DefinitionEntity::getConfigurations)
                .map(TimeoffDefinitionConfigEntity::getCarryForwardConfig)
                .map(CarryForwardConfigEntity::getEnabled)
                .orElse(false)) {
            summaryDBO.carriedCount(0D);
        }
        summaryDBO.totalEntitledCount(Optional.ofNullable(summaryDBO.allocatedCount()).orElse(0D)
                + Optional.ofNullable(summaryDBO.carriedCount()).orElse(0D));
        return summaryDBO;
    }

    private Set<Long> getContractIds(List<TimeoffEntitlementDBO> timeoffEntitlementDBOS) {
        return timeoffEntitlementDBOS.stream()
                .map(TimeoffEntitlementDBO::contractId)
                .collect(Collectors.toSet());
    }


    private Optional<TimeoffSummaryDBO> getSummary(Long contractId, TimeoffTypeDBO type, Optional<LocalDate> intersectingDate) {

        var filters = new TimeoffSummaryFilters().contractIds(Set.of(contractId)).typeIds(Set.of(type.id()));
        intersectingDate.ifPresent(localDate -> filters.fromDate(localDate).toDate(localDate));

        // also sort by `id desc` to get the newest record and ignore duplicated ones
        var sorting = List.of(
                new SortDefinition()
                        .columns(Set.of(SummaryColumns.PERIOD_START))
                        .direction(SortDirection.DESCENDING),
                new SortDefinition()
                        .columns(Set.of(SummaryColumns.ID))
                        .direction(SortDirection.DESCENDING)
        );

        return summaryRepo
                .findAll(
                        summarySpecBuilder.buildSorting(
                                summarySpecBuilder.build(filters),
                                sorting),
                        PageRequest.of(0, 1))
                .stream()
                .findFirst();
    }


    private Optional<TimeoffSummaryDBO> getSummary(Long contractId, TimeoffTypeDBO type) {

        return getSummary(contractId, type, Optional.empty());
    }


    private TimeoffSummaryDBO getOrCreateLatestSummary(Long contractId, TimeoffTypeDBO type) {

        var summary = getSummary(contractId, type).orElse(null);
        if (summary == null) {
            log.info("Creating summary. contractId={}, typeId={}, typeKey={}", contractId, type.id(), type.key());
            summary = createSummary(contractId, type);
        }

        return summary;
    }

    private TimeoffSummaryDBO createSummary(Long contractId, TimeoffTypeDBO type) {
        return new TimeoffSummaryDBO()
                .contractId(contractId)
                .timeoffType(type)
                .takenCount(0D)
                .pendingCount(0D)
                // ALLOCATOR is responsible for doing the below.
                .periodStart(LocalDate.now().with(java.time.temporal.TemporalAdjusters.firstDayOfYear()))
                .periodEnd(LocalDate.now().with(java.time.temporal.TemporalAdjusters.lastDayOfYear()));
    }


    public List<TimeoffSummaryDBO> getLatestSummariesExpiredBeforeOrOn(LocalDate expiryTime) {
        return summaryRepo.getLastestExpiredSummaryForAContractIDAndTypeID(expiryTime);
    }

    public List<TimeoffSummaryDBO> getLatestSummariesExpiredBefore(LocalDate expiryDate) {
        return summaryRepo.findLatestSummariesExpiredBefore(expiryDate);
    }


    public List<TimeoffSummaryDBO> getLatestSummariesExpiredBeforeForContracts(LocalDate expiryDate, Collection<Long> contractIds) {
        return summaryRepo.findLatestSummariesExpiredBeforeAndContractIdIn(expiryDate, new HashSet<>(contractIds));
    }

    @Transactional
    public void onTimeOffChange(TimeoffDBO value) {
        var summary = getSummary(value.contractId(), value.type(), Optional.of(value.startDate()));
        if (summary.isEmpty()) {
            log.error("Summary not found for TimeOff. contractId={}, typeId={}, typeKey={}", value.contractId(), value.type().id(), value.type().key());
            return;
        }
        summaryRepo.saveAndFlush(updateSummary(summary.get()));
    }

    @Transactional
    public void onTimeOffChangesForBulk(List<TimeoffDBO> changedTimeOffDBOs) {
        Set<Long> contractIds = contractIds(changedTimeOffDBOs);
        Set<Long> typeIds = typeIds(changedTimeOffDBOs);
        val contractIdTypeIdToSummaryMap = getContractIdTypeIdToSummaryMap(contractIds, typeIds);

        // we need to load all time offs related to changed timeoff type & contract id,to update the summaries correctly
        val earliestSummaryStartDate = getEarliestSummaryStartDate(contractIdTypeIdToSummaryMap.values());
        val contractIdTypeIdToTimeOffMap = bulkTimeOffHelper.getContractIdTypeIdToTimeOffsMap(contractIds, typeIds, earliestSummaryStartDate);

        val updatedSummaries = changedTimeOffDBOs.stream()
                .map(timeoffDBO -> updateEachSummaryForBulk(timeoffDBO, contractIdTypeIdToSummaryMap, contractIdTypeIdToTimeOffMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        summaryRepo.saveAllAndFlush(updatedSummaries);
    }

    public LocalDate getEarliestSummaryStartDate(Collection<TimeoffSummaryDBO> timeoffSummaryDBOs) {
        // if no summaries found, return first day of the current year as the start date
        return timeoffSummaryDBOs.stream()
                .map(TimeoffSummaryDBO::periodStart)
                .filter(Objects::nonNull)
                .min(LocalDate::compareTo)
                .orElse(LocalDate.now().withDayOfYear(1));
    }

    private Map<String, TimeoffSummaryDBO> getContractIdTypeIdToSummaryMap(Set<Long> contractIds, Set<Long> typeIds) {
        return findLatestSummariesForContractIdInAndTypeIdIn(contractIds, typeIds)
                .stream()
                .collect(Collectors.toMap(
                        ts -> toContractIdTypeIDKey(ts.contractId(), ts.typeId()),
                        Function.identity()));
    }

    private Map<String, TimeoffSummaryDBO> createContractIdTypeIdToSummaryMap(List<TimeoffEntitlementDBO> entitlements)  {
        return entitlements.stream()
                .collect(Collectors.toMap(
                        entitlementDBO -> toContractIdTypeIDKey(entitlementDBO.contractId(), entitlementDBO.typeId()),
                        entitlementDBO -> createSummary(entitlementDBO. contractId(), entitlementDBO.type())));
    }

    private TimeoffSummaryDBO updateEachSummaryForBulk(TimeoffDBO timeoffDBO, Map<String, TimeoffSummaryDBO> contractIdTypeIdToSummaryMap, Map<String, List<TimeoffDBO>> contractIdTypeIdToTimeOffsMap) {
        String key = toContractIdTypeIDKey(timeoffDBO.contractId(), timeoffDBO.typeId());
        val summary = contractIdTypeIdToSummaryMap.get(key);

        if (summary == null) {
            log.error("Summary not found for TimeOff. contractId={}, typeId={}, typeKey={}", timeoffDBO.contractId(), timeoffDBO.type().id(), timeoffDBO.type().key());
            return null;
        }

        val pendingStatuses = List.of(TimeOffStatus.APPROVAL_IN_PROGRESS);
        var allTimeOffs = contractIdTypeIdToTimeOffsMap.getOrDefault(key, Collections.emptyList());
        double totalEntitledCount = Optional.ofNullable(summary.allocatedCount()).orElse(0D) + Optional.ofNullable(summary.carriedCount()).orElse(0D);
        double takenCount = allTimeOffs.stream().filter(m -> !pendingStatuses.contains(m.status())).mapToDouble(TimeoffDBO::noOfDays).sum();
        double pendingCount = allTimeOffs.stream().filter(m -> pendingStatuses.contains(m.status())).mapToDouble(TimeoffDBO::noOfDays).sum();

        return summary
                .totalEntitledCount(totalEntitledCount)
                .takenCount(takenCount)
                .pendingCount(pendingCount);
    }


    private Set<Long> contractIds(List<TimeoffDBO> timeoffDBOs) {
        return timeoffDBOs.stream()
                .map(TimeoffDBO::contractId)
                .collect(Collectors.toSet());
    }

    private Set<Long> typeIds(List<TimeoffDBO> timeoffDBOs) {
        return timeoffDBOs.stream()
                .map(TimeoffDBO::typeId)
                .collect(Collectors.toSet());
    }

    @Transactional
    public void onTimeOffEntitlementChange(TimeoffEntitlementDBO entitlementDBO) {

        var summaryDBO = getOrCreateLatestSummary(entitlementDBO.contractId(), entitlementDBO.type());
        summaryDBO.allocatedCount(TimeOffUtil.round(entitlementDBO.value()));

        var validatedAndMutatedSummaryDBO = timeOffConfigurationService.validateAndMutateSummary(summaryDBO, entitlementDBO, true);
        if (validatedAndMutatedSummaryDBO == null) {
            log.info("SummaryDBO has become null because of the validations for entitlement typeID={}", entitlementDBO.typeId());
            return;
        }
        summaryRepo.saveAndFlush(updateSummary(validatedAndMutatedSummaryDBO));
    }

    /**
     * Reset timeoff summaries based on given entitlements. Pending & taking counts are set to 0.0
     * @param entitlements : entitlements
     * @param idToContractMap : contract id to contract map for given entitlements
     */
    @Transactional
    public void onTimeOffEntitlementsResetBulk(List<TimeoffEntitlementDBO> entitlements, Map<Long, ContractOuterClass.Contract> idToContractMap) {
        deleteAllExistingSummaries(entitlements);
        val entitlementsPartitionedByFutureLeave = partitionEntitlements(entitlements, idToContractMap);
        generateOrUpdateSummariesWhenFutureLeaveFlagIsOn(entitlementsPartitionedByFutureLeave.get(true), idToContractMap);
        generateOrUpdateSummariesWhenFutureLeaveFlagIsOff(entitlementsPartitionedByFutureLeave.get(false), idToContractMap);
    }


    private void generateOrUpdateSummariesWhenFutureLeaveFlagIsOff(List<TimeoffEntitlementDBO> entitlements, Map<Long, ContractOuterClass.Contract> idToContractMap) {
        log.info("Future leaves disabled - Generating summaries for entitlements (size : {})", entitlements.size());
        if (CollectionUtils.isEmpty(entitlements)) {
            return;
        }

        val contractIdTypeIdToSummaryMap = createContractIdTypeIdToSummaryMap(entitlements);
        List<TimeoffSummaryDBO> validatedAndMutatedSummaries = timeOffConfigurationService.validateAndMutateSummaries(entitlements, contractIdTypeIdToSummaryMap, idToContractMap, true);
        setSummaryCountsPostValidate(validatedAndMutatedSummaries);
        summaryRepo.saveAllAndFlush(validatedAndMutatedSummaries);
    }

    private void generateOrUpdateSummariesWhenFutureLeaveFlagIsOn(List<TimeoffEntitlementDBO> entitlements, Map<Long, ContractOuterClass.Contract> idToContractMap) {
        log.info("Future leaves enabled - Generating summaries for entitlements (size : {})", entitlements.size());
        if (CollectionUtils.isEmpty(entitlements)) {
            return;
        }

        Map<Long, DefinitionEntity> idToDefinitionMap = getIdToDefinitionMap(entitlements);
        Map<Long, TimeoffTypeDBO> idToTypeMap = getIdToTimeoffTypeMap(entitlements);
        Map<Long, Month> companyIdToFinancialYearEndMonthMap = getCompanyIdToFinancialYearEndMonthMap(idToContractMap.values());

        List<TimeoffSummaryDBO> allSummaries = new ArrayList<>();
        List<EntitlementChangeRecordEntity> allChangeRecords = new ArrayList<>();

        for (var entitlement : entitlements) {
            val definition = TimeOffUtil.getDefinitionForEntitlement(entitlement, idToDefinitionMap);
            val contract = idToContractMap.get(entitlement.contractId());
            val timeoffType = idToTypeMap.get(entitlement.typeId());

            if (isInvalidData(definition, contract, timeoffType, entitlement)) {
                continue;
            }

            log.info("Summary generation for entitlement id : {}, contract id : {}, type id : {} ==> STARTED",
                    entitlement.id(), entitlement.contractId(), entitlement.typeId());
            val results = createSummariesFromContractStartOn(
                    contract,
                    companyIdToFinancialYearEndMonthMap.getOrDefault(contract.getCompanyId(), Month.DECEMBER),
                    entitlement,
                    timeoffType,
                    definition,
                    true
            );

            allSummaries.addAll(results.getFirst());
            allChangeRecords.addAll(results.getSecond());

            log.info("Summary generation for entitlement id : {}, contract id : {}, type id : {} ==> COMPLETED (total_generated = {})",
                    entitlement.id(), entitlement.contractId(), entitlement.typeId(), results.getFirst().size());
        }

        summaryRepo.saveAll(allSummaries);
        entitlementChangeRecordRepository.saveAll(allChangeRecords);
    }

    private boolean isInvalidData(DefinitionEntity definition,
                                  ContractOuterClass.Contract contract,
                                  TimeoffTypeDBO timeoffType,
                                  TimeoffEntitlementDBO entitlement) {
        if (definition == null) {
            log.error("Definition not found for entitlement. contractId={}, typeId={}", entitlement.contractId(), entitlement.typeId());
            return true;
        }

        if (contract == null) {
            log.error("Contract not found for entitlement. contractId={}", entitlement.contractId());
            return true;
        }

        if (timeoffType == null) {
            log.error("TimeoffType not found for entitlement. typeId={}", entitlement.typeId());
            return true;
        }
        return false;
    }

    private Map<Boolean, List<TimeoffEntitlementDBO>> partitionEntitlements(List<TimeoffEntitlementDBO> entitlements, Map<Long, ContractOuterClass.Contract> idToContractMap) {
        Map<Long, Boolean> companyIdToFutureLeavesEnabledMap = getCompanyIdToFutureLeavesEnabledMap(getCompanyIds(idToContractMap.values()));
        return entitlements.stream()
                .collect(Collectors.partitioningBy(
                        e -> isFutureLeaveFeatureEnabled(idToContractMap.get(e.contractId()), companyIdToFutureLeavesEnabledMap)));
    }

    private Set<Long> getCompanyIds(Collection<ContractOuterClass.Contract> contracts) {
        return contracts.stream()
                .map(ContractOuterClass.Contract::getCompanyId)
                .collect(Collectors.toSet());
    }

    private Map<Long, Boolean> getCompanyIdToFutureLeavesEnabledMap(Set<Long> companyIds) {
        return companyIds.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        companyId -> featureFlagService.isOn(FeatureFlags.FUTURE_LEAVES, Map.of("company", companyId))));
    }

    private boolean isFutureLeaveFeatureEnabled(ContractOuterClass.Contract contract, Map<Long, Boolean> companyIdToFutureLeavesEnabledMap) {
        if (contract == null) {
            return false;
        }
        return companyIdToFutureLeavesEnabledMap.getOrDefault(contract.getCompanyId(), false);
    }


    private Map<Long, Month> getCompanyIdToFinancialYearEndMonthMap(Collection<ContractOuterClass.Contract> contracts) {
        return companyServiceAdapter.getCompanyByIds(getCompanyIds(contracts)).stream()
                .collect(Collectors.toMap(CompanyOuterClass.Company::getId,
                        TimeOffUtil::getFinancialEndMonth));
    }

    private Map<Long, TimeoffTypeDBO> getIdToTimeoffTypeMap(List<TimeoffEntitlementDBO> entitlements) {
        return timeoffTypeService.findAllTypeDBOsByIds(getTypeIds(entitlements))
                .stream()
                .collect(Collectors.toMap(TimeoffTypeDBO::id, Function.identity()));
    }

    private Map<Long, DefinitionEntity> getIdToDefinitionMap(List<TimeoffEntitlementDBO> entitlements) {
        val definitionIds = entitlements.stream()
                .map(TimeoffEntitlementDBO::definitionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(definitionIds)) {
            return Collections.emptyMap();
        }

        return definitionEntityRepository.findAllById(definitionIds)
                .stream()
                .collect(Collectors.toMap(DefinitionEntity::getId, Function.identity()));
    }


    private Pair<List<TimeoffSummaryDBO>, List<EntitlementChangeRecordEntity>> createSummariesFromContractStartOn(ContractOuterClass.Contract contract,
                                                                                                                  Month companyFinancialYearEndMonth,
                                                                                                                  TimeoffEntitlementDBO entitlementDBO,
                                                                                                                  TimeoffTypeDBO timeoffTypeDBO,
                                                                                                                  DefinitionEntity definition,
                                                                                                                  boolean shouldCreateExpiredSummaries) {
        val futureLeaveConfig = getFutureLeaveConfig(definition);
        val allocationConfig = getAllocationConfig(definition);
        LocalDate today = LocalDate.now(clock);

        LocalDate currentFinancialYearEnd = getFinancialYearEnd(today, companyFinancialYearEndMonth);
        LocalDate currentFinancialYearStart = currentFinancialYearEnd.minusYears(1).plusMonths(1).withDayOfMonth(1);
        val contractStartOn = ProtobufExtensionsKt.toLocalDate(contract.getStartOn());
        val annualAllocationInDays = getEntitledValueInDays(entitlementDBO);

        List<TimeoffSummaryDBO> summaries = new ArrayList<>();
        List<EntitlementChangeRecordEntity> changeRecords = new ArrayList<>();

        // create expired summaries
        if (shouldCreateExpiredSummaries && contractStartOn.isBefore(currentFinancialYearStart)) {
            generateExpiredSummaries(
                    contractStartOn,
                    currentFinancialYearStart.minusDays(1),
                    companyFinancialYearEndMonth,
                    contract.getId(),
                    timeoffTypeDBO,
                    summaries
            );
        }

        // create current summary
        val currentSummaryStartDate = contractStartOn.isAfter(currentFinancialYearStart) ? contractStartOn : currentFinancialYearStart;
        val allocationCount = allocationConfig.getProrated()
                ? prorateAllocation(annualAllocationInDays, entitlementDBO.unit(), currentSummaryStartDate, currentFinancialYearEnd)
                : annualAllocationInDays;
        generateActiveSummaryAndEntitlementChangeRecord(
                currentSummaryStartDate,
                currentFinancialYearEnd,
                contract.getId(),
                timeoffTypeDBO,
                allocationCount,
                summaries,
                changeRecords
        );


        // create future summaries
        if (futureLeaveConfig.getEnabled()) {
            generateFutureSummariesAndEntitlementChangeRecords(
                    currentFinancialYearEnd.plusDays(1),
                    futureLeaveConfig.getAllowedYears(),
                    contract.getId(),
                    timeoffTypeDBO,
                    annualAllocationInDays,
                    summaries,
                    changeRecords
            );
        }

        return new Pair<>(summaries, changeRecords);
    }


    private @NotNull AllocationConfigEntity getAllocationConfig(@NotNull DefinitionEntity definition) {
        return Optional.ofNullable(definition.getConfigurations())
                .map(TimeoffDefinitionConfigEntity::getAllocationConfig)
                .orElse(new AllocationConfigEntity());
    }

    private @NotNull FutureLeaveConfigEntity getFutureLeaveConfig(DefinitionEntity definition) {
        return Optional.ofNullable(definition.getConfigurations())
                .map(TimeoffDefinitionConfigEntity::getFutureLeaveConfig)
                .orElse(new FutureLeaveConfigEntity());
    }

    private void generateActiveSummaryAndEntitlementChangeRecord(
            LocalDate startDate,
            LocalDate endDate,
            Long contractId,
            TimeoffTypeDBO timeoffTypeDBO,
            Double allocationValue,
            List<TimeoffSummaryDBO> updatedSummaries,
            List<EntitlementChangeRecordEntity> updatedEntitlementChangeRecords) {
        val summary = TimeoffSummaryDBO.builder()
                .periodStart(startDate)
                .periodEnd(endDate)
                .contractId(contractId)
                .timeoffType(timeoffTypeDBO)
                .allocatedCount(allocationValue)
                .carryForwardCount(0.0)
                .carryForwardExpiredCount(0.0)
                .usedFromAllocatedCount(0.0)
                .usedFromCarryForwardCount(0.0)
                .usedFromLapsableCount(0.0)
                .usedFromNextCycleCarryForwardCount(0.0)
                .totalEntitledCount(allocationValue)
                .status(TimeOffSummaryStatus.ACTIVE)
                .build();
        updatedSummaries.add(summary);
        updatedEntitlementChangeRecords.add(new EntitlementChangeRecordEntity(
                null, // id is null for new records
                timeoffTypeDBO.id(),
                contractId,
                EntitlementChangeCategory.ALLOCATION,
                allocationValue,
                startDate,
                endDate,
                null // refId is null
        ));

        log.info("[generateActiveSummary] ACTIVE summary generated (contractId : {}, type id : {}) {} to {}",
                contractId, timeoffTypeDBO.id(), summary.periodStart(), summary.periodEnd());
    }

    private void generateFutureSummariesAndEntitlementChangeRecords(
            LocalDate from,
            int allowedYears,
            Long contractId,
            TimeoffTypeDBO timeoffType,
            Double allocationValue,
            List<TimeoffSummaryDBO> updatedSummaries,
            List<EntitlementChangeRecordEntity> updatedEntitlementChangeRecords) {
        log.info("[generateFutureSummariesAndEntitlementChangeRecords] generating future summaries (contract id : {}, type id : {}) from : {}",
                contractId,timeoffType.id(), from);

        int upcomingSummariesCount = 0;
        var startDate = from;

        while (upcomingSummariesCount < allowedYears) {
            val endDate = startDate.plusYears(1).minusDays(1);
            updatedSummaries.add(TimeoffSummaryDBO.builder()
                    .periodStart(startDate)
                    .periodEnd(endDate)
                    .contractId(contractId)
                    .timeoffType(timeoffType)
                    .allocatedCount(allocationValue)
                    .carryForwardCount(0.0)
                    .carryForwardExpiredCount(0.0)
                    .usedFromAllocatedCount(0.0)
                    .usedFromCarryForwardCount(0.0)
                    .usedFromLapsableCount(0.0)
                    .usedFromNextCycleCarryForwardCount(0.0)
                    .totalEntitledCount(allocationValue)
                    .status(TimeOffSummaryStatus.UPCOMING)
                    .build()
            );
            updatedEntitlementChangeRecords.add(new EntitlementChangeRecordEntity(
                    null, // id is null for new records
                    timeoffType.id(),
                    contractId,
                    EntitlementChangeCategory.ALLOCATION,
                    allocationValue,
                    startDate,
                    endDate,
                    null // refId is null
            ));
            log.info("[generateFutureSummaries] UPCOMING summary generated from : {} to : {}", startDate, endDate);
            startDate = endDate.plusDays(1);
            upcomingSummariesCount++;
        }

    }

    private Set<Long> getTypeIds(List<TimeoffEntitlementDBO> entitlements) {
        return entitlements.stream()
                .map(TimeoffEntitlementDBO::typeId)
                .collect(Collectors.toSet());
    }

    private void deleteAllExistingSummaries(List<TimeoffEntitlementDBO> entitlements) {
        summaryRepo.deleteAllByContractIdIn(getContractIds(entitlements));
        summaryRepo.flush();
    }
    private void setSummaryCountsPostValidate(List<TimeoffSummaryDBO> timeoffSummaryDBOS) {
        timeoffSummaryDBOS.forEach( summaryDBO -> summaryDBO.totalEntitledCount(summaryDBO.allocatedCount()));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TimeoffSummaryDBO legacyReallocateTimeoff(TimeoffSummaryDBO summaryDBO, ContractOuterClass.Contract contract) {
        if (isCarryForwardExpiryEnabled(contract.getCompanyId())) {
            return null;
        }

        log.info("Trying to reallocate timeoff balance (legacy-mode) for typeId = {} and contractId = {}", summaryDBO.typeId(), summaryDBO.contractId());

        var entitlement = entitlementDBORepository.findByContractIdAndTypeId(summaryDBO.contractId(), summaryDBO.typeId());
        if (entitlement.isEmpty()) {
            log.warn("Entitlement for typeId = {} and contractId = {} not found", summaryDBO.typeId(), summaryDBO.contractId());
            return null;
        }

        var mutatedSummaryDBO = timeOffConfigurationService.validateAndMutateSummary(summaryDBO, entitlement.get(), false);
        if (mutatedSummaryDBO == null) {
            log.warn("Stop reallocating timeoff balance, validations for entitlement id = {} and typeID = {} failed", entitlement.get().id, entitlement.get().typeId());
            return null;
        }

        if (!Optional.ofNullable(mutatedSummaryDBO.id()).orElse(-1L).equals(summaryDBO.id())) {
            carryForwardLeavesService.carryForwardLeaves(entitlement.get(), summaryDBO, mutatedSummaryDBO, contract);
        }

        return summaryRepo.saveAndFlush(updateSummary(mutatedSummaryDBO));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public TimeoffSummaryDBO reallocateTimeoff(TimeoffSummaryDBO summaryDBO) {
        log.info("Trying to reallocate timeoff balance for typeId = {} and contractId = {}", summaryDBO.typeId(), summaryDBO.contractId());

        var entitlement = entitlementDBORepository.findByContractIdAndTypeId(summaryDBO.contractId(), summaryDBO.typeId());
        if (entitlement.isEmpty()) {
            log.warn("Entitlement for typeId = {} and contractId = {} not found", summaryDBO.typeId(), summaryDBO.contractId());
            return null;
        }

        var mutatedSummaryDBO = timeOffConfigurationService.validateAndMutateSummary(summaryDBO, entitlement.get(), false);
        if (mutatedSummaryDBO == null) {
            log.warn("Stop reallocating timeoff balance, validations for entitlement id = {} and typeID = {} failed", entitlement.get().id, entitlement.get().typeId());
            return null;
        }

        return summaryRepo.saveAndFlush(updateSummary(mutatedSummaryDBO));
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public CarryForwardResult carryForwardLeaves(TimeoffSummaryDBO expiredSummaryDBO, TimeoffSummaryDBO newSummaryDBO, ContractOuterClass.Contract contract) {
        log.info("Trying to carry forward timeoff balance for typeId = {} and contractId = {}", expiredSummaryDBO.typeId(), contract.getId());

        var entitlement = entitlementDBORepository.findByContractIdAndTypeId(expiredSummaryDBO.contractId(), expiredSummaryDBO.typeId());
        if (entitlement.isEmpty()) {
            log.warn("Entitlement for typeId = {} and contractId = {} not found", expiredSummaryDBO.typeId(), expiredSummaryDBO.contractId());
            return null;
        }

        val result = carryForwardLeavesService.carryForwardLeaves(entitlement.get(), expiredSummaryDBO, newSummaryDBO, contract);
        if (result != null) {
            summaryRepo.saveAndFlush(updateSummary(newSummaryDBO));
        }

        return result;
    }

    @Transactional
    public void updateTimeOffSummary(List<UpdateTimeOffSummaryInput> inputs) {
        List<TimeoffSummaryDBO> summariesToUpdate = new ArrayList<>();

        // Retrieve a mapping of summary IDs to their corresponding TimeoffSummaryDBO objects
        Map<Long, TimeoffSummaryDBO> idToSummaryMap = getIdToSummaryMap(inputs);

        // Retrieve a mapping of summary IDs to their associated TimeoffDBO (time-off entries)
        Map<Long, List<TimeoffDBO>> summaryIdToExistingTimeoffs = getExistingTimeoffs(idToSummaryMap.values());

        for (UpdateTimeOffSummaryInput input : inputs) {
            TimeoffSummaryDBO summary = idToSummaryMap.get(input.getSummaryId());

            if (summary == null) {
                throw new ValidationException("Summary not found for ID: " + input.getSummaryId());
            }

            List<TimeoffDBO> summaryTimeOffs = summaryIdToExistingTimeoffs.getOrDefault(summary.id(), Collections.emptyList());

            double newAllocatedDays = Optional.ofNullable(input.getAllocatedDaysCount())
                    .orElse(Optional.ofNullable(summary.allocatedCount()).orElse(0.0));
            double newCarriedDays = Optional.ofNullable(input.getCarriedDaysCount())
                    .orElse(Optional.ofNullable(summary.carriedCount()).orElse(0.0));
            double newEntitledDays = Optional.ofNullable(input.getEntitledDaysCount())
                    .orElse(Optional.ofNullable(summary.totalEntitledCount()).orElse(0.0));

            double takenCount = summaryTimeOffs.stream()
                    .filter(m -> !PENDING_STATUSES.contains(m.status())).mapToDouble(TimeoffDBO::noOfDays).sum();
            double pendingCount = summaryTimeOffs.stream()
                    .filter(m -> PENDING_STATUSES.contains(m.status())).mapToDouble(TimeoffDBO::noOfDays).sum();

            // Validate constraints
            validateSummaryUpdate(input.getSummaryId(), newAllocatedDays, newEntitledDays, newCarriedDays, takenCount, pendingCount);

            // Apply updates
            summary.allocatedCount(newAllocatedDays);
            summary.carriedCount(newCarriedDays);
            summary.totalEntitledCount(newEntitledDays);
            summary.takenCount(takenCount);
            summary.pendingCount(pendingCount);
            summariesToUpdate.add(summary);
            log.info("Successfully updated summaryId: {}", input.getSummaryId());
        }
        summaryRepo.saveAll(summariesToUpdate);
    }

    public LocalDate getEarliestActiveSummaryStartDate(Long contractId) {
        return summaryRepo.findEarliestActiveSummaryStartDate(contractId);
    }

    public LocalDate getFurthestSummaryEndDate(Long contractId) {
        return summaryRepo.findFurthestSummaryEndDate(contractId);
    }

    private Map<Long, List<TimeoffDBO>> getExistingTimeoffs(Collection<TimeoffSummaryDBO> summaries) {
        val timeoffFilter = createTimeOffFilter(summaries);
        val specBuilder = timeoffSpecBuilder.specBuilderByContractIdAndTypeId(timeoffFilter);
        val timeoffs = timeoffRepo.findAll(specBuilder);

        Map<Long, Map<Long, List<TimeoffDBO>>> contractIdToTimeoffsByTypeId = timeoffs.stream()
                .collect(Collectors.groupingBy(TimeoffDBO::contractId,
                        Collectors.groupingBy(TimeoffDBO::typeId)));

        return summaries.stream()
                .collect(Collectors.toMap(
                        BaseEntity::id,
                        summary ->  {
                            val contractId = summary.contractId();
                            val typeId = summary.typeId();

                            return contractIdToTimeoffsByTypeId.getOrDefault(contractId, Map.of()).getOrDefault(typeId, List.of());
                        }
                ));

    }

    private TimeoffFilters createTimeOffFilter(Collection<TimeoffSummaryDBO> summaryDBOS) {
        val contractIdTypeIdFilters =  summaryDBOS.stream()
                .map( summary -> TimeoffFilters.ContractIdTypeIdFilter.builder()
                        .contractId(summary.contractId())
                        .typeId(summary.typeId())
                        .statuses(Set.of(TimeOffStatus.APPROVAL_IN_PROGRESS, TimeOffStatus.APPROVED, TimeOffStatus.TAKEN))
                        .startDateFrom(summary.periodStart())
                        .toDate(summary.periodEnd())
                        .build())
                .toList();
        return TimeoffFilters.builder()
                .contractIdTypeIdFilters(contractIdTypeIdFilters)
                .build();
    }

    private Map<Long, TimeoffSummaryDBO> getIdToSummaryMap(List<UpdateTimeOffSummaryInput> inputs) {
        Set<Long> summaryIds = inputs.stream().map(UpdateTimeOffSummaryInput::getSummaryId).collect(Collectors.toSet());
        return summaryRepo.findAllById(summaryIds)
                .stream()
                .collect(Collectors.toMap(TimeoffSummaryDBO::id,
                        Function.identity()));
    }

    private void validateSummaryUpdate(Long summaryId, double allocatedDays, double entitledDays, double carriedDays, double takenDays, double pendingDays) {
        List<String> errors = new ArrayList<>();

        if (allocatedDays > entitledDays) {
            errors.add("Allocated count cannot be greater than entitled count.");
        }
        if (allocatedDays + carriedDays != entitledDays) {
            errors.add("Allocated count + Carried count must equal Entitled count.");
        }
        if (takenDays + pendingDays > entitledDays) {
            errors.add("Pending count + Taken count cannot exceed Entitled count.");
        }

        if (!errors.isEmpty()) {
            String errorMessage = String.join("; ", errors);
            throw new ValidationException(String.format("Validation failed for summaryId %d: %s", summaryId, errorMessage));
        }
    }

    private boolean isCarryForwardExpiryEnabled(final long companyId) {
        return featureFlagService.feature(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY, Map.of("company", companyId)).on();
    }

    public TimeoffSummaryDBO getPredecessor(final TimeoffSummaryDBO timeoffSummaryDBO) {
        return summaryRepo.findBySuccessor(timeoffSummaryDBO);
    }

    public TimeoffSummaryDBO getSuccessor(final TimeoffSummaryDBO timeoffSummaryDBO) {
        return summaryRepo.findByPredecessor(timeoffSummaryDBO);
    }

    public List<TimeoffSummaryDBO> getSummariesNotExpiredOnOrAfter(final LocalDate expiryDate) {
        return summaryRepo.findAllSummariesNotExpiredOnOrAfter(expiryDate);
    }

    public List<TimeoffSummaryDBO> getSummariesNotExpiredOnOrAfterForContracts(final LocalDate expiryDate, final Collection<Long> contractIds) {
        return summaryRepo.findAllSummariesNotExpiredOnOrAfterAndContractIdIn(expiryDate, new HashSet<>(contractIds));
    }

    public void deductCarryForwardLeave(final long summaryId, final double count) {
        val summary = summaryRepo.findById(summaryId)
                .orElseThrow(() -> new EntityNotFoundException("Timeoff summary id = " + summaryId + " not found"));

        if (summary.carriedCount() == null || summary.carriedCount() < count) {
            log.error("Deducting carried count ({}) is greater than available carried count ({}), summary id = {}", count, summary.carriedCount(), summaryId);
            throw new ValidationException("Deducting carried count is greater than available carried count, summary id = " + summaryId);
        }
        if (summary.totalEntitledCount() < count) {
            log.error("Deducting carried count ({}) is greater than total entitled count ({}), summary id = {}", count, summary.totalEntitledCount(), summaryId);
            throw new ValidationException("Deducting carried count is greater than total entitled count, summary id = " + summaryId);
        }

        summary.carriedCount(summary.carriedCount() - count);
        summary.totalEntitledCount(summary.totalEntitledCount() - count);
        summaryRepo.save(summary);
    }

    public void deleteSummaryByContractIdAndTypeId(Long contractId, Long typeId) {
        summaryRepo.deleteAllByContractIdAndTypeId(contractId, typeId);
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void generateFutureTimeoffSummaries(LocalDate currentDate) {
        try {
            log.info("[generateFutureTimeoffSummaries] Scheduler for date = [{}] - TRIGGERED", currentDate);
            val startTime = LocalDateTime.now();

            // First, check the count of summaries needing future entries.
            Long totalSummaryCount = summaryRepo.countSummariesNeedingFutureEntries(currentDate);
            if (totalSummaryCount == null || totalSummaryCount == 0) {
                val elapsedTime = getElapsedTime(startTime);
                log.info("[generateFutureTimeoffSummaries] Scheduler for date = [{}] - SKIPPED (no summaries found needing future entries) (time taken: {} min {} sec)", currentDate, elapsedTime.getFirst(), elapsedTime.getSecond());
                return;
            }
            log.info("[generateFutureTimeoffSummaries] Found total [{}] summaries needing future entries", totalSummaryCount);


            // Process only a batch of summaries at a time.
            List<TimeoffSummaryDBO> currentBatch = summaryRepo.findBatchOfSummariesNeedingFutureEntries(currentDate, timeoffFutureSummaryGenerationBatchSize);
            log.info("[generateFutureTimeoffSummaries] Timeoff future summaries generation process - STARTED (total_to_process: {},  current_batch_size: {})", totalSummaryCount, currentBatch.size());

            // Group summaries by contract and type.
            Map<Pair<Long, Long>, List<TimeoffSummaryDBO>> groupedSummariesMap = groupSummariesByContractIdAndTypeId(currentBatch);

            // Get entitlements for these summaries.
            Map<Pair<Long, Long>, TimeoffEntitlementDBO> simplifiedEntitlementsMap = getSimplifiedEntitlementsForSummaries(groupedSummariesMap);

            // Generate new upcoming summaries.
            List<TimeoffSummaryDBO> newSummaries = generateUpcomingTimeoffSummaries(groupedSummariesMap, simplifiedEntitlementsMap);

            // Save the new summaries.
            saveTimeoffSummaries(newSummaries);
            log.info("[generateFutureTimeoffSummaries] Saved {} new summaries", newSummaries.size());

            // Save allocation records for the new summaries.
            saveAllocationRecordsForNewSummaries(newSummaries);

            val elapsedTime = getElapsedTime(startTime);
            log.info("[generateFutureTimeoffSummaries] Timeoff future summaries generation process - COMPLETED. {} summaries created. (time taken: {} min {} sec)", newSummaries.size(), elapsedTime.getFirst(), elapsedTime.getSecond());

        } catch (Exception e) {
            log.error("[generateFutureTimeoffSummaries] Timeoff future summaries generation process - FAILED", e);
        }
    }

    private Map<Pair<Long, Long>, List<TimeoffSummaryDBO>> groupSummariesByContractIdAndTypeId(List<TimeoffSummaryDBO> timeoffSummaryList) {
        return timeoffSummaryList.stream()
                .collect(Collectors.groupingBy(summary ->
                        new Pair<>(summary.contractId(), summary.typeId())));
    }

    private Map<Pair<Long, Long>, TimeoffEntitlementDBO> getSimplifiedEntitlementsForSummaries(Map<Pair<Long, Long>, List<TimeoffSummaryDBO>> groupedSummariesMap) {
        if (groupedSummariesMap.isEmpty()) {
            return Collections.emptyMap();
        }

        // Extract all contractIds and typeIds from the keys.
        Set<Long> contractIds = new HashSet<>();
        Set<Long> typeIds = new HashSet<>();

        for (Pair<Long, Long> pair : groupedSummariesMap.keySet()) {
            contractIds.add(pair.getFirst());
            typeIds.add(pair.getSecond());
        }

        // Fetch simplified entitlements (i.e., only with the fields we need) for these contracts and types in one query.
        List<TimeoffEntitlementDBO> entitlements = entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(contractIds, typeIds);
        log.info("[generateFutureTimeoffSummaries] Fetched {} entitlements", entitlements.size());

        // Map the results to Pair keys.
        Map<Pair<Long, Long>, TimeoffEntitlementDBO> result = new HashMap<>();
        for (TimeoffEntitlementDBO entitlement : entitlements) {
            Pair<Long, Long> key = new Pair<>(entitlement.contractId(), entitlement.typeId());
            result.put(key, entitlement);
        }
        return result;
    }

    private List<TimeoffSummaryDBO> generateUpcomingTimeoffSummaries(Map<Pair<Long, Long>, List<TimeoffSummaryDBO>> groupedSummariesMap,
                                                                     Map<Pair<Long, Long>, TimeoffEntitlementDBO> simplifiedEntitlementsMap) {
        List<TimeoffSummaryDBO> summariesToCreate = new ArrayList<>();
        var totalContractIdTypeIdCount = groupedSummariesMap.size();
        var processedContractIdTypeIdCount = 0;

        for (var entry : groupedSummariesMap.entrySet()) {
            Pair<Long, Long> key = entry.getKey();
            List<TimeoffSummaryDBO> summaries = entry.getValue();
            if (CollectionUtils.isEmpty(summaries)) {
                log.info("[generateFutureTimeoffSummaries] No active summary found for (contractId, typeId) = {}, so future summary generation SKIPPED.", key);
                continue;
            }

            TimeoffSummaryDBO existingSummary = summaries.get(0);
            TimeoffSummaryDBO newSummary = createNewUpcomingSummary(existingSummary, simplifiedEntitlementsMap);
            if (newSummary == null) { // No new summary created if entitlement not found.
                continue;
            }
            summariesToCreate.add(newSummary);

            log.info("[generateFutureTimeoffSummaries] Added a future summary for (contractId, typeId) = {}. (total: {}, processed: {})", key, totalContractIdTypeIdCount, ++processedContractIdTypeIdCount);
        }
        return summariesToCreate;
    }

    private TimeoffSummaryDBO createNewUpcomingSummary(TimeoffSummaryDBO existingSummary, Map<Pair<Long, Long>, TimeoffEntitlementDBO> simplifiedEntitlementsMap) {
        // Calculate next period start and end dates (assuming yearly periods).
        LocalDate nextPeriodStart = existingSummary.periodEnd().plusDays(1);
        LocalDate nextPeriodEnd = nextPeriodStart.plusYears(1).minusDays(1);

        Pair<Long, Long> key = new Pair<>(existingSummary.contractId(), existingSummary.typeId());
        TimeoffEntitlementDBO entitlement = simplifiedEntitlementsMap.get(key);
        if (entitlement == null) {
            log.error("[generateFutureTimeoffSummaries] No entitlement found for (contractId, typeId) = {}, so future summary generation SKIPPED.", key);
            return null;
        }
        Double entitledValueInDays = getEntitledValueInDays(entitlement);

        return new TimeoffSummaryDBO()
                .contractId(existingSummary.contractId())
                .timeoffType(existingSummary.timeoffType())
                .typeId(existingSummary.typeId())
                .allocatedCount(entitledValueInDays)
                .takenCount(0D)
                .pendingCount(0D)
                .carriedCount(0D)
                .totalEntitledCount(entitledValueInDays)
                .status(TimeOffSummaryStatus.UPCOMING)
                .periodStart(nextPeriodStart)
                .periodEnd(nextPeriodEnd);
    }

    private Double prorateAllocation(Double entitledValueInDays,
                                     TimeOffUnit entitledUnit,
                                     LocalDate periodStart,
                                     LocalDate periodEnd) {
        int noOfDaysInPeriod = (int) ChronoUnit.DAYS.between(periodStart, periodEnd) + 1;
        double perDayValue = entitledValueInDays / 365;
        Double proratedValueInDays = perDayValue * noOfDaysInPeriod;
        if (entitledUnit == TimeOffUnit.DAYS && proratedValueInDays > entitledValueInDays) {
            // e.g. entitlement: 184 days, prorated = 185 days (probably happens in leap years) => should stop at 184 only
            proratedValueInDays = entitledValueInDays;
        }
        return Math.ceil(TimeOffUtil.round(proratedValueInDays));

    }

    private Double getEntitledValueInDays(TimeoffEntitlementDBO entitlement) {
        return TimeOffUtil.getDays(entitlement.value(), entitlement.unit());
    }

    private void saveTimeoffSummaries(List<TimeoffSummaryDBO> summaries) {
        if (CollectionUtils.isNotEmpty(summaries)) {
            summaryRepo.saveAllAndFlush(summaries);
        }
    }

    private void generateExpiredSummaries(
            LocalDate from,
            LocalDate to,
            Month companyFinancialYearEndMonth,
            Long contractId,
            TimeoffTypeDBO timeoffType,
            List<TimeoffSummaryDBO> updatedSummaries) {
        log.info("[generateExpiredSummaries] generating expired summaries (contract id : {}, type id : {}) from: {}, to: {}",
                contractId, timeoffType.id(), from, to);
        var startDate = from;
        while (startDate.isBefore(to)) {
            LocalDate financialYearEnd = getFinancialYearEnd(from, companyFinancialYearEndMonth);
            var endDate = financialYearEnd.isBefore(to) ? financialYearEnd : to;
            updatedSummaries.add(
                    TimeoffSummaryDBO.builder()
                            .periodStart(startDate)
                            .periodEnd(endDate)
                            .contractId(contractId)
                            .timeoffType(timeoffType)
                            .allocatedCount(0.0D)
                            .carryForwardCount(0.0D)
                            .carryForwardExpiredCount(0.0D)
                            .usedFromAllocatedCount(0.0D)
                            .usedFromCarryForwardCount(0.0D)
                            .usedFromLapsableCount(0.0D)
                            .usedFromNextCycleCarryForwardCount(0.0D)
                            .status(TimeOffSummaryStatus.EXPIRED)
                            .build()
            );
            log.info("[generateExpiredSummaries] EXPIRED summary generated (contract id : {}, type id : {}) {} to {}",
                    contractId, timeoffType.id(), startDate, endDate);
            startDate = endDate.plusDays(1);
        }
    }


    /**
     * Gets closest next financial year-end date for a given date
     * */
    private LocalDate getFinancialYearEnd(LocalDate referenceDate, Month financialYearEndMonth) {
        int year = referenceDate.getYear();
        LocalDate financialYearEnd = LocalDate.of(year, financialYearEndMonth, 1)
                .with(java.time.temporal.TemporalAdjusters.lastDayOfMonth());

        // If the given date is after the financial year-end, move to next year's financial year-end
        if (referenceDate.isAfter(financialYearEnd)) {
            financialYearEnd = financialYearEnd.plusYears(1);
        }

        return financialYearEnd;
    }

    private void saveAllocationRecordsForNewSummaries(List<TimeoffSummaryDBO> newSummaries) {
        if (CollectionUtils.isEmpty(newSummaries)) {
            return;
        }
        List<EntitlementChangeRecordEntity> allocationRecords = createAllocationRecordsForNewSummaries(newSummaries);
        entitlementChangeRecordRepository.saveAllAndFlush(allocationRecords);
        log.info("[generateFutureTimeoffSummaries] Saved {} allocation records (entitlement change records)", allocationRecords.size());
    }

    private List<EntitlementChangeRecordEntity> createAllocationRecordsForNewSummaries(List<TimeoffSummaryDBO> newSummaries) {
        return newSummaries.stream()
                .map(summary -> new EntitlementChangeRecordEntity(
                        null, // id is null for new records
                        summary.typeId(),
                        summary.contractId(),
                        EntitlementChangeCategory.ALLOCATION,
                        summary.allocatedCount(),
                        summary.periodStart(),
                        summary.periodEnd(),
                        null // refId is null
                ))
                .toList();
    }

    private Pair<Long, Long> getElapsedTime(LocalDateTime startTime) {
        Duration elapsed = Duration.between(startTime, LocalDateTime.now());
        long minutes = elapsed.toMinutes();
        long seconds = elapsed.minusMinutes(minutes).getSeconds();
        return new Pair<>(minutes, seconds);
    }


    public Pair<TimeoffSummaryDBO, TimeoffSummaryDBO> getCurrentAndNextSummaryByContractIdAndTypeId(Long contractId, Long typeId) {
        val summaries = summaryRepo.findCurrentAndNextSummaryByContractIdAndTypeId(contractId, typeId);
        val currentSummary = summaries.stream()
                .filter(s -> s.status() == TimeOffSummaryStatus.ACTIVE)
                .findFirst()
                .orElse(null);
        val nextSummary = summaries.stream()
                .filter(s -> s.status() == TimeOffSummaryStatus.UPCOMING)
                .findFirst()
                .orElse(null);
        return new Pair<>(currentSummary, nextSummary);
    }



    
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void activateNextTimeoffSummaries(LocalDate currentDate) {
        try {
            log.info("[activateNextTimeoffSummaries] Scheduler for date = [{}] - TRIGGERED", currentDate);
            val startTime = LocalDateTime.now();

            // Count active summaries that have a past period end date (i.e., period_end < currentDate)
            Long totalActiveSummaryCount = summaryRepo.countActiveSummariesWithPastPeriodEnd(currentDate);
            if (totalActiveSummaryCount == null || totalActiveSummaryCount == 0) {
                val elapsedTime = getElapsedTime(startTime);
                log.info("[activateNextTimeoffSummaries] Scheduler for date = [{}] - SKIPPED (no active summaries found with period_end < [{}]) (time taken: {} min {} sec)", currentDate, currentDate, elapsedTime.getFirst(), elapsedTime.getSecond());
                return;
            }
            log.info("[activateNextTimeoffSummaries] Found total [{}] active summaries having period_end < [{}]", totalActiveSummaryCount, currentDate);


            // Fetch a batch of active summaries that have a past period end date (i.e., period_end < currentDate)
            List<TimeoffSummaryDBO> batchToProcess = summaryRepo.findBatchOfActiveSummariesWithPastPeriodEnd(currentDate, timeoffNextSummaryActivationBatchSize);
            log.info("[activateNextTimeoffSummaries] Timeoff next summaries activation process - STARTED (total_to_process: {},  current_batch_size: {})", totalActiveSummaryCount, batchToProcess.size());


            // Build a map of active summaries, keeping the one with the latest period_end for each (contractId, typeId) pair
            Map<Pair<Long, Long>, TimeoffSummaryDBO> activeSummariesMap = buildActiveSummariesMap(batchToProcess);
            log.info("[activateNextTimeoffSummaries] Found {} active summaries", activeSummariesMap.size());

            // Create a unique set of (contract_id, type_id) pairs from the batch we're processing.
            Set<Pair<Long, Long>> contractIdTypeIdPairs = extractContractIdTypeIdPairs(batchToProcess);
            log.info("[activateNextTimeoffSummaries] Found {} unique (contractId, typeId) pairs in the active summaries", contractIdTypeIdPairs.size());


            // Get all unique contract IDs and type IDs from the current batch.
            Set<Long> contractIds = extractUniqueContractIds(batchToProcess);
            Set<Long> typeIds = extractUniqueTypeIds(batchToProcess);


            // Find all upcoming summaries for these contract IDs and type IDs where the date is within the period.
            List<TimeoffSummaryDBO> upcomingSummaries = summaryRepo.findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(contractIds, typeIds, currentDate);
            if (CollectionUtils.isEmpty(upcomingSummaries)) {
                log.info("[activateNextTimeoffSummaries] Timeoff next summaries activation process - SKIPPED (no upcoming summaries found with period_start <= [{}] <= period_end)", currentDate);
                return;
            }

            // Build a map of upcoming summaries, keeping the one with the earliest period_start for each (contractId, typeId) pair
            Map<Pair<Long, Long>, TimeoffSummaryDBO> upcomingSummariesMap = buildUpcomingSummariesMap(upcomingSummaries, contractIdTypeIdPairs);
            if (upcomingSummariesMap.isEmpty()) {
                log.info("[activateNextTimeoffSummaries] Timeoff next summaries activation process - SKIPPED (no upcoming summaries found for any of the active summaries' (contractId, typeId) pairs)");
                return;
            }
            log.info("[activateNextTimeoffSummaries] Found {} upcoming summaries", upcomingSummariesMap.size());


            // Get contracts from contract service for all contract_ids.
            Map<Long, ContractOuterClass.ContractBasicInfo> contractMap = fetchContractsMap(contractIds);
            log.info("[activateNextTimeoffSummaries] Fetched {} contracts data from contract-service", contractMap.size());


            // Get entitlements for all contract_id and type_id pairs.
            Map<Pair<Long, Long>, TimeoffEntitlementDBO> entitlementMap = fetchEntitlementsMap(contractIds, typeIds);
            log.info("[activateNextTimeoffSummaries] Fetched {} entitlements", entitlementMap.size());


            // Get definitions for all definition IDs.
            Map<Long, DefinitionEntity> definitionMap = fetchDefinitionsMap(entitlementMap.values());
            log.info("[activateNextTimeoffSummaries] Fetched {} definitions", definitionMap.size());


            List<TimeoffSummaryDBO> summariesToUpdate = new ArrayList<>();
            List<EntitlementChangeRecordEntity> carryForwardRecordsToCreate = new ArrayList<>();

            int activatedCount = processAndActivateNextSummaries(activeSummariesMap, upcomingSummariesMap, contractMap, entitlementMap, definitionMap, summariesToUpdate, carryForwardRecordsToCreate);
            log.info("[activateNextTimeoffSummaries] Next summary activation ready for total [{}] summaries", activatedCount);

            // Save updated summaries.
            saveTimeoffSummaries(summariesToUpdate);
            log.info("[activateNextTimeoffSummaries] Updated {} summaries", summariesToUpdate.size());

            // Save carry forward records.
            saveCarryForwardRecords(carryForwardRecordsToCreate);
            log.info("[activateNextTimeoffSummaries] Created {} carry forward records (entitlement_change_records)", carryForwardRecordsToCreate.size());


            val elapsedTime = getElapsedTime(startTime);
            log.info("[activateNextTimeoffSummaries] Timeoff next summaries activation process - COMPLETED. {} summaries activated. (time taken: {} min {} sec)", activatedCount, elapsedTime.getFirst(), elapsedTime.getSecond());

        } catch (Exception e) {
            log.error("[activateNextTimeoffSummaries] Timeoff next summaries activation process - FAILED", e);
        }
    }


    private Set<Long> extractUniqueContractIds(Collection<TimeoffSummaryDBO> timeoffSummaryList) {
        return timeoffSummaryList.stream()
                .map(TimeoffSummaryDBO::contractId)
                .collect(Collectors.toSet());
    }

    private Set<Long> extractUniqueTypeIds(Collection<TimeoffSummaryDBO> timeoffSummaryList) {
        return timeoffSummaryList.stream()
                .map(TimeoffSummaryDBO::typeId)
                .collect(Collectors.toSet());
    }

    private Map<Pair<Long, Long>, TimeoffSummaryDBO> buildActiveSummariesMap(Collection<TimeoffSummaryDBO> activeSummaries) {
        return activeSummaries.stream()
                .collect(Collectors.toMap(
                        summary -> new Pair<>(summary.contractId(), summary.typeId()),
                        Function.identity(),
                        (existing, incoming) -> incoming.periodEnd().isAfter(existing.periodEnd()) ? incoming: existing // Keep the latter one if duplicates
                ));
    }

    private Set<Pair<Long, Long>> extractContractIdTypeIdPairs(Collection<TimeoffSummaryDBO> summaries) {
        return summaries.stream()
                .map(summary -> new Pair<>(summary.contractId(), summary.typeId()))
                .collect(Collectors.toSet());
    }


    private Map<Pair<Long, Long>, TimeoffSummaryDBO> buildUpcomingSummariesMap(Collection<TimeoffSummaryDBO> upcomingSummaries, Set<Pair<Long, Long>> activeSummaryContractIdTypeIdPairs) {
        // Filter upcoming_summaries to only keep those that match the active summaries (contract_id, type_id) pairs.
        upcomingSummaries = filterUpcomingSummariesToKeepContractIdTypeIdPairs(upcomingSummaries, activeSummaryContractIdTypeIdPairs);
        if (CollectionUtils.isEmpty(upcomingSummaries)) {
            return Collections.emptyMap();
        }

        // Then build a map of upcoming summaries, keeping the one with the earliest period_start for each (contractId, typeId) pair.
        return upcomingSummaries.stream()
                .collect(Collectors.toMap(
                        summary -> new Pair<>(summary.contractId(), summary.typeId()),
                        Function.identity(),
                        (existing, incoming) -> existing.periodStart().isBefore(incoming.periodStart()) ? existing : incoming // Keep the earlier one if duplicates
                ));
    }

    private List<TimeoffSummaryDBO> filterUpcomingSummariesToKeepContractIdTypeIdPairs(Collection<TimeoffSummaryDBO> upcomingSummaries, Set<Pair<Long, Long>> activeSummaryContractIdTypeIdPairs) {
        return upcomingSummaries.stream()
                .filter(summary -> activeSummaryContractIdTypeIdPairs.contains(new Pair<>(summary.contractId(), summary.typeId())))
                .toList();
    }

    private Map<Long, ContractOuterClass.ContractBasicInfo> fetchContractsMap(Collection<Long> contractIds) {
        return contractServiceAdapter.getBasicContractsByIds(new HashSet<>(contractIds));
    }

    private Map<Pair<Long, Long>, TimeoffEntitlementDBO> fetchEntitlementsMap(Collection<Long> contractIds, Collection<Long> typeIds) {
        return entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(contractIds, typeIds)
                .stream()
                .collect(Collectors.toMap(
                        entitlement -> new Pair<>(entitlement.contractId(), entitlement.typeId()),
                        Function.identity(),
                        (existing, incoming) -> existing // In case of duplicates, keep the first one
                ));
    }

    private Map<Long, DefinitionEntity> fetchDefinitionsMap(Collection<TimeoffEntitlementDBO> entitlements) {
        Set<Long> definitionIds = entitlements.stream()
                .map(TimeoffEntitlementDBO::definitionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(definitionIds)) {
            return Collections.emptyMap();
        }

        return definitionEntityRepository.findAllById(definitionIds)
                .stream()
                .collect(Collectors.toMap(
                        DefinitionEntity::getId,
                        Function.identity()
                ));
    }

    private int processAndActivateNextSummaries(
            Map<Pair<Long, Long>, TimeoffSummaryDBO> activeSummariesMap,
            Map<Pair<Long, Long>, TimeoffSummaryDBO> upcomingSummariesMap,
            Map<Long, ContractOuterClass.ContractBasicInfo> contractMap,
            Map<Pair<Long, Long>, TimeoffEntitlementDBO> entitlementMap,
            Map<Long, DefinitionEntity> definitionMap,
            List<TimeoffSummaryDBO> summariesToUpdate,
            List<EntitlementChangeRecordEntity> carryForwardRecordsToCreate) {

        int totalToProcess = activeSummariesMap.size();
        int processedCount = 0;
        int activatedCount = 0;

        for (Map.Entry<Pair<Long, Long>, TimeoffSummaryDBO> entry : activeSummariesMap.entrySet()) {
            Pair<Long, Long> key = entry.getKey();
            TimeoffSummaryDBO activeSummary = entry.getValue();
            processedCount++;

            try {
                TimeoffSummaryDBO upcomingSummary = upcomingSummariesMap.get(key);
                if (upcomingSummary == null) {
                    log.info("[activateNextTimeoffSummaries] No upcoming summary found for (contractId, typeId) = {}. (total: {}, processed: {})", key, totalToProcess, processedCount);
                    continue;
                }

                // Mark the active summary as EXPIRED.
                markActiveSummaryAsExpired(activeSummary, summariesToUpdate);

                // Handle contract ended case.
                ContractOuterClass.ContractBasicInfo contract = contractMap.get(key.getFirst());
                if (isContractEnded(contract)) {
                    markUpcomingSummaryAsExpired(upcomingSummary, summariesToUpdate);
                    log.info("[activateNextTimeoffSummaries] Contract is ENDED, so setting next summary status to EXPIRED for (contractId, typeId) = {}. (total: {}, processed: {})", key, totalToProcess, processedCount);
                    continue;
                }

                // Process carry forward.
                TimeoffEntitlementDBO entitlement = entitlementMap.get(key);
                CarryForwardInfo carryForwardInfo = determineCarryForwardConfig(entitlement, definitionMap, key);
                log.info("Carry forward is [{}] for (contractId, typeId) = {}", carryForwardInfo.enabled ? "enabled" : "disabled", key);

                // Calculate and apply carry forward.
                Double possibleCarryForwardCount = calculatePossibleCarryForwardCount(carryForwardInfo, activeSummary);
                log.info("Possible carry forward count = {} for (contractId, typeId) = {}", possibleCarryForwardCount, key);

                // Activate the upcoming summary.
                markUpcomingSummaryAsActive(upcomingSummary, activeSummary, possibleCarryForwardCount, summariesToUpdate);
                activatedCount++;

                // Create carry forward record if needed.
                if (possibleCarryForwardCount > 0) {
                    createCarryForwardRecord(upcomingSummary, possibleCarryForwardCount, carryForwardInfo.expiry, carryForwardRecordsToCreate, key);
                }

                log.info("[activateNextTimeoffSummaries] Next summary activation ready for (contractId, typeId) = {}. (total: {}, processed: {})", key, totalToProcess, processedCount);

            } catch (Exception e) {
                log.warn("[activateNextTimeoffSummaries] Issue found processing summary for (contractId, typeId) = {}. Issue: {}", key, e.getMessage(), e);
            }
        }

        return activatedCount;
    }

    private void markActiveSummaryAsExpired(TimeoffSummaryDBO activeSummary, List<TimeoffSummaryDBO> summariesToUpdate) {
        activeSummary.status(TimeOffSummaryStatus.EXPIRED);
        summariesToUpdate.add(activeSummary);
    }

    private boolean isContractEnded(ContractOuterClass.ContractBasicInfo contract) {
        return (contract == null) ||
                (contract.getStatus() == ContractOuterClass.ContractStatus.ENDED) ||
                (contract.getStatus() == ContractOuterClass.ContractStatus.DELETED);
    }

    private void markUpcomingSummaryAsExpired(TimeoffSummaryDBO upcomingSummary, List<TimeoffSummaryDBO> summariesToUpdate) {
        upcomingSummary.status(TimeOffSummaryStatus.EXPIRED);
        summariesToUpdate.add(upcomingSummary);
    }

    private CarryForwardInfo determineCarryForwardConfig(TimeoffEntitlementDBO entitlement, Map<Long, DefinitionEntity> definitionMap, Pair<Long, Long> key) {
        CarryForwardInfo carryForwardInfo = new CarryForwardInfo();

        if (entitlement == null) {
            return carryForwardInfo;
        }

        // Handle legacy data (null definition ID).
        if (entitlement.definitionId() == null) {
            // Definition id is null for legacy HR_member data. And our legacy system returns a hardcoded carry-forward config for annual leave.
            // We need to handle the same behaviour here as well to keep the support for legacy data.
            log.info("[activateNextTimeoffSummaries] Definition id is NULL for entitlement id : {}, (contractId, typeId) = {}", entitlement.id(), key);
            if (entitlement.typeId() == 1) { // Annual leave
                carryForwardInfo.config = HrMemberTimeOffDefinitionKt.getAnnualLeaveDefinition().getDefinition().getConfigurations().getCarryForwardConfig(); // Hardcoded config for annual leave
                carryForwardInfo.enabled = true;
            }

        } else {
            // Handle the normal case with definition.
            DefinitionEntity definition = definitionMap.get(entitlement.definitionId());
            if (definition != null && definition.getConfigurations() != null && definition.getConfigurations().getCarryForwardConfig() != null) {
                carryForwardInfo.config = definition.getConfigurations().getCarryForwardConfig();
                carryForwardInfo.enabled = carryForwardInfo.config != null && carryForwardInfo.config.getEnabled();
                carryForwardInfo.expiry = (carryForwardInfo.config != null && carryForwardInfo.config.getExpiry() != null) ? carryForwardInfo.config.getExpiry() : null;
            }
        }

        return carryForwardInfo;
    }

    private Double calculatePossibleCarryForwardCount(CarryForwardInfo carryForwardInfo, TimeoffSummaryDBO activeSummary) {
        Double carryForwardCount = 0.0;
        if (carryForwardInfo.enabled) {
            carryForwardCount = TimeOffUtil.zeroIfNegative(
                    TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(carryForwardInfo.config, activeSummary));
        }
        return carryForwardCount;
    }

    private void markUpcomingSummaryAsActive(TimeoffSummaryDBO upcomingSummary, TimeoffSummaryDBO activeSummary, Double possibleCarryForwardCount, List<TimeoffSummaryDBO> summariesToUpdate) {
        upcomingSummary.status(TimeOffSummaryStatus.ACTIVE);
        upcomingSummary.carryForwardCount(possibleCarryForwardCount);
        upcomingSummary.usedFromCarryForwardCount(possibleCarryForwardCount > 0 ? activeSummary.usedFromNextCycleCarryForwardCount() : 0D);

        Double allocatedCount = upcomingSummary.allocatedCount();
        upcomingSummary.totalEntitledCount(Double.sum(allocatedCount, possibleCarryForwardCount));  // entitled = allocated + carry_forward

        summariesToUpdate.add(upcomingSummary);
    }

    private void createCarryForwardRecord(TimeoffSummaryDBO upcomingSummary, Double carryForwardAmount,
                                          CarryForwardExpiryEntity carryForwardExpiry,
                                          List<EntitlementChangeRecordEntity> carryForwardRecordsToCreate,
                                          Pair<Long, Long> key) {
        LocalDate expiryDate = determineExpiryDate(upcomingSummary, carryForwardExpiry);
        log.info("[activateNextTimeoffSummaries] Carry forward expiry date = [{}] for (contractId, typeId) = {}", expiryDate, key);

        EntitlementChangeRecordEntity carryForwardRecord = new EntitlementChangeRecordEntity(
                null, // id is null for new records
                upcomingSummary.typeId(),
                upcomingSummary.contractId(),
                EntitlementChangeCategory.CARRY_FORWARD,
                carryForwardAmount,
                upcomingSummary.periodStart(),
                expiryDate,
                upcomingSummary.id()
        );
        carryForwardRecordsToCreate.add(carryForwardRecord);
    }

    private LocalDate determineExpiryDate(TimeoffSummaryDBO upcomingSummary, CarryForwardExpiryEntity carryForwardExpiry) {
        LocalDate expiryDate = upcomingSummary.periodEnd();     // By default, the expiry date is the same as the summary period end date.
        if (carryForwardExpiry != null) {
            LocalDate lastValidEndDate = TimeOffUtil.getLastValidEndDate(upcomingSummary.periodStart(), carryForwardExpiry.getValue(), carryForwardExpiry.getUnit());
            if (lastValidEndDate != null) {
                expiryDate = lastValidEndDate;
            }
        }
        return expiryDate;
    }

    private void saveCarryForwardRecords(List<EntitlementChangeRecordEntity> carryForwardRecords) {
        if (CollectionUtils.isNotEmpty(carryForwardRecords)) {
            entitlementChangeRecordRepository.saveAllAndFlush(carryForwardRecords);
        }
    }










    // Helper class to store carry forward configuration information.
    private static class CarryForwardInfo {
        boolean enabled = false;
        CarryForwardConfigEntity config = null;
        CarryForwardExpiryEntity expiry = null;
    }










}
