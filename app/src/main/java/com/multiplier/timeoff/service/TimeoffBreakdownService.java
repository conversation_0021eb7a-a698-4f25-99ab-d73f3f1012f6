package com.multiplier.timeoff.service;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.company.schema.holiday.LegalEntityHoliday;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.HolidayServiceAdapter;
import com.multiplier.timeoff.adapters.WorkshiftServiceAdapter;
import com.multiplier.timeoff.core.common.dto.WorkshiftDTO;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.core.util.WorkshiftUtil;
import com.multiplier.timeoff.processor.TimeoffValidator;
import com.multiplier.timeoff.repository.TimeoffEntryRepository;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.repository.model.TimeoffEntryDBO;
import com.multiplier.timeoff.service.mapper.TimeOffInputMapper;
import com.multiplier.timeoff.service.mapper.TimeoffEntryMapper;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import com.netflix.graphql.dgs.InputArgument;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "TimeoffBreakdownService")
public class TimeoffBreakdownService {

    private final HolidayServiceAdapter holidayServiceAdapter;
    private final CurrentUser currentUser;
    private final ContractServiceAdapter contractServiceAdapter;
    private final AuthorizationService authorizationService;
    private final TimeoffValidator timeoffValidator;
    private final WorkshiftServiceAdapter workshiftServiceAdapter;
    private final TimeoffEntryRepository timeoffEntryRepository;
    private final TimeoffEntryMapper timeoffEntryMapper;
    private final TimeOffInputMapper timeOffInputMapper;

    public Pair<List<TimeoffEntryDBO>, Double> calculateTimeoffEntriesAndApplyingDays(
        Long contractId,
        TimeOffDate startDate,
        TimeOffDate endDate,
        @NotNull WorkshiftDTO workshift
    ) {
        log.info("[calculateTimeoffEntriesAndApplyingDays] getting timeoff entries for the timeoff from : {} to : {}", startDate, endDate);
        return getTimeOffEntriesAndNoOfDays(startDate, endDate, contractId, workshift, null, false);
    }


    public Pair<List<TimeoffEntryDBO>, Double> generateTimeOffEntries(TimeoffDBO timeoffDBO,
                                                                      WorkshiftDTO workshift) {
        return getTimeOffEntriesAndNoOfDays(
            TimeOffDate.newBuilder().dateOnly(timeoffDBO.startDate()).session(timeoffDBO.startSession()).build(),
            TimeOffDate.newBuilder().dateOnly(timeoffDBO.endDate()).session(timeoffDBO.endSession()).build(),
            timeoffDBO.contractId(),
            workshift,
            timeoffDBO,
            true
        );
    }

    public List<TimeOffBreakdown> getTimeoffBreakdown(DgsDataFetchingEnvironment dfe,
                                                      TimeOffBreakdownInput input) {
        log.info("START [getTimeoffBreakdown] timeoff breakdown, by currentUser id={}", currentUser.getContext().getId());

        // authorization
        val contract = contractServiceAdapter.getBasicContractById(input.getContractId());
        authorizationService.authorize(dfe, contract);
        log.info("[getTimeoffBreakdown] Authorization success for contractId: {}", input.getContractId());

        // pre validations
        timeoffValidator.throwIfContractIsNotValid(contract);
        log.info("[getTimeoffBreakdown] Contract validated for contractId: {}", input.getContractId());

        val workshift = workshiftServiceAdapter.getWorkshiftByContract(contract);
        log.info("[getTimeoffBreakdown] Retrieved workshift for contractId: {}", contract.getContractId());

        timeoffValidator.throwIfTimeoffBreakdownInputIsNotValid(input, contract, workshift);
        log.info("[getTimeoffBreakdown] Input validation passed");

        // fetch entry list breakdown
        val breakdownResult = calculateTimeoffEntriesAndApplyingDays(
                contract.getContractId(),
                timeOffInputMapper.map(input.getStartDate()),
                timeOffInputMapper.map(input.getEndDate()),
                workshift
        );

        val timeoffEntries = breakdownResult.getLeft();
        val noOfDays = breakdownResult.getRight();
        val result = mapToTimeOffBreakdown(timeoffEntries, input.getStartDate().getDateOnly(),
                input.getEndDate().getDateOnly(), noOfDays);

        log.info("END [getTimeoffBreakdown] Successfully generated breakdown with {} entries, total days: {}, ",
                timeoffEntries.size(), noOfDays);

        return result;

    }

    private Pair<List<TimeoffEntryDBO>, Double> getTimeOffEntriesAndNoOfDays(
            TimeOffDate startDate,
            TimeOffDate endDate,
            Long contractId,
            WorkshiftDTO workshift,
            @Nullable TimeoffDBO timeoffDBO,
            boolean isEntryGeneration
    ) {
        val startDateOnly = startDate.getDateOnly();
        val endDateOnly = endDate.getDateOnly();

        log.info("[getTimeOffEntriesAndNoOfDays] getting timeoff entries for the timeoff from : {} to : {} (isGeneration = {})",
                startDateOnly, endDateOnly, isEntryGeneration);
        val holidays = getHolidayList(startDateOnly, endDateOnly, contractId);
        val restDays = WorkshiftUtil.getRestDaysListForRange(startDateOnly, endDateOnly, workshift);

        List<TimeoffEntryDBO> timeoffEntries = new ArrayList<>();
        Double noOfDays = 0.0;

        for (var date = startDateOnly; date.isBefore(endDateOnly.plusDays(1)); date = date.plusDays(1)) {
            val timeOffEntry = getTimeOffEntry(date, holidays, restDays, startDate, endDate);
            noOfDays += timeOffEntry.value();
            timeoffEntries.add(timeOffEntry);

            if (isEntryGeneration) {
                timeOffEntry.timeoff(timeoffDBO);
            }
        }

        return Pair.of(timeoffEntries, noOfDays);
    }

    private TimeoffEntryDBO getTimeOffEntry(
            LocalDate date,
            List<LocalDate> holidays,
            List<LocalDate> restDays,
            TimeOffDate timeoffStartDate,
            TimeOffDate timeoffEndDate
    ) {

        if (holidays.contains(date)) {
            return getTimeoffEntryForHoliday(date);
        }

        if (restDays.contains(date)) {
            return getTimeoffEntryForRestDay(date);
        }

        return getTimeoffEntryForTimeoff(date, timeoffStartDate, timeoffEndDate);
    }

    private TimeoffEntryDBO getTimeoffEntryForTimeoff(LocalDate date, TimeOffDate timeoffStartDate, TimeOffDate timeoffEndDate) {
        val startDate = timeoffStartDate.getDateOnly();
        val startSession = timeoffStartDate.getSession();
        val endDate = timeoffEndDate.getDateOnly();
        val endSession = timeoffEndDate.getSession();

        // handle same-day entries
        if (startDate.equals(endDate)) {
            if (startSession == TimeOffSession.MORNING && endSession == TimeOffSession.AFTERNOON) {
                return getFullDayTimeoffEntry(date);
            } else {
                return getHalfDayTimeoffEntry(date, startSession);
            }
        }

        if (date.equals(startDate)) {
            if (startSession == TimeOffSession.MORNING) {
                return getFullDayTimeoffEntry(date);
            } else {
                return getHalfDayTimeoffEntry(date, TimeOffSession.AFTERNOON);
            }
        }
        if (date.equals(endDate)) {
            if (endSession == TimeOffSession.AFTERNOON) {
                return getFullDayTimeoffEntry(date);
            } else {
                return getHalfDayTimeoffEntry(date, TimeOffSession.MORNING);
            }
        }
        return getFullDayTimeoffEntry(date);
    }

    private TimeoffEntryDBO getTimeoffEntryForHoliday(LocalDate date) {
        return getLFullDayLeaveTimeoffEntry(date, TimeOffEntryType.HOLIDAY);
    }

    private TimeoffEntryDBO getTimeoffEntryForRestDay(LocalDate date) {
        return getLFullDayLeaveTimeoffEntry(date, TimeOffEntryType.RESTDAY);
    }

    private TimeoffEntryDBO getLFullDayLeaveTimeoffEntry(LocalDate date, TimeOffEntryType type) {
        return TimeoffEntryDBO.builder().date(date).value(0.0).type(type).session(TimeOffSession.FULL_DAY).build();
    }

    private TimeoffEntryDBO getFullDayTimeoffEntry(LocalDate date) {
        return TimeoffEntryDBO.builder().date(date).value(1.0).type(TimeOffEntryType.TIMEOFF).session(TimeOffSession.FULL_DAY).build();
    }

    private TimeoffEntryDBO getHalfDayTimeoffEntry(LocalDate date, TimeOffSession session) {
        return TimeoffEntryDBO.builder().date(date).value(0.5).type(TimeOffEntryType.TIMEOFF).session(session).build();
    }

    private List<LocalDate> getHolidayList(LocalDate startDate, LocalDate endDate, Long contractId) {
        val holidays = getHolidaysForGivenDateRange(startDate, endDate, contractId);

        return holidays.stream().map(holiday -> LocalDate.of(holiday.getYear(), holiday.getMonth(),
            holiday.getDate())).toList();
    }

    private List<LegalEntityHoliday.Holiday> getHolidaysForGivenDateRange(LocalDate startDate, LocalDate endDate,
        Long contractId) {
        val yearsSet = getYearSet(startDate, endDate);

        // Fetch holidays for all years in the range
        return yearsSet.stream().flatMap(year -> holidayServiceAdapter.getHolidays(Set.of(contractId), year, null).stream()).toList();
    }

    private List<Integer> getYearSet(LocalDate startDate, LocalDate endDate) {
        return startDate.getYear() <= endDate.getYear() ? IntStream.rangeClosed(startDate.getYear(),
            endDate.getYear()).boxed().collect(Collectors.toList()) : List.of(startDate.getYear(), endDate.getYear());
    }

    private List<TimeOffBreakdown> mapToTimeOffBreakdown(List<TimeoffEntryDBO> timeoffEntries, LocalDate startDate,
        LocalDate endDate, Double noOfDays) {
        log.debug("[mapToTimeOffBreakdown] Mapping {} timeoff entries to breakdown response", timeoffEntries.size());
        return List.of(TimeOffBreakdown.newBuilder()
            .startDate(startDate)
            .endDate(endDate)
            .noOfDays(noOfDays)
            .timeOffEntries(mapToTimeOffEntry(timeoffEntries))
            .build());
    }

    private List<TimeOffEntry> mapToTimeOffEntry(List<TimeoffEntryDBO> timeoffEntries) {
        log.debug("[mapToTimeOffEntry] Converting {} DBO entries to GraphQL entries", timeoffEntries.size());
        return timeoffEntries.stream()
            .map(entry ->
                TimeOffEntry.newBuilder()
                    .date(entry.date())
                    .session(entry.session())
                    .type(entry.type())
                    .value(entry.value())
                    .build())
            .toList();
    }

    @Transactional(readOnly = true)
    public Map<Long, List<TimeOffEntry>> getTimeOffEntriesGroupedByTimeOffId(Set<Long> timeoffIds) {
        log.info("[getTimeOffEntriesGroupedByTimeOffId] Fetching timeoff entries for {} timeoff ids", timeoffIds.size());
        List<TimeoffEntryDBO> timeoffEntryDBOs = timeoffEntryRepository.findAllByTimeoffIdIn(timeoffIds);

        // Group by timeoffId and convert TimeoffEntryDBO to TimeOffEntry using mapper
        return timeoffEntryDBOs.stream()
                .collect(Collectors.groupingBy(
                    TimeoffEntryDBO::timeoffId,
                    Collectors.mapping(
                       timeoffEntryMapper::map,
                       Collectors.toList()
                    )
                ));
    }
}
