package com.multiplier.timeoff.permission;

import com.multiplier.common.transport.user.CurrentUser;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.AuditorAware;

import java.util.Optional;

@AllArgsConstructor
public class AuditorAwareImpl implements AuditorAware<Long> {

    private CurrentUser currentUser;

    @Override
    public Optional<Long> getCurrentAuditor() {
        if (currentUser != null && currentUser.getContext() != null && currentUser.getContext().getId() != null) {
            return Optional.of(currentUser.getContext().getId());
        }

        return Optional.of(-1L);
    }
}
