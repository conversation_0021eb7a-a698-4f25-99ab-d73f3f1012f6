package com.multiplier.timeoff.permission;

public enum WebSecurityConstants {

    GRAPH_URL("/graphql"),
    HEALTH_URL("/app/up"),
    CURRENT_EXPERIENCE("Current-Experience"),
    <PERSON><PERSON>RER("Bearer "),
    AU<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_HEADER("Authorization"),
    AUTHORITIES_KEY("auth"),
    EXPERIENCES_KEY("experiences"),
    DETAILS("details"),
    ALPHA_ROLE("ROLE_"),
    METHOD_AUTHORITY("configured method authority"),
    GRANTED_AUTHORITY("user granted authority"),
    SINGLE_LEVEL_WILD_CARD("*"),
    MULTI_LEVEL_WILD_CARD("**"),
    ESCAPED_WILD_CARD("\\*"),
    DOT(".");

    private String constant;

    WebSecurityConstants(String constant) {
        this.constant = constant;
    }

    public String value(){
        return this.constant;
    }
}
