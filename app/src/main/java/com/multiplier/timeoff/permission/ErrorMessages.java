package com.multiplier.timeoff.permission;

public enum ErrorMessages {
    INVALID_TOKEN("Invalid JWT token."),
    ERROR_OCCURRED_VALIDATING_TOKEN("Error occurred while validating token : {}"),
    INVALID_PERMISSION_FORMAT_FOUND_RESOURCE_EXTRACTION("Invalid permission format found while extracting resource from "),
    INVALID_PERMISSION_FORMAT_FOUND_OPERATION_EXTRACTION("Invalid permission format found while extracting operation from "),
    INVALID_PERMISSION_FORMAT_CONFIGURED_AT_RESOURCE("Invalid permission format configured to the resource. "),
    USER_AUTHORITIES_UNAVAILABLE("user authorities unavailable"),
    COLON(" : ");

    private String errorMessage;

    ErrorMessages(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String value(){
        return this.errorMessage;
    }
}
