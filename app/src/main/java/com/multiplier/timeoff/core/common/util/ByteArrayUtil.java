package com.multiplier.timeoff.core.common.util;

import org.apache.tomcat.util.http.fileupload.ByteArrayOutputStream;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;


public class ByteArrayUtil {

    /**
     * Downloads a resource from the internet by it URL as byte-array.
     *
     * @param resourceURL URL pointing the resource.
     * @return Resource as base64 byte-array.
     * @throws IOException When something unexpected happens to the stream.
     */
    public static byte[] fromURL(String resourceURL) throws IOException {

        try (InputStream inStream = new URL(resourceURL).openStream()) {
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();
            int nRead;
            byte[] databytes = new byte[1024];
            while ((nRead = inStream.read(databytes, 0, databytes.length)) != -1) {
                buffer.write(databytes, 0, nRead);
            }

            buffer.flush();
            return buffer.toByteArray();
        }
    }


    /**
     * Converts resource stream to byte-array.
     *
     * @param inStream In-flowing resource stream.
     * @return Resource as base64 byte-array.
     * @throws IOException When something unexpected happens to the stream/buffer.
     */
    public static byte[] fromStream(InputStream inStream) throws IOException {

        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] databytes = new byte[1024];
        while ((nRead = inStream.read(databytes, 0, databytes.length)) != -1) {
            buffer.write(databytes, 0, nRead);
        }

        buffer.flush();
        return buffer.toByteArray();
    }
}