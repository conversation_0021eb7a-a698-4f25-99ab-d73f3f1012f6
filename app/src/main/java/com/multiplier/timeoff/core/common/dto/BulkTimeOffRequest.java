package com.multiplier.timeoff.core.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class BulkTimeOffRequest {
    private Long companyId;
    private Long entityId;
    private List<Input> inputs;

    @Builder
    @AllArgsConstructor
    @Data
    public static class Input {
        private String inputId;
        private String externalTimeOffId;
        private Long contractId;
        private String type;
        private String noOfDays;
        private String startDate;
        private String endDate;
        private String description;
        private String employeeId;
        private String employeeFullName;
    }
}
