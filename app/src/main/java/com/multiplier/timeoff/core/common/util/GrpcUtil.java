package com.multiplier.timeoff.core.common.util;

import com.google.protobuf.Timestamp;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;

public class GrpcUtil {
    public static Timestamp mapToTimestamp(LocalDate localDate) {
        if (localDate == null) {
            return Timestamp.getDefaultInstance();
        }

        var localDateTime = localDate.atStartOfDay();
        return Timestamp.newBuilder()
                .setSeconds(localDateTime.toEpochSecond(ZoneOffset.UTC))
                .setNanos(localDateTime.getNano())
                .build();

    }

    public static Timestamp mapToTimestamp(Instant instant) {
        if (instant == null) {
            return Timestamp.getDefaultInstance();
        }

        return Timestamp.newBuilder()
                .setSeconds(instant.getEpochSecond())
                .setNanos(instant.getNano())
                .build();

    }
}
