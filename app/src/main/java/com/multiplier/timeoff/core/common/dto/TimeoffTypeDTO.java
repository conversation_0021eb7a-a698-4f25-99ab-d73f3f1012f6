package com.multiplier.timeoff.core.common.dto;

/**
 * The DTO class for TimeoffTypeDBO (JPA) and timeoff.timeoff_type (DB)<br>
 * Due to naming confusion of `type TimeOffType` (graphql), I create a DTO here for internal usage across domains
 */
public class TimeoffTypeDTO {
    private Long id;
    private String key;
    private String label;
    private String description;

    public TimeoffTypeDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public TimeoffTypeDTO setKey(String key) {
        this.key = key;
        return this;
    }

    public TimeoffTypeDTO setLabel(String label) {
        this.label = label;
        return this;
    }

    public TimeoffTypeDTO setDescription(String description) {
        this.description = description;
        return this;
    }

    public Long getId() {
        return id;
    }

    public String getKey() {
        return key;
    }

    public String getLabel() {
        return label;
    }

    public String getDescription() {
        return description;
    }
}
