package com.multiplier.timeoff.core.common.db;


import com.multiplier.timeoff.core.common.constant.Database;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@MappedSuperclass
@Accessors(fluent = true, chain = true)
@EntityListeners(AuditingEntityListener.class)
public abstract class AuditableBaseEntity extends BaseEntity {

    @Column(name = Database.AuditableEntity.Column.CREATED_BY)
    @CreatedBy
    private Long createdBy;

    @Column(name = Database.AuditableEntity.Column.CREATED_ON)
    @CreatedDate
    private LocalDateTime createdOn;

    @Column(name = Database.AuditableEntity.Column.UPDATED_BY)
    @LastModifiedBy
    private Long updatedBy;

    @Column(name = Database.AuditableEntity.Column.UPDATED_ON)
    @LastModifiedDate
    private LocalDateTime updatedOn;
}
