package com.multiplier.timeoff.core.common.exceptionhandler;

import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import jakarta.servlet.http.HttpServletRequest;

/**
 * copied from core-service
 */
@Slf4j
@ControllerAdvice
public class CustomControllerAdvice {

    @ExceptionHandler(ClientAbortException.class)
    public void handleClientAbortException(ClientAbortException exception, HttpServletRequest request) {
        // Do nothing for now as this happen when user close the browser tab or moved to
        // different page while request data is still being in processing/transfering.
        // No impact to any functionalitiy. 
        log.warn("Client abort exception", exception);
    }
}
