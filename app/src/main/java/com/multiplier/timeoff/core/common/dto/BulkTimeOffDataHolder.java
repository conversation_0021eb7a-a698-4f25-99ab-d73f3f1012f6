package com.multiplier.timeoff.core.common.dto;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
public class BulkTimeOffDataHolder {
    private Long companyId;
    private Long entityId;
    private Map<Long, ContractOuterClass.Contract> idToContractMap;
    private Map<String, TimeoffTypeDBO> labelToCompanyTimeOffTypeMap;
    private Map<String, TimeoffSummaryDBO> contractIdTypeIdToSummaryMap;
    private Map<String, List<TimeoffDBO>> contractIdTypeIdToTimeOffsMap;
    private Map<String, TimeoffDBO> externalIdToExistingTimeOffMap;
    private Map<String, List<EntitlementChangeRecordEntity>> contractIdTypeIdToAllocationECRsMap;
    private List<EntitlementChangeRecordEntity> deductionECRs;
    private Set<String> duplicatedExternalIds;
    private Set<String> duplicatedCombinationsHashStrings;
}
