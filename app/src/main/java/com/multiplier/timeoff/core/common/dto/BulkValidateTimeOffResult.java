package com.multiplier.timeoff.core.common.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class BulkValidateTimeOffResult implements Serializable {
    private boolean success;
    private List<BulkValidateTimeOffResultItem> items;

    @Data
    @Builder
    public static class BulkValidateTimeOffResultItem implements Serializable {
        private String externalTimeOffId;
        private List<String> errors;
    }
}
