package com.multiplier.timeoff.core.common.dto;

import java.time.DayOfWeek;
import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class WorkshiftDTO {

    private final DayOfWeek startDate; // here DayOfWeek is from java.time
    private final DayOfWeek endDate;
    private final WorkingHoursDTO workHours;
    private final WorkingHoursDTO breakHours;

    @Data
    @Builder
    public static class WorkingHoursDTO {

        private final LocalTime startTime;
        private final LocalTime endTime;

    }

}
