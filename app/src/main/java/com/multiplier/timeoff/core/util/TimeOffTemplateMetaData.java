package com.multiplier.timeoff.core.util;

import com.multiplier.grpc.common.bulkupload.v1.*;

import java.util.LinkedHashMap;
import java.util.Map;

public class TimeOffTemplateMetaData {
    private static final Map<String, FieldRequirement> fieldRequirements;

    public static final String ENTITY_ID = "entityId";
    public static final String COMPANY_ID = "companyId";
    public static final String CONTRACT_ID = "contractId";

    public static final String TIME_OFF_ID = "externalTimeOffId";
    public static final String TIME_OFF_TYPE = "timeOffType";
    public static final String NO_OF_DAYS = "noOfDays";
    public static final String TIME_OFF_START_DATE = "timeOffStartDate";
    public static final String TIME_OFF_END_DATE = "timeOffEndDate";
    public static final String DESCRIPTION = "description";
    public static final String EMPLOYEE_ID = "employeeId";
    public static final String EMPLOYEE_FULL_NAME = "employeeFullName";

    static {
        fieldRequirements = new LinkedHashMap<>();

        fieldRequirements.put(TIME_OFF_ID, FieldRequirement.newBuilder()
                        .setKey(TIME_OFF_ID)
                        .setLabel("TimeOff ID")
                        .setType(FieldType.FIELD_TYPE_TEXT)
                        .setMandatory(true)
                        .setDescription("Unique Identifier for a timeoff")
                        .build()
        );

        fieldRequirements.put(EMPLOYEE_ID, FieldRequirement.newBuilder()
                        .setKey(EMPLOYEE_ID)
                        .setLabel("Employee ID")
                        .setType(FieldType.FIELD_TYPE_TEXT)
                        .setMandatory(true)
                        .setDescription("Unique identifier for an employee")
                        .build()
        );

        fieldRequirements.put(EMPLOYEE_FULL_NAME, FieldRequirement.newBuilder()
                        .setKey(EMPLOYEE_FULL_NAME)
                        .setLabel("Employee Full Name")
                        .setType(FieldType.FIELD_TYPE_TEXT)
                        .setMandatory(false)
                        .setDescription("")
                        .build()
        );

        fieldRequirements.put(TIME_OFF_TYPE, FieldRequirement.newBuilder()
                        .setKey(TIME_OFF_TYPE)
                        .setLabel("TimeOff Type")
                        .setType(FieldType.FIELD_TYPE_SELECT)
                        .setMandatory(true)
                        .setDescription("")
                        .build()
        );

        fieldRequirements.put(NO_OF_DAYS, FieldRequirement.newBuilder()
                        .setKey(NO_OF_DAYS)
                        .setLabel("No of Days")
                        .setType(FieldType.FIELD_TYPE_NUMBER)
                        .setMandatory(true)
                        .setDescription("Indicates the duration of the entire leave. " +
                                "Can be in multiples of 0.5.")
                        .build()
        );

        fieldRequirements.put(TIME_OFF_START_DATE, FieldRequirement.newBuilder()
                        .setKey(TIME_OFF_START_DATE)
                        .setLabel("TimeOff Start Date")
                        .setType(FieldType.FIELD_TYPE_DATE)
                        .setMandatory(true)
                        .setDescription("Indicates the start date when the leave was taken. " +
                                "Should be in format of YYYY-MM-DD.")
                        .build()
        );

        fieldRequirements.put(TIME_OFF_END_DATE, FieldRequirement.newBuilder()
                        .setKey(TIME_OFF_END_DATE)
                        .setLabel("TimeOff End Date")
                        .setType(FieldType.FIELD_TYPE_DATE)
                        .setMandatory(true)
                        .setDescription("Indicates the end date when the leave was taken. " +
                                "Should be in format of YYYY-MM-DD.")
                        .build()
        );

        fieldRequirements.put(DESCRIPTION, FieldRequirement.newBuilder()
                        .setKey(DESCRIPTION)
                        .setLabel("Description")
                        .setType(FieldType.FIELD_TYPE_TEXT)
                        .setMandatory(false)
                        .setDescription("")
                        .build()
        );
    }
    // Private constructor to prevent instantiation
    private TimeOffTemplateMetaData() {
        throw new IllegalStateException("TimeoffTemplateMetaData is a utility static class. Hence instantiation not supported");
    }

    public static Map<String, FieldRequirement> getFieldRequirements() {
        return fieldRequirements;
    }
}
