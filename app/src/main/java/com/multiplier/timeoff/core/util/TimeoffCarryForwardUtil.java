package com.multiplier.timeoff.core.util;

import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.repository.model.CarryForwardConfigEntity;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import lombok.val;


/**
 * Utility class for timeoff carry forward related calculations and operations.
 * This class consolidates common carry forward logic used across multiple services.
 */
@UtilityClass
@Slf4j
public class TimeoffCarryForwardUtil {

    /**
     * Calculates the next cycle carry forward balance for a given summary and carry forward configuration.
     * This method determines how much balance can be carried forward to the next cycle based on:
     * - Current summary balance
     * - Maximum carry forward limit
     * - Already used carry forward amount
     *
     * @param carryForwardConfig the carry forward configuration
     * @param summaryDBO the timeoff summary
     * @return the calculated carry forward balance for next cycle
     */
    public static Double calculateNextCycleCarryForwardBalance(CarryForwardConfigEntity carryForwardConfig, TimeoffSummaryDBO summaryDBO) {
        if (carryForwardConfig == null || !carryForwardConfig.getEnabled()) {
            log.info("[calculateNextCycleCarryForwardBalance] Carry forward not enabled for summary id : {}", summaryDBO.id());
            return 0.0;
        }
        if (summaryDBO.calculateBalance() <= 0) {
            log.info("[calculateNextCycleCarryForwardBalance] No balance to carry forward for summary id : {}", summaryDBO.id());
            return 0.0;
        }
        val currentSummaryBalance = summaryDBO.calculateCurrentSummaryBalance();
        val maxCarryForward = getMaximumCarryForwardCount(carryForwardConfig);
        val availableNextCycleCarryForwardBalance = Math.min(currentSummaryBalance, maxCarryForward);
        val nextCycleCarryForwardBalance = availableNextCycleCarryForwardBalance - summaryDBO.usedFromNextCycleCarryForwardCount();
        log.info("[calculateNextCycleCarryForwardBalance] Next cycle carry forward balance ({}) = min(current summary balance ({}), max carry forward ({})) - used from next cycle carry forward ({}))",
                nextCycleCarryForwardBalance, currentSummaryBalance, maxCarryForward, summaryDBO.usedFromNextCycleCarryForwardCount());
        return nextCycleCarryForwardBalance;
    }



    /**
     * Gets the maximum carry forward count from the carry forward configuration.
     * Returns Double.MAX_VALUE if no limit is set, or 0.0 if carry forward is disabled.
     *
     * @param carryForwardConfig the carry forward configuration
     * @return the maximum carry forward count
     */
    public static Double getMaximumCarryForwardCount(CarryForwardConfigEntity carryForwardConfig) {
        if (carryForwardConfig == null || !carryForwardConfig.getEnabled()) {
            return 0.0;
        }
        if (carryForwardConfig.getMaxLimit() == null) {
            return Double.MAX_VALUE;
        }
        return carryForwardConfig.getMaxLimit().getValue() == null ? Double.valueOf(Double.MAX_VALUE) : carryForwardConfig.getMaxLimit().getValue();
    }


}
