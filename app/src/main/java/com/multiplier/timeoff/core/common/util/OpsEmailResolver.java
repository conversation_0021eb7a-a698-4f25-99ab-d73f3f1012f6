package com.multiplier.timeoff.core.common.util;

import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class OpsEmailResolver {
    private final CompanyServiceAdapter companyServiceAdapter;
    private final String opsEmail;
    private final String opsTestEmail;
    private final String salesEmail;
    private final String salesTestEmail;
    private final String opsCustomerSuccessEmail;
    private final String opsPayrollUpdatesEmail;
    private final String opsMemberOnboardingEmail;
    private final String opsSalesEmail;
    private final String opsFinanceEmail;
    private final String multiplierSignatoryEmail;

    public OpsEmailResolver(CompanyServiceAdapter companyServiceAdapter,
                            @Value("${ops.recipient-email}") String opsEmail,
                            @Value("${ops.test-recipient-email}") String opsTestEmail,
                            @Value("${ops.sales-email}") String salesEmail,
                            @Value("${ops.msa-sales-email}") String salesTestEmail,
                            @Value("${ops.customer-success-email}") String opsCustomerSuccessEmail,
                            @Value("${ops.payroll-updates-email}") String opsPayrollUpdatesEmail,
                            @Value("${ops.member-onboarding-email}") String opsMemberOnboardingEmail,
                            @Value("${ops.sales-email}") String opsSalesEmail ,
                            @Value("${ops.finance-email}") String opsFinanceEmail,
                            @Value("${ops.eor-contract-signer-email}") String multiplierSignatoryEmail) {
        this.companyServiceAdapter = companyServiceAdapter;
        this.opsEmail = opsEmail;
        this.opsTestEmail = opsTestEmail;
        this.salesEmail = salesEmail;
        this.salesTestEmail = salesTestEmail;
        this.opsCustomerSuccessEmail = opsCustomerSuccessEmail;
        this.opsPayrollUpdatesEmail = opsPayrollUpdatesEmail;
        this.opsMemberOnboardingEmail = opsMemberOnboardingEmail;
        this.opsSalesEmail = opsSalesEmail;
        this.opsFinanceEmail = opsFinanceEmail;
        this.multiplierSignatoryEmail = multiplierSignatoryEmail;
    }

    public String getOpsRecipientEmail(long companyId) {
        return companyServiceAdapter.isTestCompany(companyId) ? opsTestEmail : opsEmail;
    }

    public String getOpsRecipientEmail(boolean isTestAccount) {
        return isTestAccount ? opsTestEmail : opsEmail;
    }

    public String getOpsCustomerSuccessEmail(long companyId) {
        return companyServiceAdapter.isTestCompany(companyId) ? opsTestEmail : opsCustomerSuccessEmail;
    }

    public String getOpsPayrollUpdatesEmail(long companyId) {
        return companyServiceAdapter.isTestCompany(companyId) ? opsTestEmail : opsPayrollUpdatesEmail;
    }

    public String getOpsMemberOnboardingEmail(long companyId) {
        return companyServiceAdapter.isTestCompany(companyId) ? opsTestEmail : opsMemberOnboardingEmail;
    }

    public String getOpsSalesEmail(long companyId) {
        return companyServiceAdapter.isTestCompany(companyId) ? opsTestEmail : opsSalesEmail;
    }

    public String getOpsFinanceEmail(long companyId) {
        return companyServiceAdapter.isTestCompany(companyId) ? opsTestEmail : opsFinanceEmail;
    }

    public String getSalesEmail(long companyId) {
        return companyServiceAdapter.isTestCompany(companyId) ? salesTestEmail: salesEmail;
    }

    public String getMultiplierSignatoryEmail(long companyId) {
        return companyServiceAdapter.isTestCompany(companyId) ? opsTestEmail : multiplierSignatoryEmail;
    }
}
