package com.multiplier.timeoff.core.common.util;

import com.multiplier.timeoff.types.PageResult;
import org.springframework.data.domain.Page;

public class PageResultHelper {
    public static <T> PageResult build(Page<T> page) {
        return PageResult.newBuilder()
                .pageSize(page.getSize())
                .pageNumber(page.getNumber())
                .count((int) page.getTotalElements())
                .build();
    }
}
