package com.multiplier.timeoff.core.common.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
public class BulkUpsertTimeOffResult implements Serializable {
    private boolean success;
    private String message;
    private List<Item> items;

    @Data
    @Builder
    public static class Item implements Serializable {
        private String externalTimeOffId;
        private Long timeOffId;
        private List<String> errors;
    }
}
