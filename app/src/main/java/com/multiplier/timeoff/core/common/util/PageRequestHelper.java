package com.multiplier.timeoff.core.common.util;


import com.multiplier.timeoff.types.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

import static com.multiplier.timeoff.types.Order.DESC;

@Component
public class PageRequestHelper {
    static final Integer DEFAULT_PAGE_SIZE = 10;
    static final Integer MAXIMUM_PAGE_SIZE = 100;
    static final Integer DEFAULT_PAGE_NUMBER = 0;

    public org.springframework.data.domain.PageRequest toSpringPageRequestWithDefaultSort(PageRequest graphPageRequest, String defaultSortCol) {
        graphPageRequest = refine(graphPageRequest);
        addDefaultSortIfAbsent(graphPageRequest, defaultSortCol);

        Sort sort = Sort.by(
                graphPageRequest.getSort().stream().map(graphSort ->
                        new Sort.Order(Sort.Direction.valueOf(graphSort.getOrder().name()), graphSort.getOrderBy())
                ).toList()
        );

        return org.springframework.data.domain.PageRequest.of(graphPageRequest.getPageNumber(), graphPageRequest.getPageSize(), sort);
    }

    private PageRequest refine(PageRequest pageRequest) {
        if (Objects.isNull(pageRequest)) {
            pageRequest = PageRequest.newBuilder()
                    .pageNumber(DEFAULT_PAGE_NUMBER)
                    .pageSize(DEFAULT_PAGE_SIZE)
                    .build();
        }

        if (pageRequest.getPageNumber() < 0) {
            pageRequest.setPageNumber(DEFAULT_PAGE_NUMBER);
        }
        if (pageRequest.getPageSize() > MAXIMUM_PAGE_SIZE) {
            pageRequest.setPageSize(MAXIMUM_PAGE_SIZE);
        }
        if (pageRequest.getPageSize() < 1) {
            pageRequest.setPageSize(DEFAULT_PAGE_SIZE);
        }

        return pageRequest;
    }

    private void addDefaultSortIfAbsent(PageRequest pageRequest, String defaultSortCol) {
        if (CollectionUtils.isEmpty(pageRequest.getSort())) {
            pageRequest.setSort(List.of(new com.multiplier.timeoff.types.Sort(defaultSortCol, DESC)));
        }
    }
}
