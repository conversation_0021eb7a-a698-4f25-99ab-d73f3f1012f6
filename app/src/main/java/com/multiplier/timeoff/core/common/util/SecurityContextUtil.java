package com.multiplier.timeoff.core.common.util;

import com.multiplier.common.transport.user.UserContext;
import com.multiplier.timeoff.core.common.db.AuditUser;
import org.jetbrains.annotations.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.CollectionUtils;

import java.util.Optional;

/**
 * To be used in some non-bean classes where `currentUser` is needed. E.g. in @Entity classes
 */
public class SecurityContextUtil {
    private SecurityContextUtil() {
        // for SONAR
    }

    public static Optional<UserContext> getUserDetails() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return Optional.empty();
        }
        Object details = authentication.getDetails();
        if (details instanceof UserContext userContext) {
            return Optional.of(userContext);
        }
        return Optional.empty();
    }


    /**
     * @return an AuditUser object with currentUser's userId and experience
     */
    @Nullable
    public static AuditUser getAuditUser() {
        var userDetails = getUserDetails().orElse(null);
        if (userDetails == null) {
            return null;
        }
        var exp = CollectionUtils.isEmpty(userDetails.getExperiences())
                ? null
                : userDetails.getExperience();
        return new AuditUser()
                .setUserId(userDetails.getId())
                .setExperience(exp);
    }
}
