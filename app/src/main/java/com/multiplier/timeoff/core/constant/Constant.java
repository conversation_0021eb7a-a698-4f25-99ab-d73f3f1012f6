package com.multiplier.timeoff.core.constant;

public interface Constant {
    String LOGO = "logo";
    String COUNTRY = "country";
    String COMPANY_NAME = "name";

    Double UNLIMITED_LEAVES = 365.0;

    /**
     * userdb.platform_group
     */
    interface USER_ROLE {

        String SUPER_ADMIN = "Super Admin";
        String INTERNAL_SYSTEM = "Internal System";
        String ADMIN = "Admin";
        String COMPANY_SUPER_ADMIN = "Company Super Admin";
        String COMPANY_ADMIN = "Company Admin";
        String COMPANY_MANAGER = "Company Manager";
        String MEMBER = "Member";
        String OPERATIONS_ADMIN = "Operations Admin";
        String PARTNER_SUPER_ADMIN = "Partner Super Admin";
        String PARTNER_ADMIN = "Partner Admin";
        String ONBOARDING_LEAD = "Onboarding Lead";
        String ONBOARDING_SPECIALIST = "Onboarding Specialist";
        String CUSTOMER_SUCCESS_LEAD = "Customer Success Lead";
        String CUSTOMER_SUCCESS_SPECIALIST = "Customer Success Specialist";
        String SALES_OPS = "Sales Ops";
        String COMPANY_ZOMBIE = "Company Zombie";

    }

    interface USER_SIGNUP_CHANNEL {

        String ORGANIC = "ORGANIC";
        String COMPANY_USER = "COMPANY_USER";
        String SALES = "SALES";
        String OPERATIONS = "OPERATIONS";

    }

    interface Compensation {

        String BASE_PAY = "basePay";
        String ANNUAL_BONUS = "annualBonus";
        String JOINING_BONUS = "joiningBonus";
        String VARIABLE_PERFORMANCE_BONUS = "variablePerformanceBonus";
        String ALLOWANCES = "allowances";
        String OTHER = "other";
        String THR = "thr";
        String MONTH_13TH = "13thMonth";

    }

    interface SCHEDULED_JOB_NAME {

        String SendJoiningDayEmailForAllStartingEmployeeContracts = "sendJoiningDayEmailForAllStartingEmployeeContracts";
        String SendExpenseReminders = "sendExpenseReminders";
        String GenerateFreelancerInvoices = "generateFreelancerInvoices";
        String ActivateReviews = "activateReviews";
        String TriggerTimeOffUpdate = "triggerTimeOffUpdate";
        String TriggerLegacyTimeoffReallocation = "triggerLegacyTimeoffReallocation";
        String TriggerTimeoffAllocation = "triggerTimeoffAllocation";
        String TriggerTimeoffCarryForward = "triggerTimeoffCarryForward";
        String TriggerCarryForwardExpiration = "triggerCarryForwardExpiration";
        String GENERATE_FUTURE_TIMEOFF_SUMMARIES = "generateFutureTimeoffSummaries";
        String ACTIVATE_NEXT_TIMEOFF_SUMMARIES = "activateNextTimeoffSummaries";

    }

    interface SHEDLOCK_TIME_CONFIGURATION {

        String defaultLockAtMostFor = "PT40M";
        String defaultLockAtLeastFor = "PT20M";
        String lockAtLeastForString = "PT30M";
        String lockAtMostForString = "PT60M";
        String LOCK_AT_LEAST_FOR_5_MIN = "PT5M";
        String LOCK_AT_MOST_FOR_30_MIN = "PT30M";

    }

    interface EXPERIENCE {
        String MEMBER_EXP = "member";
        String COMPANY_EXP = "company";
        String OPERATIONS_EXP = "operations";
    }
}
