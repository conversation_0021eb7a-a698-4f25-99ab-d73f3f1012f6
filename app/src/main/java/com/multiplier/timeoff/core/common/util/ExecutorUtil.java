package com.multiplier.timeoff.core.common.util;

import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.security.concurrent.DelegatingSecurityContextExecutor;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.concurrent.Executor;

public class ExecutorUtil {
    /**
     * For a service logic to access the security context (currentUser) when executed in a child thread, e.g. Data Loader.<br>
     * Without this, the child thread will crash if trying to get `currentUser` or similar, because security context is not delegated by default.
     * @return an executor having SecurityContext delegated from the parent thread
     */
    public static Executor getNewSecurityContextExecutor() {
        SimpleAsyncTaskExecutor delegateExecutor = new SimpleAsyncTaskExecutor();
        return new DelegatingSecurityContextExecutor(delegateExecutor, SecurityContextHolder.getContext());
    }
}
