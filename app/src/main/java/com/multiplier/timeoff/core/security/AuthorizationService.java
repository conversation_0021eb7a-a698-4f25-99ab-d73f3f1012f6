package com.multiplier.timeoff.core.security;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.attribute.ContractAccessService;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ApprovalServiceAdapter;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.core.constant.Constant;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.multiplier.timeoff.adapters.CompanyServiceAdapterKt.toCompanyUserFilters;

@Service
@AllArgsConstructor
@Slf4j(topic = "AuthorizationService")
public class AuthorizationService {
    private final ContractAccessService contractAccessService;
    private final CurrentUser currentUser;
    private final ApprovalServiceAdapter approvalServiceAdapter;
    private CompanyServiceAdapter companyServiceAdapter;

    public List<TimeoffDBO> filterTimeOffs(DgsDataFetchingEnvironment dfe, List<TimeoffDBO> timeOffs) {
        if (CollectionUtils.isEmpty(timeOffs)) {
            return Collections.emptyList();
        }

        try {
            Set<Long> timeoffContractIds = timeOffs.stream()
                    .map(TimeoffDBO::contractId)
                    .collect(Collectors.toSet());
            Collection<Long> allowedContractIDs = filterAccessibleContractIds(dfe, timeoffContractIds);

            return timeOffs.stream()
                    .filter(timeOff -> allowedContractIDs.contains(timeOff.contractId()))
                    .toList();

        } catch (Exception e) {
            log.warn("[filterTimeOffs] Error while filtering timeoffs", e);
            return Collections.emptyList();
        }
    }

    public void authorize(DgsDataFetchingEnvironment dfe, @NotNull ContractOuterClass.ContractBasicInfo contract) {
        authorize(dfe, contract.getContractId(), contract.getMemberId());
    }

    public void authorize(DgsDataFetchingEnvironment dfe, @NotNull ContractOuterClass.Contract contract) {
        authorize(dfe, contract.getId(), contract.getMemberId());
    }

    private void authorize(DgsDataFetchingEnvironment dfe, Long contractId, Long memberId) {
        log.info("[authorize] authorizing request for user id : {}, contract id : {}", currentUser.getContext().getId(), contractId);
        if (Constant.EXPERIENCE.MEMBER_EXP.equals(currentUser.getContext().getExperience())) {
            // Access control library not validating access for member role as of now (return true for all members). Hence, domain need
            // to validate for member. Once ABAC library implements member validation we can remove member validations from here
            authorizeForMember(memberId);
            return;
        }

        if (!contractAccessService.hasContractAccess(dfe, contractId)) {
            if (isApproverForContract(contractId)) {
                // ABAC library return access denied for timeoff approver. Hence, We need to handle it from domain side as
                // of now. If current user is timeoff approver => we authorize the request
                return;
            }
            // current user does not have access and also not a timeoff approver of contract. Hence, Access denied
            // todo: convert this to MPL Error
            throw new AccessDeniedException("Access denied for user id : " + currentUser.getContext().getId());
        }

        log.info("[authorize] user id : {} authorized for contract id : {}", currentUser.getContext().getId(), contractId);
    }

    public Collection<Long> filterAccessibleContractIds(DgsDataFetchingEnvironment dfe, Collection<Long> contractIds) {
        if (CollectionUtils.isEmpty(contractIds)) {
            return Collections.emptyList();
        }
        log.info("[filterTimeOffs] timeoffContractIds count:{}, contractIds:  {}", contractIds.size(), contractIds);
        Collection<Long> allowedContractIDs = contractAccessService.applyContractAttributes(dfe, contractIds);
        log.info("[filterTimeOffs] allowedContractIDs count:{}, contractIds:  {}", allowedContractIDs.size(), allowedContractIDs);
        return allowedContractIDs;
    }

    private void authorizeForMember(Long memberId) {
        if (!currentUser.getContext().getScopes().getMemberId().equals(memberId)) {
            throw new AccessDeniedException("Access denied for user id : " + currentUser.getContext().getId());
        }
    }

    private boolean isApproverForContract(Long contractId) {
        // if current user added as an approver of timeoffs we need to bypass all ABAC validations since ABAC
        // library not validating approvers
        //  USE CASE :
        //     A is a manager => X,Y are subordinates
        //     B is a manager => N,M are subordinates
        //     N added to timeoff approving list of A
        //        - Now A can CRUD timeoffs for X, Y (since he is the manager) and N (since he is the approver)
        //        - B also can CRUD timeoffs for N since he is the manager
        if (Constant.EXPERIENCE.MEMBER_EXP.equals(currentUser.getContext().getExperience())) {
            // member can not be timeoff approver
            return false;
        }
        var companyUser = companyServiceAdapter.getCompanyUser(toCompanyUserFilters(currentUser));
        if (companyUser.getIsManager()) {
            List<Long> managedContractIds = Objects.requireNonNull(approvalServiceAdapter.getManagedContractIds(companyUser.getId()));
            if (managedContractIds.contains(contractId)) {
                log.info("[isApproverForContract] current user is an approver of the contract id : {}. So by passing ABAC validations", contractId);
                return true;
            }
        }
        return false;
    }
}
