package com.multiplier.timeoff.core.common.exceptionhandler;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * copied from core-service
 */
@Getter
public class MPLError {
    private final MPLErrorType errorType;   // Mandatory
    private Map<String, Object> params;     // Optional

    public MPLError(MPLErrorType errorType) {
        this.errorType = errorType;
    }

    public MPLError(MPLErrorType errorType, Map<String, Object> params) {
        this.errorType = errorType;
        this.params = params;
    }

    /**
     * Return the modified "MPLErrorType" enum message in which parameter values are replaced by the actual values.
     * Example:
     *      <NAME_EMAIL> already exists
     *
     * @return the error message
     */
    public String getMessage() {
        if (params == null) {
            return errorType.getMessage();
        }

        String message = errorType.getMessage();
        for (var entry : params.entrySet()) {
            message = message.replace("{{" + entry.getKey() + "}}", toStringValue(entry.getValue()));
        }
        return message;
    }

    private String toStringValue(Object obj) {
        if (obj == null) {
            return "null";
        }

        // If a list, convert list items to a comma seperated string & return.
        if (obj instanceof List) {
            List<Object> lst = (List<Object>) obj;
            List<String> itemStrings = new ArrayList<>();
            for (Object item : lst) {
                itemStrings.add(item == null ? "null" : item.toString());
            }
            return String.join(", ", itemStrings);
        }

        // Else, return the string value.
        return obj.toString();
    }


}
