package com.multiplier.timeoff.core.common.util;


import com.multiplier.timeoff.types.CountryCode;

import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class LocaleUtil {

    private static Map<String, String> COUNTRY_CODE_MAP_ALPHA3_ALPHA2 = createCountryCodeMapAlpha3Alpha2();


    private static Map<String, String> createCountryCodeMapAlpha3Alpha2() {

        return Locale.getISOCountries(Locale.IsoCountryCode.PART1_ALPHA2).stream().collect(
                Collectors.toMap(
                        alpha2 -> new Locale("", alpha2).getISO3Country().toUpperCase(),
                        alpha2 -> alpha2));
    }

    public static Locale createDefault(String countryIsoAlpha3Code, String language) {

        return new Locale(Optional.ofNullable(language).orElse(""), COUNTRY_CODE_MAP_ALPHA3_ALPHA2.get(countryIsoAlpha3Code.toUpperCase()));
    }

    public static Locale createDefault(CountryCode country, String language) {

        return createDefault(country.name(), language);
    }

    public static Locale createDefault(CountryCode country) {

        return createDefault(country, null);
    }
}
