package com.multiplier.timeoff.core.common.util;

import org.jetbrains.annotations.Nullable;

public class EmailUtil {
    @Nullable
    public static String maskEmail(String email) {
        if (email == null) {
            return null;
        }
        email = email.trim();
        var emailParts = email.split("@");
        if (emailParts.length != 2) { // invalid email format, skip masking
            return email;
        }
        return maskString(emailParts[0]) + "@" + maskString(emailParts[1]);
    }

    @Nullable
    private static String maskString(String str) {
        if (str == null) {
            return null;
        }
        if (str.isEmpty()) {
            return "";
        }
        if (str.length() == 1) {
            return "*";
        }
        var sb = new StringBuilder(str);
        var maskedCharCount = str.length() / 2; // round-down
        for (int i = 0; i < maskedCharCount; i++) {
            sb.setCharAt(str.length() - 2 - i, '*');
        }
        return sb.toString();
    }

}
