package com.multiplier.timeoff.core.common.util;

public class NumberUtil {

    public static Double roundToNearest(Double source, Double denominator) {

        /* TODO: Throw exception for denominator being zero.. */
        return (source == null || denominator == null)
                ? null
                : (((int) (source / denominator)) * denominator) + ((((source % denominator) >= (denominator / 2)) ? 1 : 0) * denominator);
    }
}
