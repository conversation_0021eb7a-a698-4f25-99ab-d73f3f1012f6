package com.multiplier.timeoff.core.common.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
import java.util.function.Supplier;


@Slf4j(topic = "[OptionalUtil]")
public abstract class OptionalUtil {

    /**
     * Wrapper to execute a possibly throwable-action and wrap it within an {@link Optional}...
     *
     * @param action   action to execute.
     * @param <TValue> excepted return value type.
     * @return If exception was thrown, then a NUll, Else the value is wrapped within an optional.
     */
    public static <TValue> Optional<TValue> tryOptional(Supplier<? extends TValue> action) {

        try {
            return Optional.ofNullable(action.get());
        } catch (Exception e) {
            return Optional.empty();
        }
    }

}
