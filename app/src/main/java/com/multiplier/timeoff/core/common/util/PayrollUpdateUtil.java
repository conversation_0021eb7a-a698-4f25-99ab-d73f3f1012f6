//package com.multiplier.timeoff.core.common.util;
//
//
//import java.time.Instant;
//import java.time.LocalDateTime;
//import java.time.ZoneOffset;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//import java.util.Set;
//import java.util.stream.Collectors;
//
//public class PayrollUpdateUtil {
//    /**
//     * initialize batch list with empty `updateItems`: List[{createdOn: someInstant1, updateItems: []}, {createdOn: someInstant2, updateItems: []}]
//     */
//    public static List<PayrollUpdateBatch> initPayrollUpdateBatchesFromInstants(Set<Instant> batchCreatedOns) {
//        return batchCreatedOns.stream()
//                .map(batchCreatedOn -> PayrollUpdateBatch.newBuilder()
//                        .createdOn(LocalDateTime.ofInstant(batchCreatedOn, ZoneOffset.UTC))
//                        .updateItems(new ArrayList<>())
//                        .build())
//                .collect(Collectors.toList());
//    }
//
//    /**
//     * initialize batch list with empty `updateItems`: List[{createdOn: someInstant1, updateItems: []}, {createdOn: someInstant2, updateItems: []}]
//     */
//    public static List<PayrollUpdateBatch> initPayrollUpdateBatches(Set<LocalDateTime> batchCreatedOns) {
//        return batchCreatedOns.stream()
//                .map(batchCreatedOn -> PayrollUpdateBatch.newBuilder()
//                        .createdOn(batchCreatedOn)
//                        .updateItems(new ArrayList<>())
//                        .build())
//                .collect(Collectors.toList());
//    }
//
//    /**
//     * @return filter with lists populated with an empty list, to avoid NPE
//     */
//    public static PayrollUpdateBatchCreateFilter standardizeFilter(PayrollUpdateBatchCreateFilter filter) {
//        if (filter  == null) {
//            filter = new PayrollUpdateBatchCreateFilter();
//        }
//        if (filter.getExpenseIds() == null) {
//            filter.setExpenseIds(Collections.emptyList());
//        }
//        if (filter.getPaySupplementIds() == null) {
//            filter.setPaySupplementIds(Collections.emptyList());
//        }
//        if (filter.getContractIds() == null) {
//            filter.setContractIds(Collections.emptyList());
//        }
//        return filter;
//    }
//}
