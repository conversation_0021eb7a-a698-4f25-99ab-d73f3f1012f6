package com.multiplier.timeoff.core.common.util;


import com.multiplier.timeoff.types.CountryCode;
import com.multiplier.timeoff.types.CurrencyCode;

import java.util.Currency;

public abstract class CurrencyUtil {

    public static String getDefault(String countryIsoAlpha3Code) {

        return Currency.getInstance(LocaleUtil.createDefault(countryIsoAlpha3Code, null)).getCurrencyCode();
    }

    public static CurrencyCode getDefault(CountryCode country) {

        return CurrencyCode.valueOf(getDefault(country.name()));
    }
}


