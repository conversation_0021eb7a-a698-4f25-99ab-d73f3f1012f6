package com.multiplier.timeoff.core.util;

import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.company.schema.holiday.LegalEntityHoliday;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.country.schema.holiday.HolidayOuterClass;
import com.multiplier.timeoff.core.common.util.NumberUtil;
import com.multiplier.timeoff.repository.model.DefinitionEntity;
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import com.multiplier.timeoff.service.HrMemberTimeOffDefinitionKt;
import com.multiplier.timeoff.types.EntityType;
import com.multiplier.timeoff.types.TimeOffSession;
import com.multiplier.timeoff.types.TimeOffStatus;
import com.multiplier.timeoff.types.TimeOffUnit;
import com.multiplier.timeoff.service.exception.ValidationException;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Month;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j(topic = "TimeOffUtil")
public class TimeOffUtil {

    static final Double NEAREST_ROUNDING = 0.5;

    public static final List<ContractOuterClass.ContractType> CONTRACT_TYPES_ELIGIBLE_FOR_TIMEOFFS = List.of(
            ContractOuterClass.ContractType.EMPLOYEE,
            ContractOuterClass.ContractType.HR_MEMBER);

    public static final EnumSet<DayOfWeek> WEEKENDS = EnumSet.of(DayOfWeek.SATURDAY, DayOfWeek.SUNDAY);

    public static final int CONTRACT_QUERY_CHUNK_SIZE = 10000;

    public static final Integer MAX_FUTURE_LEAVES_ALLOWED = 5;

    public static final String EOR_VIRTUAL_ENTITY_NAME = "All Multiplier Entities";
    public static final Long EOR_VIRTUAL_ENTITY_ID = -1L;

    public static final List<TimeOffStatus> PENDING_STATUSES = List.of(TimeOffStatus.APPROVAL_IN_PROGRESS);

    // User experience constants
    public static final String MEMBER_EXP = "member";
    public static final String COMPANY_EXP = "company";

    /**
     * Rounds the timeoff values to the nearest 0.5.
     *
     * @param source value to round off
     * @return rounded value
     */
    public static Double round(Double source) {

        return NumberUtil.roundToNearest(source, NEAREST_ROUNDING);
    }


    /**
     * @param startInclusive start date, counted
     * @param endExclusive   end date, not counted
     * @return number of week days (i.e. working days i.e. weekends excluded) from startInclusive to endExclusive (e.g. Mon to Fri = 4 days, Mon to next-Mon = 5 days)
     */
    public static long countWeekDays(final LocalDate startInclusive, final LocalDate endExclusive) {

        final long days = ChronoUnit.DAYS.between(startInclusive, endExclusive);
        long result = days - 2 * (days / 7);

        if (days % 7 != 0) { // deal with the rest days
            if (startInclusive.getDayOfWeek() == DayOfWeek.SUNDAY) {
                result -= 1;
            } else if (endExclusive.getDayOfWeek() == DayOfWeek.SUNDAY) { // i.e. end date (inclusive) is Sat
                result -= 1;
            } else if (startInclusive.getDayOfWeek().getValue() > endExclusive.getDayOfWeek().getValue()) { // additional weekend e.g. Fri -> Tue
                result -= 2;
            }
        }

        return result;
    }

    public static boolean isLastDayOfMonth(LocalDate date) {
        return date.with(TemporalAdjusters.lastDayOfMonth()).equals(date);
    }

    public static DefinitionEntity getDefinitionForEntitlement(TimeoffEntitlementDBO entitlementDBO) {
        return getDefinition(entitlementDBO, entitlementDBO.definition());
    }

    public static DefinitionEntity getDefinitionForEntitlement(TimeoffEntitlementDBO entitlementDBO, Map<Long, DefinitionEntity> idToDefinitionMap) {
        return getDefinition(entitlementDBO, idToDefinitionMap.get(entitlementDBO.definitionId()));

    }

    private static DefinitionEntity getDefinition(TimeoffEntitlementDBO entitlementDBO, @Nullable DefinitionEntity definition) {
        if (definition != null) {
            return definition;
        }
        val typeId = entitlementDBO.typeId() != null ? entitlementDBO.typeId() : entitlementDBO.type().id();

        if (typeId.equals(HrMemberTimeOffDefinitionKt.getUnpaidLeaveDefinition().getTypeId())) {
            return HrMemberTimeOffDefinitionKt.getUnpaidLeaveDefinition().getDefinition();
        } else if (typeId.equals(HrMemberTimeOffDefinitionKt.getAnnualLeaveDefinition().getTypeId())) {
            return HrMemberTimeOffDefinitionKt.getAnnualLeaveDefinition().getDefinition();
        } else {
            log.warn("[getDefinition] definition not found for entitlement id : {}, type id : {}, contract id : {}", entitlementDBO.id(), entitlementDBO.typeId(), entitlementDBO.contractId());
            return null;
        }
    }

    public static @NotNull Month getFinancialEndMonth(CompanyOuterClass.Company company) {
        int companyFinancialStartMonth = company.hasFinancialYear() ? company.getFinancialYear().getValue() : -1;
        return companyFinancialStartMonth < 0 ? Month.DECEMBER : Month.of(companyFinancialStartMonth).minus(1);
    }


    public static Double getDays(Double value, TimeOffUnit unit) {

        return switch (unit) {
            case DAYS -> value;
            case MONTHS -> (value * 31);
            case WEEKS -> (value * 7);
            case YEARS -> value * 365;
        };
    }

    public static String toContractIdTypeIDKey(Long contractId, Long typeId) {
        return contractId + "_" + typeId;
    }

    public static boolean isEntityHolidayAPublicHoliday(LocalDate dateToCheck, List<LegalEntityHoliday.Holiday> holidays) {
        if (CollectionUtils.isEmpty(holidays)) {
            return false;
        }
        return holidays.stream().anyMatch(holiday -> dateToCheck.getDayOfMonth() == holiday.getDate() && dateToCheck.getMonthValue() == holiday.getMonth());
    }

    public static boolean isCountryHolidayAPublicHoliday(LocalDate dateToCheck, List<HolidayOuterClass.Holiday> holidays) {
        if (CollectionUtils.isEmpty(holidays)) {
            return false;
        }
        return holidays.stream().anyMatch(holiday -> dateToCheck.getDayOfMonth() == holiday.getDate() && dateToCheck.getMonthValue() == holiday.getMonth());
    }

    public static  <T> List<T> flatMap(Collection<List<T>> collection) {
        return collection.stream().flatMap(Collection::stream)
                .toList();
    }

    public static <T> Set<T> toSet(Collection<T> collection) {
        return new HashSet<>(collection);
    }

    @SafeVarargs
    public static <T> List<T> mergeLists(List<T>... lists) {
        Set<T> unionSet = new LinkedHashSet<>();
        for (List<T> list : lists) {
            unionSet.addAll(list);
        }
        return new ArrayList<>(unionSet);
    }

    public static String generateExternalId(Long contractId, String externalTimeOffId) {
        return contractId + "-" + externalTimeOffId;
    }

    public static String companyDefinitionsByEntityKey(Long companyId, Long entityId, EntityType entityType) {
        return companyId + "_" + entityId +  "_" + entityType;
    }

    public static EntityType getEntityTypeForContract(ContractOuterClass.ContractType contractType) {
        if (contractType == ContractOuterClass.ContractType.EMPLOYEE) {
            return EntityType.EOR_PARTNER_ENTITY;
        }
        return EntityType.COMPANY_ENTITY;
    }

    public static double zeroIfNull(Double value) {
        return value == null ? 0.0 : value;
    }

    public static String getLabelToTimeOffTypeMapKey(String label) {
        return label.trim().toLowerCase();
    }

    public static List<Set<Long>> splitList(List<Long> inputList) {
        Set<Long> uniqueElements = new HashSet<>(inputList);
        List<Long> sanitizedList = new ArrayList<>(uniqueElements);

        int numChunks = (int) Math.ceil((double) sanitizedList.size() / CONTRACT_QUERY_CHUNK_SIZE); // Calculate number of chunks
        return IntStream.range(0, numChunks)
                .mapToObj(i -> {
                    var startIndex = i * CONTRACT_QUERY_CHUNK_SIZE;
                    var endIndex = Math.min((i + 1) * CONTRACT_QUERY_CHUNK_SIZE, sanitizedList.size());
                    return sanitizedList.subList(startIndex, endIndex);
                })
                .map(HashSet::new)
                .collect(Collectors.toList());
    }

    public static boolean falseIfNull(Boolean value) {
        return value != null && value;
    }

    public static List<LocalDate> convertHolidaysToLocalDates(List<LegalEntityHoliday.Holiday> holidays) {
        return holidays.stream()
                .map(holiday -> LocalDate.of(holiday.getYear(), holiday.getMonth(), holiday.getDate()))
                .toList();
    }

    public static double calculateNoOfTimeoffDays(LocalDate startDate, LocalDate endDate, TimeOffSession startSession, TimeOffSession endSession, List<LocalDate> holidayDates) {
        if (endDate == null || endDate.isBefore(startDate)) {
            log.info("[calculateNoOfTimeoffDays] end date is invalid (start date : {}, end date : {})", startDate, endDate);
            return 0.0;
        }

        double businessDays = countWeekDays(startDate, endDate.plusDays(1));
        double noOfHolidaysInWeekdays = startDate.datesUntil(endDate.plusDays(1))
                .filter(date -> holidayDates.contains(date) && isWeekDay(date))
                .count();

        double noOfDays = businessDays - noOfHolidaysInWeekdays;
        double adjustmentForHalfDaySession = handleHalfDaySession(startDate, endDate, startSession, endSession);
        noOfDays += adjustmentForHalfDaySession;

        return Math.max(0.0, noOfDays);
    }

    public static double handleHalfDaySession(LocalDate startDate, LocalDate endDate, TimeOffSession startSession, TimeOffSession endSession) {
        double adjustment = 0.0;
        if (startDate.equals(endDate)) {
            if (startSession == endSession && startSession != TimeOffSession.FULL_DAY) {
                adjustment -= 0.5;
            }
        } else {
            if (isWeekDay(startDate) && startSession == TimeOffSession.AFTERNOON) {
                adjustment -= 0.5;
            }
            if (isWeekDay(endDate) && endSession == TimeOffSession.MORNING) {
                adjustment -= 0.5;
            }
        }
        return adjustment;
    }

    public static boolean isWeekDay(LocalDate date) {
        return date.getDayOfWeek().getValue() < 6 ;
    }

    public static boolean isPayrollAdmin(CompanyOuterClass.CompanyUser companyUser) {
        return companyUser.getRolesList().contains(CompanyOuterClass.CompanyUserRole.PAYROLL_ADMIN);
    }

    public static boolean isHrAdmin(CompanyOuterClass.CompanyUser companyUser) {
        return companyUser.getRolesList().contains(CompanyOuterClass.CompanyUserRole.HR_ADMIN);
    }

    /**
     * Returns 0.0 if the value is negative, otherwise returns the original value.
     * This is a utility method to ensure non-negative values in calculations.
     *
     * @param value the value to check
     * @return 0.0 if value is negative, otherwise the original value
     */
    public static Double zeroIfNegative(Double value) {
        return value < 0 ? 0.0 : value;
    }

    /**
     * Calculates the last valid end date based on start date, duration value and unit.
     * This method adds the specified duration to the start date and returns the last valid date
     * (i.e., the calculated date minus 1 day).
     *
     * @param startDate the start date
     * @param value the duration value
     * @param unit the duration unit (DAYS, WEEKS, MONTHS, YEARS)
     * @return the last valid end date, or null if parameters are invalid
     */
    public static LocalDate getLastValidEndDate(LocalDate startDate, Double value, TimeOffUnit unit) {
        if (value == null || unit == null) {
            return null;
        }

        // If the value is not a whole number, we'll need to convert the unit to DAYS.
        if (value % 1 != 0) {
            // value is not a whole number
            value = round(getDays(value, unit));
            unit = TimeOffUnit.DAYS;
        }

        long longValue = Math.round(value);
        LocalDate validEndDate = startDate;
        switch (unit) {
            case DAYS:
                validEndDate = validEndDate.plusDays(longValue);
                break;
            case WEEKS:
                validEndDate = validEndDate.plusWeeks(longValue);
                break;
            case MONTHS:
                validEndDate = validEndDate.plusMonths(longValue);
                break;
            case YEARS:
                validEndDate = validEndDate.plusYears(longValue);
                break;
            default:
                throw new ValidationException("[getLastValidEndDate] Unit not supported: " + unit);
        }

        return validEndDate.minusDays(1);
    }
}
