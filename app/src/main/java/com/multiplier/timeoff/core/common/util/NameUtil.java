package com.multiplier.timeoff.core.common.util;

public class NameUtil {
    public static String getFullNameFrom(String firstName, String lastName) {
        if (firstName == null && lastName == null) {
            return null;
        }
        firstName = firstName == null ? "" : firstName;
        lastName = lastName == null ? "" : lastName;

        return (firstName + " " + lastName).trim();
    }
}
