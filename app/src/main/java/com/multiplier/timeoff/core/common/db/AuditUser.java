package com.multiplier.timeoff.core.common.db;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * We can consider migrating all AuditorAware<Long> related code to AuditorAware<AuditUser>, which leads to these changes on every entity:<br>
 * - deprecate `Long createdBy/updatedBy` and use `AuditUser createdByInfo/updatedByInfo` (annotate @CreatedBy/@LastModifiedBy on these fields instead)<br>
 * - corresponding DB changes: new columns created_by_info/updated_by_info (jsonb)<br>
 * - change all `createdBy/updatedBy` code to the new fields<br>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AuditUser implements Serializable {
    private Long userId;
    private String experience;
}
