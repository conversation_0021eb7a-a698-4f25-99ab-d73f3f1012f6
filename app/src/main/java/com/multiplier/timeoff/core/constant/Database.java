package com.multiplier.timeoff.core.constant;

public interface Database {

    String SCHEMA = "timeoff";

    interface Table {

        interface Type {

            String TABLE_NAME = "timeoff_type";
        }

        interface TimeOff {

            String TABLE_NAME = "timeoff";

            interface Column {

                String TYPE_ID = "type_id";
            }

            interface Constraint {

                String FK_TYPE_IN_TIMEOFF = "fk_type_in_timeoff";
            }
        }

        interface Entitlement {

            String TABLE_NAME = "entitlement";

            interface Column {

                String TYPE_ID = "type_id";

                String DEFINITION_ID = "definition_id";
                String CONTRACT_ID = "contract_id";
                String VALUE = "value";
                String KEY = "key";
            }

            interface Constraint {

                String FK_TYPE_IN_ENTITLEMENT = "fk_type_in_entitlement";
                String FK_DEFINITION_IN_ENTITLEMENT = "fk_definition_in_entitlement";
                String UQ_CONTRACT_ENTITLEMENT = "uq_contract_entitlement";
            }
        }

        interface EntitlementChangeRecord {
            String TABLE_NAME = "entitlement_change_record";

            interface Column {

                String ID = "id";
                String TYPE_ID = "type_id";
                String REF_ID = "ref_id";
            }
        }

        interface Summary {
            String TABLE_NAME = "timeoff_summary";

            interface Column {

                String TYPE_ID = "type_id";
                String TOTAL_ENTITLED_COUNT = "entitled_count";
            }

            interface Constraint {

                String FK_TYPE_IN_SUMMARY = "fk_type_in_summary";
            }
        }

        interface Definition {

            String TABLE_NAME = "definition";

            interface Column {

                String COUNTRY_CODE = "country_code";
                String STATE_CODE = "state_code";
                String STATUS = "status";
                String NAME = "name";
            }

        }

        interface CountryDefinition {

            String TABLE_NAME = "country_definition";

            interface Column {

                String TYPE_ID = "type_id";
                String DEFINITION_ID = "definition_id";
            }

            interface Constraint {

                String FK_TYPE_IN_COUNTRY_DEFINITION = "fk_type_in_country_definition";
                String FK_DEFINITION_IN_COUNTRY_DEFINITION = "fk_definition_in_country_definition";
                String UQ_DEFINITION = "uq_country_definition";
            }
        }

        interface CompanyDefinition {

            String TABLE_NAME = "company_definition";

            interface Column {

                String TYPE_ID = "type_id";
                String COMPANY_ID = "company_id";
                String DEFINITION_ID = "definition_id";
            }

            interface Constraint {

                String FK_TYPE_IN_COMPANY_DEFINITION = "fk_type_in_company_definition";
                String FK_DEFINITION_IN_COMPANY_DEFINITION = "fk_definition_in_company_definition";
                String UQ_DEFINITION = "uq_company_definition";
            }
        }

        interface TimeOffDefinitionRule {

            String TABLE_NAME = "timeoff_definition_rule";

            interface Column {

                String COMPANY_DEFINITION_ID = "company_definition_id";
            }

            interface Constraint {
                String FK_COMPANY_DEFINITION_IN_DEFINITION_RULE = "fk_company_definition_in_timeoff_definition_rule";
            }
        }

        interface TimeOffEntry {
            String TABLE_NAME = "timeoff_entry";

            interface Column {

                String TIME_OFF_ID = "timeoff_id";

            }

            interface Constraint {

                String FK_TIMEOFF_ID_IN_TIMEOFF_ENTRY = "fk_timeoff_id_in_timeoff_entry";

            }
        }

        interface TimeOffUsage {
            String TABLE_NAME = "timeoff_usage";

            interface Column {
                String TIMEOFF_ID = "timeoff_id";
            }

            interface Constraint {
                String FK_TIMEOFF_IN_TIMEOFF_USAGE = "fk_timeoff_in_timeoff_usage";
            }
        }
    }
}
