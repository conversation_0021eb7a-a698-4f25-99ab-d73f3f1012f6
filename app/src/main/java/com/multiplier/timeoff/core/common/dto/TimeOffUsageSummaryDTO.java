package com.multiplier.timeoff.core.common.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TimeOffUsageSummaryDTO {
    private double currentSummaryBalance; // balance after applying the timeoff
    private double nextSummaryBalance; // balance after applying the timeoff
    private double usedFromAllocated;
    private double usedFromCarryForward;
    private double usedFromLapsable;
    private double usedFromNextCycleCarryForward;
    private double usedFromNextCycleAllocated;
}
