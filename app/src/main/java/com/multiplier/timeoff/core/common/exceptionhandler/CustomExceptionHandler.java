package com.multiplier.timeoff.core.common.exceptionhandler;

import com.multiplier.timeoff.exception.TimeoffValidationException;
import com.multiplier.timeoff.grpc.MPLException;
import com.netflix.graphql.dgs.exceptions.DefaultDataFetcherExceptionHandler;
import com.netflix.graphql.types.errors.TypedGraphQLError;
import graphql.GraphQLError;
import graphql.execution.DataFetcherExceptionHandler;
import graphql.execution.DataFetcherExceptionHandlerParameters;
import graphql.execution.DataFetcherExceptionHandlerResult;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.multiplier.timeoff.grpc.GrpcExtensionsKt.toMPLException;

/**
 * Copied from core-service. Non-timeoff exceptions are removed
 */
@Slf4j
@Component
public class CustomExceptionHandler implements DataFetcherExceptionHandler {
    private final DefaultDataFetcherExceptionHandler defaultHandler = new DefaultDataFetcherExceptionHandler();

    @Override
    public CompletableFuture<DataFetcherExceptionHandlerResult> handleException(final DataFetcherExceptionHandlerParameters handlerParameters) {
        CompletableFuture<DataFetcherExceptionHandlerResult> result = new CompletableFuture<>();
        try {

            if (handlerParameters.getException() instanceof MPLException ex) {
                result.complete(getExceptionHandlerResult(ex.getMplError(), ex));
                return result;
            }

            if (handlerParameters.getException() instanceof StatusRuntimeException) {
                RuntimeException ex = toMPLException((StatusRuntimeException) handlerParameters.getException());
                if (ex instanceof MPLException e) {
                    result.complete(getExceptionHandlerResult(e.getMplError(), e));
                    return result;
                }
            }

            if (handlerParameters.getException() instanceof TimeoffValidationException ex) {
                result.complete(getExceptionHandlerResult(ex.getMplError(), ex));
                return result;
            }

            // Other custom exceptions....

            // AccessDeniedException
            if (handlerParameters.getException() instanceof AccessDeniedException) {
                GraphQLError graphqlError = TypedGraphQLError.newPermissionDeniedBuilder()
                        .message(handlerParameters.getException().getMessage())
                        .build();

                result.complete(DataFetcherExceptionHandlerResult.newResult()
                        .error(graphqlError)
                        .build());
                return result;
            }

            // Default handler.
            DataFetcherExceptionHandlerResult defaultResult = defaultHandler.handleException(handlerParameters).get();
            result.complete(defaultResult);

        } catch (Exception e) {
            // In case any unexpected exceptions occur, complete exceptionally.
            result.completeExceptionally(e);
        }

        return result;










    }

    private DataFetcherExceptionHandlerResult getExceptionHandlerResult(MPLError mplError, Throwable ex) {
        // Set additional error details.
        Map<String, Object> debugInfo = new HashMap<>();
        debugInfo.put("exceptionType", ex.getClass().getSimpleName());
        if (mplError != null) {
            debugInfo.put("errorCode", mplError.getErrorType().getCode());
            debugInfo.put("errorParams", mplError.getParams() == null ? Map.of() : mplError.getParams());
        }

        // Create new graphql error.
        GraphQLError graphqlError = TypedGraphQLError.newInternalErrorBuilder()
                .message(mplError != null ? mplError.getMessage() : ex.getMessage())
                .debugInfo(debugInfo)
                .build();

        // Log the exception.
        log.error("", ex);

        // Return the result.
        return DataFetcherExceptionHandlerResult.newResult()
                .error(graphqlError)
                .build();
    }
}
