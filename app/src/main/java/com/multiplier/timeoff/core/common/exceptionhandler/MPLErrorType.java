package com.multiplier.timeoff.core.common.exceptionhandler;

import java.util.Arrays;

/**
 * From 0001 to 0024 are copied from core-service. New timeoff errors will be from 0500<br>
 * Later will be extracted to a common place, so keeping non-timeoff stuff for less comparing and merging effort
 */
public enum MPLErrorType {

    // -------------------------------
    // Error codes and messages
    // -------------------------------
    MPL0001_MEMBER_EMAIL_EXISTS("MPL0001", "Member email {{email}} already exists"),
    MPL0002_MEMBER_ID_EXISTS("MPL0002", "Member id {{memberId}} already exists"),
    MPL0003_MEMBER_NOT_FOUND_FOR_ID("MPL0003", "Member not found for member id {{memberId}}"),
    MPL0004_MEMBER_NOT_FOUND_FOR_USER_ID("MPL0004", "Member not found for user id {{userId}}"),
    MPL0005_DOB_NOT_FOUND("MPL0005", "Member date of birth not found"),
    MPL0006_PRIMARY_EMAIL_NOT_EXIST("MPL0006", "Primary email not exist for id {{memberId}}")
    ,MPL0007_CAN_ONLY_APPROVE_REJECT_EXPENSES_IN_APPROVAL_IN_PROGRESS("MPL0007", "Can only approve/reject expenses with status = APPROVAL_IN_PROGRESS, while this expense (id={{expenseId}}) is {{expenseStatus}}")
    ,MPL0008_CAN_ONLY_APPROVE_REJECT_TIMEOFFS_IN_APPROVAL_IN_PROGRESS("MPL0008", "Can only approve/reject timeoffs with status = APPROVAL_IN_PROGRESS, while this timeoff (id={{timeOffId}}) is {{timeOffStatus}}")
    ,MPL0009_CAN_ONLY_APPROVE_REJECT_MEMBER_PAYABLES_IN_APPROVAL_IN_PROGRESS("MPL0009", "Can only approve/reject memberPayables with status = APPROVAL_IN_PROGRESS, while this memberPayable (id={{memberPayableId}}) is {{memberPayableStatus}}")
    ,MPL0010_CAN_ONLY_APPROVE_REJECT_LEGAL_DOCS_IN_SUBMITTED("MPL0010", "Can only approve/reject legal documents with status = SUBMITTED, while this legal document (id={{legalDocId}}) is {{legalDocStatus}}")
    ,MPL0011_CONTRACT_ALREADY_ASSIGNED_TO_OTHER_MANAGERS("MPL0011", "The Manager (company_user.id = {{companyUserId}}) cannot be a {{approveCategory}} approver of contract id = {{contractId}} because it has already been assigned to following manager(s) (company_user.id): {{otherApproverCUIds}}")
    ,MPL0012_CONTRACT_AND_MANAGER_ARE_SAME_USER("MPL0012", "The Manager (company_user.id = {{companyUserId}}) cannot be a {{approveCategory}} approver of contract id = {{contractId}} because they are the same user")
    ,MPL0013_ENTITLEMENT_VALUE_OUT_OF_MIN_MAX_RANGE("MPL0013", "Entitlement value must be in [min, max]=[{{min}}, {{max}}]. ContractID={{contractId}}. Input={{input}}")
    ,MPL0014_CAN_ONLY_APPROVE_REJECT_MEMBER_DATA_REQUEST_IN_SUBMITTED("MPL0014", "Can only approve/reject member data request with status = SUBMITTED, while this change request (id={{changeRequestId}}) is {{changeRequestStatus}}")
    ,MPL0015_MEMBER_DATA_CHANGE_REQUEST_NOT_FOUND_FOR_ID("MPL0015", "No member data change request found for id={{changeRequestId}}")
    ,MPL0016_CANNOT_FORWARD_MEMBER_DATA_CHANGE_REQUEST("MPL0016", "Cannot forward a Member data change request. Item id={{itemId}}")
    , MPL0017_APPROVAL_ITEM_NOT_FOUND_FOR_ID_AND_CATEGORY("MPL0017", "Cannot find the approval item for itemID={{itemId}} and catogory={{category}}")

    ,MPL0018_CHANGE_REQUEST_CATEGORY_EXISTS("MPL0018", "Change request for {{category}} already exists")
    ,MPL0019_MEMBER_CHANGE_REQUEST_NOT_EXIST("MPL0019", "Change request id {{id}} is not exists")
    ,MPL0020_MEMBER_CAN_NOT_APPROVE_NON_SUBMITTED_CHANGE_REQUEST("MPL0020", "Can not approve non SUBMITTED change request id {{id}}")
    ,MPL0021_MEMBER_CAN_NOT_REJECT_NON_SUBMITTED_CHANGE_REQUEST("MPL0021", "Can not reject non SUBMITTED change request id {{id}}")
    ,MPL0022_CAN_ONLY_APPROVE_REJECT_MEMBER_DATA_REQUEST_IF_OPS_USER("MPL0022", "Trying to perform Status update={{changeRequestStatus}} on an memberDataChangeRequestId={{changeRequestId}}, but the operationsUserId is not present")
    ,MPL0023_CAN_ONLY_APPROVE_REJECT_ITEM_FOR_IN_PROGRESS_ITEM("MPL0023", "Cannot approve/reject this item (id={{itemId}}, category={{category}}) because the status is already {{itemStatus}}")
    ,MPL0024_APPROVER_NOT_FOUND_FOR_ITEM_ID("MPL0024", "Approver for id={{appoverId}} is not found for itemId={{itemId}} and category={{category}}")


    ,MPL9999_UNCLASSIFIED_ERROR("MPL9999", "An unclassified error has occurred.")

    // insert next enums here for Timeoff errors with index starts from 500, e.g.:
    // ,MPL0500_SOMETHING("MPL0500", "Something went wrong here")
    // The purpose is to reserve "< 500" slots for existing and upcoming codes on core-service,
    // which should be safe enough until we introduce a common lib for MPLError framework

    , MPL0500_INSUFFICIENT_BALANCE("MPL0500", "Oops, you do not have sufficient leave balance to apply time-off for " +
        "the selected dates. One possible reason could be that your carried-over leaves have expired. Please try " +
        "again in sometime, and if it persists, please reach out to us through our in-app chat."),
    MPL0501_INVALID_TIMEOFF_TYPE("MPL0501", "Invalid timeoff type"),
    MPL0502_INVALID_TIMEOFF_START_DATE_CONTRACT_START_DATE("MPL0502", "Timeoff start date is before contract start " +
        "date"),
    MPL0503_INVALID_TIMEOFF_START_DATE_CONTRACT_END_DATE("MPL0503", "Timeoff start date is after contract end " +
        "date"),
    MPL0504_INVALID_TIMEOFF_END_DATE_CONTRACT_START_DATE("MPL0504", "Timeoff end date is before contract start " +
        "date"),
    MPL0505_INVALID_TIMEOFF_END_DATE_CONTRACT_END_DATE("MPL0505", "Timeoff end date is after contract end " +
        "date"),
    MPL0506_INVALID_TIMEOFF_START_DATE_END_DATE("MPL0506", "Timeoff start date is after end date"),
    MPL0507_INVALID_TIMEOFF_START_DATE("MPL0507", "Timeoff start date is invalid"),
    MPL0508_INVALID_TIMEOFF_END_DATE("MPL0508", "Timeoff end date is invalid"),
    MPL0509_INVALID_TIMEOFF_START_DATE_HOLIDAY("MPL0509", "Timeoff start date is a holiday"),
    MPL0510_INVALID_TIMEOFF_END_DATE_HOLIDAY("MPL0510", "Timeoff end date is a holiday"),
    MPL0511_INVALID_TIMEOFF_START_DATE_REST_DAY("MPL0511", "Timeoff start date is a rest day"),
    MPL0512_INVALID_TIMEOFF_END_DATE_REST_DAY("MPL0512", "Timeoff end date is a rest day"),
    MPL0513_INVALID_TIMEOFF_START_DATE_SUMMARY_PERIOD("MPL0513", "Timeoff start date is not within summary period"),
    MPL0514_INVALID_TIMEOFF_END_DATE_SUMMARY_PERIOD("MPL0514", "Timeoff end date is not within summary period"),
    MPL0515_INVALID_TIMEOFF_CONTRACT_NOT_STARTED("MPL0515", "Cannot submit because contract has not started"),
    MPL0516_INVALID_TIMEOFF_OVERLAPPING("MPL0516", "Timeoff is overlapping with existing timeoff"),
    MPL0517_INVALID_TIMEOFF_CONTRACT_STATUS("MPL0517", "Cannot submit because contract is not in valid status"),
    MPL0518_INVALID_TIMEOFF_START_DATE_SESSION("MPL0518", "Timeoff start date session is invalid"),
    MPL0519_INVALID_TIMEOFF_START_SESSION("MPL0519", "Timeoff start session is invalid"),
    MPL0520_INVALID_TIMEOFF_END_SESSION("MPL0520", "Timeoff end session is invalid"),
    MPL0521_INVALID_TIMEOFF_START_DATE_REST_DAY("MPL0521", "Timeoff start date is a rest day"),
    MPL0522_INVALID_TIMEOFF_END_DATE_REST_DAY("MPL0522", "Timeoff end date is a rest day"),
    MPL0523_INVALID_PAST_TIMEOFF("MPL523", "Cannot apply for time-off with a date before {{cutoff-date}}. " +
            "Please contact your HR administrator for assistance with backdated time-off requests."),
    MPL0524_INVALID_FUTURE_TIMEOFF("MPL524", "Cannot apply for time-off with a date after {{cutoff-date}}. since future leaves are disabled"),
    MPL0525_INVALID_TIMEOFF_STATUS_TO_SUBMIT("MPL0525", "Can only submit DRAFT timeoffs, while this timeoff (id={{timeOffId}}) is {{timeOffStatus}}"),
    MPL0525_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_MEMBER("MPL0526", "Cannot access the timeoff id = {{timeOffId}}, as it was created in Team/Company view OR not created by you"),
    MPL0526_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_COMPANY("MPL0527", "Cannot access the timeoff id = {{timeOffId}}, as it was created in Personal/Member view OR not created by you"),
    MPL0527_UNAUTHORIZED_EXPERIENCE_TO_SUBMIT_TIMEOFFS("MPL0528", "{{experience}} is not authorized to submit experience time-offs."),

    ;






    // -------------------------------
    // Helper methods
    // -------------------------------
    private final String code;
    private final String message;

    MPLErrorType(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public static MPLErrorType getByCode(String code) {
        return Arrays.stream(MPLErrorType.values())
                .filter(type -> type.getCode().equals(code))
                .findFirst().orElse(MPLErrorType.MPL9999_UNCLASSIFIED_ERROR);
    }
}
