package com.multiplier.timeoff.shedlock;

import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.sql.DataSource;

import static com.multiplier.timeoff.core.constant.Constant.SHEDLOCK_TIME_CONFIGURATION.defaultLockAtLeastFor;
import static com.multiplier.timeoff.core.constant.Constant.SHEDLOCK_TIME_CONFIGURATION.defaultLockAtMostFor;

@Configuration
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = defaultLockAtMostFor,
        defaultLockAtLeastFor = defaultLockAtLeastFor) // lock for at most for 40 minutes by default; we can override at individual task
public class ShedlockConfiguration {

    @Bean
    public LockProvider lockProvider(DataSource dataSource) {


        return new JdbcTemplateLockProvider(
                JdbcTemplateLockProvider.Configuration.builder()
                        .withJdbcTemplate(new JdbcTemplate(dataSource))
                        .withTableName("timeoff.shedlock")
                        .build()
        );
    }
}

