package com.multiplier.timeoff.validation;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.repository.model.AllocationConfigEntity;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.types.AllocationBasis;
import com.multiplier.timeoff.types.TimeOffUnit;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Month;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;

import static com.multiplier.timeoff.ProtobufExtensionsKt.toLocalDate;

@Service
@Slf4j
public class TimeoffDefinitionAllocator {

    public void allocate(TimeoffDefinitionConfigRequest request) {
        if (request.isAllocation()) {
            allocateTimeoff(request);
        } else {
            reAllocateTimeoff(request);
        }
    }

    public void fixTimeoffAllocation(TimeoffDefinitionConfigRequest request) {
        val definition = request.definitionEntity();
        AllocationConfigEntity configuration = definition.getConfigurations().getAllocationConfig();
        log.info("[TimeoffDefinitionAllocation] Fix timeoff allocation for basis={}, prorated={}, description={}", configuration.getBasis(), configuration.getProrated(), definition.getDescription());
        Double totalEntitledValue = request.timeoffEntitlementDBO().value();
        TimeOffUnit entitledUnit = request.timeoffEntitlementDBO().unit();
        Double entitledValueInDays = getEntitledValueInDays(totalEntitledValue, entitledUnit);
        request.timeoffSummaryDBO().allocatedCount(TimeOffUtil.round(entitledValueInDays));

        if (configuration.getProrated()) {
            int workingDays = (int) (ChronoUnit.DAYS.between(request.timeoffSummaryDBO().periodStart(), request.timeoffSummaryDBO().periodEnd()) + 1);
            Double perDayValue = getPerDay(totalEntitledValue, entitledUnit);
            Double proratedValueInDays = perDayValue * workingDays;
            if (entitledUnit == TimeOffUnit.DAYS && proratedValueInDays > totalEntitledValue) {
                // e.g. entitlement: 184 days, prorated = 185 days (probably happens in leap years) => should stop at 184 only
                proratedValueInDays = totalEntitledValue;
            }
            request.timeoffSummaryDBO().allocatedCount(Math.ceil(TimeOffUtil.round(proratedValueInDays)));
        }
    }

    /**
     * Method to reallocate time off if required
     * Reallocation happens only if the basis in allocation config is not once.
     * No prorata is applied, total entitled are allocated
     * id is made null to create a new entry
     * startPeriod would be previous end period
     * endPeriod would be startPeriod +
     * a. 1 day if basis is daily or financial year whichever is sooner
     * b. 1 month if basis is monthly or financial year whichever is sooner
     * c. 3 months if basis is quarterly or financial year whichever is sooner
     * d. 1 year if basis is yearly or financial year whichever is sooner
     * e. 1 week if basis is weekly or financial year whichever is sooner
     *
     * @param request request
     */
    private void reAllocateTimeoff(TimeoffDefinitionConfigRequest request) {
        AllocationConfigEntity configuration = request.definitionEntity().getConfigurations().getAllocationConfig();
        val basis = AllocationBasis.valueOf(configuration.getBasis());
        if (basis != AllocationBasis.ONCE) {
            log.info("[TimeoffDefinitionAllocation] Reallocation allowed for non-once basis");
            TimeoffSummaryDBO existingSummaryDBO = request.timeoffSummaryDBO();
            LocalDate currentPeriodEnd = existingSummaryDBO.periodEnd();
            if (LocalDate.now().isBefore(currentPeriodEnd) || LocalDate.now().equals(currentPeriodEnd)) {
                log.info("[TimeoffDefinitionAllocation] The Period has not ended yet. Period={}", currentPeriodEnd);
                return;
            }

            Double totalEntitledValue = request.timeoffEntitlementDBO().value();
            request.resetTimeoffSummaryDBO(
                    new TimeoffSummaryDBO()
                            .periodStart(currentPeriodEnd.plusDays(1))
                            .takenCount(0.00) // pending and taken count will be calculated by TimeoffSummaryService.updateSummary()
                            .typeId(existingSummaryDBO.typeId())
                            .contractId(existingSummaryDBO.contractId())
                            .pendingCount(0.0) // pending and taken count will be calculated by TimeoffSummaryService.updateSummary()
                            .timeoffType(existingSummaryDBO.timeoffType())
                            .allocatedCount(TimeOffUtil.round(getEntitledValueInDays(totalEntitledValue, request.timeoffEntitlementDBO().unit())))
                            .periodEnd(getPeriodEndForReAllocation(currentPeriodEnd, basis, request.companyFinancialYearEndMonth()))
            );
        }
    }

    /**
     * Method to allocate time off
     * Prorata is applied as follows -
     * a. we get entitled time in per day value - our atomic unit
     * b. we get the total working days till <confirm this>
     * c. we get the prorated value in units mentioned in entitlement - days, weeks, months, etc.
     * Period start is the startOn date
     * Period end is startOn date +
     * a. 1 day if basis is daily or financial year whichever is sooner
     * b. 1 month if basis is monthly or financial year whichever is sooner
     * c. 3 months if basis is quarterly or financial year whichever is sooner
     * d. 1 year if basis is yearly or financial year whichever is sooner
     * e. 1 week if basis is weekly or financial year whichever is sooner
     *
     * @param request request
     */
    private void allocateTimeoff(TimeoffDefinitionConfigRequest request) {
        AllocationConfigEntity configuration = request.definitionEntity().getConfigurations().getAllocationConfig();
        log.info("[TimeoffDefinitionAllocation] Allocation timeoff for basis={}, prorated={}, description={}", configuration.getBasis(), configuration.getProrated(), request.definitionEntity().getDescription());
        ContractOuterClass.Contract contract = request.contract();
        LocalDate startOn = toLocalDate(contract.getStartOn());
        Double totalEntitledValue = request.timeoffEntitlementDBO().value();
        TimeOffUnit entitledUnit = request.timeoffEntitlementDBO().unit();
        Double entitledValueInDays = getEntitledValueInDays(totalEntitledValue, entitledUnit);
        request.timeoffSummaryDBO().allocatedCount(TimeOffUtil.round(entitledValueInDays));

        Month financialEndMonth = request.companyFinancialYearEndMonth();

        Pair<LocalDate, LocalDate> periodStartEnd = getPeriodStartEndForAllocation(startOn, financialEndMonth);
        request.timeoffSummaryDBO().periodStart(periodStartEnd.getLeft());
        request.timeoffSummaryDBO().periodEnd(periodStartEnd.getRight());

        if (configuration.getProrated()) {
            int workingDays = getWorkingDays(startOn, financialEndMonth);
            Double perDayValue = getPerDay(totalEntitledValue, entitledUnit);
            Double proratedValueInDays = perDayValue * workingDays;
            if (entitledUnit == TimeOffUnit.DAYS && proratedValueInDays > totalEntitledValue) {
                // e.g. entitlement: 184 days, prorated = 185 days (probably happens in leap years) => should stop at 184 only
                proratedValueInDays = totalEntitledValue;
            }
            request.timeoffSummaryDBO().allocatedCount(Math.ceil(TimeOffUtil.round(proratedValueInDays)));
        }
    }


    // TODO: confirm all of these - handling leap year etc
    private Double getPerDay(Double value, TimeOffUnit unit) {
        switch (unit) {
            case DAYS:
                return value / 365;
            case MONTHS:
                return (value * 31) / 365;
            case WEEKS:
                return (value * 7) / 365;
            case YEARS:
                return value;
        }
        return value;
    }

    private Double getEntitledValueInDays(Double value, TimeOffUnit unit) {

        return TimeOffUtil.getDays(value, unit);
    }

    private int getWorkingDays(LocalDate contractStartDate, Month financialEndMonth) {
        Pair<LocalDate, LocalDate> periodStartEnd = getPeriodStartEndForAllocation(contractStartDate, financialEndMonth);
        return (int) ChronoUnit.DAYS.between(periodStartEnd.getLeft(), periodStartEnd.getRight()) + 1;
    }

    /**
     * assuming we're not going to use basis != ANNUALLY/ONCE soon. In fact, almost all companies use a period of 1 year.
     */
    private Pair<LocalDate, LocalDate> getPeriodStartEndForAllocation(LocalDate contractStartDate, Month financialEndMonth) {
        int currentYear = LocalDate.now().getYear();
        LocalDate currentFinancialEndDate = LocalDate.of(currentYear, financialEndMonth, 1).with(TemporalAdjusters.lastDayOfMonth());
        LocalDate currentFinancialStartDate = currentFinancialEndDate.minusYears(1).plusMonths(1).withDayOfMonth(1);

        // if last year cycle already ended, move to this year cycle
        if (LocalDate.now().isAfter(currentFinancialEndDate)) {
            currentFinancialStartDate = currentFinancialStartDate.plusYears(1);
            currentFinancialEndDate = currentFinancialEndDate.plusYears(1);
        }

        LocalDate periodStart, periodEnd;

        if (contractStartDate.isBefore(currentFinancialStartDate)) { // HR_MEMBER with far-past startOn
            periodStart = currentFinancialStartDate;
            periodEnd = currentFinancialEndDate;
        } else if (contractStartDate.isBefore(currentFinancialEndDate) || contractStartDate.isEqual(currentFinancialEndDate)) { // startOn is in the current cycle
            periodStart = contractStartDate;
            periodEnd = currentFinancialEndDate;
        } else { // startOn is in the next cycle
            periodStart = contractStartDate;
            periodEnd = currentFinancialEndDate.plusYears(1);
        }

        return Pair.of(periodStart, periodEnd);
    }

    private LocalDate getPeriodEndForReAllocation(LocalDate currentPeriodEnd, AllocationBasis basis, Month financialEndMonth) {
        int currentYear = LocalDate.now().getYear();
        LocalDate financialEndDate = getFinancialEndDateForReAllocation(currentPeriodEnd, financialEndMonth, currentYear);

        // Extract logic for calculating the next date based on the allocation basis
        switch (basis) {
            case DAILY:
                return getNextDateForDailyBasisiAllocation(currentPeriodEnd, financialEndDate);
            case MONTHLY:
                return getNextDateForMonthlyBasisAllocation(currentPeriodEnd, financialEndDate);
            case QUARTERLY:
                return getNextDateForQuarterlyBasisAllocation(currentPeriodEnd, financialEndDate);
            case WEEKLY:
                return getNextDateForWeeklyBasisAllocation(currentPeriodEnd, financialEndDate);
            /**
             * Doesn't make sense to return `startDate` (i.e., the period will be 1 day only)
             * for summaries having basis=ONCE like this.
             * So, adding `case ONCE:` here, assuming "ONCE" leaves (e.g., compensatory)
             * are valid within the financial year cycle just like "ANNUALLY" ones.
             */
            case ANNUALLY:
            case ONCE:
                return getNextDateForAnnuallyOrOnceBasisAllocation(currentPeriodEnd, financialEndDate);
        }
        return currentPeriodEnd;
    }

    private LocalDate getNextDateForDailyBasisiAllocation(LocalDate currentPeriodEnd, LocalDate financialEndDate) {
        return currentPeriodEnd.plusDays(1).isBefore(financialEndDate) ? currentPeriodEnd.plusDays(1) : financialEndDate;
    }

    private LocalDate getNextDateForMonthlyBasisAllocation(LocalDate currentPeriodEnd, LocalDate financialEndDate) {
        // because last day of month can be one of 28/29 (Feb), 30/31 (others), below logic makes sure xxxx-02-28 or 29 + 1month = xxxx-03-31 (and similar)
        var nextMonth = TimeOffUtil.isLastDayOfMonth(currentPeriodEnd)
                ? currentPeriodEnd.plusMonths(1).with(TemporalAdjusters.lastDayOfMonth())
                : currentPeriodEnd.plusMonths(1);
        return nextMonth.isBefore(financialEndDate) ? nextMonth : financialEndDate;
    }

    private LocalDate getNextDateForQuarterlyBasisAllocation(LocalDate currentPeriodEnd, LocalDate financialEndDate) {
        var nextQuarter = TimeOffUtil.isLastDayOfMonth(currentPeriodEnd)
                ? currentPeriodEnd.plusMonths(3).with(TemporalAdjusters.lastDayOfMonth())
                : currentPeriodEnd.plusMonths(3);
        return nextQuarter.isBefore(financialEndDate) ? nextQuarter : financialEndDate;
    }

    private LocalDate getNextDateForWeeklyBasisAllocation(LocalDate currentPeriodEnd, LocalDate financialEndDate) {
        return currentPeriodEnd.plusWeeks(1).isBefore(financialEndDate) ? currentPeriodEnd.plusWeeks(1) : financialEndDate;
    }

    private LocalDate getNextDateForAnnuallyOrOnceBasisAllocation(LocalDate currentPeriodEnd, LocalDate financialEndDate) {
        var nextYear = TimeOffUtil.isLastDayOfMonth(currentPeriodEnd)
                ? currentPeriodEnd.plusYears(1).with(TemporalAdjusters.lastDayOfMonth())
                : currentPeriodEnd.plusYears(1);
        return nextYear.isBefore(financialEndDate) ? nextYear : financialEndDate;
    }

    @NotNull
    private LocalDate getFinancialEndDateForReAllocation(LocalDate currentPeriodEnd, Month financialEndMonth, int currentYear) {
        LocalDate financialEndDateCurrentYear = LocalDate.of(currentYear, financialEndMonth, 1).with(TemporalAdjusters.lastDayOfMonth());
        LocalDate financialEndDateNextYear = LocalDate.of(currentYear + 1, financialEndMonth, 1).with(TemporalAdjusters.lastDayOfMonth());

        return currentPeriodEnd.isAfter(financialEndDateCurrentYear) || currentPeriodEnd.equals(financialEndDateCurrentYear)
                ? financialEndDateNextYear
                : financialEndDateCurrentYear;
    }
}
