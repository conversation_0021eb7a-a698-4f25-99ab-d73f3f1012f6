package com.multiplier.timeoff.validation;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.repository.model.DefinitionEntity;
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.Month;

@Getter
@Setter(AccessLevel.PRIVATE)
@ToString
@EqualsAndHashCode
@Accessors(fluent = true, chain = true)
public class TimeoffDefinitionConfigRequest {

    private TimeoffEntitlementDBO timeoffEntitlementDBO;
    private TimeoffSummaryDBO timeoffSummaryDBO;
    private ContractOuterClass.Contract contract;
    private Month companyFinancialYearEndMonth;
    private boolean isAllocation;
    private DefinitionEntity definitionEntity;

    private TimeoffDefinitionConfigRequest() {
    }

    public static TimeoffDefinitionConfigRequest of(DefinitionEntity definition, TimeoffEntitlementDBO timeoffEntitlementDBO,
                                                    ContractOuterClass.Contract contract, TimeoffSummaryDBO summaryDBO, boolean isAllocation, Month companyFinancialYearEndMonth) {
        return new TimeoffDefinitionConfigRequest()
                .contract(contract)
                .isAllocation(isAllocation)
                .timeoffSummaryDBO(summaryDBO)
                .definitionEntity(definition)
                .timeoffEntitlementDBO(timeoffEntitlementDBO)
                .companyFinancialYearEndMonth(companyFinancialYearEndMonth);

    }

    public void resetTimeoffSummaryDBO(TimeoffSummaryDBO newTimeoffSummaryDBO) {
        this.timeoffSummaryDBO = newTimeoffSummaryDBO;
    }

}
