package com.multiplier.timeoff.repository;

import com.multiplier.timeoff.core.constant.Database;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.types.TimeOffTypeStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import jakarta.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface TimeoffTypeRepository extends JpaRepository<TimeoffTypeDBO, Long> {

    String DEFAULT_TIME_OFF_TYPE_KEY = "annual";

    @Query(nativeQuery = true, value = "SELECT *" +
            " FROM " + Database.SCHEMA + "." + Database.Table.Type.TABLE_NAME +
            " WHERE key = " + "'" + DEFAULT_TIME_OFF_TYPE_KEY + "'" +
            " AND company_id IS NULL" +
            " AND status != 'DELETED' " +
            " LIMIT 1")
    Optional<TimeoffTypeDBO> findDefault();

    @Query("SELECT t FROM TimeoffTypeDBO t " +
            "WHERE t.key = :key AND t.status != 'DELETED' AND (t.companyId = :companyId OR t.companyId IS null)")
    TimeoffTypeDBO findByKeyAndCompanyId(String key, @NotNull Long companyId);

    @Query(value = "select t from TimeoffTypeDBO t where (t.companyId = :companyId or t.companyId IS null) and t.status != 'DELETED' ")
    List<TimeoffTypeDBO> findAllForCompany(@Param("companyId") Long companyId);

    @Query("SELECT CASE WHEN COUNT(t) > 0 THEN true ELSE false END FROM TimeoffTypeDBO t " +
            "WHERE t.key = :key AND t.status != 'DELETED' AND (t.companyId = :companyId OR t.companyId IS null)")
    boolean existsByKeyAndCompanyId(@Param("key") String key,  @Param("companyId") Long companyId);

    @Query("SELECT CASE WHEN COUNT(t) > 0 THEN true ELSE false END FROM TimeoffTypeDBO t " +
            "WHERE t.id = :typeId AND t.status != 'DELETED' AND (t.companyId = :companyId OR t.companyId IS null)")
    boolean existsByTypeIdAndCompanyId(Long typeId, Long companyId);

    @Query(value = "select t from TimeoffTypeDBO t where (t.companyId = :companyId or t.companyId = null) and t.id in :ids and t.status not in :statusesNotIn")
    List<TimeoffTypeDBO> findByIdInAndCompanyIdAndStatusNotIn(@Param("ids") Collection<Long> ids, @Param("companyId")Long companyId, @Param("statusesNotIn") Collection<TimeOffTypeStatus> statusesNotIn);

    TimeoffTypeDBO findByIdAndCompanyIdAndStatusNotIn(Long id, Long companyId, Collection<TimeOffTypeStatus> statusesNotIn);

    TimeoffTypeDBO findByIdAndStatusNotIn(Long id, Collection<TimeOffTypeStatus> statusesNot);

    @Query(value = "select t from TimeoffTypeDBO t where (t.companyId = :companyId or t.companyId is null) and t.isPaidLeave = :isPaidLeave and t.status not in :statusNot")
    List<TimeoffTypeDBO> findAllByCompanyIdAndIsPaidLeaveAndStatusNotIn(@Param("companyId") Long companyId, @Param("isPaidLeave") boolean isPaidLeave, @Param("statusNot") Collection<TimeOffTypeStatus> statusesNotIn);
}
