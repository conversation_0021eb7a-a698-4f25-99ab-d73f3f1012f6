package com.multiplier.timeoff.repository;

import com.multiplier.timeoff.repository.model.TimeOffDefinitionRuleDBO;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Set;

public interface TimeOffDefinitionRuleRepository extends JpaRepository<TimeOffDefinitionRuleDBO, Long> {

    List<TimeOffDefinitionRuleDBO> findByCompanyDefinitionId(Long companyDefinitionId);

    List<TimeOffDefinitionRuleDBO> findByCompanyDefinitionIdIn(Set<Long> companyDefinitionIds);

}
