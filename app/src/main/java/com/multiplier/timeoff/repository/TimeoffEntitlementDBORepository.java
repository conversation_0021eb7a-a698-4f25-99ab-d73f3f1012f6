package com.multiplier.timeoff.repository;

import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface TimeoffEntitlementDBORepository extends JpaRepository<TimeoffEntitlementDBO, Long>, JpaSpecificationExecutor<TimeoffEntitlementDBO> {

    List<TimeoffEntitlementDBO> findAllByContractId(Long contractID);
    Optional<TimeoffEntitlementDBO> findByContractIdAndTypeId(Long contractID, Long typeId);
    List<TimeoffEntitlementDBO> findAllByDefinitionIdIn(Collection<Long> definitionIds);

    @Query("select t from TimeoffEntitlementDBO t where t.definitionId =:definitionId AND t.value > 0")
    List<TimeoffEntitlementDBO> findAllByDefinitionId(Long definitionId);
    List<TimeoffEntitlementDBO> findAllByContractIdInAndTypeId(Collection<Long> contractIds, Long typeId);
    void deleteAllByContractId(Long contractId);

    void deleteAllByContractIdAndTypeId(Long contractId, Long typeId);

    void deleteAllByContractIdIn(Collection<Long> contractIds);

    @Query("SELECT new com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO(t.typeId, t.definitionId, t.contractId, t.value, t.unit) " +
            "FROM TimeoffEntitlementDBO t " +
            "WHERE t.contractId IN :contractIds AND t.typeId IN :typeIds")
    List<TimeoffEntitlementDBO> findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Collection<Long> contractIds, Collection<Long> typeIds);

}