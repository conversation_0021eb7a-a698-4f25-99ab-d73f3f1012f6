package com.multiplier.timeoff.repository.model;

import com.multiplier.timeoff.core.common.db.AuditableBaseEntity;
import com.multiplier.timeoff.core.constant.Database;
import com.multiplier.timeoff.types.TimeOffUnit;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import jakarta.persistence.*;


@Entity
@Getter
@Setter
@Audited
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(fluent = true, chain = true)
@AuditOverride(forClass = AuditableBaseEntity.class)
@Table(name = Database.Table.Entitlement.TABLE_NAME, schema = Database.SCHEMA,
        uniqueConstraints = {
                @UniqueConstraint(
                        name = Database.Table.Entitlement.Constraint.UQ_CONTRACT_ENTITLEMENT,
                        columnNames = {
                                Database.Table.Entitlement.Column.TYPE_ID,
                                Database.Table.Entitlement.Column.CONTRACT_ID
                        })})
public class TimeoffEntitlementDBO extends AuditableBaseEntity {

    @Column(name = Database.Table.Entitlement.Column.TYPE_ID, insertable = false, updatable = false)
    private Long typeId;

    @Column(name = Database.Table.Entitlement.Column.DEFINITION_ID, insertable = false, updatable = false)
    private Long definitionId;

    @Column(name = Database.Table.Entitlement.Column.CONTRACT_ID, nullable = false)
    private Long contractId;

    @Column(name = "\"" + Database.Table.Entitlement.Column.VALUE + "\"")
    private Double value;

    @Enumerated(EnumType.STRING)
    private TimeOffUnit unit;

    @JoinColumn(nullable = false, name = Database.Table.Entitlement.Column.TYPE_ID, foreignKey = @ForeignKey(name = Database.Table.Entitlement.Constraint.FK_TYPE_IN_ENTITLEMENT))
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private TimeoffTypeDBO type;

    @JoinColumn(nullable = false, name = Database.Table.Entitlement.Column.DEFINITION_ID, foreignKey = @ForeignKey(name = Database.Table.Entitlement.Constraint.FK_DEFINITION_IN_ENTITLEMENT))
    @ManyToOne(fetch = FetchType.LAZY)
    private DefinitionEntity definition;

    public TimeoffEntitlementDBO(Long typeId, Long definitionId, Long contractId, Double value, TimeOffUnit unit) {
        this.typeId = typeId;
        this.definitionId = definitionId;
        this.contractId = contractId;
        this.value = value;
        this.unit = unit;
    }
}
