package com.multiplier.timeoff.repository.model;

import com.multiplier.timeoff.core.common.db.AuditableBaseEntity;
import com.multiplier.timeoff.core.constant.Database;
import com.multiplier.timeoff.types.TimeOffSummaryStatus;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.BatchSize;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import java.time.LocalDate;

@Entity
@Getter
@Setter
@Audited
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(fluent = true, chain = true)
@AuditOverride(forClass = AuditableBaseEntity.class)
@Table(name = Database.Table.Summary.TABLE_NAME, schema = Database.SCHEMA)
@BatchSize(size = 500)
public class TimeoffSummaryDBO extends AuditableBaseEntity {

    // user only for query
    @Column(name = Database.Table.Summary.Column.TYPE_ID, insertable = false, updatable = false)
    private Long typeId;

    @Column(nullable = false)
    private long contractId;
    private LocalDate periodStart;
    private LocalDate periodEnd;

    @Column(name = Database.Table.Summary.Column.TOTAL_ENTITLED_COUNT)
    private Double totalEntitledCount;
    private Double allocatedCount;
    private Double carriedCount;
    private Double takenCount;
    private Double pendingCount;

    @JoinColumn(nullable = false, name = Database.Table.Summary.Column.TYPE_ID, foreignKey = @ForeignKey(name = Database.Table.Summary.Constraint.FK_TYPE_IN_SUMMARY))
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private TimeoffTypeDBO timeoffType;

    @Enumerated(EnumType.STRING)
    private TimeOffSummaryStatus status;

    private Double carryForwardCount;
    private Double carryForwardExpiredCount;
    private Double usedFromAllocatedCount;
    private Double usedFromCarryForwardCount;
    private Double usedFromLapsableCount;
    private Double usedFromNextCycleCarryForwardCount;

    public Double calculateBalance() {
        val totalEntitled = calculateEntitledDays();
        val usedInCurrentCycle = zeroIfNull(this.usedFromAllocatedCount()) + zeroIfNull(this.usedFromCarryForwardCount());
        val usedInNextCycle = zeroIfNull(this.usedFromNextCycleCarryForwardCount()) + zeroIfNull(this.usedFromLapsableCount());
        return totalEntitled - usedInCurrentCycle - usedInNextCycle;
    }

    public Double calculateEntitledDays() {
        return zeroIfNull(this.allocatedCount()) + zeroIfNull(this.carryForwardCount()) - zeroIfNull(this.carryForwardExpiredCount());
    }

    public Double calculateCurrentSummaryBalance() {
        return zeroIfNull(calculateEntitledDays()) - zeroIfNull(this.usedFromAllocatedCount()) - zeroIfNull(this.usedFromCarryForwardCount());
    }

    public Double calculateCarryForwardBalance() {
        return zeroIfNull(this.carryForwardCount()) - zeroIfNull(this.carryForwardExpiredCount()) - zeroIfNull(this.usedFromCarryForwardCount());
    }

    private Double zeroIfNull(Double value) {
        return value == null ? 0.0 : value;
    }
}
