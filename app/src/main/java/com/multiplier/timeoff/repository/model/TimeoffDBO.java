package com.multiplier.timeoff.repository.model;

import com.multiplier.timeoff.core.common.constant.ColumnDefinition;
import com.multiplier.timeoff.core.common.db.AuditUser;
import com.multiplier.timeoff.core.common.db.AuditableBaseEntity;
import com.multiplier.timeoff.core.common.util.SecurityContextUtil;
import com.multiplier.timeoff.core.constant.Database;
import com.multiplier.timeoff.types.TimeOffSession;
import com.multiplier.timeoff.types.TimeOffStatus;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@Audited
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(fluent = true, chain = true)
@AuditOverride(forClass = AuditableBaseEntity.class)
@Table(name = Database.Table.TimeOff.TABLE_NAME, schema = Database.SCHEMA)
public class TimeoffDBO extends AuditableBaseEntity {

    // user only for query
    @Column(name = Database.Table.TimeOff.Column.TYPE_ID, insertable = false, updatable = false)
    private Long typeId;

    @Column(nullable = false)
    private Long contractId;

    @Enumerated(EnumType.STRING)
    private TimeOffStatus status;
    private LocalDate startDate;

    @Enumerated(EnumType.STRING)
    private TimeOffSession startSession;

    private LocalDate endDate;

    @Enumerated(EnumType.STRING)
    private TimeOffSession endSession;

    @JoinColumn(nullable = false, name = Database.Table.TimeOff.Column.TYPE_ID, foreignKey = @ForeignKey(name = Database.Table.TimeOff.Constraint.FK_TYPE_IN_TIMEOFF))
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.PERSIST)
    private TimeoffTypeDBO type;

    private String changeReason;
    private Double noOfDays;
    private String description;

    private Instant approvedOn;

    @JdbcTypeCode(SqlTypes.JSON)
    @Type(JsonBinaryType.class)
    @Column(columnDefinition = ColumnDefinition.JSONB)
    private AuditUser createdByInfo;

    @JdbcTypeCode(SqlTypes.JSON)
    @Type(JsonBinaryType.class)
    @Column(columnDefinition = ColumnDefinition.JSONB)
    private AuditUser updatedByInfo;

    private String externalId;

    @OneToMany(mappedBy = "timeoff", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("id")
    @Builder.Default
    @NotAudited
    @BatchSize(size=100)
    private List<TimeoffEntryDBO> timeoffEntries = new ArrayList<>();

    @OneToOne(mappedBy = "timeoff", cascade = {CascadeType.PERSIST, CascadeType.MERGE}, orphanRemoval = true)
    private TimeoffUsageDBO timeoffUsage;

    // Migrating everything to AuditorAware<AuditUser> is a huge job. So, we only extend the entity like this
    @PrePersist
    private void onPrePersist() {
        createdByInfo = SecurityContextUtil.getAuditUser();
        if (createdByInfo == null) {
            createdByInfo = new AuditUser();
            createdByInfo.setUserId(-1L);
        }
        updatedByInfo = createdByInfo;
    }

    // Migrating everything to AuditorAware<AuditUser> is a huge job. So, we only extend the entity like this
    @PreUpdate
    private void onPreUpdate() {
        updatedByInfo = SecurityContextUtil.getAuditUser();
    }
}
