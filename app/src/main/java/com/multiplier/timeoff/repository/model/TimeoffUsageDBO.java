package com.multiplier.timeoff.repository.model;

import com.multiplier.timeoff.core.common.db.AuditableBaseEntity;
import com.multiplier.timeoff.core.constant.Database;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

@Entity
@Getter
@Setter
@Audited
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(fluent = true, chain = true)
@AuditOverride(forClass = AuditableBaseEntity.class)
@Table(name = Database.Table.TimeOffUsage.TABLE_NAME, schema = Database.SCHEMA)
public class TimeoffUsageDBO extends AuditableBaseEntity {

    @Column(name = Database.Table.TimeOffUsage.Column.TIMEOFF_ID, insertable = false, updatable = false)
    private Long timeoffId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = Database.Table.TimeOffUsage.Column.TIMEOFF_ID, foreignKey = @ForeignKey(name = Database.Table.TimeOffUsage.Constraint.FK_TIMEOFF_IN_TIMEOFF_USAGE))
    private TimeoffDBO timeoff;


    private Double usedFromCarryForwardCount;
    private Double usedFromAllocatedCount;
    private Double usedFromLapsableCount;
    private Double usedFromNextCycleCarryForwardCount;
    private Double usedFromNextCycleAllocatedCount;


}
