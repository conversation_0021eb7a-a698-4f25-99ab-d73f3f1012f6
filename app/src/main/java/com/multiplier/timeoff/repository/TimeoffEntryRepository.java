package com.multiplier.timeoff.repository;

import com.multiplier.timeoff.repository.dto.TimeoffEntryWithDescription;
import com.multiplier.timeoff.repository.model.TimeoffEntryDBO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;

public interface TimeoffEntryRepository extends JpaRepository<TimeoffEntryDBO, Long>,
    JpaSpecificationExecutor<TimeoffEntryDBO> {

    @Query("""
            SELECT new com.multiplier.timeoff.repository.dto.TimeoffEntryWithDescription(entry.id, entry.date, entry.value, entry.session, entry.type, timeoff.description)
            FROM TimeoffEntryDBO entry JOIN TimeoffDBO timeoff ON entry.timeoffId = timeoff.id
            WHERE entry.type = 'TIMEOFF'
            AND entry.date BETWEEN :fromDate AND :toDate 
            AND timeoff.status NOT IN ('DRAFT', 'DELETED') 
            AND timeoff.contractId = :contractId
            """)
    List<TimeoffEntryWithDescription> findTimeOffEntriesWithDescriptionInDateRangeByContractId(
            @Param("fromDate") LocalDate fromDate,
            @Param("toDate") LocalDate toDate,
            @Param("contractId") Long contractId
    );

    List<TimeoffEntryDBO> findAllByTimeoffIdIn(Set<Long> timeoffIds);
}
