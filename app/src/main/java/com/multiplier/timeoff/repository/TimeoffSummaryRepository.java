package com.multiplier.timeoff.repository;

import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.types.TimeOffSummaryStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface TimeoffSummaryRepository extends JpaRepository<TimeoffSummaryDBO, Long>, JpaSpecificationExecutor<TimeoffSummaryDBO> {

    @Query(nativeQuery = true, value = "with max_id as " +
            "(" +
            "select max(id) as id " +
            "from timeoff.timeoff_summary group by contract_id, type_id " +
            ") " +
            "select * from timeoff.timeoff_summary ts where ts.period_end < :expiryTime and id in (select id from max_id) LIMIT 150000;")
    List<TimeoffSummaryDBO> getLastestExpiredSummaryForAContractIDAndTypeID(LocalDate expiryTime);

    List<TimeoffSummaryDBO> deleteAllByContractId(Long contractId);

    List<TimeoffSummaryDBO> deleteAllByContractIdAndTypeIdIn(Long contractId, Collection<Long> typeIds);

    void deleteAllByContractIdAndTypeId(Long contractId, Long typeId);

    @Query(nativeQuery = true, value =
            " SELECT * FROM timeoff.timeoff_summary ts " +
            " WHERE  ts.type_id = :#{#successorSummary.typeId} " +
            " AND    ts.contract_id = :#{#successorSummary.contractId} " +
            " AND    ts.period_end < :#{#successorSummary.periodStart} " +
            " ORDER BY ts.period_start DESC, ts.id DESC " +
            " LIMIT 1 ")
    TimeoffSummaryDBO findBySuccessor(TimeoffSummaryDBO successorSummary);

    @Query(nativeQuery = true, value =
            " SELECT * FROM timeoff.timeoff_summary ts " +
            " WHERE id IN (SELECT MAX(id) FROM timeoff.timeoff_summary GROUP BY contract_id, type_id) " +
            " AND period_end < :expiryDate ")
    List<TimeoffSummaryDBO> findLatestSummariesExpiredBefore(LocalDate expiryDate);

    @Query(nativeQuery = true, value =
            " SELECT * FROM timeoff.timeoff_summary ts " +
            " WHERE id IN (SELECT MAX(id) FROM timeoff.timeoff_summary GROUP BY contract_id, type_id) " +
            " AND period_end < :expiryTime" +
            " AND contract_id IN :contractIds ")
    List<TimeoffSummaryDBO> findLatestSummariesExpiredBeforeAndContractIdIn(LocalDate expiryTime, Set<Long> contractIds);

    @Query(value =
            " SELECT ts FROM TimeoffSummaryDBO ts " +
            " WHERE ts.periodEnd >= :expiryDate ")
    List<TimeoffSummaryDBO> findAllSummariesNotExpiredOnOrAfter(LocalDate expiryDate);

    @Query(value =
            " SELECT ts FROM TimeoffSummaryDBO ts " +
            " WHERE ts.periodEnd >= :expiryDate " +
            " AND ts.contractId IN :contractIds ")
    List<TimeoffSummaryDBO> findAllSummariesNotExpiredOnOrAfterAndContractIdIn(LocalDate expiryDate, Set<Long> contractIds);

    @Query(nativeQuery = true, value =
            " SELECT * FROM timeoff.timeoff_summary ts " +
                    " WHERE  ts.type_id = :#{#predecessorSummary.typeId} " +
                    " AND    ts.contract_id = :#{#predecessorSummary.contractId} " +
                    " AND    ts.period_start > :#{#predecessorSummary.periodEnd} " +
                    " ORDER BY ts.period_start ASC, ts.id DESC " +
                    " LIMIT 1 ")
    TimeoffSummaryDBO findByPredecessor(TimeoffSummaryDBO predecessorSummary);

    List<TimeoffSummaryDBO> findAllByContractIdIn(Set<Long> contractIds);

    @Query(nativeQuery = true, value =
            "SELECT ts.* FROM timeoff.timeoff_summary ts " +
                    "WHERE ts.id IN (" +
                        "SELECT MAX(id) FROM timeoff.timeoff_summary ts1 " +
                        "WHERE ts1.contract_id IN :contractIds " +
                        "AND ts1.type_id = :typeId " +
                        "GROUP BY ts1.contract_id )"
    )
    List<TimeoffSummaryDBO> findLatestSummaryForContractIdInAndTypeId(@Param("contractIds") Collection<Long> contractIds, @Param("typeId") Long typeId);

    @Query(nativeQuery = true, value =
            "SELECT ts.* FROM timeoff.timeoff_summary ts " +
                    "WHERE ts.id IN (" +
                    "SELECT MAX(id) FROM timeoff.timeoff_summary ts1 " +
                    "WHERE ts1.contract_id IN :contractIds " +
                    "AND ts1.type_id IN :typeIds " +
                    "GROUP BY ts1.contract_id, ts1.type_id )"
    )
    List<TimeoffSummaryDBO> findLatestSummaryForContractIdInAndTypeIdIn(@Param("contractIds") Collection<Long> contractIds, @Param("typeIds") Collection<Long> typeIds);

    @Query("SELECT ts FROM TimeoffSummaryDBO ts WHERE ts.contractId = :contractId AND ts.periodStart <= :referenceDate AND ts.periodEnd >= :referenceDate")
    List<TimeoffSummaryDBO> findSummariesByContractIdAndDate(Long contractId, LocalDate referenceDate);

    void deleteAllByContractIdIn(Collection<Long> contractIds);


    @Query("SELECT ts.periodStart FROM TimeoffSummaryDBO ts WHERE ts.contractId = :contractId AND ts.status IN (com.multiplier.timeoff.types.TimeOffSummaryStatus.ACTIVE, com.multiplier.timeoff.types.TimeOffSummaryStatus.UPCOMING) " +
            "ORDER BY ts.periodStart ASC LIMIT 1")
    LocalDate findEarliestActiveSummaryStartDate(Long contractId);

    @Query("SELECT ts.periodEnd FROM TimeoffSummaryDBO ts WHERE ts.contractId = :contractId AND ts.status IN (com.multiplier.timeoff.types.TimeOffSummaryStatus.ACTIVE, com.multiplier.timeoff.types.TimeOffSummaryStatus.UPCOMING) " +
            "ORDER BY ts.periodEnd DESC LIMIT 1")
    LocalDate findFurthestSummaryEndDate(Long contractId);

    @Query(nativeQuery = true, value =
            "WITH active_summaries AS (" +
                    "  SELECT contract_id, type_id, COUNT(*) as summary_count " +
                    "  FROM timeoff.timeoff_summary " +
                    "  WHERE period_end >= :currentDate " +
                            "AND status != 'EXPIRED'" +
                    "  GROUP BY contract_id, type_id " +
                    ") " +
                    "SELECT COUNT(*) " +
                    "FROM timeoff.timeoff_summary ts " +
                    "JOIN active_summaries a ON ts.contract_id = a.contract_id AND ts.type_id = a.type_id " +
                    "WHERE ts.period_end >= :currentDate " +
                        "AND ts.status != 'EXPIRED'" +
                        "AND a.summary_count = 1")
    Long countSummariesNeedingFutureEntries(@Param("currentDate") LocalDate currentDate);

    @Query(nativeQuery = true, value =
            "WITH active_summaries AS (" +
                    "  SELECT contract_id, type_id, COUNT(*) as summary_count " +
                    "  FROM timeoff.timeoff_summary " +
                    "  WHERE period_end >= :currentDate " +
                            "AND status != 'EXPIRED'" +
                    "  GROUP BY contract_id, type_id " +
                    ") " +
                    "SELECT ts.* " +
                    "FROM timeoff.timeoff_summary ts " +
                    "JOIN active_summaries a ON ts.contract_id = a.contract_id AND ts.type_id = a.type_id " +
                    "WHERE ts.period_end >= :currentDate " +
                            "AND ts.status != 'EXPIRED'" +
                            "AND a.summary_count = 1 " +
                    "ORDER BY ts.contract_id, ts.type_id " +
                    "LIMIT :batchSize")
    List<TimeoffSummaryDBO> findBatchOfSummariesNeedingFutureEntries(@Param("currentDate") LocalDate currentDate, @Param("batchSize") int batchSize);



    @Query(nativeQuery = true, value =
            "SELECT COUNT(*) FROM timeoff.timeoff_summary ts " +
                    "WHERE ts.status = 'ACTIVE' " +
                    "AND ts.period_end < :currentDate")
    Long countActiveSummariesWithPastPeriodEnd(LocalDate currentDate);

    @Query(nativeQuery = true, value =
            "SELECT * FROM timeoff.timeoff_summary ts " +
                    "WHERE ts.status = 'ACTIVE' " +
                    "AND ts.period_end < :currentDate " +
                    "LIMIT :batchSize")
    List<TimeoffSummaryDBO> findBatchOfActiveSummariesWithPastPeriodEnd(LocalDate currentDate, @Param("batchSize") int batchSize);


    @Query("SELECT ts FROM TimeoffSummaryDBO ts WHERE ts.status = 'UPCOMING' " +
            "AND ts.contractId IN :contractIds AND ts.typeId IN :typeIds " +
            "AND ts.periodStart <= :currentDate AND ts.periodEnd >= :currentDate")
    List<TimeoffSummaryDBO> findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(Set<Long> contractIds, Set<Long> typeIds, LocalDate currentDate);



    @Query("SELECT ts FROM TimeoffSummaryDBO ts WHERE ts.contractId = :contractId AND ts.typeId = :typeId AND ts.status != 'EXPIRED' ORDER BY ts.periodStart ASC LIMIT 2")
    List<TimeoffSummaryDBO> findCurrentAndNextSummaryByContractIdAndTypeId(Long contractId, Long typeId);

    List<TimeoffSummaryDBO> findAllByContractIdInAndTypeIdAndStatusIn(Set<Long> contractIds, Long typeId, Set<TimeOffSummaryStatus> statuses);
}
