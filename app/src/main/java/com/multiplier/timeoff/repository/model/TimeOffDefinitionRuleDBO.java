package com.multiplier.timeoff.repository.model;

import com.multiplier.timeoff.core.common.db.AuditableBaseEntity;
import com.multiplier.timeoff.core.constant.Database;
import com.multiplier.timeoff.types.ConditionKey;
import com.multiplier.timeoff.types.ConditionOperator;
import com.multiplier.timeoff.types.RuleType;
import io.hypersistence.utils.hibernate.type.array.StringArrayType;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import jakarta.persistence.*;
import org.hibernate.type.SqlTypes;

@Entity
@Getter
@Setter
@Audited
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(fluent = true, chain = true)
@AuditOverride(forClass = AuditableBaseEntity.class)
@Table(name = Database.Table.TimeOffDefinitionRule.TABLE_NAME, schema = Database.SCHEMA)
@BatchSize(size = 200)
public class TimeOffDefinitionRuleDBO extends AuditableBaseEntity {

    private Long companyId;

    @Enumerated(EnumType.STRING)
    private RuleType ruleType;

    @Enumerated(EnumType.STRING)
    private ConditionKey conditionKey;

    @Enumerated(EnumType.STRING)
    private ConditionOperator conditionOperator;

    @Column(name = "condition_values", columnDefinition = "TEXT[]")
    @JdbcTypeCode(SqlTypes.ARRAY)
    @Type(StringArrayType.class)
    private String[] conditionValues;

    @Column(name = Database.Table.TimeOffDefinitionRule.Column.COMPANY_DEFINITION_ID, insertable = false, updatable = false)
    private Long companyDefinitionId;

    @JoinColumn(nullable = false, name = Database.Table.TimeOffDefinitionRule.Column.COMPANY_DEFINITION_ID, foreignKey = @ForeignKey(name = Database.Table.TimeOffDefinitionRule.Constraint.FK_COMPANY_DEFINITION_IN_DEFINITION_RULE))
    @ManyToOne(fetch = FetchType.LAZY)
    private CompanyDefinitionEntity companyDefinition;

}
