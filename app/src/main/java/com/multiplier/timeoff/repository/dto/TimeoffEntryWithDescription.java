package com.multiplier.timeoff.repository.dto;

import com.multiplier.timeoff.types.TimeOffEntryType;
import com.multiplier.timeoff.types.TimeOffSession;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

import java.time.LocalDate;

@Getter
@AllArgsConstructor
@Builder
public class TimeoffEntryWithDescription {
    private Long id;
    private LocalDate date;
    private Double value;
    private TimeOffSession session;
    private TimeOffEntryType type;
    private String description;
}
