package com.multiplier.timeoff.repository.model;

import com.multiplier.timeoff.core.constant.Database;
import com.multiplier.timeoff.types.TimeOffTypeStatus;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import com.multiplier.timeoff.core.common.db.AuditableBaseEntity;

import jakarta.persistence.*;

@Entity
@Getter
@Setter
@Audited
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(fluent = true, chain = true)
@AuditOverride(forClass = AuditableBaseEntity.class)
@Table(name = Database.Table.Type.TABLE_NAME, schema = Database.SCHEMA)
public class TimeoffTypeDBO extends AuditableBaseEntity {

    @Column(name = "\"" + Database.Table.Entitlement.Column.KEY + "\"")
    private String key;
    private Long companyId;
    private String label;
    private String description;
    private Boolean isPaidLeave;

    @Enumerated(EnumType.STRING)
    private TimeOffTypeStatus status;

}
