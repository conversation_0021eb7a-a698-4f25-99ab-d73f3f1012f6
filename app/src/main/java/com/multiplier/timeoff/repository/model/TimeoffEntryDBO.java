package com.multiplier.timeoff.repository.model;

import com.multiplier.timeoff.core.common.db.AuditableBaseEntity;
import com.multiplier.timeoff.core.constant.Database;
import com.multiplier.timeoff.types.TimeOffEntryType;
import com.multiplier.timeoff.types.TimeOffSession;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import java.time.LocalDate;

@Entity
@Getter
@Setter
@SuperBuilder
@Audited
@AllArgsConstructor
@NoArgsConstructor
@Accessors(fluent = true, chain = true)
@AuditOverride(forClass = AuditableBaseEntity.class)
@Table(name = Database.Table.TimeOffEntry.TABLE_NAME, schema = Database.SCHEMA)
public class TimeoffEntryDBO extends AuditableBaseEntity {

    @Column(name = Database.Table.TimeOffEntry.Column.TIME_OFF_ID, insertable = false, updatable = false)
    private Long timeoffId;

    private LocalDate date;
    private Double value;

    @Enumerated(EnumType.STRING)
    private TimeOffSession session;

    @Enumerated(EnumType.STRING)
    private TimeOffEntryType type;

    @JoinColumn(nullable = false, name = Database.Table.TimeOffEntry.Column.TIME_OFF_ID,
        foreignKey = @ForeignKey(name = Database.Table.TimeOffEntry.Constraint.FK_TIMEOFF_ID_IN_TIMEOFF_ENTRY))
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private TimeoffDBO timeoff;
}
