package com.multiplier.timeoff.repository;

import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.types.TimeOffStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface TimeoffRepository extends JpaRepository<TimeoffDBO, Long>, JpaSpecificationExecutor<TimeoffDBO> {

    List<TimeoffDBO> findByStatusAndStartDateBetween(TimeOffStatus status, LocalDate start, LocalDate end);

    @Query(
            " FROM TimeoffDBO " +
                    " WHERE contractId IN :contractIds AND status in :statuses " +
                    " AND type in (:timeoffTypeDBOs) " +
                    " AND ( " +
                    "       (startDate >= :startDate AND startDate <= :endDate) " +
                    "       OR (endDate >= :startDate AND endDate <= :endDate)) "
    )
    List<TimeoffDBO> findWithinDateRange(Set<Long> contractIds, List<TimeoffTypeDBO> timeoffTypeDBOs, Set<TimeOffStatus> statuses, LocalDate startDate, LocalDate endDate);


    @Query(
            "FROM TimeoffDBO " +
                    " WHERE contractId IN :contractIds AND status in :statuses " +
                    " AND ( " +
                    "     (startDate <= :startDate OR endDate >= :endDate) " +
                    "     OR (approvedOn IS NULL OR approvedOn >= :approvedOnGreaterThanEqTo) " +
                    " ) "
    )
    List<TimeoffDBO> findAllForPayroll(Set<Long> contractIds, Set<TimeOffStatus> statuses, LocalDate startDate, LocalDate endDate, Instant approvedOnGreaterThanEqTo);

    @Query(
            " FROM TimeoffDBO" +
                    " WHERE contractId IN :contractIds" +
                    "   AND status IN :statuses" +
                    "   AND (" +
                    "       (" +
                    "           (" +
                    "               (DATE(startDate) >= :startDate AND DATE(startDate) <= :endDate)" +
                    "               OR " +
                    "               (DATE(endDate) >= :startDate AND DATE(endDate) <= :endDate)" +
                    "           )" +
                    "           AND DATE(approvedOn) <= :cutOffTo" +
                    "       )" +
                    "       OR" +
                    "       (" +
                    "           (DATE(startDate) <= :endDate OR DATE(endDate) <= :endDate)" +
                    "           AND" +
                    "           (DATE(approvedOn) >= :cutOffFrom AND DATE(approvedOn) <= :cutOffTo)" +
                    "       )" +
                    "   )"
    )
    List<TimeoffDBO> findAllForPayrollCycle(Set<Long> contractIds, Set<TimeOffStatus> statuses, LocalDate startDate,
                                            LocalDate endDate, LocalDate cutOffFrom, LocalDate cutOffTo);


    /**
     * @deprecated
     */
    @Deprecated
    @Query(
            " FROM TimeoffDBO " +
                    " WHERE contractId IN :contractIds AND status in :statuses " +
                    " AND type in (:timeoffTypeKeys) " +
                    " AND ( " +
                    "     (startDate <= :startDate OR endDate >= :endDate) " +
                    "     OR (approvedOn IS NULL OR approvedOn >= :approvedOnGreaterThanEqTo) " +
                    " ) "
    )
    List<TimeoffDBO> findAllWithCutoffDateRangeOrApprovedAfter(Set<Long> contractIds, List<TimeoffTypeDBO> timeoffTypeKeys, Set<TimeOffStatus> statuses, LocalDate startDate, LocalDate endDate, Instant approvedOnGreaterThanEqTo);


    @Query(
            "SELECT t FROM TimeoffDBO t " +
                    " JOIN TimeoffTypeDBO tt on tt.id = t.typeId " +
                    " WHERE t.contractId = :contractId " +
                    " AND LOWER(tt.key) = LOWER(:type) " +
                    " AND t.status in :statuses "
    )
    List<TimeoffDBO> findAllByTypeAndContractIdAndStatusIn(String type, Long contractId, Set<TimeOffStatus> statuses);

    List<TimeoffDBO> findAllByTypeIdAndContractIdAndStatusIn(Long typeId, Long contractId, Set<TimeOffStatus> statuses);

    @Query(
            "SELECT t FROM TimeoffDBO t " +
                    " JOIN TimeoffTypeDBO tt on tt.id = t.typeId " +
                    " WHERE t.contractId IN :contractIds " +
                    " AND t.typeId IN :typeIds " +
                    " AND t.status in :statuses AND t.startDate >= :startDateFrom"
    )
    List<TimeoffDBO> findAllByTypeInAndContractIdInAndStatusInAndStartDateFrom(Collection<Long> typeIds, Collection<Long> contractIds, Set<TimeOffStatus> statuses, LocalDate startDateFrom);

    Integer countAllByStatusIsIn(Collection<TimeOffStatus> status);

    Integer countAllByStatusIsNot(TimeOffStatus status);

    @Query("SELECT t FROM TimeoffDBO t WHERE t.approvedOn IS NULL")
    List<TimeoffDBO> findAllWhereApprovedOnIsEmpty();

    List<TimeoffDBO> findAllByContractIdIn(Collection<Long> contractIds);

    List<TimeoffDBO> findAllByExternalIdIn(Collection<String> externalIds);

    List<TimeoffDBO> findAllByIdInAndStatusIn(Set<Long> ids, Set<TimeOffStatus> status);

    @Query(
            "FROM TimeoffDBO t " +
                    "WHERE t.updatedOn > :updatedOnFromExclusive " +
                    "AND t.updatedOn <= :updatedOnToInclusive " +
                    "AND t.status IN(:statuses) "
    )
    List<TimeoffDBO> findAllByUpdatedOnAndStatusIn(LocalDateTime updatedOnFromExclusive, LocalDateTime updatedOnToInclusive, Set<TimeOffStatus> statuses);

    @Query(
            "SELECT t FROM TimeoffDBO t " +
                    "WHERE t.contractId = :contractId " +
                    "AND t.typeId IN :typeIds " +
                    "AND (t.startDate >= :fromDate AND t.startDate <= :toDate) " +
                    "AND t.status IN :statuses "
    )
    List<TimeoffDBO> findAllByContractIdTypeIdInStartDateInRangeStatusIn(Long contractId, List<Long> typeIds, LocalDate fromDate, LocalDate toDate, List<TimeOffStatus> statuses);

    @Query(
            "SELECT t FROM TimeoffDBO t " +
                    "WHERE t.contractId = :contractId " +
                    "AND t.status NOT IN ('REJECTED', 'DELETED')" +
                    "AND (t.startDate >= :fromDate AND t.startDate <= :toDate) "
    )
    List<TimeoffDBO> findTimeoffsByContractAndDateRange(Long contractId, LocalDate fromDate, LocalDate toDate);

    @Query(
        "SELECT DISTINCT t FROM TimeoffDBO t " +
            "LEFT JOIN FETCH t.timeoffEntries " +
            "WHERE t.contractId = :contractId " +
            "AND t.status IN :statusList " +
            "AND t.startDate <= :toDate " +
            "AND t.endDate >= :fromDate"
    )
    List<TimeoffDBO> findAllTimeoffsWithinRangeByContractId(
        Long contractId,
        Set<TimeOffStatus> statusList,
        LocalDate fromDate,
        LocalDate toDate
    );
}