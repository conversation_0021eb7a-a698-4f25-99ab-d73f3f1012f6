package com.multiplier.service

import com.multiplier.company.schema.holiday.LegalEntityHoliday
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.country.schema.Country
import com.multiplier.country.schema.holiday.HolidayOuterClass
import com.multiplier.grpc.common.toTimestamp
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.adapters.HolidayServiceAdapter
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.repository.TimeoffRepository
import com.multiplier.timeoff.repository.model.TimeoffDBO
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO
import com.multiplier.timeoff.service.TimeOffEntryService
import com.multiplier.timeoff.toDate
import com.multiplier.timeoff.types.TimeOffSession
import com.multiplier.timeoff.types.TimeOffStatus
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDate
import java.time.LocalDateTime

@ExtendWith(MockKExtension::class)
internal class TimeOffEntryServiceTest {
    @MockK
    lateinit var timeoffRepository: TimeoffRepository

    @MockK
    private lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    private lateinit var holidayServiceAdapter: HolidayServiceAdapter

    @MockK
    private lateinit var featureFlagService: FeatureFlagService;

    @InjectMockKs
    private lateinit var timeOffEntryService: TimeOffEntryService

    @Test
    fun `getTimeOffEntriesByTimeOffIdsAndStatus, should return entries when time-offs are available in database`() {
        // Time off Details:
        // 24 AFTERNOON session
        // 25 HOLIDAY
        // 26 FULL DAY
        // 27-28 WEEKENDS HOLIDAY
        // 29 FULL DAY
        // 30 MORNING session
        val startDate = LocalDate.of(2024, 4, 24)
        val endDate = LocalDate.of(2024, 4, 30)
        val startSession = TimeOffSession.AFTERNOON
        val endSession = TimeOffSession.MORNING
        val timeOff = getMockTimeOff(
            startDate = startDate,
            endDate = endDate,
            startSession = startSession,
            endSession = endSession,
        )
        holidayByEntityFeatureOn()
        every {
            timeoffRepository.findAllByIdInAndStatusIn(setOf(timeOff.id), setOf(timeOff.status()))
        } returns listOf(timeOff)

        val country = Country.GrpcCountryCode.IND
        val contract = ContractOuterClass.Contract.newBuilder().setId(timeOff.contractId()).setCountry(country.name).build()
        every {  contractServiceAdapter.getContractsByIdsAnyStatus(setOf(timeOff.contractId()), true) } returns listOf(contract)

        val holiday = LegalEntityHoliday.Holiday.newBuilder()
            .setYear(2024)
            .setMonth(4)
            .setDate(25)
            .setCountryCode(country.name)
            .addAllContractIds(listOf(timeOff.contractId()))
            .build()
        every {
            holidayServiceAdapter.getHolidays(
                setOf(contract.id),
                2024,
                null,
            )
        } returns listOf(holiday)

        val timeOffEntries = timeOffEntryService.getTimeOffEntriesByTimeOffIdsAndStatus(
            timeOffIds = setOf(timeOff.id),
            statuses = setOf(timeOff.status()),
        )

        assertThat(timeOffEntries).hasSize(4)

        timeOffEntries.forEach {
            assertThat(it.createdOn).isEqualTo(timeOff.createdOn().toTimestamp())
            assertThat(it.createdBy).isEqualTo(timeOff.createdBy())
            assertThat(it.updatedOn).isEqualTo(timeOff.updatedOn().toTimestamp())
            assertThat(it.updatedBy).isEqualTo(timeOff.updatedBy())
            assertThat(it.contractId).isEqualTo(timeOff.contractId())
            assertThat(it.timeOffId).isEqualTo(timeOff.id())
            assertThat(it.timeOffType.key).isEqualTo(timeOff.type().key())
            assertThat(it.status.name).isEqualTo(timeOff.status().name)
            assertThat(it.description).isEqualTo(timeOff.description())
            assertThat(it.hasApprovedOn()).isFalse()
        }

        val firstDay = timeOffEntries[0]
        firstDay.let {
            assertThat(it.id).isEqualTo(*********)
            assertThat(it.entryDate).isEqualTo(LocalDate.of(2024, 4, 24).toDate())
            assertThat(it.startSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
            assertThat(it.endSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
            assertThat(it.noOfDays).isEqualTo(0.5)
            assertThat(it.effectiveDate).isEqualTo(LocalDate.of(2024, 4, 24).toDate())
        }

        val secondDay = timeOffEntries[1]
        secondDay.let {
            assertThat(it.id).isEqualTo(*********)
            assertThat(it.startSession.name).isEqualTo(TimeOffSession.MORNING.name)
            assertThat(it.endSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
            assertThat(it.noOfDays).isEqualTo(1.0)
            assertThat(it.entryDate).isEqualTo(LocalDate.of(2024, 4, 26).toDate())
            assertThat(it.effectiveDate).isEqualTo(LocalDate.of(2024, 4, 26).toDate())
        }

        val thirdDay = timeOffEntries[2]
        thirdDay.let {
            assertThat(it.id).isEqualTo(120240429)
            assertThat(it.startSession.name).isEqualTo(TimeOffSession.MORNING.name)
            assertThat(it.endSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
            assertThat(it.noOfDays).isEqualTo(1.0)
            assertThat(it.entryDate).isEqualTo(LocalDate.of(2024, 4, 29).toDate())
            assertThat(it.effectiveDate).isEqualTo(LocalDate.of(2024, 4, 29).toDate())
        }

        val finalDay = timeOffEntries[3]
        finalDay.let {
            assertThat(it.id).isEqualTo(120240430)
            assertThat(it.startSession.name).isEqualTo(TimeOffSession.MORNING.name)
            assertThat(it.endSession.name).isEqualTo(TimeOffSession.MORNING.name)
            assertThat(it.noOfDays).isEqualTo(0.5)
            assertThat(it.entryDate).isEqualTo(LocalDate.of(2024, 4, 30).toDate())
            assertThat(it.effectiveDate).isEqualTo(LocalDate.of(2024, 4, 30).toDate())
        }
    }

    @Test
    fun `getTimeOffEntriesByTimeOffIdsAndStatus, should return empty when time-offs are not available in database`() {
        val timeOffIds = setOf(1L)
        val statuses = setOf(TimeOffStatus.APPROVED)
        every { timeoffRepository.findAllByIdInAndStatusIn(timeOffIds, statuses) } returns emptyList()

        val timeOffEntries = timeOffEntryService.getTimeOffEntriesByTimeOffIdsAndStatus(
            timeOffIds = timeOffIds,
            statuses = statuses,
        )

        assertThat(timeOffEntries).isEmpty()
        verify(exactly = 0) {  contractServiceAdapter.getContractsByIdsAnyStatus(any(), false) }
        verify(exactly = 0) {  holidayServiceAdapter.getHolidays(any(), any(), any()) }
    }

    @Test
    fun `getTimeOffEntriesByStatusAndUpdatedOn, should return entries when time-offs are available in database`() {
        // Time off Details:
        // 24 AFTERNOON session
        // 25 HOLIDAY
        // 26 FULL DAY
        // 27-28 WEEKENDS HOLIDAY
        // 29 FULL DAY
        // 30 MORNING session
        holidayByEntityFeatureOn()

        val startDate = LocalDate.of(2024, 4, 24)
        val endDate = LocalDate.of(2024, 4, 30)
        val startSession = TimeOffSession.AFTERNOON
        val endSession = TimeOffSession.MORNING
        val timeOff = getMockTimeOff(
            startDate = startDate,
            endDate = endDate,
            startSession = startSession,
            endSession = endSession,
        )

        val updatedOnFromExclusive = LocalDateTime.of(2024, 4, 1, 0, 0, 0)
        val updatedOnToInclusive = LocalDateTime.of(2024, 4, 30, 23, 59, 59)

        every {
            timeoffRepository.findAllByUpdatedOnAndStatusIn(
                updatedOnFromExclusive,
                updatedOnToInclusive,
                setOf(timeOff.status())
            )
        } returns listOf(timeOff)

        val country = Country.GrpcCountryCode.IND
        val contract = ContractOuterClass.Contract.newBuilder().setId(timeOff.contractId()).setCountry(country.name).build()
        holidayByEntityFeatureOn()
        every {  contractServiceAdapter.getContractsByIdsAnyStatus(setOf(timeOff.contractId()), true) } returns listOf(contract)

        val holiday = LegalEntityHoliday.Holiday.newBuilder()
            .setYear(2024)
            .setMonth(4)
            .setDate(25)
            .setCountryCode(country.name)
            .addAllContractIds(listOf(timeOff.contractId()))
            .build()
        every {
            holidayServiceAdapter.getHolidays(
                setOf(contract.id),
                2024,
                null
            )
        } returns listOf(holiday)

        val timeOffEntries = timeOffEntryService.getTimeOffEntriesByStatusAndUpdatedOn(
            updatedOnFromExclusive = updatedOnFromExclusive,
            updatedOnToInclusive = updatedOnToInclusive,
            statuses = setOf(TimeOffStatus.APPROVED)
        )

        assertThat(timeOffEntries).hasSize(4)

        timeOffEntries.forEach {
            assertThat(it.createdOn).isEqualTo(timeOff.createdOn().toTimestamp())
            assertThat(it.createdBy).isEqualTo(timeOff.createdBy())
            assertThat(it.updatedOn).isEqualTo(timeOff.updatedOn().toTimestamp())
            assertThat(it.updatedBy).isEqualTo(timeOff.updatedBy())
            assertThat(it.contractId).isEqualTo(timeOff.contractId())
            assertThat(it.timeOffId).isEqualTo(timeOff.id())
            assertThat(it.timeOffType.key).isEqualTo(timeOff.type().key())
            assertThat(it.status.name).isEqualTo(timeOff.status().name)
            assertThat(it.description).isEqualTo(timeOff.description())
            assertThat(it.hasApprovedOn()).isFalse()
        }

        val firstDay = timeOffEntries[0]
        firstDay.let {
            assertThat(it.id).isEqualTo(*********)
            assertThat(it.entryDate).isEqualTo(LocalDate.of(2024, 4, 24).toDate())
            assertThat(it.startSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
            assertThat(it.endSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
            assertThat(it.noOfDays).isEqualTo(0.5)
            assertThat(it.effectiveDate).isEqualTo(LocalDate.of(2024, 4, 24).toDate())
        }

        val secondDay = timeOffEntries[1]
        secondDay.let {
            assertThat(it.id).isEqualTo(*********)
            assertThat(it.startSession.name).isEqualTo(TimeOffSession.MORNING.name)
            assertThat(it.endSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
            assertThat(it.noOfDays).isEqualTo(1.0)
            assertThat(it.entryDate).isEqualTo(LocalDate.of(2024, 4, 26).toDate())
            assertThat(it.effectiveDate).isEqualTo(LocalDate.of(2024, 4, 26).toDate())
        }

        val thirdDay = timeOffEntries[2]
        thirdDay.let {
            assertThat(it.id).isEqualTo(120240429)
            assertThat(it.startSession.name).isEqualTo(TimeOffSession.MORNING.name)
            assertThat(it.endSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
            assertThat(it.noOfDays).isEqualTo(1.0)
            assertThat(it.entryDate).isEqualTo(LocalDate.of(2024, 4, 29).toDate())
            assertThat(it.effectiveDate).isEqualTo(LocalDate.of(2024, 4, 29).toDate())
        }

        val finalDay = timeOffEntries[3]
        finalDay.let {
            assertThat(it.id).isEqualTo(120240430)
            assertThat(it.startSession.name).isEqualTo(TimeOffSession.MORNING.name)
            assertThat(it.endSession.name).isEqualTo(TimeOffSession.MORNING.name)
            assertThat(it.noOfDays).isEqualTo(0.5)
            assertThat(it.entryDate).isEqualTo(LocalDate.of(2024, 4, 30).toDate())
            assertThat(it.effectiveDate).isEqualTo(LocalDate.of(2024, 4, 30).toDate())
        }
    }

    @Test
    fun `getTimeOffEntriesByStatusAndUpdatedOn, should return empty when time-offs are not available in database`() {
        val updatedOnFromExclusive = LocalDateTime.of(2024, 4, 1, 0, 0, 0)
        val updatedOnToInclusive = LocalDateTime.of(2024, 4, 30, 23, 59, 59)
        val statuses = setOf(TimeOffStatus.APPROVED)
        holidayByEntityFeatureOn()
        every {
            timeoffRepository.findAllByUpdatedOnAndStatusIn(
                updatedOnFromExclusive, updatedOnToInclusive, statuses
            )
        } returns emptyList()

        val timeOffEntries = timeOffEntryService.getTimeOffEntriesByStatusAndUpdatedOn(
            updatedOnFromExclusive = updatedOnFromExclusive,
            updatedOnToInclusive = updatedOnToInclusive,
            statuses = statuses,
        )

        assertThat(timeOffEntries).isEmpty()
        verify(exactly = 0) {  contractServiceAdapter.getContractsByIdsAnyStatus(any(), true) }
        verify(exactly = 0) {  holidayServiceAdapter.getHolidays(any(), any(), any()) }
    }

    @Test
    fun `getTimeOffEntriesByIdsAndStatus, should return time off entries`() {
        // Time off Details:
        // 24 AFTERNOON session
        // 25 HOLIDAY
        // 26 FULL DAY
        // 27-28 WEEKENDS HOLIDAY
        // 29 FULL DAY
        // 30 MORNING session
        val startDate = LocalDate.of(2024, 4, 24)
        val endDate = LocalDate.of(2024, 4, 30)
        val startSession = TimeOffSession.AFTERNOON
        val endSession = TimeOffSession.MORNING
        val timeOff = getMockTimeOff(
            startDate = startDate,
            endDate = endDate,
            startSession = startSession,
            endSession = endSession,
        )
        holidayByEntityFeatureOn()
        every {
            timeoffRepository.findAllByIdInAndStatusIn(setOf(timeOff.id), setOf(timeOff.status()))
        } returns listOf(timeOff)

        val country = Country.GrpcCountryCode.IND
        val contract = ContractOuterClass.Contract.newBuilder().setId(timeOff.contractId()).setCountry(country.name).setCompanyId(1L).build()
        every {  contractServiceAdapter.getContractsByIdsAnyStatus(setOf(timeOff.contractId()), true) } returns listOf(contract)

        val holiday = LegalEntityHoliday.Holiday.newBuilder()
            .setYear(2024)
            .setMonth(4)
            .setDate(25)
            .addAllContractIds(listOf(timeOff.contractId()))
            .build()
        every {
            holidayServiceAdapter.getHolidays(
                setOf(contract.id),
                2024,
                null)
        } returns listOf(holiday)

        val timeOffEntryId = "${timeOff.id}20240426".toLong()

        val timeOffEntries = timeOffEntryService.getTimeOffEntriesByIdsAndStatus(
            timeOffEntryIds = setOf(timeOffEntryId),
            statuses = setOf(timeOff.status()),
        )

        assertThat(timeOffEntries).hasSize(1)

        val entry = timeOffEntries[0]
        assertThat(entry.id).isEqualTo(*********)
        assertThat(entry.startSession.name).isEqualTo(TimeOffSession.MORNING.name)
        assertThat(entry.endSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
        assertThat(entry.noOfDays).isEqualTo(1.0)
        assertThat(entry.entryDate).isEqualTo(LocalDate.of(2024, 4, 26).toDate())
        assertThat(entry.effectiveDate).isEqualTo(LocalDate.of(2024, 4, 26).toDate())
        assertThat(entry.createdOn).isEqualTo(timeOff.createdOn().toTimestamp())
        assertThat(entry.createdBy).isEqualTo(timeOff.createdBy())
        assertThat(entry.updatedOn).isEqualTo(timeOff.updatedOn().toTimestamp())
        assertThat(entry.updatedBy).isEqualTo(timeOff.updatedBy())
        assertThat(entry.contractId).isEqualTo(timeOff.contractId())
        assertThat(entry.timeOffId).isEqualTo(timeOff.id())
        assertThat(entry.timeOffType.key).isEqualTo(timeOff.type().key())
        assertThat(entry.status.name).isEqualTo(timeOff.status().name)
        assertThat(entry.description).isEqualTo(timeOff.description())
        assertThat(entry.hasApprovedOn()).isFalse()
    }

    @Test
    fun `getTimeOffEntriesByTimeOffIdsAndStatus, should handle holidays by country when feature flag is off`() {
        // Time off Details:
        // 24 AFTERNOON session
        // 25 HOLIDAY
        // 26 FULL DAY
        // 27-28 WEEKENDS HOLIDAY
        // 29 FULL DAY
        // 30 MORNING session
        val startDate = LocalDate.of(2024, 4, 24)
        val endDate = LocalDate.of(2024, 4, 30)
        val startSession = TimeOffSession.AFTERNOON
        val endSession = TimeOffSession.MORNING
        val timeOff = getMockTimeOff(
            startDate = startDate,
            endDate = endDate,
            startSession = startSession,
            endSession = endSession,
        )

        // Feature flag is off
        every {
            featureFlagService.isOn(eq(FeatureFlags.HOLIDAYS_BY_ENTITY), any())
        } returns false

        every {
            timeoffRepository.findAllByIdInAndStatusIn(setOf(timeOff.id), setOf(timeOff.status()))
        } returns listOf(timeOff)

        val country = Country.GrpcCountryCode.IND
        val contract = ContractOuterClass.Contract.newBuilder()
            .setId(timeOff.contractId())
            .setCountry(country.name)
            .setCompanyId(1L)
            .build()
        every { contractServiceAdapter.getContractsByIdsAnyStatus(setOf(timeOff.contractId()), true) } returns listOf(contract)

        val holiday = HolidayOuterClass.Holiday.newBuilder()
            .setYear(2024)
            .setMonth(4)
            .setDate(25)
            .setCountryCode(Country.GrpcCountryCode.IND)
            .build()
        every {
            holidayServiceAdapter.getHolidays(
                setOf(country),
                2024,
                null,
                null
            )
        } returns listOf(holiday)

        val timeOffEntries = timeOffEntryService.getTimeOffEntriesByTimeOffIdsAndStatus(
            timeOffIds = setOf(timeOff.id),
            statuses = setOf(timeOff.status()),
        )

        assertThat(timeOffEntries).hasSize(4)

        // Verify the entries are correct
        val firstDay = timeOffEntries[0]
        assertThat(firstDay.id).isEqualTo(*********)
        assertThat(firstDay.entryDate).isEqualTo(LocalDate.of(2024, 4, 24).toDate())
        assertThat(firstDay.startSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
        assertThat(firstDay.endSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
        assertThat(firstDay.noOfDays).isEqualTo(0.5)

        val secondDay = timeOffEntries[1]
        assertThat(secondDay.id).isEqualTo(*********)
        assertThat(secondDay.entryDate).isEqualTo(LocalDate.of(2024, 4, 26).toDate())
        assertThat(secondDay.startSession.name).isEqualTo(TimeOffSession.MORNING.name)
        assertThat(secondDay.endSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
        assertThat(secondDay.noOfDays).isEqualTo(1.0)

        val thirdDay = timeOffEntries[2]
        assertThat(thirdDay.id).isEqualTo(120240429)
        assertThat(thirdDay.entryDate).isEqualTo(LocalDate.of(2024, 4, 29).toDate())
        assertThat(thirdDay.startSession.name).isEqualTo(TimeOffSession.MORNING.name)
        assertThat(thirdDay.endSession.name).isEqualTo(TimeOffSession.AFTERNOON.name)
        assertThat(thirdDay.noOfDays).isEqualTo(1.0)

        val finalDay = timeOffEntries[3]
        assertThat(finalDay.id).isEqualTo(120240430)
        assertThat(finalDay.entryDate).isEqualTo(LocalDate.of(2024, 4, 30).toDate())
        assertThat(finalDay.startSession.name).isEqualTo(TimeOffSession.MORNING.name)
        assertThat(finalDay.endSession.name).isEqualTo(TimeOffSession.MORNING.name)
        assertThat(finalDay.noOfDays).isEqualTo(0.5)
    }

    private fun getMockTimeOff(
        startDate: LocalDate,
        endDate: LocalDate,
        startSession: TimeOffSession,
        endSession: TimeOffSession,
        contractId: Long = 11L
    ): TimeoffDBO {
        val timeOffId = 1L
        val status = TimeOffStatus.APPROVED
        val timeOffTypeKey = "timeOffKey"
        val description = "description"
        val createdBy = -1L
        val updatedBy = -1L
        val createdOn = LocalDateTime.of(2024, 4, 1, 0, 0, 0)
        val updatedOn = LocalDateTime.of(2024, 4, 1, 0, 0, 0)

        return TimeoffDBO.builder()
            .id(timeOffId)
            .startDate(startDate)
            .endDate(endDate)
            .startSession(startSession)
            .endSession(endSession)
            .type(TimeoffTypeDBO.builder()
                .id(1L)
                .key(timeOffTypeKey)
                .label("label")
                .key("key")
                .description("description")
                .isPaidLeave(true)
                .build())
            .status(status)
            .contractId(contractId)
            .description(description)
            .createdBy(createdBy)
            .createdOn(createdOn)
            .updatedBy(updatedBy)
            .updatedOn(updatedOn)
            .build()
    }

    private fun holidayByEntityFeatureOn() {
        every {
            featureFlagService.isOn(eq(FeatureFlags.HOLIDAYS_BY_ENTITY), any())
        } returns true
    }

}
