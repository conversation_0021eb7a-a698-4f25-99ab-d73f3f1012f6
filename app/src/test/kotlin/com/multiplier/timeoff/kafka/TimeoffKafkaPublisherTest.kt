package com.multiplier.timeoff.kafka

import com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufDeserializer
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import com.multiplier.timeoff.config.EVENT_ID_KEY
import com.multiplier.timeoff.config.EVENT_TIMESTAMP_KEY
import com.multiplier.timeoff.config.kafka.properties.TimeoffKafkaProperties
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.kafka.proto.TimeoffEventMessageOuterClass.*
import com.multiplier.timeoff.schema.GrpcTimeOffStatus
import com.multiplier.timeoff.types.Contract
import com.multiplier.timeoff.types.TimeOff
import com.multiplier.timeoff.types.TimeOffStatus
import org.apache.kafka.clients.consumer.Consumer
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.common.header.Headers
import org.apache.kafka.common.serialization.StringDeserializer
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.mockito.kotlin.doReturn
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.kafka.core.DefaultKafkaConsumerFactory
import org.springframework.kafka.test.EmbeddedKafkaBroker
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.kafka.test.utils.KafkaTestUtils
import org.springframework.test.context.ActiveProfiles


@SpringBootTest
@ActiveProfiles("integration-test")
@EmbeddedKafka(kraft = false, partitions = 1, brokerProperties = ["listeners=PLAINTEXT://localhost:9092", "port=9092"])
class TimeoffKafkaPublisherTest {

    @Autowired
    private lateinit var broker: EmbeddedKafkaBroker

    @Autowired
    private lateinit var timeoffKafkaPublisher: TimeoffKafkaPublisher

    @Autowired
    private lateinit var kafkaProperties: TimeoffKafkaProperties

    @MockBean
    private lateinit var featureFlagService: FeatureFlagService

    private lateinit var consumer: Consumer<String, TimeoffEventMessage>

    @BeforeEach
    fun setUp() {
        val consumerFactory = DefaultKafkaConsumerFactory(
            KafkaTestUtils.consumerProps("test", "false", broker),
            StringDeserializer(),
            KafkaProtobufDeserializer(TimeoffEventMessage.parser())
        )

        consumer = consumerFactory.createConsumer()
        consumer.subscribe(listOf(kafkaProperties.topic))

        val gbResult = GBFeatureResult(true, true, false, GBFeatureSource.force)
        doReturn(gbResult).`when`(featureFlagService).feature(FeatureFlags.TIME_OFF_PUBLISH_EVENT, mapOf())
    }

//    @Test
//    @Disabled("temporarily disabled until dependency libs are updated")
//    fun can_publish_timeoff_update_event() {
//        // given
//        val timeoff = TimeOff.newBuilder()
//            .id(1L)
//            .contract(Contract.newBuilder().id(2L).build())
//            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
//            .build()
//
//        // when
//        timeoffKafkaPublisher.publishTimeoffUpdateEvent(timeoff)
//        val consumedMessage: ConsumerRecord<String, TimeoffEventMessage> = KafkaTestUtils.getSingleRecord(consumer, kafkaProperties.topic)
//
//        // then
//        val message: TimeoffEventMessage = consumedMessage.value()
//        val headers: Headers = consumedMessage.headers()
//        assertAll(
//            { assertEquals("1", consumedMessage.key()) },
//            { assertEquals(TimeoffEventType.TIMEOFF_UPDATED, message.eventType) },
//            { assertEquals(1L, message.event.timeoffId) },
//            { assertEquals(2L, message.event.contractId) },
//            { assertEquals(GrpcTimeOffStatus.APPROVAL_IN_PROGRESS, message.event.timeoffStatus) },
//            { assertNotNull(headers.headers(EVENT_ID_KEY).first()) },
//            { assertNotNull(headers.headers(EVENT_TIMESTAMP_KEY).first()) },
//        )
//    }
}