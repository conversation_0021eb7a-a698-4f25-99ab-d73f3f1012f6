package com.multiplier.timeoff

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.testing.HttpClient
import com.multiplier.testing.HttpConfig
import com.multiplier.testing.getResourceAsFile
import com.multiplier.timeoff.repository.CountryDefinitionRepository
import com.multiplier.timeoff.repository.model.CountryDefinitionEntity
import com.multiplier.timeoff.repository.model.DefinitionEntity
import com.multiplier.timeoff.types.CountryCode
import com.netflix.graphql.dgs.autoconfig.DgsAutoConfiguration
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.slot
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.test.context.ActiveProfiles

@ActiveProfiles("integration-test")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [TimeoffServiceApplication::class, DgsAutoConfiguration::class]
)
class UpdateCountryTimeOffDefinitionMutationTest(
    @LocalServerPort private val port: Int
) {
    @MockkBean
    private lateinit var countryDefinitionRepo: CountryDefinitionRepository

    private val httpClient = HttpClient(port)

    private val authorizedHttpConfig = HttpConfig(
            userId = 12345L,
            permissions = listOf("update.operations.timeoff-definition"),
            experience = "operations"
    )


    @Disabled("Temporally disabled due to a failure in only github pipeline")
    fun `should return an access denied error when the user does not have the appropriate role`() {
        val config = HttpConfig(
                userId = 12345L,
                permissions = listOf("update.not.timeoff"),
                experience = "company"
        )

        val csrfPreventionHeader = HttpHeaders()
        csrfPreventionHeader.add("apollo-require-preflight", "true")

        val response = httpClient.graphQLFileUpload<String>(
            httpConfig = config,
            query = "/queries/UpdateCountryTimeOffDefinitions.graphql",
            file = getResourceAsFile("/definitions/country-definitions.csv"),
            headers = csrfPreventionHeader
        )


        val json = ObjectMapper().readTree(response.body)
        val errorMessage = json.at("/errors/0/message").asText()

        assertThat(errorMessage)
            .withFailMessage("Expected error message to be 'access denied' but was '$errorMessage'")
            .isEqualToIgnoringCase("access denied")
    }


    @Test
    fun `should be able to upload new country timeoff definitions`() {
        val slot = slot<List<CountryDefinitionEntity>>()
        every { countryDefinitionRepo.findAll() } returns emptyList()
        every { countryDefinitionRepo.saveAll(capture(slot)) } answers { slot.captured }

        val csrfPreventionHeader = HttpHeaders()
        csrfPreventionHeader.add("apollo-require-preflight", "true")

        val response = httpClient.graphQLFileUpload<String>(
            httpConfig = authorizedHttpConfig,
            query = "/queries/UpdateCountryTimeOffDefinitions.graphql",
            file = getResourceAsFile("/definitions/country-definitions.csv"),
            headers = csrfPreventionHeader
        )

        val savedEntities = slot.captured

        assertThat(response.statusCode).isEqualTo(HttpStatus.OK)
        assertThat(savedEntities).hasSize(3)

        assertThat(savedEntities[0].definition?.countryCode).isNull()
        assertThat(savedEntities[0].definition?.stateCode).isNull()
        assertThat(savedEntities[0].typeId).isEqualTo(1L)

        assertThat(savedEntities[1].definition?.countryCode).isEqualTo(CountryCode.AUS)
        assertThat(savedEntities[1].definition?.stateCode).isNull()
        assertThat(savedEntities[1].typeId).isEqualTo(1L)

        assertThat(savedEntities[2].definition?.countryCode).isEqualTo(CountryCode.AUS)
        assertThat(savedEntities[2].definition?.stateCode).isNull()
        assertThat(savedEntities[2].typeId).isEqualTo(2L)
    }


    @Test
    fun `should be able to update existing country timeoff definitions`() {
        val slot = slot<List<CountryDefinitionEntity>>()

        val existingDefaultId = 555L
        val existingAustraliaId = 666L
        val existingAustraliaSickId = 777L

        every { countryDefinitionRepo.findAll() } returns listOf(
            CountryDefinitionEntity(
                id = existingDefaultId,
                typeId = 1L,
                definition = DefinitionEntity(
                    countryCode = null,
                    stateCode = null,
                ),
            ),
            CountryDefinitionEntity(
                id = existingAustraliaId,
                typeId = 1L,
                definition = DefinitionEntity(
                    countryCode = CountryCode.AUS,
                    stateCode = null,
                ),
            ),
            CountryDefinitionEntity(
                id = existingAustraliaSickId,
                typeId = 2L,
                definition = DefinitionEntity(
                    countryCode = CountryCode.AUS,
                    stateCode = null,
                ),
            )
        )
        every { countryDefinitionRepo.saveAll(capture(slot)) } answers { slot.captured }

        val csrfPreventionHeader = HttpHeaders()
        csrfPreventionHeader.add("apollo-require-preflight", "true")

        val response = httpClient.graphQLFileUpload<String>(
            httpConfig = authorizedHttpConfig,
            query = "/queries/UpdateCountryTimeOffDefinitions.graphql",
            file = getResourceAsFile("/definitions/country-definitions.csv"),
            headers = csrfPreventionHeader
        )

        val savedEntities = slot.captured

        assertThat(response.statusCode).isEqualTo(HttpStatus.OK)
        assertThat(savedEntities).hasSize(3)
        assertThat(savedEntities[0].id).isEqualTo(existingDefaultId)
        assertThat(savedEntities[1].id).isEqualTo(existingAustraliaId)
        assertThat(savedEntities[2].id).isEqualTo(existingAustraliaSickId)
    }

}