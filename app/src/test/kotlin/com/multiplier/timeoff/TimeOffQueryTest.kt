package com.multiplier.timeoff

import com.github.javafaker.Faker
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.UserContext
import com.multiplier.common.transport.user.UserScopes
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.testing.GraphQLTestClient
import com.multiplier.testing.HttpClient
import com.multiplier.testing.HttpConfig
import com.multiplier.testing.getJwt
import com.multiplier.timeoff.adapters.CompanyServiceAdapter
import com.multiplier.timeoff.adapters.CompanyUserFilters
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.repository.TimeoffRepository
import com.multiplier.timeoff.repository.TimeoffSummaryRepository
import com.multiplier.timeoff.repository.model.TimeoffDBO
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO
import com.multiplier.timeoff.types.ContractTimeOff
import com.netflix.graphql.dgs.autoconfig.DgsAutoConfiguration
import com.netflix.graphql.dgs.client.ErrorType
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.domain.Specification
import org.springframework.test.context.ActiveProfiles

@ActiveProfiles("integration-test")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [TimeoffServiceApplication::class, DgsAutoConfiguration::class]
)
class TimeOffQueryTest(
    @LocalServerPort private val port: Int
) {

    @MockkBean
    private lateinit var timeoffRepository: TimeoffRepository

    @MockkBean
    private lateinit var timeoffSummaryRepository: TimeoffSummaryRepository

    @MockkBean
    private lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockkBean
    private lateinit var companyServiceAdapter: CompanyServiceAdapter

    @MockkBean
    private lateinit var currentUser: CurrentUser

    private val graphqlClient = GraphQLTestClient(port)

    private val httpClient = HttpClient(port)


    @BeforeEach
    fun beforeEach() {
        every {
            timeoffRepository.findAll(
                any<Specification<TimeoffDBO>>(),
                any<Sort>()
            )
        } returns emptyList()

        every {
            timeoffSummaryRepository.findAll(
                any<Specification<TimeoffSummaryDBO>>(),
                any<Sort>()
            )
        } returns emptyList()
    }

    @Test
    fun `when the user is a company user, fetching timeoffs for a contract of a different company should return PERMISSION_DENIED`() {

        val userId = 12345L
        val companyId = 1111L
        val companyUserId = 1111L

        val contractId = 3333L
        val contractCompanyId = 2222L

        mockUserContext(userId, companyId, companyUserId, "view.company.time-off")
        mockGetContractAnyStatus(contractId, contractCompanyId)

        val response = graphqlClient.execute(
            query = "/queries/ContractTimeOffs.graphql",
            variables = contractTimeOffVariables(contractId),
            config = htpConfig(userId)
        )

        assertThat(response.errors.first().extensions?.errorType).isEqualTo(ErrorType.PERMISSION_DENIED)
    }

    @Test
    fun `when the user is a company user, should be able to fetch timeoffs for contract of own company`() {

        val userId = 12345L
        val companyId = 1111L
        val companyUserId = 1111L
        val contractId = 3333L

        mockUserContext(userId, companyId, companyUserId, "view.company.time-off")
        mockGetContractAnyStatus(contractId, companyId)

        val response = graphqlClient.execute(
            query = "/queries/ContractTimeOffs.graphql",
            variables = contractTimeOffVariables(contractId),
            config = htpConfig(userId)
        )

        val contractTimeOff = response.extractValueAsObject("data._entities[0].timeOff", ContractTimeOff::class.java)

        assertThat(contractTimeOff).isNotNull()
    }


    private fun mockUserContext(userId: Long, companyId: Long, companyUserId: Long, permission: String) {

        mockCurrentUserContext(userId, companyId, companyUserId, permission)
        mockGetCompanyUser(userId, companyId, companyUserId)
    }

    private fun mockCurrentUserContext(userId: Long, companyId: Long, companyUserId: Long, permission: String) {
        every {
            currentUser.context
        } returns
                UserContext(
                    id = userId,
                    scopes = UserScopes(
                        companyId = companyId,
                        companyUserId = companyUserId
                    ),
                    auth = permission,
                    experience = "company"
                )
    }

    private fun mockGetCompanyUser(userId: Long, companyId: Long, companyUserId: Long) {
        every {
            companyServiceAdapter.getCompanyUser(
                CompanyUserFilters(
                    id = companyId,
                    companyId = companyId,
                    userId = userId
                )
            )
        } returns CompanyOuterClass.CompanyUser.newBuilder()
            .setId(companyUserId)
            .setCompanyId(companyId)
            .build()
    }

    private fun mockGetContractAnyStatus(contractId: Long, companyId: Long) {
        every {
            contractServiceAdapter.getContractByIdAnyStatus(contractId)
        } returns ContractOuterClass.Contract.newBuilder()
            .setId(contractId)
            .setStarted(true)
            .setCompanyId(companyId)
            .build()
    }


    private fun contractTimeOffVariables(contractId: Long = Faker().number().randomNumber()) =
        mapOf(
            "representations" to listOf(
                mapOf(
                    "__typename" to "Contract",
                    "id" to "$contractId"
                )
            )
        )

    private fun htpConfig(userId: Long) = HttpConfig(
        userId = userId,
        permissions = listOf("view.company.time-off")
    )
}
