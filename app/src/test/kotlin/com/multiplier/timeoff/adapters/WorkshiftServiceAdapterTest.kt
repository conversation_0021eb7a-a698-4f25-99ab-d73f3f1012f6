package com.multiplier.timeoff.adapters

import com.multiplier.contract.schema.common.Common
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractServiceGrpc
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayNameGeneration
import org.junit.jupiter.api.DisplayNameGenerator
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentMatchers.any
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import java.time.DayOfWeek

@ExtendWith(MockitoExtension::class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores::class)
class WorkshiftServiceAdapterTest {

    @Mock
    private lateinit var mockStub: ContractServiceGrpc.ContractServiceBlockingStub

    @InjectMocks
    private lateinit var workshiftServiceAdapter: DefaultWorkshiftServiceAdapter

    @Test
    fun `getBasicContractById should return workshift from contract service`() {
        // Given
        val contractId = 123L

        val responseWorkshift = ContractOuterClass.WorkShift.newBuilder()
            .setStartDay(Common.DayOfWeek.MONDAY)
            .setEndDay(Common.DayOfWeek.FRIDAY)
            .build()

        val responseContract = ContractOuterClass.ContractBasicInfo.newBuilder()
            .setContractId(contractId).setWorkShift(responseWorkshift)
            .build()

        val response = ContractOuterClass.ListOfContractBasicInfos
            .newBuilder()
            .addContracts(responseContract)
            .build()

        `when`(mockStub.getBasicContractsByIds(any())).thenReturn(response)

        // When
        val result = workshiftServiceAdapter.getWorkshiftByContractId(contractId)

        // Then
        assertThat(result.startDate).isEqualTo(DayOfWeek.MONDAY)
        assertThat(result.endDate).isEqualTo(DayOfWeek.FRIDAY)
    }
}
