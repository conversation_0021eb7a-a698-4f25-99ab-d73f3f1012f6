package com.multiplier.timeoff.integration

import com.multiplier.grpc.common.toTimestamp
import com.multiplier.timeoff.schema.GrpcTimeOffStatus
import com.multiplier.timeoff.schema.grpc.timeoffentry.getTimeOffEntriesByIdsAndStatusRequest
import com.multiplier.timeoff.schema.grpc.timeoffentry.getTimeOffEntriesByTimeOffIdsAndStatusRequest
import com.multiplier.timeoff.schema.grpc.timeoffentry.getTimeOffEntriesByStatusAndUpdatedOnRequest
import com.multiplier.timeoff.schema.grpc.timeoffentry.timeOffEntry
import com.multiplier.timeoff.service.TimeOffEntryService
import com.multiplier.timeoff.types.TimeOffStatus
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDateTime

@ExtendWith(MockKExtension::class)
class GrpcTimeOffEntryServiceTest {
    @MockK
    private lateinit var timeOffEntryService: TimeOffEntryService

    @InjectMockKs
    private lateinit var grpcTimeOffEntryService: GrpcTimeOffEntryService

    @Test
    fun testGetTimeOffEntriesByStatusAndTimeOffIds_shouldReturnData() {
        val timeOffId = 1L
        val timeOffEntryId = 11L
        val request = getTimeOffEntriesByTimeOffIdsAndStatusRequest{
            this.timeOffIds += timeOffId
            this.status += GrpcTimeOffStatus.APPROVED
        }

        val timeOffEntry = timeOffEntry {
            this.timeOffId = timeOffId
            this.id = timeOffEntryId
        }

        every {
            timeOffEntryService.getTimeOffEntriesByTimeOffIdsAndStatus(
                statuses = setOf(TimeOffStatus.APPROVED),
                timeOffIds = setOf(timeOffId)

            )
        } returns listOf(timeOffEntry)

        val response = runBlocking {
            grpcTimeOffEntryService.getTimeOffEntriesByTimeOffIdsAndStatus(request)
        }

        assertThat(response.timeOffEntriesList).hasSize(1)
        assertThat(response.timeOffEntriesList[0]).isEqualTo(timeOffEntry)
    }

    @Test
    fun testGetTimeOffEntriesByStatusAndUpdatedOn_shouldReturnData() {
        val timeOffId = 1L
        val timeOffEntryId = 11L
        val startDate = LocalDateTime.of(2024, 4, 1, 0, 0, 0)
        val endDate = LocalDateTime.of(2024, 4, 30, 23, 59, 59)
        val request = getTimeOffEntriesByStatusAndUpdatedOnRequest{
            this.fromExclusive = startDate.toTimestamp()
            this.untilInclusive = endDate.toTimestamp()
            this.status += GrpcTimeOffStatus.APPROVED
        }

        val timeOffEntry = timeOffEntry {
            this.timeOffId = timeOffId
            this.id = timeOffEntryId
        }

        every {
            timeOffEntryService.getTimeOffEntriesByStatusAndUpdatedOn(
                statuses = setOf(TimeOffStatus.APPROVED),
                updatedOnFromExclusive = startDate,
                updatedOnToInclusive = endDate,
            )
        } returns listOf(timeOffEntry)

        val response = runBlocking {
            grpcTimeOffEntryService.getTimeOffEntriesByStatusAndUpdatedOn(request)
        }

        assertThat(response.timeOffEntriesList).hasSize(1)
        assertThat(response.timeOffEntriesList[0]).isEqualTo(timeOffEntry)
    }

    @Test
    fun testGetTimeOffEntriesByIdsAndStatus_shouldReturnData() {
        val timeOffEntryId = 11L
        val request = getTimeOffEntriesByIdsAndStatusRequest{
            this.ids += setOf(timeOffEntryId)
            this.status += GrpcTimeOffStatus.APPROVED
        }

        val timeOffEntry = timeOffEntry {
            this.timeOffId = timeOffId
            this.id = timeOffEntryId
        }

        every {
            timeOffEntryService.getTimeOffEntriesByIdsAndStatus(
                statuses = setOf(TimeOffStatus.APPROVED),
                timeOffEntryIds = setOf(timeOffEntryId),
            )
        } returns listOf(timeOffEntry)

        val response = runBlocking {
            grpcTimeOffEntryService.getTimeOffEntriesByIdsAndStatus(request)
        }

        assertThat(response.timeOffEntriesList).hasSize(1)
        assertThat(response.timeOffEntriesList[0]).isEqualTo(timeOffEntry)
    }
}
