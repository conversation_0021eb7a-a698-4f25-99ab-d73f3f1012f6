package com.multiplier.timeoff.integration

import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsResponse
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkResponse
import io.grpc.stub.StreamObserver
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class DeprecatedGrpcBulkTimeOffServiceTest {
    @MockK(relaxed = true)
    private lateinit var grpcBulkTimeOffService: GrpcBulkTimeOffService

    @InjectMockKs
    private lateinit var service: DeprecatedGrpcBulkTimeOffService

    @Test
    fun `calls grpcBulkTimeOffService#getFieldRequirements`() {
        val request = FieldRequirementsRequest.newBuilder().build()
        val responseObserver = mockk<StreamObserver<FieldRequirementsResponse>>(relaxed = true)

        service.getFieldRequirements(request, responseObserver)

        verify { grpcBulkTimeOffService.getFieldRequirements(request, responseObserver) }
    }

    @Test
    fun `calls grpcBulkTimeOffService#bulkValidateUpsertInput`() {
        val request = ValidateUpsertInputBulkRequest.newBuilder().build()
        val responseObserver = mockk<StreamObserver<ValidateUpsertInputBulkResponse>>(relaxed = true)

        service.bulkValidateUpsertInput(request, responseObserver)
        verify { grpcBulkTimeOffService.bulkValidateUpsertInput(request, responseObserver) }
    }

    @Test
    fun `calls grpcBulkTimeOffService#bulkUpsert`() {
        val request = UpsertBulkRequest.newBuilder().build()
        val responseObserver = mockk<StreamObserver<UpsertBulkResponse>>(relaxed = true)

        service.bulkUpsert(request, responseObserver)
        verify { grpcBulkTimeOffService.bulkUpsert(request, responseObserver) }
    }
}