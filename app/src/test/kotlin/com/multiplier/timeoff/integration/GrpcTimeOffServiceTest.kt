package com.multiplier.timeoff.integration

import com.google.type.Date
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractBasicInfo
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.core.common.dto.BulkTimeOffRequest
import com.multiplier.timeoff.core.common.dto.BulkUpsertTimeOffResult
import com.multiplier.timeoff.core.common.dto.BulkValidateTimeOffResult
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO
import com.multiplier.timeoff.schema.*
import com.multiplier.timeoff.service.TimeOffRequirementsService
import com.multiplier.timeoff.service.TimeoffCron
import com.multiplier.timeoff.service.TimeoffQuery
import com.multiplier.timeoff.service.TimeoffService
import com.multiplier.timeoff.service.TimeoffTypeService
import com.multiplier.timeoff.service.bulk.BulkTimeOffService
import com.multiplier.timeoff.types.TimeOffStatus
import io.grpc.stub.StreamObserver
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.Captor
import org.mockito.Mockito.mock
import java.time.LocalDate

@ExtendWith(MockKExtension::class)
class GrpcTimeOffServiceTest {

    @MockK
    private lateinit var requirementsSvc: TimeOffRequirementsService

    @MockK
    private lateinit var timeOffService: TimeoffService

    @MockK
    private lateinit var timeOffQuery: TimeoffQuery

    @MockK
    private lateinit var timeOffCron: TimeoffCron

    @MockK
    private lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    private lateinit var bulkTimeOffService: BulkTimeOffService
    @MockK
    private lateinit var timeOffTypeService: TimeoffTypeService

    @InjectMockKs
    private lateinit var grpcTimeOffService: GrpcTimeOffService

    val contractIds = setOf(123L, 234L)
    val startDate: LocalDate = LocalDate.of(2024, 1, 1)
    val endDate: LocalDate = LocalDate.of(2024, 1, 30)
    private val approvedOnFromInclusive: LocalDate = LocalDate.of(2024, 1, 1)
    private val approvedOnToInclusive: LocalDate = LocalDate.of(2024, 1, 15)
    private val statuses = setOf(TimeOffStatus.TAKEN, TimeOffStatus.APPROVED)

    @Test
    fun testGetTimeOffsForPayrollCycle_shouldReturnData() {
        val request = GetTimeOffsForPayrollCycleRequest.newBuilder()
            .addAllContractIds(contractIds)
            .setTimeOffRange(
                DateRange.newBuilder().setStartDate(startDate.toProtoDate())
                    .setEndDate(endDate.toProtoDate()).build()
            )
            .setApprovedOnFromInclusive(approvedOnFromInclusive.toProtoDate())
            .setApprovedOnToInclusive(approvedOnToInclusive.toProtoDate())
            .build()
        val responseObserver = mock(StreamObserver::class.java) as StreamObserver<GetTimeOffsForPayrollResponse>
        every {
            timeOffService.getTimeOffsForPayrollCycle(
                contractIds,
                statuses,
                startDate,
                endDate,
                approvedOnFromInclusive,
                approvedOnToInclusive
            )
        } returns
                emptyList()
        val res = grpcTimeOffService.getTimeOffsForPayrollCycle(request, responseObserver)
        verify(exactly = 1) {
            timeOffService.getTimeOffsForPayrollCycle(
                contractIds,
                statuses,
                startDate,
                endDate,
                approvedOnFromInclusive,
                approvedOnToInclusive
            )
        }
        Assertions.assertNotNull(res)
    }

    @Test
    fun testGetTimeOffsForPayrollCycle_shouldThrowException() {
        val request = GetTimeOffsForPayrollCycleRequest.newBuilder()
            .addAllContractIds(contractIds)
            .setTimeOffRange(
                DateRange.newBuilder().setStartDate(startDate.toProtoDate())
                    .setEndDate(endDate.toProtoDate()).build()
            )
            .setApprovedOnFromInclusive(approvedOnFromInclusive.toProtoDate())
            .setApprovedOnToInclusive(approvedOnToInclusive.toProtoDate())
            .build()
        val responseObserver = mock(StreamObserver::class.java) as StreamObserver<GetTimeOffsForPayrollResponse>
        every {
            timeOffService.getTimeOffsForPayrollCycle(
                contractIds,
                statuses,
                startDate,
                endDate,
                approvedOnFromInclusive,
                approvedOnToInclusive
            )
        } throws
                Exception()
        val res = grpcTimeOffService.getTimeOffsForPayrollCycle(request, responseObserver)
        verify(exactly = 1) {
            timeOffService.getTimeOffsForPayrollCycle(
                contractIds,
                statuses,
                startDate,
                endDate,
                approvedOnFromInclusive,
                approvedOnToInclusive
            )
        }
        Assertions.assertNotNull(res)
    }

    @Test
    fun `should throw error when request to set entitlements to default requirements`() {
        val request = GrpcContractIds.newBuilder()
            .addAllIds(listOf(1L, 2L))
            .build()
        val responseObserver = mock(StreamObserver::class.java) as StreamObserver<GrpcEmpty>
        every { timeOffService.setEntitlementsToDefaultRequirementsBulk(request.idsList) } throws  Exception()
        val res = grpcTimeOffService.setEntitlementsToDefaultRequirements(request, responseObserver)
        verify(exactly = 1) { timeOffService.setEntitlementsToDefaultRequirementsBulk(request.idsList) }
        Assertions.assertNotNull(res)

    }

    @Test
    fun `should set entitlements to default requirements`() {
        val request = GrpcContractIds.newBuilder()
            .addAllIds(listOf(1L, 2L))
            .build()
        val responseObserver = mock(StreamObserver::class.java) as StreamObserver<GrpcEmpty>
        every { timeOffService.setEntitlementsToDefaultRequirementsBulk(request.idsList) } returns Unit
        grpcTimeOffService.setEntitlementsToDefaultRequirements(request, responseObserver)
        verify(exactly = 1) { timeOffService.setEntitlementsToDefaultRequirementsBulk(request.idsList) }
    }

    @Test
    fun `should throw error when request to get company timeoff types`() {
        val companyId = 100L
        val responseObserver = mock(StreamObserver::class.java) as StreamObserver<GrpcCompanyTimeOffTypesResponse>
        every { timeOffTypeService.findTimeOffTypeDBOsForCompany(companyId) } throws Exception()
        grpcTimeOffService.getCompanyTimeOffTypes(GrpcCompanyTimeOffTypesRequest.newBuilder().setCompanyId(companyId).build(), responseObserver)
        verify(exactly = 1) { timeOffTypeService.findTimeOffTypeDBOsForCompany(companyId) }
    }

    @Test
    fun `should send company timeoff types when request to get company timeoff types`() {
        val companyId = 100L
        val timeoffTypes = listOf(
            TimeoffTypeDBO.builder().id(1L).key("ourAnnualLeave").label("Our Annual Leave").isPaidLeave(true).companyId(100L).build(),
            TimeoffTypeDBO.builder().id(2L).key("sickLeave").label("Sick Leave").isPaidLeave(false).build(),
        )
        val responseObserver = mock(StreamObserver::class.java) as StreamObserver<GrpcCompanyTimeOffTypesResponse>
        every { timeOffTypeService.findTimeOffTypeDBOsForCompany(companyId) } returns  timeoffTypes

        val res = grpcTimeOffService.getCompanyTimeOffTypes(GrpcCompanyTimeOffTypesRequest.newBuilder().setCompanyId(companyId).build(), responseObserver)

        Assertions.assertNotNull(res)
    }


    @Test
    fun `should revoke timeoffs in bulk`() {
        val request = GrpcBulkRevokeTimeOffRequest.newBuilder()
            .setCompanyId(1L)
            .addExternalTimeOffIds("test1")
            .build()
        val responseObserver = mock(StreamObserver::class.java) as StreamObserver<GrpcEmpty>


        every { bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(any(), any()) } returns Unit

        assertDoesNotThrow {
            grpcTimeOffService.bulkRevokeTimeOffs(request, responseObserver)
        }
    }

    @Test
    fun `should_handle_exception_when_service_methods_throws_the_exception`() {
        val request = GrpcBulkRevokeTimeOffRequest.newBuilder()
            .setCompanyId(1L)
            .addExternalTimeOffIds("test1")
            .build()
        val responseObserver = mock(StreamObserver::class.java) as StreamObserver<GrpcEmpty>

        every { bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(any(), any()) } throws Exception()
        grpcTimeOffService.bulkRevokeTimeOffs(request, responseObserver)
        verify { bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(request.companyId, request.externalTimeOffIdsList) }
    }

    @Test
    fun `should validate bulk timeoff upsert requests`() {
        val request = GrpcBulkTimeOffRequest.newBuilder()
            .setCompanyId(1L)
            .addAllInputs(listOf(
                GrpcBulkTimeOffInput.newBuilder().setExternalTimeOffId("EXT-011").setEmployeeId("E001").setStartDate("2024-05-03").setNoOfDays("3.0").setType("1").build(),
                GrpcBulkTimeOffInput.newBuilder().setExternalTimeOffId("EXT-012").setEmployeeId("E002").setStartDate("2024-06-03").setNoOfDays("3.5").setType("2").build(),
            ))
            .build()
        val expectedResult = BulkValidateTimeOffResult.builder()
            .items(listOf(
                BulkValidateTimeOffResult.BulkValidateTimeOffResultItem.builder().externalTimeOffId("EXT-011").errors(listOf("errors-1")).build(),
                BulkValidateTimeOffResult.BulkValidateTimeOffResultItem.builder().externalTimeOffId("EXT-012").errors(emptyList()).build(),
            ))
            .success(false)
            .build()
        val employeeToContractMap = mapOf("E001" to ContractBasicInfo.newBuilder().setContractId(100L).build(), "E002" to ContractBasicInfo.newBuilder().setContractId(101L).build())

        val responseObserver = mock(StreamObserver::class.java) as StreamObserver<GrpcBulkValidateTimeOffResponse>

        val bulkTimeOffRequestCaptor = slot<BulkTimeOffRequest>()


        every { bulkTimeOffService.validateBulkTimeOffs(capture(bulkTimeOffRequestCaptor)) } returns expectedResult
        every { contractServiceAdapter.getContractsByEmployeeIds(listOf("E001", "E002"), 1L) } returns employeeToContractMap

        grpcTimeOffService.bulkValidateTimeOffUpsert(request, responseObserver)

        val bulkTimeOffValidateInput = bulkTimeOffRequestCaptor.captured
        Assertions.assertEquals(1L, bulkTimeOffValidateInput.companyId)
        Assertions.assertEquals(2, bulkTimeOffValidateInput.inputs.size)
        Assertions.assertEquals("EXT-011", bulkTimeOffValidateInput.inputs[0].externalTimeOffId)
        Assertions.assertEquals(100L, bulkTimeOffValidateInput.inputs[0].contractId)
        Assertions.assertEquals("3.0", bulkTimeOffValidateInput.inputs[0].noOfDays)
        Assertions.assertEquals("2024-05-03", bulkTimeOffValidateInput.inputs[0].startDate)

        Assertions.assertEquals("EXT-012", bulkTimeOffValidateInput.inputs[1].externalTimeOffId)
        Assertions.assertEquals(101L, bulkTimeOffValidateInput.inputs[1].contractId)
        Assertions.assertEquals("3.5", bulkTimeOffValidateInput.inputs[1].noOfDays)
        Assertions.assertEquals("2024-06-03", bulkTimeOffValidateInput.inputs[1].startDate)
    }
    
    @Test
    fun `should upsert bulk timeoffs`() {
        val request = GrpcBulkTimeOffRequest.newBuilder()
            .setCompanyId(1L)
            .addAllInputs(listOf(
                GrpcBulkTimeOffInput.newBuilder().setExternalTimeOffId("EXT-011").setEmployeeId("E001").setStartDate("2024-05-03").setNoOfDays("3.0").setType("1").build(),
                GrpcBulkTimeOffInput.newBuilder().setExternalTimeOffId("EXT-012").setEmployeeId("E002").setStartDate("2024-06-03").setNoOfDays("3.5").setType("2").build(),
            ))
            .build()
        val expectedResult = BulkUpsertTimeOffResult.builder()
            .items(listOf(
                BulkUpsertTimeOffResult.Item.builder().externalTimeOffId("EXT-011").timeOffId(400L).errors(emptyList()).build(),
                BulkUpsertTimeOffResult.Item.builder().externalTimeOffId("EXT-012").timeOffId(401L).errors(emptyList()).build(),
            ))
            .success(true)
            .build()
        val employeeToContractMap = mapOf("E001" to ContractBasicInfo.newBuilder().setContractId(100L).build(), "E002" to ContractBasicInfo.newBuilder().setContractId(101L).build())

        val responseObserver = mock(StreamObserver::class.java) as StreamObserver<GrpcBulkTimeOffResponse>

        val bulkTimeOffRequestCaptor = slot<BulkTimeOffRequest>()


        every { bulkTimeOffService.bulkUpsertTimeOffs(capture(bulkTimeOffRequestCaptor)) } returns expectedResult
        every { contractServiceAdapter.getContractsByEmployeeIds(listOf("E001", "E002"), 1L) } returns employeeToContractMap

        grpcTimeOffService.bulkUpsertTimeOffs(request, responseObserver)

        val bulkTimeOffUpsertInput = bulkTimeOffRequestCaptor.captured
        Assertions.assertEquals(1L, bulkTimeOffUpsertInput.companyId)
        Assertions.assertEquals(2, bulkTimeOffUpsertInput.inputs.size)
        Assertions.assertEquals("EXT-011", bulkTimeOffUpsertInput.inputs[0].externalTimeOffId)
        Assertions.assertEquals(100L, bulkTimeOffUpsertInput.inputs[0].contractId)
        Assertions.assertEquals("3.0", bulkTimeOffUpsertInput.inputs[0].noOfDays)
        Assertions.assertEquals("2024-05-03", bulkTimeOffUpsertInput.inputs[0].startDate)

        Assertions.assertEquals("EXT-012", bulkTimeOffUpsertInput.inputs[1].externalTimeOffId)
        Assertions.assertEquals(101L, bulkTimeOffUpsertInput.inputs[1].contractId)
        Assertions.assertEquals("3.5", bulkTimeOffUpsertInput.inputs[1].noOfDays)
        Assertions.assertEquals("2024-06-03", bulkTimeOffUpsertInput.inputs[1].startDate)
    }


}

private fun LocalDate.toProtoDate() = Date.newBuilder().setDay(this.dayOfMonth)
    .setMonth(this.monthValue)
    .setYear(this.year).build()
