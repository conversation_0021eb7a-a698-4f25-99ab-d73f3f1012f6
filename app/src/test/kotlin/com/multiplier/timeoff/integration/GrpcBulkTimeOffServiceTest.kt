package com.multiplier.timeoff.integration

import com.multiplier.grpc.common.bulkupload.v1.BulkDataRequest
import com.multiplier.grpc.common.bulkupload.v1.BulkDataResponse
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirement
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsResponse
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.UpsertBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.UpsertRequest
import com.multiplier.grpc.common.bulkupload.v1.UpsertResponse
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputRequest
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputResponse
import com.multiplier.timeoff.integration.exception.GrpcExceptionWrapper
import com.multiplier.timeoff.service.bulk.BulkTimeOffServiceV2
import io.grpc.stub.StreamObserver
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mockito
import org.mockito.kotlin.any

@ExtendWith(MockKExtension::class)
class GrpcBulkTimeOffServiceTest {
    @MockK
    private lateinit var bulkTimeOffServiceV2: BulkTimeOffServiceV2

    @InjectMockKs
    private lateinit var grpcBulkTimeOffService: GrpcBulkTimeOffService

    @Test
    fun `bulkValidateUpsertInput should return expected result when service method does not throw exception`() {
        val inputId = "1"

        val request = ValidateUpsertInputBulkRequest.newBuilder()
                .addAllInputs(listOf(
                        ValidateUpsertInputRequest.newBuilder()
                                .setInputId(inputId)
                                .build()
                ))
                .build()

        val expectedResult = ValidateUpsertInputBulkResponse.newBuilder()
                .addAllResults(listOf(
                        ValidateUpsertInputResponse.newBuilder()
                                .setInputId(inputId)
                                .setSuccess(true)
                                .addAllMessages(emptyList())
                                .build()
                ))
                .build()

        val responseObserver = Mockito.mock(StreamObserver::class.java) as StreamObserver<ValidateUpsertInputBulkResponse>

        every { bulkTimeOffServiceV2.validateBulkTimeOffs(any()) } returns expectedResult

        grpcBulkTimeOffService.bulkValidateUpsertInput(request, responseObserver)

        assertEquals(true, expectedResult.getResults(0).success,)
        Mockito.verify(responseObserver).onCompleted()
    }

    @Test
    fun `bulkValidateUpsertInput should throw StatusRuntimeException when service method throws exception`() {
        val request = ValidateUpsertInputBulkRequest.newBuilder()
                .addAllInputs(listOf(
                        ValidateUpsertInputRequest.newBuilder().setInputId("1").putAllData(mapOf("key" to "value")).build()
                ))
                .build()

        val responseObserver = Mockito.mock(StreamObserver::class.java) as StreamObserver<ValidateUpsertInputBulkResponse>

        every { bulkTimeOffServiceV2.validateBulkTimeOffs(any()) } throws Exception()

        grpcBulkTimeOffService.bulkValidateUpsertInput(request, responseObserver)

        Mockito.verify(responseObserver).onError(any<GrpcExceptionWrapper>())
    }

    @Test
    fun `bulkUpsert should return expected result when service method does not throw exception`() {
        val inputId = "1"

        val request = UpsertBulkRequest.newBuilder()
                .addAllInputs(listOf(
                        UpsertRequest.newBuilder()
                                .setInputId(inputId)
                                .build()
                ))
                .build()

        val expectedResult = UpsertBulkResponse.newBuilder()
                .addAllResults(listOf(
                        UpsertResponse.newBuilder()
                                .setInputId(inputId)
                                .setSuccess(true)
                                .build()
                ))
                .build()

        val responseObserver = Mockito.mock(StreamObserver::class.java) as StreamObserver<UpsertBulkResponse>

        every { bulkTimeOffServiceV2.bulkUpsertTimeOffs(any()) } returns expectedResult

        grpcBulkTimeOffService.bulkUpsert(request, responseObserver)

        assertEquals(true, expectedResult.getResults(0).success)
        Mockito.verify(responseObserver).onCompleted()
    }

    @Test
    fun `bulkUpsert should throw StatusRuntimeException when service method throws exception`() {
        val request = UpsertBulkRequest.newBuilder()
                .addAllInputs(listOf(
                        UpsertRequest.newBuilder().setInputId("1").putAllData(mapOf("key" to "value")).build()
                ))
                .build()

        val responseObserver = Mockito.mock(StreamObserver::class.java) as StreamObserver<UpsertBulkResponse>

        every { bulkTimeOffServiceV2.bulkUpsertTimeOffs(any()) } throws Exception()

        grpcBulkTimeOffService.bulkUpsert(request, responseObserver)

        Mockito.verify(responseObserver).onError(any<GrpcExceptionWrapper>())
    }

    @Test
    fun `getFieldRequirements should return expected result when service method does not throw exception`() {
        val request = FieldRequirementsRequest.newBuilder()
                .setCompanyId(123L)
                .build()

        val expectedResult = FieldRequirementsResponse.newBuilder()
                .addFieldRequirements(FieldRequirement.newBuilder()
                        .setKey("TimeOff_ID")
                        .setLabel("TimeOff ID")
                        .setMandatory(true)
                        .setDescription("Mandatory- Unique Identifier for a timeoff")
                        .build()
                )
                .build()

        val responseObserver = mockk<StreamObserver<FieldRequirementsResponse>>(relaxed = true)

        every { bulkTimeOffServiceV2.getFieldRequirements(any()) } returns expectedResult

        grpcBulkTimeOffService.getFieldRequirements(request, responseObserver)

        verify { responseObserver.onNext(expectedResult) }
        verify { responseObserver.onCompleted() }
    }

    @Test
    fun `getFieldRequirements should throw StatusRuntimeException when service method throws exception`() {
        val request = FieldRequirementsRequest.newBuilder()
                .setCompanyId(123L)
                .build()

        val responseObserver = mockk<StreamObserver<FieldRequirementsResponse>>(relaxed = true)

        every { bulkTimeOffServiceV2.getFieldRequirements(any()) } throws Exception()

        grpcBulkTimeOffService.getFieldRequirements(request, responseObserver)

        verify { responseObserver.onError(any<GrpcExceptionWrapper>()) }
    }

    @Test
    fun `getFieldData should return expected result when service method does not throw exception`() {
        val request = BulkDataRequest.newBuilder()
            .addAllCompanyIds(listOf(123L))
            .build()

        val expectedResult = BulkDataResponse.newBuilder().build()

        val responseObserver = mockk<StreamObserver<BulkDataResponse>>(relaxed = true)

        grpcBulkTimeOffService.getFieldData(request, responseObserver)

        verify { responseObserver.onNext(expectedResult) }
        verify { responseObserver.onCompleted() }
    }
}