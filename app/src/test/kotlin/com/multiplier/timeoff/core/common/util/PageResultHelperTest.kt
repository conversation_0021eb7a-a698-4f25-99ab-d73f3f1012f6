package com.multiplier.timeoff.core.common.util

import com.multiplier.timeoff.repository.model.TimeoffDBO
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.data.domain.Page

internal class PageResultHelperTest  {
    @Test
    internal fun `builds PageResult from spring Page`() {
        val input : Page<TimeoffDBO> = Page.empty()
        val page = PageResultHelper.build(input)
        assertThat(page.pageNumber).isEqualTo(0)
        assertThat(page.pageSize).isEqualTo(0)
        assertThat(page.count).isEqualTo(0)
    }
}
