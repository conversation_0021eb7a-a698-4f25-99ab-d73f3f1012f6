package com.multiplier.timeoff.core.common.util

import com.multiplier.timeoff.types.Order
import com.multiplier.timeoff.types.PageRequest
import com.multiplier.timeoff.types.Sort
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class PageRequestHelperTest {
    val pageRequestHelper = PageRequestHelper()

    @Test
    internal fun `returns spring PageRequest when all inputs are passed`() {
        val sort = Sort("id", Order.DESC)
        val graphPageRequest = PageRequest(10, 2, listOf(sort))

        val result = pageRequestHelper.toSpringPageRequestWithDefaultSort(graphPageRequest, "id")

        assertThat(result.pageNumber).isEqualTo(2)
        assertThat(result.pageSize).isEqualTo(10)
        assertThat(result.sort).isEqualTo(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "id"))
    }

    @Test
    internal fun `adds default pageSize, pageNumber and sort if not passed`() {
        val result = pageRequestHelper.toSpringPageRequestWithDefaultSort(null, "id")

        assertThat(result.pageNumber).isEqualTo(0)
        assertThat(result.pageSize).isEqualTo(10)
        assertThat(result.sort).isEqualTo(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "id"))
    }
}
