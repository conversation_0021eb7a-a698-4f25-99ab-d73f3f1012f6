package com.multiplier.timeoff.processor

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.repository.EntitlementChangeRecordRepository
import com.multiplier.timeoff.repository.model.EntitlementChangeCategory
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO
import com.multiplier.timeoff.service.CarryForwardLeaveService
import com.multiplier.timeoff.service.TimeoffSummaryService
import com.multiplier.timeoff.service.dto.CarryForwardResult
import com.multiplier.timeoff.types.CarryForwardConfig
import com.multiplier.timeoff.types.TimeOffAllocationInput
import com.multiplier.timeoff.types.TimeOffDuration
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import java.time.LocalDate
import java.util.*

@ExtendWith(MockitoExtension::class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores::class)
internal class CarryForwardProcessorTest {

    @Mock
    private lateinit var timeoffSummaryService: TimeoffSummaryService
    @Mock
    private lateinit var carryForwardLeaveService: CarryForwardLeaveService
    @Mock
    private lateinit var entitlementChangeRecordRepo: EntitlementChangeRecordRepository
    @Mock
    private lateinit var contractServiceAdapter: ContractServiceAdapter
    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @InjectMocks
    private lateinit var underTest: CarryForwardProcessor

    companion object {
        private val TEST_CONTRACT = ContractOuterClass.Contract.newBuilder().setId(123456).setCompanyId(345).build()
        private val TEST_EXPIRY_DATE = YEAR_2023.START
        private val TEST_ALLOCATION_INPUT_NO_COMPANIES = TimeOffAllocationInput.newBuilder().expiryDate(TEST_EXPIRY_DATE).build()

        private object YEAR_2022 {
            val START = LocalDate.of(2022, 1, 1)
            val END = LocalDate.of(2022, 12, 31)
        }

        private object YEAR_2023 {
            val START = LocalDate.of(2023, 1, 1)
            val MID = LocalDate.of(2023, 5, 31)
            val END = LocalDate.of(2023, 12, 31)
        }

        private val EXPIRED_SUMMARY = TimeoffSummaryDBO.builder()
            .typeId(1)
            .contractId(TEST_CONTRACT.id)
            .periodStart(YEAR_2022.START)
            .periodEnd(YEAR_2022.END)
            .build()

        private val NEW_SUMMARY = TimeoffSummaryDBO.builder()
            .id(999)
            .typeId(1)
            .contractId(TEST_CONTRACT.id)
            .periodStart(YEAR_2023.START)
            .periodEnd(YEAR_2023.END)
            .allocatedCount(15.0)
            .build()
    }

    @Nested
    inner class CarryForwardTimeoffBalance {

        @Test
        fun should_look_for_non_expired_timeoff_from_contracts_of_given_company_ids() {
            // given
            turnCarryForwardExpiryFeatureFlagOn()
            val companyIds = mutableListOf(111L, 222L)
            val contractIds = listOf(1L, 2L, 3L, 4L)
            doReturn(contractIds).`when`(contractServiceAdapter).getContractIdsByCompanyIds(companyIds)
            val contracts = listOf(
                ContractOuterClass.Contract.newBuilder().setId(1).build(),
                ContractOuterClass.Contract.newBuilder().setId(2).build(),
                ContractOuterClass.Contract.newBuilder().setId(3).build(),
                ContractOuterClass.Contract.newBuilder().setId(4).build()
            )
            doReturn(contracts).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIdsInChunks(contractIds)

            // when
            underTest.carryForwardTimeoffBalances(
                TimeOffAllocationInput.newBuilder()
                    .expiryDate(TEST_EXPIRY_DATE)
                    .companyIds(mutableListOf(111, 222))
                    .build()
            )

            // then
            val captor = argumentCaptor<Collection<Long>>()
            verify(timeoffSummaryService).getSummariesNotExpiredOnOrAfterForContracts(
                eq(TEST_EXPIRY_DATE),
                captor.capture()
            )
            assertThat(captor.firstValue).hasSameElementsAs(contractIds)
        }

        @Test
        fun should_look_for_non_expired_timeoff_from_all_contracts_when_company_ids_are_not_given() {
            // when
            underTest.carryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(timeoffSummaryService).getSummariesNotExpiredOnOrAfter(TEST_EXPIRY_DATE)
        }

        @Nested
        inner class WithNotYetCarriedSummariesFoundAndFeatureFlagOn {

            @BeforeEach
            fun setUp() {
                turnCarryForwardExpiryFeatureFlagOn()

                doReturn(listOf(NEW_SUMMARY)).`when`(timeoffSummaryService).getSummariesNotExpiredOnOrAfter(TEST_EXPIRY_DATE)
                doReturn(listOf(TEST_CONTRACT)).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIdsInChunks(listOf(TEST_CONTRACT.id))
            }

            @Test
            fun should_carry_forward_leaves_and_record_carry_forward_event() {
                // given
                doReturn(emptyList<Long>()).`when`(entitlementChangeRecordRepo).findAllRefIdsIn(any())
                doReturn(EXPIRED_SUMMARY).`when`(timeoffSummaryService).getPredecessor(NEW_SUMMARY)
                val carryForwardResult = CarryForwardResult(5.0, YEAR_2023.MID)
                doReturn(carryForwardResult).`when`(timeoffSummaryService)
                    .carryForwardLeaves(EXPIRED_SUMMARY, NEW_SUMMARY, TEST_CONTRACT)

                // when
                underTest.carryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

                // then
                verify(timeoffSummaryService).carryForwardLeaves(EXPIRED_SUMMARY, NEW_SUMMARY, TEST_CONTRACT)
                verifyCarryForwardEventIsRecorded()
            }

            @Test
            fun should_do_nothing_given_all_carry_forward_have_been_done() {
                // given
                doReturn(listOf(NEW_SUMMARY.id)).`when`(entitlementChangeRecordRepo)
                    .findAllRefIdsIn(listOf(NEW_SUMMARY.id))

                // when
                underTest.carryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

                // then
                verify(timeoffSummaryService, never()).carryForwardLeaves(any(), any(), any())
                verify(entitlementChangeRecordRepo, never()).save(any())
            }

            @Test
            fun should_not_record_carry_forward_event_given_carry_forward_not_successful() {
                // given
                doReturn(emptyList<Long>()).`when`(entitlementChangeRecordRepo).findAllRefIdsIn(any())
                doReturn(EXPIRED_SUMMARY).`when`(timeoffSummaryService).getPredecessor(NEW_SUMMARY)
                doThrow(RuntimeException::class).`when`(timeoffSummaryService)
                    .carryForwardLeaves(EXPIRED_SUMMARY, NEW_SUMMARY, TEST_CONTRACT)

                // when
                underTest.carryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

                // then
                verify(entitlementChangeRecordRepo, never()).save(any())
            }
        }

        @Test
        fun should_do_nothing_given_no_expired_timeoff_balance_found() {
            // when
            underTest.carryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(timeoffSummaryService, never()).carryForwardLeaves(any(), any(), any())
            verify(entitlementChangeRecordRepo, never()).save(any())
        }

        @Test
        fun should_do_nothing_given_feature_flag_is_off() {
            // given
            val result = GBFeatureResult(value = false, on = false, source = GBFeatureSource.force)
            Mockito.doReturn(result)
                .`when`(featureFlagService).feature(eq(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY), any())
            doReturn(listOf(TEST_CONTRACT)).`when`(contractServiceAdapter)
                .getNonDeletedNonEndedContractsByIdsInChunks(listOf(TEST_CONTRACT.id))
            doReturn(listOf(NEW_SUMMARY)).`when`(timeoffSummaryService)
                .getSummariesNotExpiredOnOrAfter(TEST_EXPIRY_DATE)

            // when
            underTest.carryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(timeoffSummaryService, never()).carryForwardLeaves(any(), any(), any())
            verify(entitlementChangeRecordRepo, never()).save(any())
        }
    }

    private fun turnCarryForwardExpiryFeatureFlagOn() {
        val result = GBFeatureResult(value = true, on = true, source = GBFeatureSource.force)
        Mockito.doReturn(result).`when`(featureFlagService)
            .feature(eq(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY), any())
    }

    private fun verifyCarryForwardEventIsRecorded() {
        val captor = argumentCaptor<EntitlementChangeRecordEntity>()
        verify(entitlementChangeRecordRepo).save(captor.capture())
        val changeRecordEntity = captor.firstValue
        assertAll(
            { assertEquals(1, changeRecordEntity.typeId) },
            { assertEquals(TEST_CONTRACT.id, changeRecordEntity.contractId) },
            { assertEquals(EntitlementChangeCategory.CARRY_FORWARD, changeRecordEntity.category) },
            { assertEquals(5.0, changeRecordEntity.count) },
            { assertEquals(YEAR_2023.START, changeRecordEntity.validFrom) },
            { assertEquals(YEAR_2023.MID, changeRecordEntity.validToInclusive) },
            { assertEquals(NEW_SUMMARY.id, changeRecordEntity.refId) }
        )
    }

    @Nested
    inner class BackfillCarryForwardRecords {
        @BeforeEach
        fun setUp() {
            turnCarryForwardExpiryFeatureFlagOn()

            doReturn(listOf(NEW_SUMMARY)).`when`(timeoffSummaryService).getSummariesNotExpiredOnOrAfter(TEST_EXPIRY_DATE)
            doReturn(listOf(TEST_CONTRACT)).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIdsInChunks(listOf(TEST_CONTRACT.id))
        }

        @Test
        fun should_record_carry_forward_event_given_carry_forward_leave_count_is_greater_than_zero() {
            // given
            doReturn(emptyList<Long>()).`when`(entitlementChangeRecordRepo).findAllRefIdsIn(any())
            NEW_SUMMARY.carriedCount(5.0)
            doReturn(
                Optional.of(CarryForwardConfig.newBuilder()
                .expiry(TimeOffDuration.newBuilder().build())
                .build())).`when`(carryForwardLeaveService).getCarryForwardConfig(TEST_CONTRACT, NEW_SUMMARY.typeId())
            doReturn(YEAR_2023.MID).`when`(carryForwardLeaveService).getLastValidDate(eq(YEAR_2023.START), any())

            // when
            underTest.backfillCarryForwardRecords(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verifyCarryForwardEventIsRecorded()
        }

        @Test
        fun should_not_record_carry_forward_event_given_carry_forward_leave_count_is_zero() {
            // given
            doReturn(emptyList<Long>()).`when`(entitlementChangeRecordRepo).findAllRefIdsIn(any())
            NEW_SUMMARY.carriedCount(0.0)

            // when
            underTest.backfillCarryForwardRecords(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(entitlementChangeRecordRepo, never()).save(any())
        }

        @Test
        fun should_not_record_carry_forward_event_given_carry_forward_leave_count_is_null() {
            // given
            doReturn(emptyList<Long>()).`when`(entitlementChangeRecordRepo).findAllRefIdsIn(any())

            // when
            underTest.backfillCarryForwardRecords(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(entitlementChangeRecordRepo, never()).save(any())
        }
    }
}