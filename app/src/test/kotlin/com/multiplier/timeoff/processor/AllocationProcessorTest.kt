package com.multiplier.timeoff.processor

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.repository.EntitlementChangeRecordRepository
import com.multiplier.timeoff.repository.model.EntitlementChangeCategory
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO
import com.multiplier.timeoff.service.TimeoffSummaryService
import com.multiplier.timeoff.types.TimeOffAllocationInput
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.eq
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import java.time.LocalDate

@ExtendWith(MockitoExtension::class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores::class)
internal class AllocationProcessorTest {

    @Mock
    private lateinit var timeoffSummaryService: TimeoffSummaryService
    @Mock
    private lateinit var entitlementChangeRecordRepo: EntitlementChangeRecordRepository
    @Mock
    private lateinit var contractServiceAdapter: ContractServiceAdapter
    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @InjectMocks
    private lateinit var underTest: AllocationProcessor

    companion object {
        private val TEST_CONTRACT = ContractOuterClass.Contract.newBuilder().setId(123456).setCompanyId(345).build()
        private val TEST_EXPIRY_DATE = YEAR_2023.START
        private val TEST_ALLOCATION_INPUT_NO_COMPANIES = TimeOffAllocationInput.newBuilder().expiryDate(TEST_EXPIRY_DATE).build()

        private object YEAR_2022 {
            val START = LocalDate.of(2022, 1, 1)
            val END = LocalDate.of(2022, 12, 31)
        }

        private object YEAR_2023 {
            val START = LocalDate.of(2023, 1, 1)
            val END = LocalDate.of(2023, 12, 31)
        }

        private val EXPIRED_SUMMARY = TimeoffSummaryDBO.builder()
            .typeId(1)
            .contractId(TEST_CONTRACT.id)
            .periodStart(YEAR_2022.START)
            .periodEnd(YEAR_2022.END)
            .build()

        private val NEW_SUMMARY = TimeoffSummaryDBO.builder()
            .typeId(1)
            .contractId(TEST_CONTRACT.id)
            .periodStart(YEAR_2023.START)
            .periodEnd(YEAR_2023.END)
            .allocatedCount(15.0)
            .build()
    }
    
    @Nested
    inner class AllocateTimeoffBalances {

        @Test
        fun should_look_for_expired_summaries_from_contracts_of_given_company_ids() {
            // given
            turnCarryForwardExpiryFeatureFlagOn()
            val companyIds = mutableListOf(111L, 222L)
            val contractIds = listOf(1L, 2L, 3L, 4L)
            doReturn(contractIds).`when`(contractServiceAdapter).getContractIdsByCompanyIds(companyIds)
            val contracts = listOf(
                ContractOuterClass.Contract.newBuilder().setId(1).build(),
                ContractOuterClass.Contract.newBuilder().setId(2).build(),
                ContractOuterClass.Contract.newBuilder().setId(3).build(),
                ContractOuterClass.Contract.newBuilder().setId(4).build())
            doReturn(contracts).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIdsInChunks(contractIds)

            // when
            underTest.allocateTimeoffBalances(TimeOffAllocationInput.newBuilder()
                .expiryDate(TEST_EXPIRY_DATE)
                .companyIds(mutableListOf(111, 222))
                .build())

            // then
            verify(timeoffSummaryService).getLatestSummariesExpiredBeforeForContracts(TEST_EXPIRY_DATE, contractIds)
        }

        @Test
        fun should_look_for_expired_summaries_from_all_contracts_when_company_ids_are_not_given() {
            // when
            underTest.allocateTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(timeoffSummaryService).getLatestSummariesExpiredBefore(TEST_EXPIRY_DATE)
        }

        @Nested
        inner class WithExpiredSummariesFoundAndFeatureFlagOn {

            @BeforeEach
            fun setUp() {
                turnCarryForwardExpiryFeatureFlagOn()

                doReturn(listOf(EXPIRED_SUMMARY)).`when`(timeoffSummaryService).getLatestSummariesExpiredBefore(TEST_EXPIRY_DATE)
                doReturn(listOf(TEST_CONTRACT)).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIdsInChunks(listOf(TEST_CONTRACT.id))
            }

            @Test
            fun should_allocate_timeoff_balance_and_record_allocation_event() {
                // given
                doReturn(NEW_SUMMARY).`when`(timeoffSummaryService).reallocateTimeoff(EXPIRED_SUMMARY)

                // when
                underTest.allocateTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

                // then
                verify(timeoffSummaryService).reallocateTimeoff(EXPIRED_SUMMARY)
                verifyAllocationEventIsRecorded()
            }

            @Test
            fun should_not_record_allocation_event_given_allocation_does_not_happen() {
                // when
                underTest.allocateTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

                // then
                verify(entitlementChangeRecordRepo, never()).save(any())
            }
        }

        @Test
        fun should_do_nothing_given_no_expired_timeoff_balance_found() {
            // when
            underTest.allocateTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(timeoffSummaryService, never()).reallocateTimeoff(any())
            verify(entitlementChangeRecordRepo, never()).save(any())
        }

        @Test
        fun should_do_nothing_given_feature_flag_is_off() {
            // given
            turnCarryForwardExpiryFeatureFlagOff()
            doReturn(listOf(EXPIRED_SUMMARY)).`when`(timeoffSummaryService).getLatestSummariesExpiredBefore(TEST_EXPIRY_DATE)
            doReturn(listOf(TEST_CONTRACT)).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIdsInChunks(listOf(TEST_CONTRACT.id))

            // when
            underTest.allocateTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(timeoffSummaryService, never()).reallocateTimeoff(any())
            verify(entitlementChangeRecordRepo, never()).save(any())
        }
    }

    private fun turnCarryForwardExpiryFeatureFlagOn() {
        val result = GBFeatureResult(value = true, on = true, source = GBFeatureSource.force)
        Mockito.doReturn(result).`when`(featureFlagService)
            .feature(eq(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY), any())
    }

    private fun turnCarryForwardExpiryFeatureFlagOff() {
        val result = GBFeatureResult(value = false, on = false, source = GBFeatureSource.force)
        Mockito.doReturn(result).`when`(featureFlagService)
            .feature(eq(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY), any())
    }

    private fun verifyAllocationEventIsRecorded() {
        val captor = ArgumentCaptor.forClass(EntitlementChangeRecordEntity::class.java)
        verify(entitlementChangeRecordRepo).save(captor.capture())
        val changeRecordEntity = captor.value
        assertAll(
            { assertEquals(1, changeRecordEntity.typeId) },
            { assertEquals(TEST_CONTRACT.id, changeRecordEntity.contractId) },
            { assertEquals(EntitlementChangeCategory.ALLOCATION, changeRecordEntity.category) },
            { assertEquals(15.0, changeRecordEntity.count) },
            { assertEquals(YEAR_2023.START, changeRecordEntity.validFrom) },
            { assertEquals(YEAR_2023.END, changeRecordEntity.validToInclusive) }
        )
    }

    @Nested
    inner class BackfillAllocationRecords {

        @Test
        fun should_look_for_non_expired_summaries_from_contracts_of_given_company_ids() {
            // given
            turnCarryForwardExpiryFeatureFlagOn()
            val companyIds = mutableListOf(111L, 222L)
            val contractIds = listOf(1L, 2L, 3L, 4L)
            doReturn(contractIds).`when`(contractServiceAdapter).getContractIdsByCompanyIds(companyIds)
            val contracts = listOf(
                ContractOuterClass.Contract.newBuilder().setId(1).build(),
                ContractOuterClass.Contract.newBuilder().setId(2).build(),
                ContractOuterClass.Contract.newBuilder().setId(3).build(),
                ContractOuterClass.Contract.newBuilder().setId(4).build())
            doReturn(contracts).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIdsInChunks(contractIds)

            // when
            underTest.backfillAllocationRecords(TimeOffAllocationInput.newBuilder()
                .expiryDate(TEST_EXPIRY_DATE)
                .companyIds(companyIds)
                .build())

            // then
            verify(timeoffSummaryService).getSummariesNotExpiredOnOrAfterForContracts(TEST_EXPIRY_DATE, contractIds)
        }

        @Test
        fun should_look_for_non_expired_summaries_from_all_contracts_when_company_ids_are_not_given() {
            // when
            underTest.backfillAllocationRecords(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(timeoffSummaryService).getSummariesNotExpiredOnOrAfter(TEST_EXPIRY_DATE)
        }

        @Nested
        inner class WithNonExpiredSummariesFoundAndFeatureFlagOn {

            @BeforeEach
            fun setUp() {
                turnCarryForwardExpiryFeatureFlagOn()

                doReturn(listOf(NEW_SUMMARY)).`when`(timeoffSummaryService).getSummariesNotExpiredOnOrAfter(TEST_EXPIRY_DATE)
                doReturn(listOf(TEST_CONTRACT)).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIdsInChunks(listOf(TEST_CONTRACT.id))
            }

            @Test
            fun should_create_allocation_record_if_not_yet_done() {
                // when
                underTest.backfillAllocationRecords(TEST_ALLOCATION_INPUT_NO_COMPANIES)

                // then
                verifyAllocationEventIsRecorded()
            }

            @Test
            fun should_not_create_allocation_record_if_already_done() {
                // given
                doReturn(EntitlementChangeRecordEntity()).`when`(entitlementChangeRecordRepo).findByTypeIdAndContractIdAndCategoryAndValidFromAndValidToInclusive(
                    NEW_SUMMARY.typeId(),
                    NEW_SUMMARY.contractId(),
                    EntitlementChangeCategory.ALLOCATION,
                    NEW_SUMMARY.periodStart(),
                    NEW_SUMMARY.periodEnd()
                )

                // when
                underTest.backfillAllocationRecords(TEST_ALLOCATION_INPUT_NO_COMPANIES)

                // then
                verify(entitlementChangeRecordRepo, never()).save(any())
            }
        }

        @Test
        fun do_nothing_given_no_non_expired_summaries_found() {
            // given
            doReturn(emptyList<TimeoffSummaryDBO>()).`when`(timeoffSummaryService).getSummariesNotExpiredOnOrAfter(TEST_EXPIRY_DATE)

            // when
            underTest.backfillAllocationRecords(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(entitlementChangeRecordRepo, never()).save(any())
        }

        @Test
        fun do_nothing_given_feature_flag_is_off() {
            // given
            turnCarryForwardExpiryFeatureFlagOff()
            doReturn(listOf(TEST_CONTRACT)).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIdsInChunks(listOf(TEST_CONTRACT.id))
            doReturn(listOf(NEW_SUMMARY)).`when`(timeoffSummaryService).getSummariesNotExpiredOnOrAfter(TEST_EXPIRY_DATE)

            // when
            underTest.backfillAllocationRecords(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(entitlementChangeRecordRepo, never()).save(any())
        }
    }
}