package com.multiplier.timeoff.processor

import com.google.type.Date
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.UserContext
import com.multiplier.company.schema.holiday.LegalEntityHoliday.Holiday
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractBasicInfo
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import com.multiplier.timeoff.adapters.HolidayServiceAdapter
import com.multiplier.timeoff.core.common.db.AuditUser
import com.multiplier.timeoff.core.common.dto.WorkshiftDTO
import com.multiplier.timeoff.core.common.exceptionhandler.MPLErrorType
import com.multiplier.timeoff.exception.TimeoffValidationException
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.repository.EntitlementChangeRecordRepository
import com.multiplier.timeoff.repository.TimeoffRepository
import com.multiplier.timeoff.repository.TimeoffTypeRepository
import com.multiplier.timeoff.repository.model.EntitlementChangeCategory
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity
import com.multiplier.timeoff.repository.model.TimeoffDBO
import com.multiplier.timeoff.repository.model.TimeoffEntryDBO
import com.multiplier.timeoff.service.TimeoffCalendarService
import com.multiplier.timeoff.types.*
import org.apache.commons.lang3.tuple.Pair
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.doReturn
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@ExtendWith(MockitoExtension::class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores::class)
internal class TimeoffValidatorTest {

    @Mock
    lateinit var changeRecordRepo: EntitlementChangeRecordRepository

    @Mock
    lateinit var timeoffRepo: TimeoffRepository

    @Mock
    lateinit var featureFlagService: FeatureFlagService

    @Mock
    lateinit var timeoffTypeRepo: TimeoffTypeRepository

    @Mock
    lateinit var holidayServiceAdapter: HolidayServiceAdapter

    @Mock
    lateinit var timeoffCalendarService: TimeoffCalendarService

    @Mock
    lateinit var currentUser: CurrentUser

    @InjectMocks
    lateinit var underTest: TimeoffValidator


    companion object {
        private const val CONTRACT_ID = 123L
        private val TEST_CONTRACT = Contract.newBuilder().setId(CONTRACT_ID).build()
        private val TEST_BASIC_CONTRACT = ContractBasicInfo.newBuilder().setContractId(CONTRACT_ID).build()
        private val TEST_BASIC_CONTRACT_WITH_STATUS =
            ContractBasicInfo.newBuilder().setContractId(CONTRACT_ID).setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                .setStartOn(Date.newBuilder().setYear(2025).setMonth(1).setDay(1)).build()
    }


    @Nested
    inner class ThrowIfBalanceIsInsufficient {

        @Nested
        inner class WithFeatureToggleOn {

            @BeforeEach
            fun setUp() {
                val result = GBFeatureResult(value = true, on = true, source = GBFeatureSource.force)
                doReturn(result).`when`(featureFlagService).feature(
                    org.mockito.kotlin.eq(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY), any()
                )
            }

            @Nested
            inner class ShouldNotThrowGivenBalanceIsSufficient {

                @Test
                fun given_no_timeoff_taken() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "new_timeoff,        ,  2, 03-01-2023, 04-01-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun given_carry_forward_but_no_timeoff_taken() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "new_timeoff,        ,  2, 03-01-2023, 04-01-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun given_carry_forward_and_one_timeoff_taken() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 15, 10-01-2023, 24-01-2023, 10-01-2023",
                            "new_timeoff,        ,  2, 03-02-2023, 04-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun given_carry_forward_and_two_timeoffs_taken() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 10, 10-01-2023, 24-01-2023, 10-01-2023",
                            "timeoff,           2,  5, 10-02-2023, 14-02-2023, 10-02-2023",
                            "new_timeoff,        ,  2, 03-03-2023, 04-03-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun given_carry_forward_and_carry_forward_leaves_were_used_first_then_annual_leaves() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 14, 10-01-2023, 23-01-2023, 10-01-2023",
                            "new_timeoff,        ,  6, 03-07-2023, 08-07-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun given_carry_forward_and_timeoff_comprises_balances_from_multiple_allocations() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 10, 25-05-2023, 03-06-2023, 10-01-2023", // this timeoff comprises 7 days from annual leave and 3 days from carry forwarded leave
                            "timeoff,           2,  5, 10-02-2023, 14-02-2023, 10-02-2023",
                            "new_timeoff,        ,  2, 03-03-2023, 04-03-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun can_use_carry_forward_leave_till_its_last_valid_date() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "new_timeoff,        , 20, 12-05-2023, 31-05-2023",
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun can_use_remaining_carry_forward_leave_after_encashment() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "encashment,        3,  2,          2, 10-01-2023",
                            "new_timeoff,        , 18, 12-05-2023, 29-05-2023",
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun should_still_work_given_not_related_allocation_found() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2022, 31-12-2022",
                            "carry_forward,     2,  5, 01-01-2022, 31-05-2022",
                            "annual_alloc,      3, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     4,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 15, 10-01-2023, 24-01-2023, 10-01-2023",
                            "new_timeoff,        ,  2, 03-02-2023, 04-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun should_still_work_given_non_expired_carry_forward() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      3, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     4,  5, 01-01-2023, null",
                            "timeoff,           1, 15, 10-01-2023, 24-01-2023, 10-01-2023",
                            "new_timeoff,        ,  2, 03-02-2023, 04-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun should_still_work_given_timeoff_request_end_date_is_null() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      3, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     4,  5, 01-01-2023, null",
                            "timeoff,           1, 15, 10-01-2023, 24-01-2023, 10-01-2023",
                            "new_timeoff,        ,  2, 03-02-2023, null"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }

                @Test
                fun should_still_work_given_day_counts_are_not_whole_numbers() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      3,  5, 01-01-2023, 31-12-2023",
                            "carry_forward,     4,  4, 01-01-2023, 31-05-2023",
                            "timeoff,           1,8.5, 10-01-2023, 18-01-2023, 10-01-2023",
                            "new_timeoff,        ,0.5, 03-02-2023, 03-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input)
                }
            }

            @Nested
            inner class ShouldThrowExceptionGivenBalanceIsInsufficient {

                @Test
                fun given_balance_is_zero() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "timeoff,           1, 15, 10-01-2023, 22-01-2023, 10-01-2023",
                            "new_timeoff,        ,  3, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input)
                }

                @Test
                fun given_balance_is_insufficient() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "timeoff,           1, 13, 10-01-2023, 22-01-2023, 10-01-2023",
                            "new_timeoff,        ,  3, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input)
                }

                @Test
                fun given_carry_forward_and_balance_is_zero() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 20, 10-01-2023, 29-01-2023, 10-01-2023",
                            "new_timeoff,        ,  3, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input)
                }

                @Test
                fun given_carry_forward_and_balance_is_insufficient() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 18, 10-01-2023, 27-01-2023, 10-01-2023",
                            "new_timeoff,        ,  3, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input)
                }

                @Test
                fun given_carry_forward_and_balance_is_insufficient_after_encashment() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1,  3, 10-01-2023, 12-01-2023, 10-01-2023",
                            "encashment,        3,  2,          2, 10-01-2023",
                            "new_timeoff,        , 16, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input)
                }

                @Test
                fun given_carry_forward_but_request_not_in_range() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "new_timeoff,        , 20, 30-05-2023, 18-06-2023" // carry forward can't be used after 31/05
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input)
                }

                @Test
                fun given_carry_forward_but_request_not_in_range_2() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1,  3, 10-01-2023, 19-01-2023, 10-01-2023", // 2 days carry forward leaves left
                            "new_timeoff,        , 17, 31-05-2023, 16-06-2023" // carry forward can't be used after 31/05
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input)
                }

                @Test
                fun given_no_allocation_found() {
                    val input = setUpTestScenario(
                        arrayOf(
                            "new_timeoff,        ,  1, 03-01-2023, 03-01-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input)
                }

                @Test
                fun should_still_work_given_not_related_allocation_found() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2022, 31-12-2022",
                            "carry_forward,     2,  5, 01-01-2022, 31-05-2022",
                            "annual_alloc,      3, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     4,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 18, 10-01-2023, 27-01-2023, 10-01-2023",
                            "new_timeoff,        ,  3, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input)
                }

                @Test
                fun should_still_work_given_day_counts_are_not_whole_numbers() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1,  5, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,4.3, 01-01-2023, 31-05-2023",
                            "timeoff,           1,  9, 01-02-2023, 08-02-2023, 01-01-2023",
                            "new_timeoff,        ,0.5, 01-06-2023, 01-06-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input)
                }
            }
        }

        private fun validateAndAssertBalanceIsSufficient(input: TimeOffCreateInput) {
            assertDoesNotThrow {
                underTest.throwIfBalanceIsInsufficient(input, TEST_CONTRACT)
            }
        }

        private fun validateAndAssertBalanceIsInsufficient(input: TimeOffCreateInput) {
            assertThrows(TimeoffValidationException::class.java) {
                underTest.throwIfBalanceIsInsufficient(input, TEST_CONTRACT)
            }
        }

        private fun setUpTestScenario(specs: Array<String>): TimeOffCreateInput {
            val allocationRecords = mutableListOf<EntitlementChangeRecordEntity>()
            val deductionRecords = mutableListOf<EntitlementChangeRecordEntity>()
            val timeoffs = mutableListOf<TimeoffDBO>()
            var input = TimeOffCreateInput()
            val formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy")
            for (spec in specs) {
                // format of tokens: type, id, count, startDate, endDate, refId/createdOn
                // refId is used for carry forward entry
                // createdOn is used for timeoff entry
                val tokens = spec.split(',').map { it.trim() }
                when (tokens[0]) {
                    "annual_alloc" -> allocationRecords.add(
                        EntitlementChangeRecordEntity(
                            id = tokens[1].toLong(),
                            typeId = 1,
                            category = EntitlementChangeCategory.ALLOCATION,
                            count = tokens[2].toDouble(),
                            validFrom = LocalDate.parse(tokens[3], formatter),
                            validToInclusive = LocalDate.parse(tokens[4], formatter)
                        ))
                    "carry_forward" -> allocationRecords.add(
                        EntitlementChangeRecordEntity(
                            id = tokens[1].toLong(),
                            typeId = 1,
                            category = EntitlementChangeCategory.CARRY_FORWARD,
                            count = tokens[2].toDouble(),
                            validFrom = LocalDate.parse(tokens[3], formatter),
                            validToInclusive = if (tokens[4] == "null") null else LocalDate.parse(tokens[4], formatter)
                        ))
                    "encashment" -> {
                        val encashmentRecord = EntitlementChangeRecordEntity(
                            id = tokens[1].toLong(),
                            typeId = 1,
                            category = EntitlementChangeCategory.ENCASHMENT,
                            count = tokens[2].toDouble(),
                            refId = tokens[3].toLong()
                        )
                        encashmentRecord.createdOn = LocalDate.parse(tokens[4], formatter).atStartOfDay()
                        deductionRecords.add(encashmentRecord)
                    }
                    "timeoff" -> timeoffs.add(
                        TimeoffDBO.builder().id(tokens[1].toLong())
                        .typeId(1)
                        .noOfDays(tokens[2].toDouble())
                        .startDate(LocalDate.parse(tokens[3], formatter))
                        .endDate(LocalDate.parse(tokens[4], formatter))
                        .createdOn(LocalDate.parse(tokens[5], formatter).atStartOfDay())
                        .build())
                    "new_timeoff" -> input = TimeOffCreateInput.newBuilder()
                        .type("annual_leave")
                        .noOfDays(tokens[2].toDouble())
                        .startDate(TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(tokens[3], formatter)).build())
                        .endDate(if (tokens[4] == "null") null else TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(tokens[4], formatter)).build())
                        .build()
                }
            }

            doReturn(allocationRecords).`when`(changeRecordRepo).findAllByTypeAndContractIdAndCategoryIn(
                input.type,
                TEST_CONTRACT.id,
                TimeoffValidator.ALLOCATION_CATEGORIES
            )
            if (deductionRecords.isNotEmpty()) {
                val allocationRecordIds = allocationRecords.map { it.id!! }.toSet()
                doReturn(deductionRecords).`when`(changeRecordRepo).findAllByCategoryInAndRefIdIn(TimeoffValidator.DEDUCTION_CATEGORIES, allocationRecordIds)
            }
            if (timeoffs.isNotEmpty()) {
                doReturn(timeoffs).`when`(timeoffRepo).findAllByTypeAndContractIdAndStatusIn(input.type, TEST_CONTRACT.id, TimeoffValidator.PENDING_OR_TAKEN_STATUSES)
            }

            return input
        }

        @Test
        fun should_pass_given_feature_flag_is_off_and_balance_is_zero() {
            // given
            val result = GBFeatureResult(value = false, on = false, source = GBFeatureSource.force)
            doReturn(result).`when`(featureFlagService)
                .feature(org.mockito.kotlin.eq(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY), any())
            val input = TimeOffCreateInput.newBuilder()
                .type("annual_leave")
                .noOfDays(3.0)
                .startDate(TimeOffDateInput.newBuilder().dateOnly(LocalDate.now()).build())
                .endDate(TimeOffDateInput.newBuilder().dateOnly(LocalDate.now()).build())
                .build()

            // then
            validateAndAssertBalanceIsSufficient(input)
        }

    }

    @Nested
    inner class PreValidateTimeoffCreateV2Test {

        @Nested
        inner class ShouldThrowIfTimeoffTypeIdIsInValid {

            @Test
            fun should_throw_exception_when_type_id_is_invalid() {
                doReturn(false).`when`(timeoffTypeRepo).existsByTypeIdAndCompanyId(any(), any())
                val startDate = TimeOffDateInput.newBuilder().build()
                val endDate = TimeOffDateInput.newBuilder().build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0501_INVALID_TIMEOFF_TYPE)
            }
        }

        @Nested
        inner class ShouldThrowExceptionGivenStartDateIsInValid {

            @Test
            fun should_throw_exception_when_start_date_is_null() {
                stubTimeoffTypeService()
                val startDate = TimeOffDateInput.newBuilder().build()
                val endDate = TimeOffDateInput.newBuilder().build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0507_INVALID_TIMEOFF_START_DATE)
            }

            @Test
            fun should_throw_exception_when_start_session_is_null() {
                stubTimeoffTypeService()
                val startDate = TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2024, 12, 1)).build()
                val endDate = TimeOffDateInput.newBuilder().build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0519_INVALID_TIMEOFF_START_SESSION)
            }

            @Test
            fun should_throw_exception_when_start_date_is_before_contract_start_date() {
                stubTimeoffTypeService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2024, 12, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate = TimeOffDateInput.newBuilder().build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input, MPLErrorType.MPL0502_INVALID_TIMEOFF_START_DATE_CONTRACT_START_DATE
                )
            }

            @Test
            fun should_throw_exception_when_start_date_is_after_contract_end_date() {
                stubTimeoffTypeService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2026, 12, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate = TimeOffDateInput.newBuilder().build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                val contract = ContractBasicInfo.newBuilder().setContractId(123).setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setStartOn(Date.newBuilder().setYear(2025).setMonth(1).setDay(1))
                    .setLastWorkingDay(Date.newBuilder().setYear(2026).setMonth(1).setDay(1)).build()
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input, MPLErrorType.MPL0503_INVALID_TIMEOFF_START_DATE_CONTRACT_END_DATE, contract = contract
                )
            }

            @Test
            fun should_throw_exception_when_start_date_is_on_holiday() {
                stubTimeoffTypeService()
                stubHolidayService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 6)).session(TimeOffSession.MORNING)
                        .build()
                val endDate = TimeOffDateInput.newBuilder().build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0509_INVALID_TIMEOFF_START_DATE_HOLIDAY)
            }

            @Test
            fun should_throw_exception_when_start_date_is_before_calendar_start_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 2, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate = TimeOffDateInput.newBuilder().build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input, MPLErrorType.MPL0513_INVALID_TIMEOFF_START_DATE_SUMMARY_PERIOD
                )
            }

            @Test
            fun should_throw_exception_when_start_date_is_after_calendar_end_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2026, 5, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate = TimeOffDateInput.newBuilder().build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input, MPLErrorType.MPL0513_INVALID_TIMEOFF_START_DATE_SUMMARY_PERIOD
                )
            }

            @Test
            fun should_throw_exception_when_start_date_is_on_rest_day() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate = TimeOffDateInput.newBuilder().build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                val workshift = getWorkshift(DayOfWeek.MONDAY, DayOfWeek.TUESDAY)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input, MPLErrorType.MPL0521_INVALID_TIMEOFF_START_DATE_REST_DAY, workshift
                )
            }
        }

        @Nested
        inner class ShouldThrowExceptionGivenEndDateIsInValid {
            @Test
            fun should_throw_exception_when_end_date_is_null() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate = TimeOffDateInput.newBuilder().build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0508_INVALID_TIMEOFF_END_DATE)
            }

            @Test
            fun should_throw_exception_when_end_session_is_null() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate = TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0520_INVALID_TIMEOFF_END_SESSION)
            }

            @Test
            fun should_throw_exception_when_end_date_is_before_contract_start_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2024, 9, 1)).session(TimeOffSession.MORNING)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input, MPLErrorType.MPL0504_INVALID_TIMEOFF_END_DATE_CONTRACT_START_DATE
                )
            }

            @Test
            fun should_throw_exception_when_end_date_is_after_contract_end_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()

                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2026, 11, 1)).session(TimeOffSession.MORNING)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                val contract = ContractBasicInfo.newBuilder().setContractId(123).setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setStartOn(Date.newBuilder().setYear(2025).setMonth(1).setDay(1))
                    .setLastWorkingDay(Date.newBuilder().setYear(2026).setMonth(1).setDay(1)).build()
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input, MPLErrorType.MPL0505_INVALID_TIMEOFF_END_DATE_CONTRACT_END_DATE, contract = contract
                )
            }

            @Test
            fun should_throw_exception_when_end_date_is_on_holiday() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 6)).session(TimeOffSession.MORNING)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0510_INVALID_TIMEOFF_END_DATE_HOLIDAY)
            }

            @Test
            fun should_throw_exception_when_end_date_is_before_calendar_start_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 3, 1)).session(TimeOffSession.MORNING)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input, MPLErrorType.MPL0514_INVALID_TIMEOFF_END_DATE_SUMMARY_PERIOD
                )
            }

            @Test
            fun should_throw_exception_when_end_date_is_after_calendar_end_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2026, 5, 1)).session(TimeOffSession.MORNING)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input, MPLErrorType.MPL0514_INVALID_TIMEOFF_END_DATE_SUMMARY_PERIOD
                )
            }

            @Test
            fun should_throw_exception_when_end_date_is_on_rest_day() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 5)).session(TimeOffSession.MORNING)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                val workshift = getWorkshift(DayOfWeek.MONDAY, DayOfWeek.FRIDAY)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input, MPLErrorType.MPL0522_INVALID_TIMEOFF_END_DATE_REST_DAY, workshift
                )
            }
        }

        @Nested
        inner class ShouldThrowIfStartAndEndDateIsInValid {
            @Test
            fun should_throw_exception_when_start_date_is_before_end_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 9, 1)).session(TimeOffSession.MORNING)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0507_INVALID_TIMEOFF_START_DATE)
            }

            @Test
            fun should_throw_exception_when_start_date_session_is_invalid() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.AFTERNOON)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0518_INVALID_TIMEOFF_START_DATE_SESSION)
            }
        }

        @Nested
        inner class ShouldThrowIfTimeoffInputIsOverlapped {
            @Test
            fun should_throw_exception_when_overlaps_with_other_timeoff_start_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val timeoffs = listOf(
                    TimeoffDBO.builder()
                        .startDate(LocalDate.of(2025, 10, 1)).startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.of(2025, 10, 3)).endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(
                            listOf(
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-01")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-02")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-03")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build()
                            )
                        )
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(timeoffs)

                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 9, 25)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.AFTERNOON)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0516_INVALID_TIMEOFF_OVERLAPPING)
            }

            @Test
            fun should_throw_exception_when_has_same_start_and_end_dates_and_overlaps_with_other_timeoff_start_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val timeoffs = listOf(
                    TimeoffDBO.builder()
                        .startDate(LocalDate.of(2025, 10, 1)).startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.of(2025, 10, 3)).endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(
                            listOf(
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-01")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-02")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-03")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build()
                            )
                        )
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(timeoffs)

                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 1)).session(TimeOffSession.AFTERNOON)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0516_INVALID_TIMEOFF_OVERLAPPING)
            }

            @ParameterizedTest
            @CsvSource(
                "2025-09-25, MORNING, 2025-10-02, AFTERNOON",
                "2025-10-01, MORNING, 2025-10-02, AFTERNOON",
                "2025-10-02, MORNING, 2025-10-03, AFTERNOON",
                "2025-10-03, MORNING, 2025-10-04, AFTERNOON",
                "2025-10-03, MORNING, 2025-10-05, AFTERNOON",
            )
            fun should_throw_exception_when_timeoff_entry_start_or_end_date_overlaps_existing_timeoff(
                testStartDate: String,
                testStartDateSession: TimeOffSession,
                testEndDate: String,
                testEndDateSession: TimeOffSession
            ) {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val timeoffs = listOf(
                    TimeoffDBO.builder()
                        .startDate(LocalDate.of(2025, 10, 1)).startSession(TimeOffSession.AFTERNOON)
                        .endDate(LocalDate.of(2025, 10, 4)).endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(
                            listOf(
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-01")).session(
                                    TimeOffSession
                                        .AFTERNOON
                                ).value(0.5).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-02")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-03")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-04")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build()
                            )
                        )
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(timeoffs)

                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(testStartDate)).session(testStartDateSession)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(testEndDate)).session(testEndDateSession)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)

                val workshift = getWorkshift(DayOfWeek.MONDAY, DayOfWeek.SUNDAY)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
                    input,
                    MPLErrorType.MPL0516_INVALID_TIMEOFF_OVERLAPPING,
                    workshift
                )
            }

            @Test
            fun should_throw_exception_when_overlaps_with_other_timeoff_end_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val timeoffs = listOf(
                    TimeoffDBO.builder()
                        .startDate(LocalDate.of(2025, 10, 1)).startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.of(2025, 10, 3)).endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(
                            listOf(
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-01")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-02")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-03")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build()
                            )
                        )
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(timeoffs)

                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 3)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 7)).session(TimeOffSession.AFTERNOON)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0516_INVALID_TIMEOFF_OVERLAPPING)
            }

            @Test
            fun should_throw_exception_when_has_same_start_and_end_dates_and_overlaps_with_other_timeoff_end_date() {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val timeoffs = listOf(
                    TimeoffDBO.builder()
                        .startDate(LocalDate.of(2025, 10, 1)).startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.of(2025, 10, 3)).endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(
                            listOf(
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-01")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-02")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build(),
                                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-03")).session(
                                    TimeOffSession
                                        .FULL_DAY
                                ).value(1.0).build()
                            )
                        )
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(timeoffs)

                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 3)).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.of(2025, 10, 3)).session(TimeOffSession.AFTERNOON)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0516_INVALID_TIMEOFF_OVERLAPPING)
            }

            // cases                                                 | can apply
            // 1. 2025-10-20 MORNING - 2025-10-20 MORNING 0.5        | 2025-10-20 AFTERNOON 2025-10-20 AFTERNOON 0.5
            // 2. 2025-10-20 AFTERNOON - 2025-10-20 AFTERNOON 0.5    | 2025-10-20 MORNING 2025-10-20 MORNING 0.5
            // 3. 2025-10-20 MORNING - 2025-10-21 MORNING 1.5        | 2025-10-21 AFTERNOON ...
            // 4. 2025-10-20 AFTERNOON - 2025-10-21 AFTERNOON 1.5.   | 2025-10-20 MORNING 2025-10-20 MORNING 0.5
            // 5. 2025-10-20 AFTERNOON - 2025-10-21 MORNING 1.0      | 2025-10-20 MORNING 2025-10-20 MORNING 0.5 or 2025-10-21 MORNING 2025-10-21 MORNING 0.5
            @ParameterizedTest
            @CsvSource(
                "2025-10-20, MORNING, 2025-10-20, MORNING, 2025-10-20, AFTERNOON, 2025-10-20, AFTERNOON",
                "2025-10-20, AFTERNOON, 2025-10-20, AFTERNOON, 2025-10-20, MORNING, 2025-10-20, MORNING",
            )
            fun should_not_throw_exception_when_timeoff_entry_is_valid_and_not_overlap(
                existTimeoffStartDate: String,
                existTimeoffStartDateSession: TimeOffSession,
                existTimeoffEndDate: String,
                existTimeoffEndDateSession: TimeOffSession,
                testStartDate: String,
                testStartDateSession: TimeOffSession,
                testEndDate: String,
                testEndDateSession: TimeOffSession
            ) {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse(existTimeoffStartDate))
                    .session(existTimeoffStartDateSession)
                    .value(0.5)
                    .build()
                val timeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse(existTimeoffStartDate))
                        .startSession(existTimeoffStartDateSession)
                        .endDate(LocalDate.parse(existTimeoffEndDate))
                        .endSession(existTimeoffEndDateSession)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(timeoffs)

                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(testStartDate)).session(testStartDateSession)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(testEndDate)).session(testEndDateSession)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsValid(input)
            }

            @ParameterizedTest
            @CsvSource(
                "2025-10-20, AFTERNOON, 2025-10-21, AFTERNOON, 2025-10-20, MORNING, 2025-10-20, MORNING",
                "2025-10-20, AFTERNOON, 2025-10-21, MORNING, 2025-10-20, MORNING, 2025-10-20, MORNING",
                "2025-10-20, AFTERNOON, 2025-10-21, MORNING, 2025-10-21, AFTERNOON, 2025-10-21, AFTERNOON",
            )
            fun should_not_throw_exception_when_timeoff_entry_is_valid_and_not_overlap_2(
                existTimeoffStartDate: String,
                existTimeoffStartDateSession: TimeOffSession,
                existTimeoffEndDate: String,
                existTimeoffEndDateSession: TimeOffSession,
                testStartDate: String,
                testStartDateSession: TimeOffSession,
                testEndDate: String,
                testEndDateSession: TimeOffSession
            ) {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse(existTimeoffStartDate))
                    .session(existTimeoffStartDateSession)
                    .value(0.5)
                    .build()
                val secondEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse(existTimeoffStartDate))
                    .session(
                        if (existTimeoffStartDateSession == TimeOffSession.AFTERNOON) TimeOffSession.AFTERNOON
                        else
                            TimeOffSession.AFTERNOON
                    )
                    .value(
                        if (existTimeoffStartDateSession == TimeOffSession.AFTERNOON) 1.0
                        else
                            0.5
                    )
                    .build()
                val timeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse(existTimeoffStartDate))
                        .startSession(existTimeoffStartDateSession)
                        .endDate(LocalDate.parse(existTimeoffEndDate))
                        .endSession(existTimeoffEndDateSession)
                        .timeoffEntries(listOf(firstEntry, secondEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(timeoffs)

                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(testStartDate)).session(testStartDateSession)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(testEndDate)).session(testEndDateSession)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsValid(input)
            }

            @ParameterizedTest
            @CsvSource(
                "2025-10-20, MORNING, 2025-10-21, MORNING, 2025-10-21, AFTERNOON, 2025-10-23, AFTERNOON",
            )
            fun should_not_throw_exception_when_timeoff_entry_is_valid_and_not_overlap_3(
                existTimeoffStartDate: String,
                existTimeoffStartDateSession: TimeOffSession,
                existTimeoffEndDate: String,
                existTimeoffEndDateSession: TimeOffSession,
                testStartDate: String,
                testStartDateSession: TimeOffSession,
                testEndDate: String,
                testEndDateSession: TimeOffSession
            ) {
                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse(existTimeoffStartDate))
                    .session(existTimeoffStartDateSession)
                    .value(1.0)
                    .build()
                val secondEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse(existTimeoffEndDate))
                    .session(existTimeoffEndDateSession)
                    .value(0.5)
                    .build()
                val timeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse(existTimeoffStartDate))
                        .startSession(existTimeoffStartDateSession)
                        .endDate(LocalDate.parse(existTimeoffEndDate))
                        .endSession(existTimeoffEndDateSession)
                        .timeoffEntries(listOf(firstEntry, secondEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(timeoffs)

                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(testStartDate)).session(testStartDateSession)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(testEndDate)).session(testEndDateSession)
                        .build()
                val input = buildTimeoffCreateInputV2(startDate, endDate)
                validateAndAssertPreValidateTimeoffCreateV2IsValid(input)
            }
        }

        @Nested
        inner class ShouldThrowIfAccessDeniedForExistingTimeoffTest {

            @Test
            fun should_throw_if_timeoff_is_not_draft() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val timeoff = TimeoffDBO.builder().id(timeoffId).status(TimeOffStatus.APPROVAL_IN_PROGRESS).build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)

                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0525_INVALID_TIMEOFF_STATUS_TO_SUBMIT, timeoff = timeoff)

            }

            @Test
            fun should_throw_for_operations_user() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val timeoff = TimeoffDBO.builder().id(timeoffId).status(TimeOffStatus.DRAFT).build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubOpsUser()

                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0527_UNAUTHORIZED_EXPERIENCE_TO_SUBMIT_TIMEOFFS, timeoff = timeoff)

            }

            @Test
            fun should_throw_for_member_if_draft_timeoff_is_not_belongs_to_him() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val timeoff = TimeoffDBO.builder().id(timeoffId).contractId(CONTRACT_ID + 1).status(TimeOffStatus.DRAFT).build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubMember(123L)

                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0525_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_MEMBER, timeoff = timeoff)

            }

            @Test
            fun should_throw_for_member_if_draft_timeoff_created_by_is_not_him() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val memberUserId = 234L
                val timeoff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(memberUserId + 2)
                    .build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubMember(memberUserId)

                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0525_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_MEMBER, timeoff = timeoff)

            }

            @Test
            fun should_throw_for_member_if_draft_timeoff_created_by_info_is_not_him() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val memberUserId = 234L
                val timeoff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(memberUserId + 2)
                    .createdByInfo(AuditUser(memberUserId + 2, "member"))
                    .build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubMember(memberUserId)

                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0525_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_MEMBER, timeoff = timeoff)

            }

            @Test
            fun should_throw_for_company_user_if_draft_timeoff_created_by_is_not_him() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val companyUserId = 235L
                val timeoff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(companyUserId + 2)
                    .build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubCompanyUser(companyUserId)

                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0526_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_COMPANY, timeoff = timeoff)

            }

            @Test
            fun should_throw_for_company_user_if_draft_timeoff_created_by_info_is_member() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val companyUserId = 235L
                val timeoff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(235)
                    .createdByInfo(AuditUser(235L, "member"))
                    .build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubCompanyUser(companyUserId)

                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0526_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_COMPANY, timeoff = timeoff)

            }

            @Test
            fun should_throw_for_company_user_if_draft_timeoff_created_by_info_is_other_company_user() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val companyUserId = 235L
                val timeoff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(235L)
                    .createdByInfo(AuditUser(companyUserId + 1, ",company"))
                    .build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubCompanyUser(companyUserId)

                validateAndAssertPreValidateTimeoffCreateV2IsInvalid(input, MPLErrorType.MPL0526_UNAUTHORIZED_TIMEOFF_ACCESS_FOR_COMPANY, timeoff = timeoff)

            }


            @Test
            fun should_success_member_if_draft_timeoff_is_belongs_to_him() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val timeoff = TimeoffDBO.builder().id(timeoffId).contractId(CONTRACT_ID).status(TimeOffStatus.DRAFT).build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubMember(123)

                validateAndAssertPreValidateTimeoffCreateV2IsValid(input, timeoff = timeoff)

            }

            @Test
            fun should_success_for_member_if_draft_timeoff_created_by__him() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val memberUserId = 234L
                val timeoff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(memberUserId)
                    .build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubMember(memberUserId)

                validateAndAssertPreValidateTimeoffCreateV2IsValid(input, timeoff = timeoff)

            }

            @Test
            fun should_success_for_member_if_draft_timeoff_created_by_info_is_him() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val memberUserId = 234L
                val timeoff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(memberUserId)
                    .createdByInfo(AuditUser(memberUserId, "member"))
                    .build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubMember(memberUserId)

                validateAndAssertPreValidateTimeoffCreateV2IsValid(input, timeoff = timeoff)

            }

            @Test
            fun should_success_for_company_user_if_draft_timeoff_created_by_is_him() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val companyUserId = 235L
                val timeoff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(companyUserId)
                    .build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubCompanyUser(companyUserId)

                validateAndAssertPreValidateTimeoffCreateV2IsValid(input, timeoff = timeoff)

            }

            @Test
            fun should_success_for_company_user_if_draft_timeoff_created_by_info_is_him() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val companyUserId = 235L
                val timeoff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(235L)
                    .createdByInfo(AuditUser(companyUserId, ",company"))
                    .build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubCompanyUser(companyUserId)

                validateAndAssertPreValidateTimeoffCreateV2IsValid(input, timeoff = timeoff)

            }

            @Test
            fun should_success_for_company_user_if_draft_timeoff_created_by_is_undefined() {
                val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.MORNING)
                        .build()
                val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse("2025-10-21")).session(TimeOffSession.AFTERNOON)
                        .build()
                val timeoffId = 45L
                val input = buildTimeoffCreateInputV2(startDate, endDate, timeoffId)
                val companyUserId = 235L
                val timeoff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffStatus.DRAFT)
                    .build()

                stubTimeoffTypeService()
                stubHolidayService()
                stubCalendarService()
                val firstEntry = TimeoffEntryDBO.builder()
                    .date(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .value(0.5)
                    .build()
                val existingTimeoffs = listOf(
                    TimeoffDBO.builder().startDate(LocalDate.parse("2025-10-20"))
                        .startSession(TimeOffSession.MORNING)
                        .endDate(LocalDate.parse("2025-10-20"))
                        .endSession(TimeOffSession.AFTERNOON)
                        .timeoffEntries(listOf(firstEntry))
                        .build()
                )
                stubTimeoffRepositoryForExistingTimeoffs(existingTimeoffs)
                stubCompanyUser(companyUserId)

                validateAndAssertPreValidateTimeoffCreateV2IsValid(input, timeoff = timeoff)

            }


        }

        private fun validateAndAssertPreValidateTimeoffCreateV2IsInvalid(
            input: TimeOffCreateInputV2,
            expectedErrorType: MPLErrorType, workshift: WorkshiftDTO? = null,
            timeoff: TimeoffDBO = TimeoffDBO(),
            contract: ContractBasicInfo = TEST_BASIC_CONTRACT_WITH_STATUS
        ) {
            val cWorkshift = workshift ?: getWorkshift()
            val exception = assertThrows(TimeoffValidationException::class.java) {
                underTest.preValidateTimeoffCreateV2(contract, input, cWorkshift, timeoff)
            }
            assertEquals(expectedErrorType, exception.mplError?.errorType)
        }

        private fun validateAndAssertPreValidateTimeoffCreateV2IsValid(
            input: TimeOffCreateInputV2, workshift: WorkshiftDTO? = null, timeoff: TimeoffDBO = TimeoffDBO()
        ) {
            val cWorkshift = workshift ?: getWorkshift()
            assertDoesNotThrow {
                underTest.preValidateTimeoffCreateV2(TEST_BASIC_CONTRACT_WITH_STATUS, input, cWorkshift, timeoff)
            }
        }

        private fun buildTimeoffCreateInputV2(
            startDate: TimeOffDateInput,
            endDate: TimeOffDateInput,
            timeoffId: Long? = null
        ): TimeOffCreateInputV2
        {
            return TimeOffCreateInputV2.newBuilder()
                .typeId(10)
                .contractId(123)
                .startDate(startDate)
                .endDate(endDate)
                .timeOffId(timeoffId)
                .build()
        }

        private fun stubTimeoffTypeService() {
            doReturn(true).`when`(timeoffTypeRepo).existsByTypeIdAndCompanyId(any(), any())
        }

        private fun stubCalendarService() {
            val dates = Pair.of(LocalDate.of(2025, 4, 1), LocalDate.of(2026, 3, 31))

            doReturn(dates).`when`(timeoffCalendarService).getTimeOffCalendarStartDateAndEndDateForContractId(any())
        }

        private fun stubHolidayService() {
            val holidays = listOf(
                Holiday.newBuilder().setYear(2025).setMonth(10).setDate(6).build(),
                Holiday.newBuilder().setYear(2025).setMonth(10).setDate(15).build()
            )
            doReturn(holidays).`when`(holidayServiceAdapter).getHolidays(any(), any(), any())
        }

        private fun stubTimeoffRepositoryForExistingTimeoffs(timeoffs: List<TimeoffDBO>) {
            doReturn(timeoffs).`when`(timeoffRepo).findAllTimeoffsWithinRangeByContractId(any(), any(), any(), any())
        }

        private fun stubOpsUser() {
            doReturn(opsUserContext()).`when`(currentUser).context
        }

        private fun stubMember(userId: Long) {
            doReturn(memberUserContext(userId)).`when`(currentUser).context
        }

        private fun stubCompanyUser(userId: Long) {
            doReturn(companyUserContext(userId)).`when`(currentUser).context
        }

        private fun opsUserContext() : UserContext {
            return UserContext(id = 1L, experience = "operations")
        }

        private fun memberUserContext(userId: Long) : UserContext {
            return UserContext(id = userId, experience = "member")
        }

        private fun companyUserContext(userId: Long) : UserContext {
            return UserContext(id = userId, experience = "company")
        }

        private fun getWorkshift(): WorkshiftDTO {
            return WorkshiftDTO.builder().startDate(DayOfWeek.MONDAY).endDate(DayOfWeek.FRIDAY)
                .build()
        }

        private fun getWorkshift(startDay: DayOfWeek, endDay: DayOfWeek): WorkshiftDTO {
            return WorkshiftDTO.builder().startDate(startDay).endDate(endDay).build()
        }
    }

    @Nested
    inner class ThrowIfBalanceIsInsufficientV2 {

        @Nested
        inner class WithFeatureToggleOn {

            @BeforeEach
            fun setUp() {
                val result = GBFeatureResult(value = true, on = true, source = GBFeatureSource.force)
                doReturn(result).`when`(featureFlagService).feature(
                    org.mockito.kotlin.eq(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY), any()
                )
            }

            @Nested
            inner class ShouldNotThrowGivenBalanceIsSufficient {

                @Test
                fun given_no_timeoff_taken() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "new_timeoff,        ,  2, 03-01-2023, 04-01-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 2.0)
                }

                @Test
                fun given_carry_forward_but_no_timeoff_taken() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "new_timeoff,        ,  2, 03-01-2023, 04-01-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 2.0)
                }

                @Test
                fun given_carry_forward_and_one_timeoff_taken() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 15, 10-01-2023, 24-01-2023, 10-01-2023",
                            "new_timeoff,        ,  2, 03-02-2023, 04-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 2.0)
                }

                @Test
                fun given_carry_forward_and_two_timeoffs_taken() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 10, 10-01-2023, 24-01-2023, 10-01-2023",
                            "timeoff,           2,  5, 10-02-2023, 14-02-2023, 10-02-2023",
                            "new_timeoff,        ,  2, 03-03-2023, 04-03-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 2.0)
                }

                @Test
                fun given_carry_forward_and_carry_forward_leaves_were_used_first_then_annual_leaves() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 14, 10-01-2023, 23-01-2023, 10-01-2023",
                            "new_timeoff,        ,  6, 03-07-2023, 08-07-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 6.0)
                }

                @Test
                fun given_carry_forward_and_timeoff_comprises_balances_from_multiple_allocations() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 10, 25-05-2023, 03-06-2023, 10-01-2023", // this timeoff comprises 7 days from annual leave and 3 days from carry forwarded leave
                            "timeoff,           2,  5, 10-02-2023, 14-02-2023, 10-02-2023",
                            "new_timeoff,        ,  2, 03-03-2023, 04-03-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 2.0)
                }

                @Test
                fun can_use_carry_forward_leave_till_its_last_valid_date() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "new_timeoff,        , 20, 12-05-2023, 31-05-2023",
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 20.0)
                }

                @Test
                fun can_use_remaining_carry_forward_leave_after_encashment() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "encashment,        3,  2,          2, 10-01-2023",
                            "new_timeoff,        , 18, 12-05-2023, 29-05-2023",
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 18.0)
                }

                @Test
                fun should_still_work_given_not_related_allocation_found() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2022, 31-12-2022",
                            "carry_forward,     2,  5, 01-01-2022, 31-05-2022",
                            "annual_alloc,      3, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     4,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 15, 10-01-2023, 24-01-2023, 10-01-2023",
                            "new_timeoff,        ,  2, 03-02-2023, 04-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 2.0)
                }

                @Test
                fun should_still_work_given_non_expired_carry_forward() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      3, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     4,  5, 01-01-2023, null",
                            "timeoff,           1, 15, 10-01-2023, 24-01-2023, 10-01-2023",
                            "new_timeoff,        ,  2, 03-02-2023, 04-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 2.0)
                }

                @Test
                fun should_still_work_given_day_counts_are_not_whole_numbers() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      3,  5, 01-01-2023, 31-12-2023",
                            "carry_forward,     4,  4, 01-01-2023, 31-05-2023",
                            "timeoff,           1,8.5, 10-01-2023, 18-01-2023, 10-01-2023",
                            "new_timeoff,        ,0.5, 03-02-2023, 03-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsSufficient(input, 0.5)
                }
            }

            @Nested
            inner class ShouldThrowExceptionGivenBalanceIsInsufficient {

                @Test
                fun given_balance_is_zero() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "timeoff,           1, 15, 10-01-2023, 22-01-2023, 10-01-2023",
                            "new_timeoff,        ,  3, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input, 3.0)
                }

                @Test
                fun given_balance_is_insufficient() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "timeoff,           1, 13, 10-01-2023, 22-01-2023, 10-01-2023",
                            "new_timeoff,        ,  3, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input, 3.0)
                }

                @Test
                fun given_carry_forward_and_balance_is_zero() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 20, 10-01-2023, 29-01-2023, 10-01-2023",
                            "new_timeoff,        ,  3, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input, 3.0)
                }

                @Test
                fun given_carry_forward_and_balance_is_insufficient() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 18, 10-01-2023, 27-01-2023, 10-01-2023",
                            "new_timeoff,        ,  3, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input, 3.0)
                }

                @Test
                fun given_carry_forward_and_balance_is_insufficient_after_encashment() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1,  3, 10-01-2023, 12-01-2023, 10-01-2023",
                            "encashment,        3,  2,          2, 10-01-2023",
                            "new_timeoff,        , 16, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input, 16.0)
                }

                @Test
                fun given_carry_forward_but_request_not_in_range() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "new_timeoff,        , 20, 30-05-2023, 18-06-2023" // carry forward can't be used after 31/05
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input, 20.0)
                }

                @Test
                fun given_carry_forward_but_request_not_in_range_2() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1,  3, 10-01-2023, 19-01-2023, 10-01-2023", // 2 days carry forward leaves left
                            "new_timeoff,        , 17, 31-05-2023, 16-06-2023" // carry forward can't be used after 31/05
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input, 17.0)
                }

                @Test
                fun given_no_allocation_found() {
                    val input = setUpTestScenario(
                        arrayOf(
                            "new_timeoff,        ,  1, 03-01-2023, 03-01-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input, 1.0)
                }

                @Test
                fun should_still_work_given_not_related_allocation_found() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1, 15, 01-01-2022, 31-12-2022",
                            "carry_forward,     2,  5, 01-01-2022, 31-05-2022",
                            "annual_alloc,      3, 15, 01-01-2023, 31-12-2023",
                            "carry_forward,     4,  5, 01-01-2023, 31-05-2023",
                            "timeoff,           1, 18, 10-01-2023, 27-01-2023, 10-01-2023",
                            "new_timeoff,        ,  3, 03-02-2023, 05-02-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input, 3.0)
                }

                @Test
                fun should_still_work_given_day_counts_are_not_whole_numbers() {
                    // given
                    val input = setUpTestScenario(
                        arrayOf(
                            "annual_alloc,      1,  5, 01-01-2023, 31-12-2023",
                            "carry_forward,     2,4.3, 01-01-2023, 31-05-2023",
                            "timeoff,           1,  9, 01-02-2023, 08-02-2023, 01-01-2023",
                            "new_timeoff,        ,0.5, 01-06-2023, 01-06-2023"
                        )
                    )

                    // then
                    validateAndAssertBalanceIsInsufficient(input, 0.5)
                }
            }
        }

        private fun validateAndAssertBalanceIsSufficient(input: TimeOffCreateInputV2, noOfDays: Double) {
            assertDoesNotThrow {
                underTest.throwIfBalanceIsInsufficient(input, noOfDays, TEST_BASIC_CONTRACT)
            }
        }

        private fun validateAndAssertBalanceIsInsufficient(input: TimeOffCreateInputV2, noOfDays: Double) {
            assertThrows(TimeoffValidationException::class.java) {
                underTest.throwIfBalanceIsInsufficient(input, noOfDays, TEST_BASIC_CONTRACT)
            }
        }

        private fun setUpTestScenario(specs: Array<String>): TimeOffCreateInputV2 {
            val allocationRecords = mutableListOf<EntitlementChangeRecordEntity>()
            val deductionRecords = mutableListOf<EntitlementChangeRecordEntity>()
            val timeoffs = mutableListOf<TimeoffDBO>()
            var input = TimeOffCreateInputV2()
            val formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy")
            for (spec in specs) {
                // format of tokens: type, id, count, startDate, endDate, refId/createdOn
                // refId is used for carry forward entry
                // createdOn is used for timeoff entry
                val tokens = spec.split(',').map { it.trim() }
                when (tokens[0]) {
                    "annual_alloc" -> allocationRecords.add(
                        EntitlementChangeRecordEntity(
                            id = tokens[1].toLong(),
                            typeId = 1,
                            category = EntitlementChangeCategory.ALLOCATION,
                            count = tokens[2].toDouble(),
                            validFrom = LocalDate.parse(tokens[3], formatter),
                            validToInclusive = LocalDate.parse(tokens[4], formatter)
                        )
                    )

                    "carry_forward" -> allocationRecords.add(
                        EntitlementChangeRecordEntity(
                            id = tokens[1].toLong(),
                            typeId = 1,
                            category = EntitlementChangeCategory.CARRY_FORWARD,
                            count = tokens[2].toDouble(),
                            validFrom = LocalDate.parse(tokens[3], formatter),
                            validToInclusive = if (tokens[4] == "null") null else LocalDate.parse(tokens[4], formatter)
                        )
                    )

                    "encashment" -> {
                        val encashmentRecord = EntitlementChangeRecordEntity(
                            id = tokens[1].toLong(),
                            typeId = 1,
                            category = EntitlementChangeCategory.ENCASHMENT,
                            count = tokens[2].toDouble(),
                            refId = tokens[3].toLong()
                        )
                        encashmentRecord.createdOn = LocalDate.parse(tokens[4], formatter).atStartOfDay()
                        deductionRecords.add(encashmentRecord)
                    }

                    "timeoff" -> timeoffs.add(
                        TimeoffDBO.builder()
                            .id(tokens[1].toLong())
                            .typeId(1)
                            .noOfDays(tokens[2].toDouble())
                            .startDate(LocalDate.parse(tokens[3], formatter))
                            .endDate(LocalDate.parse(tokens[4], formatter))
                            .createdOn(LocalDate.parse(tokens[5], formatter).atStartOfDay())
                            .build()
                    )

                    "new_timeoff" -> input = TimeOffCreateInputV2.newBuilder()
                        .typeId(1)
                        .startDate(
                            TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(tokens[3], formatter)).build()
                        )
                        .endDate(
                            if (tokens[4] == "null") null else TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.parse(tokens[4], formatter)).build()
                        )
                        .build()
                }
            }

            doReturn(allocationRecords).`when`(changeRecordRepo).findAllByTypeIdAndContractIdAndCategoryIn(
                input.typeId,
                TEST_CONTRACT.id,
                TimeoffValidator.ALLOCATION_CATEGORIES
            )
            if (deductionRecords.isNotEmpty()) {
                val allocationRecordIds = allocationRecords.map { it.id!! }.toSet()
                doReturn(deductionRecords).`when`(changeRecordRepo)
                    .findAllByCategoryInAndRefIdIn(TimeoffValidator.DEDUCTION_CATEGORIES, allocationRecordIds)
            }
            if (timeoffs.isNotEmpty()) {
                doReturn(timeoffs).`when`(timeoffRepo).findAllByTypeIdAndContractIdAndStatusIn(
                    input.typeId,
                    TEST_CONTRACT.id, TimeoffValidator.PENDING_OR_TAKEN_STATUSES
                )
            }

            return input
        }

        @Test
        fun should_pass_given_feature_flag_is_off_and_balance_is_zero() {
            // given
            val result = GBFeatureResult(value = false, on = false, source = GBFeatureSource.force)
            doReturn(result).`when`(featureFlagService)
                .feature(org.mockito.kotlin.eq(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY), any())
            val input = TimeOffCreateInputV2.newBuilder()
                .typeId(1)
                .startDate(TimeOffDateInput.newBuilder().dateOnly(LocalDate.now()).build())
                .endDate(TimeOffDateInput.newBuilder().dateOnly(LocalDate.now()).build())
                .build()
            val noOfDays = 3.0
            // then
            validateAndAssertBalanceIsSufficient(input, noOfDays)
        }

    }
}