package com.multiplier.timeoff.processor

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import com.multiplier.timeoff.adapters.ContractServiceAdapter
import com.multiplier.timeoff.featureflag.FeatureFlagService
import com.multiplier.timeoff.featureflag.FeatureFlags
import com.multiplier.timeoff.repository.EntitlementChangeRecordRepository
import com.multiplier.timeoff.repository.model.EntitlementChangeCategory
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO
import com.multiplier.timeoff.service.TimeoffSummaryService
import com.multiplier.timeoff.types.TimeOffAllocationInput
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import java.time.LocalDate

@ExtendWith(MockitoExtension::class)
internal class CarryForwardExpirationProcessorTest {

    @Mock
    private lateinit var entitlementChangeRecordRepo: EntitlementChangeRecordRepository
    @Mock
    private lateinit var timeoffSummaryService: TimeoffSummaryService
    @Mock
    private lateinit var contractServiceAdapter: ContractServiceAdapter
    @Mock
    private lateinit var featureFlagService: FeatureFlagService

    @InjectMocks
    private lateinit var underTest: CarryForwardExpirationProcessor

    companion object {
        private val TEST_CONTRACT = ContractOuterClass.Contract.newBuilder().setId(123456).setCompanyId(345).build()
        private val TEST_EXPIRY_DATE = YEAR_2023.START
        private val TEST_ALLOCATION_INPUT_NO_COMPANIES = TimeOffAllocationInput.newBuilder().expiryDate(TEST_EXPIRY_DATE).build()

        private object YEAR_2023 {
            val START = LocalDate.of(2023, 1, 1)
            val MID = LocalDate.of(2023, 5, 31)
            val END = LocalDate.of(2023, 12, 31)
        }

        private val NEW_SUMMARY = TimeoffSummaryDBO.builder()
            .id(111)
            .typeId(1)
            .contractId(TEST_CONTRACT.id)
            .periodStart(YEAR_2023.START)
            .periodEnd(YEAR_2023.END)
            .allocatedCount(15.0)
            .build()
    }

    @Nested
    inner class ExpireCarryForwardBalances {

        @Test
        fun should_look_for_positive_and_expired_carry_forward_records() {
            // when
            underTest.expireCarryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(entitlementChangeRecordRepo).findByPositiveCountAndExpiredBefore(TEST_EXPIRY_DATE)
        }

        @Test
        fun should_look_for_carry_forward_records_from_contracts_of_given_company_ids() {
            // when
            turnCarryForwardExpiryFeatureFlagOn()
            val companyIds = mutableListOf(111L, 222L)
            val contractIds = listOf(1L, 2L, 3L, 4L)
            doReturn(contractIds).`when`(contractServiceAdapter).getContractIdsByCompanyIds(companyIds)
            val contracts = listOf(
                ContractOuterClass.Contract.newBuilder().setId(1).build(),
                ContractOuterClass.Contract.newBuilder().setId(2).build(),
                ContractOuterClass.Contract.newBuilder().setId(3).build(),
                ContractOuterClass.Contract.newBuilder().setId(4).build())
            doReturn(contracts).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIds(contractIds)

            // when
            underTest.expireCarryForwardTimeoffBalances(TimeOffAllocationInput.newBuilder()
                .expiryDate(TEST_EXPIRY_DATE)
                .companyIds(companyIds)
                .build())

            // then
            verify(entitlementChangeRecordRepo).findByPositiveCountAndExpiredBeforeAndContractIdIn(TEST_EXPIRY_DATE, contractIds.toSet())
        }

        @Nested
        inner class WithExpiredCarryForwardFoundAndFeatureFlagOn {

            @BeforeEach
            fun setUp() {
                turnCarryForwardExpiryFeatureFlagOn()
                doReturn(listOf(TEST_CONTRACT)).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIds(listOf(TEST_CONTRACT.id))
            }

            @Test
            fun should_deduct_expired_carry_forward_count_from_summary() {
                // given
                val carryForwardRecord = EntitlementChangeRecordEntity(
                    123,
                    1,
                    TEST_CONTRACT.id,
                    EntitlementChangeCategory.CARRY_FORWARD,
                    5.0,
                    YEAR_2023.START,
                    YEAR_2023.MID,
                    NEW_SUMMARY.id
                )
                doReturn(listOf(carryForwardRecord)).`when`(entitlementChangeRecordRepo)
                    .findByPositiveCountAndExpiredBefore(TEST_EXPIRY_DATE)

                // when
                underTest.expireCarryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

                // then
                verify(timeoffSummaryService).deductCarryForwardLeave(
                    NEW_SUMMARY.id,
                    carryForwardRecord.count
                )
                verifyExpirationEventIsRecorded(carryForwardRecord.id!!)
            }

            private fun verifyExpirationEventIsRecorded(carryForwardEventId: Long) {
                val captor = argumentCaptor<EntitlementChangeRecordEntity>()
                verify(entitlementChangeRecordRepo).save(captor.capture())
                val changeRecordEntity = captor.firstValue
                org.junit.jupiter.api.assertAll(
                    { assertEquals(1, changeRecordEntity.typeId) },
                    { assertEquals(TEST_CONTRACT.id, changeRecordEntity.contractId) },
                    { assertEquals(EntitlementChangeCategory.EXPIRATION, changeRecordEntity.category) },
                    { assertEquals(5.0, changeRecordEntity.count) },
                    { assertEquals(carryForwardEventId, changeRecordEntity.refId) }
                )
            }

            @Test
            fun do_not_record_expration_event_given_summary_deduction_error() {
                // given
                val carryForwardRecord = EntitlementChangeRecordEntity(
                    123,
                    1,
                    TEST_CONTRACT.id,
                    EntitlementChangeCategory.CARRY_FORWARD,
                    5.0,
                    YEAR_2023.START,
                    YEAR_2023.MID,
                    NEW_SUMMARY.id
                )
                doReturn(listOf(carryForwardRecord)).`when`(entitlementChangeRecordRepo).findByPositiveCountAndExpiredBefore(TEST_EXPIRY_DATE)
                doThrow(RuntimeException::class).`when`(timeoffSummaryService).deductCarryForwardLeave(NEW_SUMMARY.id, carryForwardRecord.count)

                // when
                underTest.expireCarryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

                // then
                verify(entitlementChangeRecordRepo, never()).save(any())
            }
        }

        private fun turnCarryForwardExpiryFeatureFlagOn() {
            val result = GBFeatureResult(value = true, on = true, source = GBFeatureSource.force)
            Mockito.doReturn(result).`when`(featureFlagService)
                .feature(eq(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY), any())
        }

        @Test
        fun should_do_nothing_given_no_expired_carry_forward_found() {
            // given
            doReturn(emptyList<EntitlementChangeRecordEntity>()).`when`(entitlementChangeRecordRepo).findByPositiveCountAndExpiredBefore(TEST_EXPIRY_DATE)

            // when
            underTest.expireCarryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(timeoffSummaryService, never()).deductCarryForwardLeave(any(), any())
            verify(entitlementChangeRecordRepo, never()).save(any())
        }

        @Test
        fun should_do_nothing_given_feature_flag_is_off() {
            // given
            val result = GBFeatureResult(value = false, on = false, source = GBFeatureSource.force)
            Mockito.doReturn(result).`when`(featureFlagService).feature(eq(FeatureFlags.TIME_OFF_CARRY_FORWARD_EXPIRY), any())
            val carryForwardRecord = EntitlementChangeRecordEntity(
                123,
                1,
                TEST_CONTRACT.id,
                EntitlementChangeCategory.CARRY_FORWARD,
                5.0,
                YEAR_2023.START,
                YEAR_2023.MID,
                NEW_SUMMARY.id
            )
            doReturn(listOf(carryForwardRecord)).`when`(entitlementChangeRecordRepo).findByPositiveCountAndExpiredBefore(TEST_EXPIRY_DATE)
            doReturn(listOf(TEST_CONTRACT)).`when`(contractServiceAdapter).getNonDeletedNonEndedContractsByIds(listOf(TEST_CONTRACT.id))

            // when
            underTest.expireCarryForwardTimeoffBalances(TEST_ALLOCATION_INPUT_NO_COMPANIES)

            // then
            verify(timeoffSummaryService, never()).deductCarryForwardLeave(any(), any())
            verify(entitlementChangeRecordRepo, never()).save(any())
        }
    }
}