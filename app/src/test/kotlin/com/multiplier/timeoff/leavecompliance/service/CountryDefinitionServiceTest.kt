package com.multiplier.timeoff.leavecompliance.service

import com.multiplier.timeoff.core.common.dto.TimeoffTypeDTO
import com.multiplier.timeoff.leavecompliance.adapter.LeaveComplianceServiceAdapter
import com.multiplier.timeoff.leavecompliance.dto.AnnualLeaveEntitlement
import com.multiplier.timeoff.repository.CountryDefinitionRepository
import com.multiplier.timeoff.repository.model.CountryDefinitionEntity
import com.multiplier.timeoff.repository.model.DefinitionEntity
import com.multiplier.timeoff.repository.model.TimeoffDefinitionValidationEntity
import com.multiplier.timeoff.service.TimeoffTypeService
import com.multiplier.timeoff.service.dto.CountryLocation
import com.multiplier.timeoff.types.CountryCode
import com.multiplier.timeoff.types.TimeOffTypeDefinitionStatus
import com.multiplier.timeoff.types.TimeOffUnit
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class CountryDefinitionServiceTest {

    @MockK private lateinit var timeoffTypeService: TimeoffTypeService
    @MockK private lateinit var leaveComplianceServiceAdapter: LeaveComplianceServiceAdapter
    @MockK private lateinit var countryDefinitionRepository: CountryDefinitionRepository

    @InjectMockKs private lateinit var countryDefinitionService: CountryDefinitionService

    @Nested
    inner class GetCountryLocationToCountryDefinitionsMap {

        @Test
        fun `should return country state definitions when leave compliance data are unavailable`() {
            val definitionId = 2L
            val annualTypeId = 1L
            val state = "HCM"
            val countryAnnualDefinition = buildCountryLeaveCompliance(definitionId, annualTypeId, 12.0, CountryCode.VNM, state)
            every { countryDefinitionRepository.findAllDefaultAndByCountryIn(setOf(CountryCode.VNM)) } returns listOf(countryAnnualDefinition)

            val timeoffType = buildTimeoffType(annualTypeId, "annual")
            every { timeoffTypeService.findAllByIds(setOf(annualTypeId)) } returns listOf(timeoffType)

            val countryLocation = CountryLocation(CountryCode.VNM, state)
            every {
                leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(setOf(countryLocation))
            } returns emptyMap()

            val countryLocationToCountryDefinitions = countryDefinitionService.getCountryLocationToCountryDefinitionsMap(setOf(countryLocation))
            assertEquals(1, countryLocationToCountryDefinitions.keys.size)

            val countryDefinitions = assertNonNull(countryLocationToCountryDefinitions[countryLocation])
            assertEquals(1, countryDefinitions.size)

            val countryDefinition = countryDefinitions[0]
            assertEquals(annualTypeId, countryDefinition.typeId)
            assertEquals(definitionId, countryDefinition.id)

            val definition = assertNonNull(countryDefinition.definition)
            assertEquals(definitionId, definition.id)
            assertTrue(definition.isRequired)
            assertEquals(CountryCode.VNM, definition.countryCode)
            assertEquals(state, definition.stateCode)
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, definition.status)
            assertEquals("Any Clause", definition.clause)
            assertEquals("Annual Leave", definition.description)
            assertEquals("ANNUAL", definition.basis)
            assertEquals(1, definition.validations.size)

            val validation = definition.validations.iterator().next()
            assertTrue(validation.isUnlimitedLeavesAllowed == false)
            assertNull(validation.maximumValue)
            assertEquals(12.0, validation.defaultValue)
            assertEquals(12.0, validation.minimumValue)
            assertEquals(TimeOffUnit.DAYS, validation.unit)
            assertEquals(listOf("ONBOARDING", "ACTIVE"), validation.allowedContractStatuses)
        }


        @Test
        fun `should return country definitions when country state definitions and leave compliance data are unavailable`() {
            val definitionId = 2L
            val annualTypeId = 1L
            val countryAnnualDefinition = buildCountryLeaveCompliance(definitionId, annualTypeId, 13.0, CountryCode.VNM)
            every { countryDefinitionRepository.findAllDefaultAndByCountryIn(setOf(CountryCode.VNM)) } returns listOf(countryAnnualDefinition)

            val timeoffType = buildTimeoffType(annualTypeId, "annual")
            every { timeoffTypeService.findAllByIds(setOf(annualTypeId)) } returns listOf(timeoffType)

            val countryLocation = CountryLocation(CountryCode.VNM, "HCM")
            every {
                leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(setOf(countryLocation))
            } returns emptyMap()

            val countryLocationToCountryDefinitions = countryDefinitionService.getCountryLocationToCountryDefinitionsMap(setOf(countryLocation))
            assertEquals(1, countryLocationToCountryDefinitions.keys.size)

            val countryDefinitions = assertNonNull(countryLocationToCountryDefinitions[countryLocation])
            assertEquals(1, countryDefinitions.size)

            val countryDefinition = countryDefinitions[0]
            assertEquals(annualTypeId, countryDefinition.typeId)
            assertEquals(definitionId, countryDefinition.id)

            val definition = assertNonNull(countryDefinition.definition)
            assertEquals(definitionId, definition.id)
            assertNull(definition.stateCode)
            assertTrue(definition.isRequired)
            assertEquals(CountryCode.VNM, definition.countryCode)
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, definition.status)
            assertEquals("Any Clause", definition.clause)
            assertEquals("Annual Leave", definition.description)
            assertEquals("ANNUAL", definition.basis)
            assertEquals(1, definition.validations.size)

            val validation = definition.validations.iterator().next()
            assertTrue(validation.isUnlimitedLeavesAllowed == false)
            assertNull(validation.maximumValue)
            assertEquals(13.0, validation.defaultValue)
            assertEquals(12.0, validation.minimumValue)
            assertEquals(TimeOffUnit.DAYS, validation.unit)
            assertEquals(listOf("ONBOARDING", "ACTIVE"), validation.allowedContractStatuses)
        }

        @Test
        fun `should return default definitions when country definitions and leave compliance data are unavailable`() {
            val definitionId = 2L
            val annualTypeId = 1L
            val defaultAnnualDefinition = buildCountryLeaveCompliance(definitionId, annualTypeId, 14.0)
            every { countryDefinitionRepository.findAllDefaultAndByCountryIn(setOf(CountryCode.VNM)) } returns listOf(defaultAnnualDefinition)

            val timeoffType = buildTimeoffType(annualTypeId, "annual")
            every { timeoffTypeService.findAllByIds(setOf(annualTypeId)) } returns listOf(timeoffType)

            val countryLocation = CountryLocation(CountryCode.VNM)
            every {
                leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(setOf(countryLocation))
            } returns emptyMap()

            val countryLocationToCountryDefinitions = countryDefinitionService.getCountryLocationToCountryDefinitionsMap(setOf(countryLocation))
            assertEquals(1, countryLocationToCountryDefinitions.keys.size)

            val countryDefinitions = assertNonNull(countryLocationToCountryDefinitions[countryLocation])
            assertEquals(1, countryDefinitions.size)

            val countryDefinition = countryDefinitions[0]
            assertEquals(annualTypeId, countryDefinition.typeId)
            assertEquals(definitionId, countryDefinition.id)

            val definition = assertNonNull(countryDefinition.definition)
            assertEquals(definitionId, definition.id)
            assertNull(definition.countryCode)
            assertNull(definition.stateCode)
            assertTrue(definition.isRequired)
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, definition.status)
            assertEquals("Any Clause", definition.clause)
            assertEquals("Annual Leave", definition.description)
            assertEquals("ANNUAL", definition.basis)
            assertEquals(1, definition.validations.size)

            val validation = definition.validations.iterator().next()
            assertTrue(validation.isUnlimitedLeavesAllowed == false)
            assertNull(validation.maximumValue)
            assertEquals(14.0, validation.defaultValue)
            assertEquals(12.0, validation.minimumValue)
            assertEquals(TimeOffUnit.DAYS, validation.unit)
            assertEquals(listOf("ONBOARDING", "ACTIVE"), validation.allowedContractStatuses)
        }

        @Test
        fun `should return leave compliance definitions for annual leave`() {
            val definitionId = 2L
            val annualTypeId = 1L
            val defaultAnnualDefinition = buildCountryLeaveCompliance(definitionId, annualTypeId, 15.0)
            every { countryDefinitionRepository.findAllDefaultAndByCountryIn(setOf(CountryCode.VNM)) } returns listOf(defaultAnnualDefinition)

            val countryLocation = CountryLocation(CountryCode.VNM)
            every {
                leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(setOf(countryLocation))
            } returns mapOf(countryLocation to AnnualLeaveEntitlement(20.0, 20.0, TimeOffUnit.DAYS, true))

            val timeoffType = buildTimeoffType(annualTypeId, "annual")
            every { timeoffTypeService.findAllByIds(setOf(annualTypeId)) } returns listOf(timeoffType)

            val countryLocationToCountryDefinitions = countryDefinitionService.getCountryLocationToCountryDefinitionsMap(setOf(countryLocation))
            assertEquals(1, countryLocationToCountryDefinitions.keys.size)

            val countryDefinitions = assertNonNull(countryLocationToCountryDefinitions[countryLocation])
            assertEquals(1, countryDefinitions.size)

            val countryDefinition = countryDefinitions[0]
            assertEquals(annualTypeId, countryDefinition.typeId)
            assertEquals(definitionId, countryDefinition.id)

            val definition = assertNonNull(countryDefinition.definition)
            assertEquals(definitionId, definition.id)
            assertNull(definition.countryCode)
            assertNull(definition.stateCode)
            assertTrue(definition.isRequired)
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, definition.status)
            assertEquals("Any Clause", definition.clause)
            assertEquals("Annual Leave", definition.description)
            assertEquals("ANNUAL", definition.basis)
            assertEquals(1, definition.validations.size)

            val validation = definition.validations.iterator().next()
            assertTrue(validation.isUnlimitedLeavesAllowed == false)
            assertNull(validation.maximumValue)
            assertEquals(20.0, validation.defaultValue)
            assertEquals(20.0, validation.minimumValue)
            assertEquals(TimeOffUnit.DAYS, validation.unit)
            assertEquals(listOf("ONBOARDING", "ACTIVE"), validation.allowedContractStatuses)
        }

        @Test
        fun `should return timeoff definitions when no exact minimum leave day returned from engine`() {
            val definitionId = 2L
            val annualTypeId = 1L
            val defaultAnnualDefinition = buildCountryLeaveCompliance(definitionId, annualTypeId, 15.0)
            every { countryDefinitionRepository.findAllDefaultAndByCountryIn(setOf(CountryCode.VNM)) } returns listOf(defaultAnnualDefinition)

            val countryLocation = CountryLocation(CountryCode.VNM)
            every {
                leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(setOf(countryLocation))
                // With two different min entitled values, the engine will not return the exact value
            } returns mapOf(countryLocation to AnnualLeaveEntitlement(15.0, 20.0, TimeOffUnit.DAYS, true))

            val timeoffType = buildTimeoffType(annualTypeId, "annual")
            every { timeoffTypeService.findAllByIds(setOf(annualTypeId)) } returns listOf(timeoffType)

            val countryLocationToCountryDefinitions = countryDefinitionService.getCountryLocationToCountryDefinitionsMap(setOf(countryLocation))
            assertEquals(1, countryLocationToCountryDefinitions.keys.size)

            val countryDefinitions = assertNonNull(countryLocationToCountryDefinitions[countryLocation])
            assertEquals(1, countryDefinitions.size)

            val countryDefinition = countryDefinitions[0]
            assertEquals(annualTypeId, countryDefinition.typeId)
            assertEquals(definitionId, countryDefinition.id)

            val definition = assertNonNull(countryDefinition.definition)
            assertEquals(definitionId, definition.id)
            assertNull(definition.countryCode)
            assertNull(definition.stateCode)
            assertTrue(definition.isRequired)
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, definition.status)
            assertEquals("Any Clause", definition.clause)
            assertEquals("Annual Leave", definition.description)
            assertEquals("ANNUAL", definition.basis)
            assertEquals(1, definition.validations.size)

            val validation = definition.validations.iterator().next()
            assertTrue(validation.isUnlimitedLeavesAllowed == false)
            assertEquals(15.0, validation.defaultValue)
            assertEquals(12.0, validation.minimumValue)
            assertEquals(null, validation.maximumValue)
            assertEquals(TimeOffUnit.DAYS, validation.unit)
            assertEquals(listOf("ONBOARDING", "ACTIVE"), validation.allowedContractStatuses)
        }

        @Test
        fun `should return timeoff definitions when no annual leave compliance data`() {
            val definitionId = 2L
            val annualTypeId = 1L
            val defaultAnnualDefinition = buildCountryLeaveCompliance(definitionId, annualTypeId, 15.0)
            every { countryDefinitionRepository.findAllDefaultAndByCountryIn(setOf(CountryCode.VNM)) } returns listOf(defaultAnnualDefinition)

            val countryLocation = CountryLocation(CountryCode.VNM)
            every {
                leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(setOf(countryLocation))
            } returns mapOf(countryLocation to AnnualLeaveEntitlement(20.0, 20.0, TimeOffUnit.DAYS, true))

            // Leave compliance service only supplies the sick leave data
            val timeoffType = buildTimeoffType(annualTypeId, "sick")
            every { timeoffTypeService.findAllByIds(setOf(annualTypeId)) } returns listOf(timeoffType)

            val countryLocationToCountryDefinitions = countryDefinitionService.getCountryLocationToCountryDefinitionsMap(setOf(countryLocation))
            assertEquals(1, countryLocationToCountryDefinitions.keys.size)

            val countryDefinitions = assertNonNull(countryLocationToCountryDefinitions[countryLocation])
            assertEquals(1, countryDefinitions.size)

            val countryDefinition = countryDefinitions[0]
            assertEquals(annualTypeId, countryDefinition.typeId)
            assertEquals(definitionId, countryDefinition.id)

            val definition = assertNonNull(countryDefinition.definition)
            assertEquals(definitionId, definition.id)
            assertNull(definition.countryCode)
            assertNull(definition.stateCode)
            assertTrue(definition.isRequired)
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, definition.status)
            assertEquals("Any Clause", definition.clause)
            assertEquals("Annual Leave", definition.description)
            assertEquals("ANNUAL", definition.basis)
            assertEquals(1, definition.validations.size)

            val validation = definition.validations.iterator().next()
            assertTrue(validation.isUnlimitedLeavesAllowed == false)
            assertEquals(15.0, validation.defaultValue)
            assertEquals(12.0, validation.minimumValue)
            assertEquals(null, validation.maximumValue)
            assertEquals(TimeOffUnit.DAYS, validation.unit)
            assertEquals(listOf("ONBOARDING", "ACTIVE"), validation.allowedContractStatuses)
        }
    }

    @Nested
    inner class GetAllCountryDefinitionsByCountryAndState {

        @Test
        fun `should return country definitions for given country and state`() {
            val countryCode = CountryCode.VNM
            val stateCode = "HCM"
            val definitionId = 1L
            val typeId = 1L
            val countryDefinition = buildCountryLeaveCompliance(definitionId, typeId, 10.0, countryCode, stateCode)
            every {
                countryDefinitionRepository.findAllByCountryAndState(countryCode, stateCode)
            } returns listOf(countryDefinition)

            // ignored
            every { timeoffTypeService.findAllByIds(any()) } returns emptyList()
            every { leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(any()) } returns emptyMap()

            val definitions = countryDefinitionService.getAllCountryDefinitionsByCountry(countryCode, stateCode)

            assertEquals(1, definitions.size)
            val definition = definitions[0]
            assertEquals(definitionId, definition.id)
            assertEquals(typeId, definition.typeId)
            assertEquals(countryCode, definition.definition?.countryCode)
            assertEquals(stateCode, definition.definition?.stateCode)
        }

        @Test
        fun `should find country definitions when no data found for given country and state`() {
            val countryCode = CountryCode.VNM
            val stateCode = "HCM"
            val definitionId = 1L
            val typeId = 1L
            // data not found for given country and state
            every {
                countryDefinitionRepository.findAllByCountryAndState(countryCode, stateCode)
            } returns emptyList()

            // data found for country
            val countryDefinition = buildCountryLeaveCompliance(definitionId, typeId, 10.0, countryCode, stateCode)
            every {
                countryDefinitionRepository.findAllByCountryAndState(countryCode, "")
            } returns listOf(countryDefinition)

            // ignored
            every { timeoffTypeService.findAllByIds(any()) } returns emptyList()
            every { leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(any()) } returns emptyMap()

            val definitions = countryDefinitionService.getAllCountryDefinitionsByCountry(countryCode, stateCode)

            assertEquals(1, definitions.size)
            val definition = definitions[0]
            assertEquals(definitionId, definition.id)
            assertEquals(typeId, definition.typeId)
            assertEquals(countryCode, definition.definition?.countryCode)
            assertEquals(stateCode, definition.definition?.stateCode)
        }

        @Test
        fun `should find default country definitions when no data found for given country`() {
            every { countryDefinitionRepository.findAllByCountryAndState(CountryCode.VNM, "") } returns emptyList()
            every {
                countryDefinitionRepository.findAllDefaultDefinitions()
            } returns listOf(buildCountryLeaveCompliance(1L, 1L, 10.0))

            // ignored
            every { timeoffTypeService.findAllByIds(any()) } returns emptyList()
            every { leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(any()) } returns emptyMap()

            countryDefinitionService.getAllCountryDefinitionsByCountry(CountryCode.VNM)

            verify(exactly = 1) { countryDefinitionRepository.findAllDefaultDefinitions() }
        }

        @Test
        fun `should return country definitions for given country when state is null`() {
            val countryCode = CountryCode.VNM
            val definitionId = 1L
            val typeId = 1L
            val countryDefinition = buildCountryLeaveCompliance(definitionId, typeId, 10.0, countryCode, null)
            every {
                countryDefinitionRepository.findAllByCountryAndState(countryCode, "")
            } returns listOf(countryDefinition)

            // ignored
            every { timeoffTypeService.findAllByIds(any()) } returns emptyList()
            every { leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(any()) } returns emptyMap()

            val definitions = countryDefinitionService.getAllCountryDefinitionsByCountry(countryCode, null)

            assertEquals(1, definitions.size)
            val definition = definitions[0]
            assertEquals(definitionId, definition.id)
            assertEquals(typeId, definition.typeId)
            assertEquals(countryCode, definition.definition?.countryCode)
            assertNull(definition.definition?.stateCode)
        }

        @Test
        fun `should return empty list of country definitions when no data found for given country`() {
            val countryCode = CountryCode.VNM
            every {
                countryDefinitionRepository.findAllByCountryAndState(countryCode, "")
            } returns emptyList()

            every { countryDefinitionRepository.findAllDefaultDefinitions() } returns emptyList()

            val definitions = countryDefinitionService.getAllCountryDefinitionsByCountry(countryCode)

            assertTrue(definitions.isEmpty())
        }

        @Test
        fun `should return leave compliance definition for annual leave`() {
            val definitionId = 2L
            val annualTypeId = 1L
            val defaultAnnualDefinition = buildCountryLeaveCompliance(definitionId, annualTypeId, 15.0)
            every {
                countryDefinitionRepository.findAllDefaultAndByCountryIn(setOf(CountryCode.VNM))
            } returns listOf(defaultAnnualDefinition)

            val countryLocation = CountryLocation(CountryCode.VNM)
            every {
                leaveComplianceServiceAdapter.getAnnualEntitlementByCountryLocation(setOf(countryLocation))
            } returns mapOf(countryLocation to AnnualLeaveEntitlement(20.0, 20.0, TimeOffUnit.DAYS, true))

            val timeoffType = buildTimeoffType(annualTypeId, "annual")
            every { timeoffTypeService.findAllByIds(setOf(annualTypeId)) } returns listOf(timeoffType)

            val countryLocationToCountryDefinitions =
                countryDefinitionService.getCountryLocationToCountryDefinitionsMap(setOf(countryLocation))
            assertEquals(1, countryLocationToCountryDefinitions.keys.size)

            val countryDefinitions = assertNonNull(countryLocationToCountryDefinitions[countryLocation])
            assertEquals(1, countryDefinitions.size)

            val countryDefinition = countryDefinitions[0]
            assertEquals(annualTypeId, countryDefinition.typeId)
            assertEquals(definitionId, countryDefinition.id)

            val definition = assertNonNull(countryDefinition.definition)
            assertEquals(definitionId, definition.id)
            assertNull(definition.countryCode)
            assertNull(definition.stateCode)
            assertTrue(definition.isRequired)
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, definition.status)
            assertEquals("Any Clause", definition.clause)
            assertEquals("Annual Leave", definition.description)
            assertEquals("ANNUAL", definition.basis)
            assertEquals(1, definition.validations.size)

            val validation = definition.validations.iterator().next()
            assertTrue(validation.isUnlimitedLeavesAllowed == false)
            assertNull(validation.maximumValue)
            assertEquals(20.0, validation.defaultValue)
            assertEquals(20.0, validation.minimumValue)
            assertEquals(TimeOffUnit.DAYS, validation.unit)
            assertEquals(listOf("ONBOARDING", "ACTIVE"), validation.allowedContractStatuses)
        }
    }

    private fun buildCountryLeaveCompliance(
        id: Long,
        typeId: Long,
        defaultEntitledValue: Double,
        country: CountryCode? = null,
        state: String? = null,
    ) = CountryDefinitionEntity(
        id = id,
        typeId = typeId,
        definition = buildDefinition(id, defaultEntitledValue, country, state)
    )

    private fun buildDefinition(
        id: Long,
        defaultEntitledValue: Double,
        country: CountryCode? = null,
        state: String? = null,
    ) =
        DefinitionEntity(
            id = id,
            isRequired = true,
            countryCode = country,
            stateCode = state,
            status = TimeOffTypeDefinitionStatus.ACTIVE,
            clause = "Any Clause",
            description = "Annual Leave",
            basis = "ANNUAL",
            validations = setOf(buildValidation(defaultEntitledValue)),
        )

    private fun buildValidation(defaultValue: Double) =
        TimeoffDefinitionValidationEntity(
            defaultValue = defaultValue,
            minimumValue = 12.0,
            maximumValue = null,
            unit = TimeOffUnit.DAYS,
            allowedContractStatuses = listOf("ONBOARDING", "ACTIVE"),
            isUnlimitedLeavesAllowed = false,
        )

    private fun buildTimeoffType(id: Long, key: String): TimeoffTypeDTO {
        val timeoff = TimeoffTypeDTO()
        timeoff.id = id
        timeoff.key = key
        return timeoff
    }

    private fun <T> assertNonNull(value: T?): T {
        assertNotNull(value)
        return value!!
    }
}
