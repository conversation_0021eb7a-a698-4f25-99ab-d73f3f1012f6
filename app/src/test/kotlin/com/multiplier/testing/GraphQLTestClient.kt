package com.multiplier.testing

import com.fasterxml.jackson.databind.ObjectMapper
import com.github.javafaker.Faker
import com.netflix.graphql.dgs.client.GraphQLResponse
import com.netflix.graphql.dgs.client.MonoGraphQLClient
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.ResponseEntity
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.client.RestTemplate
import org.springframework.web.reactive.function.client.WebClient
import java.io.File

data class HttpConfig(
    val userId: Long? = null,
    val userName: String = Faker().name().name(),
    val permissions: List<String>? = null,
    val experience: String? = ""
)

class HttpClient(val port: Int) {

    val objectMapper = ObjectMapper()

    inline fun <reified T> post(httpConfig: HttpConfig): ResponseEntity<T> {
        return RestTemplate().postForEntity(
            "http://localhost:$port/graphql",
            HttpEntity(
                "body",
                HttpHeaders().addUserContext(httpConfig)
            ),
            T::class.java
        )
    }

    inline fun <reified T> graphQLFileUpload(
        httpConfig: HttpConfig,
        query: String,
        file: File,
        headers: HttpHeaders = HttpHeaders()
    ): ResponseEntity<T> {
        val variables = mapOf("file" to null)
        val q = getResourceAsText(query) ?: query

        val params = mapOf(
            "variables" to variables,
            "query" to q
        )

        val bytes = file.readBytes()

        val resource = object : ByteArrayResource(bytes) {
            override fun getFilename(): String = "upload.csv"
        }

        val formData = LinkedMultiValueMap<String, Any>()
        formData.set("operations", objectMapper.writeValueAsString(params))
        formData.set("map", "{\"file\": [\"variables.file\"]}")
        formData.set("file", resource)

        return RestTemplate().postForEntity(
            "http://localhost:$port/graphql",
            HttpEntity(
                formData,
                headers.addUserContext(httpConfig)
            ),
            T::class.java
        )
    }
}

class GraphQLTestClient(port: Int) {
    private val webClient: WebClient = WebClient.create("http://localhost:$port/graphql")

    fun execute(
        query: String,
        variables: Map<String, Any>? = null,
        config: HttpConfig,
    ): GraphQLResponse {
        val client = MonoGraphQLClient.createWithWebClient(webClient) { headers -> headers.addUserContext(config) }

        val q = getResourceAsText(query) ?: query

        if (variables != null) {
            return client.reactiveExecuteQuery(q, variables).block()!!
        }

        return client.reactiveExecuteQuery(q).block()!!
    }
}

fun HttpHeaders.addUserContext(httpConfig: HttpConfig): HttpHeaders {
    this["user-context-user-name"] = httpConfig.userName
    this["user-context-experience"] = httpConfig.experience


    httpConfig.userId?.let {
        this["user-context-user-id"] = it.toString()
    }

    httpConfig.permissions?.let {
        this["user-context-auth"] = httpConfig.permissions.joinToString(",")
    }

    return this
}
