package com.multiplier.testing

import com.github.javafaker.Faker
import io.jsonwebtoken.Jwts
import io.jsonwebtoken.SignatureAlgorithm
import io.jsonwebtoken.io.Decoders
import java.io.File
import java.security.KeyFactory
import java.security.spec.PKCS8EncodedKeySpec
import java.util.*

fun getJwt(
    userId: Long? = null,
    auth: String? = null
): String {
    val privateKeyBase64 = getResourceAsText("/certificates/ecdsa-private-key.pem")!!
        .replace("-----BEGIN PRIVATE KEY-----", "")
        .replace("-----END PRIVATE KEY-----", "")
        .replace(System.lineSeparator(), "")

    val key = KeyFactory.getInstance("EC").generatePrivate(PKCS8EncodedKeySpec(Decoders.BASE64.decode(privateKeyBase64)))

    val exp = Date(Date().time + 60 * 60_000)

    val details = mapOf(
        "firstName" to <PERSON>aker().name().firstName(),
        "lastName" to <PERSON>aker().name().lastName(),
        "signupChannel" to "OPERATIONS",
        "langKey" to "en",
        "imageUrl" to null,
        "id" to (userId ?: 0),
    )

    return Jwts
        .builder()
        .setSubject("test")
        .claim("details", details)
        .claim("auth", auth)
        .signWith(key, SignatureAlgorithm.ES256)
        .setExpiration(exp)
        .compact()
}

fun getResourceAsText(path: String): String? =
    object {}.javaClass.getResource(path)?.readText()

fun getResourceAsBytes(path: String): ByteArray? =
    object {}.javaClass.getResource(path)?.readBytes()

fun getResourceAsFile(path: String): File {
    val resource = object {}.javaClass.getResource(path)
    return File(resource.toURI())
}

