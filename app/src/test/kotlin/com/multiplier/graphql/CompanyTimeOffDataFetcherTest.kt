package com.multiplier.graphql

import com.multiplier.timeoff.DgsConstants
import com.multiplier.timeoff.graphql.CompanyTimeOffDataFetcher
import com.multiplier.timeoff.types.TimeOffRequirementFilters
import com.multiplier.timeoff.types.TimeOffRequirements
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class CompanyTimeOffDataFetcherTest {

    @InjectMocks
    private lateinit var dataFetcher: CompanyTimeOffDataFetcher

    @Mock
    private lateinit var dfe: DgsDataFetchingEnvironment

    @Test
    fun `fetchCompany should return Company with correct id`() {
        val values = mapOf(DgsConstants.COMPANY.Id to "123")
        val company = dataFetcher.fetchCompany(values)
        assertEquals(123L, company.id)
    }

    @Test
    fun `fetchRequirements should return empty TimeOffRequirements`() {
        val filters = TimeOffRequirementFilters()
        val result = dataFetcher.fetchRequirements(filters, dfe)
        assertEquals(TimeOffRequirements(), result)
    }
}