# noinspection GraphQLUnresolvedReference

query ($representations:[_Any!]!) {
    _entities(representations: $representations){
        ...on
        Contract {
            timeOff {
                timeOffs {
                    id
                    status
                    createdOn
                    createdBy
                    noOfDays
                    description
                    startDate {
                        dateOnly
                        session
                    }
                    endDate {
                        dateOnly
                        session
                    }
                    type {
                        key
                        definition {
                            type
                            label
                        }
                    }
                }
            }
        }
    }
}
