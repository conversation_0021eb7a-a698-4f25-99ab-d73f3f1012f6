# noinspection GraphQLUnresolvedReference

query GetCountryTimeOffRequirements(
    $representations:[_Any!]!
    $contractType:ContractType!
    $contractTerm:ContractTerm
    $contractStatus:ContractStatus
){
    _entities(representations:$representations){
        ...on
        CountryCompliance {
            timeOffRequirements(contractType:$contractType term:$contractTerm contractStatus:$contractStatus){
                clause
                definitions {
                    type
                    required
                    label
                    description
                    validation {
                        __typename
                        ...on TimeOffFixedValidation {
                            minimum
                            maximum
                            defaultValue
                            unit __typename
                        }
                    }
                    __typename
                }
                __typename
            }
        }
    }
}

# example vars
# {"contractType":"EMPLOYEE","contractTerm":"PERMANENT","contractStatus":"ONBOARDING","representations":[{"__typename":"CountryCompliance","countryCode":"CHE","countryState":null}]}