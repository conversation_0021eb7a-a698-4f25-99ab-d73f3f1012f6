country,state,leave_type,leave_type_id (DB Index),allocation_basis,allocation_unit,"allocation_default","allocation_range (min:max) -1 for infinity",allocation_allowed_contract_statuses,is_mandatory,config_allocation_basis,config_allocation_is_prorated,description,clause,,,,
,,Annual,1,ANNUAL,DAYS,7,0:-1,"ONBOARDING,ACTIVE",TRUE,ANNUALLY,TRUE,Annual leave/Earned Leave - Default,"Please note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed.",,,,"Annual1ANNUALDAYS70:-1ONBOARDING,ACTIVETRUEANNUALLYTRUEAnnual leave/Earned Leave - DefaultPlease note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed."
AUS,,annual,1,ANNUAL,DAYS,20,20:-1,"ONBOARDING,ACTIVE",TRUE,ANNUALLY,TRUE,Annual leave/Earned Leave Leave - Australia,"Please note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed.",,,,"AUSannual1ANNUALDAYS2020:-1ONBOARDING,ACTIVETRUEANNUALLYTRUEAnnual leave/Earned Leave Leave - AustraliaPlease note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed."
,,Annual,1,ANNUAL,DAYS,7,0:-1,"ONBOARDING,ACTIVE",TRUE,ANNUALLY,TRUE,Annual leave/Earned Leave - Default,"Please note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed.",,,,"Annual1ANNUALDAYS70:-1ONBOARDING,ACTIVETRUEANNUALLYTRUEAnnual leave/Earned Leave - DefaultPlease note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed."
AUS,,annual,1,ANNUAL,DAYS,20,20:-1,"ONBOARDING,ACTIVE",TRUE,ANNUALLY,TRUE,Annual leave/Earned Leave Leave - Australia,"Please note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed.",,,,"AUSannual1ANNUALDAYS2020:-1ONBOARDING,ACTIVETRUEANNUALLYTRUEAnnual leave/Earned Leave Leave - AustraliaPlease note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed."
AUS,,sick,2,ANNUAL,DAYS,20,20:-1,"ONBOARDING,ACTIVE",TRUE,ANNUALLY,TRUE,Annual leave/Earned Leave Leave - Australia,"Please note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed.",,,,"AUSannual1ANNUALDAYS2020:-1ONBOARDING,ACTIVETRUEANNUALLYTRUEAnnual leave/Earned Leave Leave - AustraliaPlease note that Statutory, Bank Holidays and Public Holidays, based on employee's country of residence is duly followed."