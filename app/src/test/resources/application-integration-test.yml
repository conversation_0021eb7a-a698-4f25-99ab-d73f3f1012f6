spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:db;DB_CLOSE_DELAY=-1;INIT=CREATE SCHEMA IF NOT EXISTS timeoff
    username: sa
    password: sa
  liquibase:
    enabled: false
  jpa:
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        default_schema: timeoff

platform:
  frontend:
    baseurl: https://stage-app.usemultiplier.com
  timeoff:
    future-summary-generation:
      batch-size: 100

jwt:
  public-key: MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAETS938PH+qMKnKpr+9Vx987cerlaLM/E2xJxceMi10v0InLlttdEZ0rY6iY+MrHO5tk4XiLoUr9fj92M3NUkr9A==

grpc:
  client:
    core-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    leave-compliance-service:
      address: dns:///localhost:9092
      negotiation-type: PLAINTEXT
    ai-service:
      address: dns:///localhost:9093
      negotiation-type: PLAINTEXT
    access-control-service:
      address: dns:///localhost:9094
      negotiation-type: PLAINTEXT

  server:
    port: 0 #assigns a random port
    security:
      enabled: false

logging:
  level:
    org.springframework.security.web: DEBUG

growthbook:
  base-url: https://api-gateway.api.acc.staging.usemultiplier.com
  env-key: sdk-baFSLlWpxEWQ6bSZ  #stage
  refresh-frequency-ms: 15000