package com.multiplier.timeoff.integrationTests;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.SyncTaskExecutor;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.Executor;

/**
 * Test configuration to make async methods run synchronously for testing.
 * This ensures that @Async methods complete before the test continues.
 */
@Configuration
@EnableAsync
public class TestAsyncConfiguration implements AsyncConfigurer {

    @Override
    @Bean
    @Primary
    public Executor getAsyncExecutor() {
        // Use SyncTaskExecutor to run async methods synchronously in tests
        return new SyncTaskExecutor();
    }
}
