package com.multiplier.timeoff.service;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.common.transport.user.UserScopes;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.company.schema.holiday.LegalEntityHoliday;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.country.schema.Country;
import com.multiplier.country.schema.holiday.HolidayOuterClass;
import com.multiplier.growthbook.sdk.model.GBFeatureResult;
import com.multiplier.growthbook.sdk.model.GBFeatureSource;
import com.multiplier.timeoff.adapters.*;
import com.multiplier.timeoff.core.common.db.AuditUser;
import com.multiplier.timeoff.core.common.dto.TimeOffUsageSummaryDTO;
import com.multiplier.timeoff.core.common.dto.WorkshiftDTO;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.kafka.TimeoffKafkaPublisher;
import com.multiplier.timeoff.processor.TimeoffValidator;
import com.multiplier.timeoff.repository.*;
import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.schema.GrpcTimeOffForPayroll;
import com.multiplier.timeoff.service.builder.TimeoffEntitlementSpecificationBuilder;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.*;
import com.multiplier.timeoff.service.rules.TimeOffDefinitionRuleService;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.val;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.AccessDeniedException;

import java.time.*;
import java.time.DayOfWeek;
import java.util.*;

import static com.multiplier.timeoff.types.TimeOffUnit.DAYS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class TimeoffServiceTest {

    @Mock
    TimeoffEntitlementDBORepository timeoffEntitlementDBORepository;
    @Mock
    ContractServiceAdapter contractServiceAdapter;

    @Mock
    CurrentUser currentUser;
    @Mock
    TimeoffTypeRepository timeoffTypeRepository;
    @Mock
    TimeoffMapper timeoffMapper;
    @Mock
    TimeoffRepository timeoffRepository;
    @Mock
    TimeoffSummaryService timeoffSummaryService;
    @Mock
    CompanyServiceAdapter companyServiceAdapter;
    @Mock
    ApprovalServiceAdapter approvalServiceAdapter;
    @Mock
    TimeoffSummaryRepository summaryRepo;
    @Mock
    HolidayServiceAdapter holidayServiceAdapter;
    @Mock
    TimeoffKafkaPublisher timeoffKafkaPublisher;
    @Mock
    TimeoffValidator timeoffValidator;
    @Mock
    FeatureFlagService featureFlagService;
    @Mock
    CountryDefinitionRepository countryDefinitionRepository;
    @Mock
    CompanyDefinitionRepository companyDefinitionRepository;
    @Mock
    TimeoffEntitlementSpecificationBuilder timeoffEntitlementSpecificationBuilder;
    @Mock
    DefinitionService definitionService;
    @Mock
    TimeOffDefinitionRuleService timeOffDefinitionRuleService;
    @Mock
    MemberServiceAdapter memberServiceAdapter;
    @Mock
    DefinitionEntityRepository definitionEntityRepository;
    @Mock
    TimeoffTypeMapper timeoffTypeMapper;
    @Mock
    TimeOffTypeDefinitionMapper timeOffTypeDefinitionMapper;
    @Mock
    WorkshiftServiceAdapter workshiftServiceAdapter;
    @Mock
    TimeoffBreakdownService timeoffBreakdownService;
    @Mock
    TimeoffSummaryBreakdownService timeoffSummaryBreakdownService;

    @Mock
    GrpcTimeoffMapper grpcTimeoffMapper;

    @Mock
    AuthorizationService authorizationService;

    @Spy
    TimeoffEntitlementMapper timeoffEntitlementMapper = new TimeoffEntitlementMapper() {
    };

    @InjectMocks
    TimeoffService timeoffService;


    @BeforeEach
    void setup() {
        timeoffService.setContractServiceAdapter(contractServiceAdapter);
        timeoffService.setCompanyServiceAdapter(companyServiceAdapter);
        timeoffService.setApprovalServiceAdapter(approvalServiceAdapter);
    }

    @Nested
    class setEntitlementsToDefaultRequirements {
        @Captor
        ArgumentCaptor<List<TimeoffEntitlementDBO>> entitlementListDBOArgumentCaptor;

        @Test
        void when_contract_status_is_not_onboarding_then_throws() {
            long contractId = 1L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));

            when(contractServiceAdapter.findContractByContractId(contractId))
                    .thenReturn(ContractOuterClass.Contract.newBuilder()
                            .setId(contractId)
                            .setCompanyId(companyId)
                            .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                            .build());

            ValidationException ex = assertThrows(ValidationException.class, () -> timeoffService.setEntitlementsToDefaultRequirements(contractId));
            assertEquals("Can clear existing and generate default timeoff entitlements ONLY when contract status is ONBOARDING. ContractID=" + contractId, ex.getMessage());
        }

        @Test
        void when_contract_is_not_belongs_to_current_user_then_throw_error() {
            long contractId = 1L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));

            when(contractServiceAdapter.findContractByContractId(contractId))
                    .thenReturn(ContractOuterClass.Contract.newBuilder()
                            .setId(contractId)
                            .setCompanyId(200L)
                            .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                            .build());

            AccessDeniedException ex = assertThrows(AccessDeniedException.class, () -> timeoffService.setEntitlementsToDefaultRequirements(contractId));
            assertEquals("Access denied for user id : 10 for the contract id : 1", ex.getMessage());
        }

        /**
         * - No company definitions found
         * - Single Mandatory country definition found
         */
        @Test
        void shouldSaveTimeOffEntitlement_givenSingleMandatoryRequirement() {
            long contractId = 1L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setCountry("IND")
                    .build();

            DefinitionEntity definitionEntity = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation = new TimeoffDefinitionValidationEntity(
                    14.0, null, null, DAYS, List.of("ONBOARDING"), true
            );
            definitionEntity.setValidations(Set.of(validation));
            definitionEntity.setRequired(true);
            CountryDefinitionEntity countryDefinition = new CountryDefinitionEntity(1L, 1L, definitionEntity);
            countryDefinition.setUpdatedOn(LocalDateTime.now());

            Map<Long, TimeOffDefinitionEntity> definitionMap = Map.of(
                    1L, countryDefinition
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(definitionService.findTypeIdToAvailableDefMapForContract(contract)).thenReturn(definitionMap);
            when(timeoffTypeRepository.findAllById(Set.of(1L))).thenReturn(List.of(TimeoffTypeDBO.builder().id(1L).key("annual").build()));

            timeoffService.setEntitlementsToDefaultRequirements(contractId);

            verify(timeoffEntitlementDBORepository).deleteAllByContractId(contractId);
            verify(timeoffEntitlementDBORepository).flush();
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListDBOArgumentCaptor.capture());

            assertEquals("annual", entitlementListDBOArgumentCaptor.getValue().get(0).type().key());
        }

        @Test
        void shouldSaveTimeOffEntitlements_givenMultipleMandatoryRequirements() {
            long contractId = 1L;
            long memberId = 100L;
            Long companyId = 100L;
            Long companyUserId = 200L;

            DefinitionEntity definitionEntity1 = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation1 = new TimeoffDefinitionValidationEntity(
                    14.0, null, null, DAYS, List.of("ONBOARDING"), false
            );
            definitionEntity1.setValidations(Set.of(validation1));
            definitionEntity1.setRequired(false);

            DefinitionEntity definitionEntity2 = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation2 = new TimeoffDefinitionValidationEntity(
                    21.0, null, null, DAYS, List.of("ONBOARDING"), false
            );
            definitionEntity2.setValidations(Set.of(validation2));
            definitionEntity2.setRequired(true);

            val companyDefinition = new CompanyDefinitionEntity(1L, 1L, definitionEntity1, companyId, null, null, new ArrayList<>());
            companyDefinition.setUpdatedOn(LocalDateTime.of(2024,02,01,10,00,00));

            val countryDefinition = new CountryDefinitionEntity(1L, 2L, definitionEntity2);
            countryDefinition.setUpdatedOn(LocalDateTime.of(2024,02,05,10,00,00));

            val definitionMap = Map.of(
                    1L, companyDefinition,
                    2L, countryDefinition
            );

            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setMemberId(memberId)
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setCountry("IND")
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId))
                    .thenReturn(contract);
            when(definitionService.findTypeIdToAvailableDefMapForContract(contract))
                    .thenReturn(definitionMap);
            when(timeoffTypeRepository.findAllById(Set.of(1L,2L)))
                    .thenReturn(List.of(
                            TimeoffTypeDBO.builder()
                                    .id(1L)
                                    .key("annual")
                                    .build(),
                            TimeoffTypeDBO.builder()
                                    .id(2L)
                                    .key("sick")
                                    .build()
                    ));

            timeoffService.setEntitlementsToDefaultRequirements(contractId);

            verify(timeoffEntitlementDBORepository).deleteAllByContractId(contractId);
            verify(timeoffEntitlementDBORepository).flush();
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListDBOArgumentCaptor.capture());

            val savedEntitlements = entitlementListDBOArgumentCaptor.getValue();

            assertEquals(2, savedEntitlements.size());
            assertTrue(savedEntitlements.stream().anyMatch(e -> e.type().key().equals("annual") && e.value() == 14.0));
            assertTrue(savedEntitlements.stream().anyMatch(e -> e.type().key().equals("sick") && e.value() == 21.0));
        }

        @Test
        void shouldNotGenerateEntitlements_whenOnboardingFreelancer() {
            long contractId = 1L;
            long memberId = 100L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setMemberId(memberId)
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setCountry("IND")
                    .setType(ContractOuterClass.ContractType.FREELANCER)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);

            timeoffService.setEntitlementsToDefaultRequirements(contractId);

            verify(timeoffEntitlementDBORepository, never()).deleteAllByContractId(contractId);
            verify(timeoffEntitlementDBORepository, never()).flush();
            verify(timeoffEntitlementDBORepository, never()).saveAllAndFlush(any());
        }

        @Test
        void shouldNotGenerateEntitlements_whenOnboardingContractor() {
            long contractId = 1L;
            long memberId = 100L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setMemberId(memberId)
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setCountry("IND")
                    .setType(ContractOuterClass.ContractType.CONTRACTOR)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);

            timeoffService.setEntitlementsToDefaultRequirements(contractId);

            verify(timeoffEntitlementDBORepository, never()).deleteAllByContractId(contractId);
            verify(timeoffEntitlementDBORepository, never()).flush();
            verify(timeoffEntitlementDBORepository, never()).saveAllAndFlush(any());
        }

        @Test
        void shouldSaveTimeOffEntitlements_whenOnboardingHRMember_with_no_company_definitions() {
            // should only create entitlements for unpaid leave for HR member when there are no
            // company definitions
            long contractId = 1L;
            long memberId = 100L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setMemberId(memberId)
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setCountry("IND")
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .build();

            List<TimeoffTypeDBO> timeOffTypesForCompany = List.of(
                    TimeoffTypeDBO.builder()
                            .id(14L)
                            .key("unpaid")
                            .build()
            );

            Map<Long, TimeOffDefinitionEntity> definitionMap = Map.of(
                    14L, HrMemberTimeOffDefinitionKt.getUnpaidLeaveDefinition()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffTypeRepository.findAllById(Set.of(14L))).thenReturn(timeOffTypesForCompany);
            when(definitionService.findTypeIdToAvailableDefMapForContract(contract)).thenReturn(definitionMap);

            timeoffService.setEntitlementsToDefaultRequirements(contractId);

            verify(timeoffEntitlementDBORepository).deleteAllByContractId(contractId);
            verify(timeoffEntitlementDBORepository).flush();
            verify(timeoffEntitlementDBORepository, times(1)).saveAllAndFlush(entitlementListDBOArgumentCaptor.capture());

            assertEquals("unpaid", entitlementListDBOArgumentCaptor.getValue().get(0).type().key());
            assertEquals(365.0, entitlementListDBOArgumentCaptor.getValue().get(0).value());

        }

        @Test
        void shouldSaveTimeOffEntitlements_whenOnboardingHRMember_with_company_definitions() {
            long contractId = 1L;
            long memberId = 100L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setMemberId(memberId)
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setCountry("IND")
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .build();

            val timeOffTypesForCompany = List.of(
                    TimeoffTypeDBO.builder()
                            .id(1L)
                            .key("annual")
                            .build(),
                    TimeoffTypeDBO.builder()
                            .id(2L)
                            .key("sick")
                            .build(),
                    TimeoffTypeDBO.builder()
                            .id(14L)
                            .key("unpaid")
                            .build()
            );

            DefinitionEntity definitionEntity1 = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation1 = new TimeoffDefinitionValidationEntity(
                    14.0, null, null, DAYS, List.of("ONBOARDING"), false
            );
            definitionEntity1.setValidations(Set.of(validation1));
            definitionEntity1.setRequired(false);

            val companyAnnualLeaveDefinition = new CompanyDefinitionEntity(1L, 1L, definitionEntity1, companyId, null, null,  new ArrayList<>());
            companyAnnualLeaveDefinition.setUpdatedOn(LocalDateTime.of(2024,02,01,10,00,00));


            Map<Long, TimeOffDefinitionEntity> definitionsForContract = Map.of(1L, companyAnnualLeaveDefinition,
                    14L, HrMemberTimeOffDefinitionKt.getUnpaidLeaveDefinition());

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 14L))).thenReturn(timeOffTypesForCompany);
            when(definitionService.findTypeIdToAvailableDefMapForContract(contract)).thenReturn(definitionsForContract);

            timeoffService.setEntitlementsToDefaultRequirements(contractId);

            verify(timeoffEntitlementDBORepository).deleteAllByContractId(contractId);
            verify(timeoffEntitlementDBORepository).flush();
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListDBOArgumentCaptor.capture());

            val savedEntitlements = entitlementListDBOArgumentCaptor.getValue();
            assertEquals(2, savedEntitlements.size());

            assertTrue(savedEntitlements.stream().anyMatch(e -> e.type().key().equals("annual") && e.value() == 14.0));
            assertTrue(savedEntitlements.stream().anyMatch(e -> e.type().key().equals("unpaid") && e.value() == 365.0));
        }

    }

    @Nested
    class setEntitlementsToDefaultRequirementsBulk {

        @Captor
        ArgumentCaptor<List<TimeoffEntitlementDBO>> entitlementListDBOArgumentCaptor;

        @Test
        void should_do_nothing_if_contract_ids_are_empty() {
            timeoffService.setEntitlementsToDefaultRequirementsBulk(Collections.emptyList());
            verify(definitionService, never()).findAvailableDefinitionsByContract(any(), any());
            verify(timeoffEntitlementDBORepository, never()).deleteAllByContractId(any());
            verify(timeoffTypeRepository, never()).findAllById(any());
            verify(timeoffEntitlementDBORepository, never()).saveAllAndFlush(any());
            verify(timeoffSummaryService, never()).onTimeOffEntitlementsResetBulk(any(), any());
        }

        @Test
        void should_do_nothing_if_all_contracts_are_not_valid() {
            val contractIds = List.of(1L, 2L, 3L);
            val myCompanyId = 100L;
            val otherCompanyId = 101L;
            val contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(otherCompanyId).build(),
                    ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(myCompanyId).setType(ContractOuterClass.ContractType.FREELANCER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(3L).setCompanyId(myCompanyId).setType(ContractOuterClass.ContractType.EMPLOYEE).setStatus(ContractOuterClass.ContractStatus.ACTIVE).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(200L, myCompanyId));
            when(contractServiceAdapter.getContractsByIdsAnyStatus(contractIds, true)).thenReturn(contracts);

            timeoffService.setEntitlementsToDefaultRequirementsBulk(contractIds);

            verify(definitionService, never()).findAvailableDefinitionsByContract(any(), any());
            verify(timeoffEntitlementDBORepository, never()).deleteAllByContractId(any());
            verify(timeoffTypeRepository, never()).findAllById(any());
            verify(timeoffEntitlementDBORepository, never()).saveAllAndFlush(any());
            verify(timeoffSummaryService, never()).onTimeOffEntitlementsResetBulk(any(), any());
        }

        @Test
        void should_set_default_requirements_for_valid_contracts() {
            val contractIds = List.of(1L, 2L, 3L, 4L);
            val myCompanyId = 100L;

            val contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(myCompanyId).setType(ContractOuterClass.ContractType.EMPLOYEE).setStatus(ContractOuterClass.ContractStatus.ONBOARDING).build(),
                    ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(myCompanyId).setType(ContractOuterClass.ContractType.HR_MEMBER).setStatus(ContractOuterClass.ContractStatus.ONBOARDING).build(),
                    ContractOuterClass.Contract.newBuilder().setId(3L).setCompanyId(myCompanyId).setType(ContractOuterClass.ContractType.HR_MEMBER).setStatus(ContractOuterClass.ContractStatus.ONBOARDING).build(),
                    ContractOuterClass.Contract.newBuilder().setId(4L).setCompanyId(myCompanyId).setType(ContractOuterClass.ContractType.HR_MEMBER).setStatus(ContractOuterClass.ContractStatus.ACTIVE).build()
            );
            Map<Long, Map<Long, TimeOffDefinitionEntity>> definitionsByContract = Map.of(
                    1L, Map.of(
                            1L, new CountryDefinitionEntity(1L, 1L, buildDefinitionEntity(14.0, false)),
                            2L, new CompanyDefinitionEntity(1L, 2L, buildDefinitionEntity(5.0, false), myCompanyId, null, null,  new ArrayList<>())
                    ),
                    2L, Map.of(
                            3L, new CompanyDefinitionEntity(2L, 3L, buildDefinitionEntity(12.0, false), myCompanyId, null, null,  new ArrayList<>())
                    )
            );
            val timeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annual").build(),
                    TimeoffTypeDBO.builder().id(2L).key("sick").build(),
                    TimeoffTypeDBO.builder().id(3L).key("maternity").build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(200L, myCompanyId));
            when(contractServiceAdapter.getContractsByIdsAnyStatus(contractIds, true)).thenReturn(contracts);
            when(definitionService.findAvailableDefinitionsByContract(eq(myCompanyId), anyCollection())).thenReturn(definitionsByContract);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L, 3L))).thenReturn(timeOffTypes);

            timeoffService.setEntitlementsToDefaultRequirementsBulk(contractIds);

            verify(timeoffEntitlementDBORepository).deleteAllByContractIdIn(Set.of(1L, 2L, 3L));
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListDBOArgumentCaptor.capture());

            val savedEntitlements = entitlementListDBOArgumentCaptor.getValue();
            assertEquals(3, savedEntitlements.size());
            assertTrue(savedEntitlements.stream().anyMatch(e -> isEntitlementMatched(e, 1L, "annual", definitionsByContract.get(1L).get(1L), 14.0)));
            assertTrue(savedEntitlements.stream().anyMatch(e -> isEntitlementMatched(e, 1L, "sick", definitionsByContract.get(1L).get(2L), 5.0)));
            assertTrue(savedEntitlements.stream().anyMatch(e -> isEntitlementMatched(e, 2L, "maternity", definitionsByContract.get(2L).get(3L), 12.0)));
        }

        private boolean isEntitlementMatched(TimeoffEntitlementDBO entitlement,
                                             Long expectedContractId,
                                             String expectedType,
                                             TimeOffDefinitionEntity expectedDefinitionEntity,
                                             Double expectedEntitlement) {
            return Objects.equals(entitlement.contractId(), expectedContractId) &&
                    entitlement.type().key().equals(expectedType) &&
                    entitlement.definition().equals(expectedDefinitionEntity.getDefinition()) &&
                    Objects.equals(entitlement.value(), expectedEntitlement);
        }
    }

    @Nested
    class updateTimeOffEntitlements {

        @Captor
        private ArgumentCaptor<TimeoffEntitlementDBO> entitlementArgumentCaptor;

        @Captor
        private ArgumentCaptor<Set<TimeoffEntitlementDBO>> entitlementSetArgumentCaptor;

        @Test
        void should_throw_error_when_contractor_update_entitlements() {
            long contractId = 1L;
            long memberId = 100L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setMemberId(memberId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setCountry("IND")
                    .setType(ContractOuterClass.ContractType.CONTRACTOR)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);

            // Precompute arguments outside the lambda
            List<ContractUpdateTimeOffEntitlementsInput> entitlements = List.of(); // Empty list
            ValidationException ex = assertThrows(ValidationException.class,
                    () -> timeoffService.updateTimeOffEntitlements(contractId, entitlements) // Only the method under test is in the lambda
            );
            assertEquals("Add/edit entitlements not supported for contract type : CONTRACTOR (contract id : 1)", ex.getMessage());
        }

        @Test
        void should_throw_error_when_freelancer_update_entitlements() {
            long contractId = 1L;
            long memberId = 100L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setMemberId(memberId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setCountry("IND")
                    .setType(ContractOuterClass.ContractType.FREELANCER)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);

            // Precompute arguments outside the lambda
            List<ContractUpdateTimeOffEntitlementsInput> entitlements = List.of(); // Empty list
            ValidationException ex = assertThrows(ValidationException.class,
                    () -> timeoffService.updateTimeOffEntitlements(contractId, entitlements) // Only the method under test is in the lambda
            );
            assertEquals("Add/edit entitlements not supported for contract type : FREELANCER (contract id : 1)", ex.getMessage());
        }

        @Test
        void should_throw_error_when_current_user_try_to_modify_entitlement_of_another_company_contract() {
            long contractId = 1L;
            Long companyId = 100L;
            Long companyUserId = 200L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId))
                    .thenReturn(ContractOuterClass.Contract.newBuilder()
                            .setId(contractId)
                            .setCompanyId(200L)
                            .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                            .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                            .build());


            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("annual").build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("sabbatical").build()
            );

            AccessDeniedException ex = assertThrows(AccessDeniedException.class, () -> timeoffService.updateTimeOffEntitlements(contractId, inputs));
            assertEquals("Access denied for user id : 10 for the contract id : 1", ex.getMessage());
        }

        @Test
        void should_throw_error_when_hr_member_add_new_entitlements() {
            long contractId = 1L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();
            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder()
                            .typeId(1L)
                            .value(14.0)
                            .unit(DAYS)
                            .type(TimeoffTypeDBO.builder().key("annual").build())
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .typeId(2L)
                            .value(7.0)
                            .unit(DAYS)
                            .type(TimeoffTypeDBO.builder().key("sick").build())
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .typeId(14L)
                            .value(365.0)
                            .unit(DAYS)
                            .type(TimeoffTypeDBO.builder().key("unpaid").build())
                            .build()
            );
            List<TimeOffDefinitionEntity> definitions = List.of(
                    new CompanyDefinitionEntity(1L, 2L, new DefinitionEntity(), companyId, null, null,  new ArrayList<>()),
                    new CompanyDefinitionEntity(2L, 10L, new DefinitionEntity(), companyId, null, null,  new ArrayList<>())
            );
            val timeOffTypes = List.of(
                    TimeoffTypeDBO.builder()
                            .id(1L)
                            .key("annual")
                            .build(),
                    TimeoffTypeDBO.builder()
                            .id(2L)
                            .key("sick")
                            .build(),
                    TimeoffTypeDBO.builder()
                            .id(10L)
                            .key("festival")
                            .build(),
                    TimeoffTypeDBO.builder()
                            .id(14L)
                            .key("unpaid")
                            .build());

            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("annual").build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("sick").build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("unpaid").build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("festival").build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(definitions);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L, 10L, 14L))).thenReturn(timeOffTypes);

            ValidationException ex = assertThrows(ValidationException.class, () -> timeoffService.updateTimeOffEntitlements(contractId, inputs));
            assertEquals("Cannot add new entitlements for HR_MEMBER contract type (contract id :1)", ex.getMessage());
        }

        @Test
        void should_throw_error_when_hr_member_delete_entitlements() {
            long contractId = 1L;
            Long companyId = 100L;
            Long companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();
            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder()
                            .typeId(1L)
                            .value(14.0)
                            .unit(DAYS)
                            .type(TimeoffTypeDBO.builder().key("annual").build())
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .typeId(2L)
                            .value(7.0)
                            .unit(DAYS)
                            .type(TimeoffTypeDBO.builder().key("sick").build())
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .typeId(14L)
                            .value(365.0)
                            .unit(DAYS)
                            .type(TimeoffTypeDBO.builder().key("unpaid").build())
                            .build()
            );
            List<TimeOffDefinitionEntity> definitions = List.of(
                    new CompanyDefinitionEntity(1L, 2L, new DefinitionEntity(), companyId, null, null, new ArrayList<>()),
                    new CompanyDefinitionEntity(2L, 10L, new DefinitionEntity(), companyId, null, null, new ArrayList<>())
            );
            val timeOffTypes = List.of(
                    TimeoffTypeDBO.builder()
                            .id(1L)
                            .key("annual")
                            .build(),
                    TimeoffTypeDBO.builder()
                            .id(2L)
                            .key("sick")
                            .build(),
                    TimeoffTypeDBO.builder()
                            .id(10L)
                            .key("festival")
                            .build(),
                    TimeoffTypeDBO.builder()
                            .id(14L)
                            .key("unpaid")
                            .build());

            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("annual").build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("unpaid").build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(definitions);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L, 10L, 14L))).thenReturn(timeOffTypes);

            ValidationException ex = assertThrows(ValidationException.class, () -> timeoffService.updateTimeOffEntitlements(contractId, inputs));
            assertEquals("Cannot delete entitlements for HR_MEMBER contract type (contract id :1)", ex.getMessage());
        }

        @Test
        void should_throw_error_when_hr_member_try_to_add_entitlement_value_larger_than_the_limit() {
            val contractId = 100L;
            val companyId = 200L;
            val companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();
            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder()
                            .id(1000L)
                            .typeId(2L)
                            .value(14.0)
                            .unit(DAYS)
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .id(2000L)
                            .typeId(14L)
                            .value(365.0)
                            .unit(DAYS)
                            .type(TimeoffTypeDBO.builder().key("unpaid").build())
                            .build()
            );
            val timeoffTypes = List.of(
                    TimeoffTypeDBO.builder().id(2L).key("sick").build(),
                    TimeoffTypeDBO.builder().id(14L).key("unpaid").build()
            );


            DefinitionEntity sickLeaveDefinitionEntity = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation = new TimeoffDefinitionValidationEntity(
                    14.0, null, 20.0, DAYS, List.of("ONBOARDING"), false
            );
            sickLeaveDefinitionEntity.setValidations(Set.of(validation));
            sickLeaveDefinitionEntity.setRequired(false);

            val sickLeaveCompanyDefinition = new CompanyDefinitionEntity(2L, 2L, sickLeaveDefinitionEntity, companyId, null, null, new ArrayList<>());


            List<TimeOffDefinitionEntity> timeOffDefinitions = List.of(sickLeaveCompanyDefinition);

            // invalid value for annual leave entitlement
            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("sick").value(30.0).unit(DAYS).build(), // larger than 20 -> should throw error
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("unpaid").value(10.0).unit(DAYS).build());

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(timeOffDefinitions);
            when(timeoffTypeRepository.findAllById(Set.of(2L, 14L))).thenReturn(timeoffTypes);

            ValidationException ex = assertThrows(ValidationException.class, () ->
                    timeoffService.updateTimeOffEntitlements(contractId, inputs));
            assertEquals("Entitlement value must be in [min, max]=[null, 20.0]. ContractID=100. Input=" + inputs.get(0), ex.getMessage());
        }

        @Test
        void should_throw_error_when_hr_member_try_to_add_entitlement_value_lower_than_the_limit() {
            val contractId = 100L;
            val companyId = 200L;
            val companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();
            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder()
                            .id(1000L)
                            .typeId(1L)
                            .value(14.0)
                            .unit(DAYS)
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .id(2000L)
                            .typeId(14L)
                            .value(365.0)
                            .unit(DAYS)
                            .type(TimeoffTypeDBO.builder().key("unpaid").build())
                            .build()
            );
            val timeoffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annual").build(),
                    TimeoffTypeDBO.builder().id(14L).key("unpaid").build()
            );

            // no company definitions
            List<TimeOffDefinitionEntity> timeOffDefinitions = List.of();

            // invalid value for annual leave entitlement
            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("annual").value(-1.0).unit(DAYS).build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("unpaid").value(10.0).unit(DAYS).build());

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(timeOffDefinitions);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 14L))).thenReturn(timeoffTypes);

            ValidationException ex = assertThrows(ValidationException.class, () ->
                    timeoffService.updateTimeOffEntitlements(contractId, inputs));
            assertEquals("Entitlement value must be in [min, max]=[0.0, null]. ContractID=100. Input=" + inputs.get(0), ex.getMessage());
        }

        @Test
        void should_update_entitlements_when_hr_member_requested() {
            val contractId = 100L;
            val companyId = 200L;
            val companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();
            val existingEntitlements = List.of(
                    // Annual leave from default definition
                    TimeoffEntitlementDBO.builder()
                            .id(1000L)
                            .typeId(1L)
                            .value(14.0)
                            .unit(DAYS)
                            .definitionId(null)
                            .build(),
                    // Unpaid leave from company definition
                    TimeoffEntitlementDBO
                            .builder()
                            .id(2000L)
                            .typeId(14L)
                            .value(365.0)
                            .definitionId(500L)
                            .unit(DAYS)
                            .type(TimeoffTypeDBO.builder().key("unpaid").build())
                            .build(),
                    // Sick leave from company definition
                    TimeoffEntitlementDBO.builder()
                            .id(3000L)
                            .typeId(2L)
                            .value(7.0)
                            .unit(DAYS)
                            .definitionId(200L)
                            .build()
            );
            val timeoffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annual").build(),
                    TimeoffTypeDBO.builder().id(2L).key("sick").build(),
                    TimeoffTypeDBO.builder().id(14L).key("unpaid").build()
            );


            DefinitionEntity sickLeaveDefinitionEntity = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation = new TimeoffDefinitionValidationEntity(
                    14.0, null, 20.0, DAYS, List.of("ONBOARDING"), false
            );
            sickLeaveDefinitionEntity.setValidations(Set.of(validation));
            sickLeaveDefinitionEntity.setRequired(false);

            DefinitionEntity unpaidLeaveDefinitionEntity = new DefinitionEntity();
            TimeoffDefinitionValidationEntity unpaidLeaveValidation = new TimeoffDefinitionValidationEntity(
                    14.0, null, 365.0, DAYS, List.of("ONBOARDING"), false
            );
            unpaidLeaveDefinitionEntity.setValidations(Set.of(unpaidLeaveValidation));
            unpaidLeaveDefinitionEntity.setRequired(false);

            val unpaidLeaveCompanyDefinition = new CompanyDefinitionEntity(500L, 14L, unpaidLeaveDefinitionEntity, companyId, null, null, new ArrayList<>());
            val sickLeaveCompanyDefinition = new CompanyDefinitionEntity(200L, 2L, sickLeaveDefinitionEntity, companyId, null, null, new ArrayList<>());


            List<TimeOffDefinitionEntity> timeOffDefinitions = List.of(sickLeaveCompanyDefinition, unpaidLeaveCompanyDefinition);

            // invalid value for annual leave entitlement
            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("annual").value(16.0).unit(DAYS).build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("sick").value(10.0).unit(DAYS).build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("unpaid").value(365.0).unit(DAYS).build());

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(timeOffDefinitions);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L, 14L))).thenReturn(timeoffTypes);

            timeoffService.updateTimeOffEntitlements(contractId, inputs);

            verify(timeoffEntitlementDBORepository, times(3)).saveAndFlush(entitlementArgumentCaptor.capture());

            val updatedEntitlements = entitlementArgumentCaptor.getAllValues();
            assertEquals(3.0, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream().anyMatch(ent -> ent.typeId().equals(1L) && ent.value().equals(16.0) && ent.definitionId() == null));
            assertTrue(updatedEntitlements.stream().anyMatch(ent -> ent.typeId().equals(2L) && ent.value().equals(10.0) && ent.definitionId().equals(200L)));
            assertTrue(updatedEntitlements.stream().anyMatch(ent -> ent.typeId().equals(14L) && ent.value().equals(365.0) && ent.definitionId().equals(500L)));
        }

        @Test
        void should_throw_error_when_eor_employee_try_to_delete_entitlements_for_active_contracts() {
            val contractId = 100L;
            val companyId = 200L;
            val companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();
            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder()
                            .typeId(1L)
                            .value(14.0)
                            .unit(DAYS)
                            .definitionId(1000L)
                            .build(),
                    TimeoffEntitlementDBO.builder()
                            .typeId(2L)
                            .value(7.0)
                            .unit(DAYS)
                            .definitionId(2000L)
                            .build()
            );
            val timeoffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annual").build(),
                    TimeoffTypeDBO.builder().id(2L).key("sick").build()
            );

            DefinitionEntity sickLeaveDefinitionEntity = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation = new TimeoffDefinitionValidationEntity(
                    14.0, null, 20.0, DAYS, List.of("ONBOARDING"), false
            );
            sickLeaveDefinitionEntity.setId(2000L);
            sickLeaveDefinitionEntity.setValidations(Set.of(validation));
            sickLeaveDefinitionEntity.setRequired(true);

            DefinitionEntity annualLeaveDefinitionEntity = new DefinitionEntity();
            TimeoffDefinitionValidationEntity unpaidLeaveValidation = new TimeoffDefinitionValidationEntity(
                    14.0, null, 365.0, DAYS, List.of("ONBOARDING"), false
            );
            annualLeaveDefinitionEntity.setId(1000L);
            annualLeaveDefinitionEntity.setValidations(Set.of(unpaidLeaveValidation));
            annualLeaveDefinitionEntity.setRequired(false);

            val annualLeaveCompanyDefinition = new CompanyDefinitionEntity(1000L, 1L, annualLeaveDefinitionEntity, companyId, null, null, new ArrayList<>());
            val sickLeaveCompanyDefinition = new CountryDefinitionEntity(2000L, 2L, sickLeaveDefinitionEntity);

            List<TimeOffDefinitionEntity> timeOffDefinitions = List.of(annualLeaveCompanyDefinition, sickLeaveCompanyDefinition);

            // no annual leaves => hence intention is to delete sick leave
            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("sick").value(10.0).unit(DAYS).build());

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L))).thenReturn(timeoffTypes);
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(timeOffDefinitions);
            ValidationException ex = assertThrows(ValidationException.class, () ->
                    timeoffService.updateTimeOffEntitlements(contractId, inputs));
            assertEquals("Deleting entitlements [1] not permitted for contract (id : 100 , status :ACTIVE)", ex.getMessage());
        }

        @Test
        void should_throw_error_when_eor_employee_try_to_delete_mandatory_entitlements() {
            val contractId = 100L;
            val companyId = 200L;
            val companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();
            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder()
                            .id(1000L)
                            .typeId(1L)
                            .definitionId(100L)
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .id(2000L)
                            .typeId(2L)
                            .definitionId(200L)
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .id(3000L)
                            .typeId(3L)
                            .definitionId(300L)
                            .build()
            );
            val timeoffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annual").build(),
                    TimeoffTypeDBO.builder().id(2L).key("sick").build(),
                    TimeoffTypeDBO.builder().id(3L).key("sabbatical").build()
            );
            DefinitionEntity annualDefinition = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation1 = new TimeoffDefinitionValidationEntity(
                    14.0, null, null, DAYS, List.of("ONBOARDING"), false
            );
            annualDefinition.setId(100L);
            annualDefinition.setValidations(Set.of(validation1));
            annualDefinition.setRequired(true);

            DefinitionEntity companySickLeaveDefinition = new DefinitionEntity();
            TimeoffDefinitionValidationEntity companySickLeaveValidation = new TimeoffDefinitionValidationEntity(
                    14.0, null, null, DAYS, List.of("ONBOARDING"), false
            );
            companySickLeaveDefinition.setId(200L);
            companySickLeaveDefinition.setValidations(Set.of(companySickLeaveValidation));
            companySickLeaveDefinition.setRequired(false);

            DefinitionEntity sabbaticalDefinition = new DefinitionEntity();
            TimeoffDefinitionValidationEntity sabbaticalValidation = new TimeoffDefinitionValidationEntity(
                    14.0, null, null, DAYS, List.of("ONBOARDING"), false
            );
            sabbaticalDefinition.setId(300L);
            sabbaticalDefinition.setValidations(Set.of(sabbaticalValidation));
            sabbaticalDefinition.setRequired(true);

            val countryDefinitionForAnnualLeave = new CountryDefinitionEntity(1L, 1L, annualDefinition);
            val companyDefinitionForSickLeave = new CompanyDefinitionEntity(2L, 2L, companySickLeaveDefinition, companyId, null, null, new ArrayList<>());
            val countryDefinitionForSabbaticalLeave = new CountryDefinitionEntity(3L, 3L, sabbaticalDefinition);


            List<TimeOffDefinitionEntity> timeOffDefinitions = List.of(countryDefinitionForAnnualLeave, companyDefinitionForSickLeave, countryDefinitionForSabbaticalLeave);

            DefinitionEntity countrySickLeaveDefinition = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation = new TimeoffDefinitionValidationEntity(
                    14.0, null, null, DAYS, List.of("ONBOARDING"), false
            );
            countrySickLeaveDefinition.setValidations(Set.of(validation));
            countrySickLeaveDefinition.setRequired(true);

            val countryDefinitionForSickLeave = new CountryDefinitionEntity(142L, 2L, countrySickLeaveDefinition);
            Map<Long, CountryDefinitionEntity> countryDefinitionMap = Map.of(
                    1L, countryDefinitionForAnnualLeave,
                    2L, countryDefinitionForSickLeave,
                    3L, countryDefinitionForSabbaticalLeave
            );

            // no keys for sick and sabbatical -> Hence intention is to delete them
            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("annual").build());

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L, 3L))).thenReturn(timeoffTypes);
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(timeOffDefinitions);
            when(definitionService.getTypeIdToEligibleCountryDefinitionMap(contract)).thenReturn(countryDefinitionMap);

            ValidationException ex = assertThrows(ValidationException.class, () ->
                    timeoffService.updateTimeOffEntitlements(contractId, inputs));

            assertEquals("Cannot delete 'Required' timeoff entitlements. Missing entitlements [2, 3] from input are required entitlements for contract id : 100", ex.getMessage());
        }

        @Test
        void should_throw_error_when_eor_employee_try_to_add_entitlement_value_larger_than_the_limit() {
            val contractId = 100L;
            val companyId = 200L;
            val companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();
            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder()
                            .id(1000L)
                            .typeId(1L)
                            .definitionId(752L)
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .id(2000L)
                            .typeId(2L)
                            .definitionId(750L)
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .id(3000L)
                            .typeId(3L)
                            .definitionId(751L)
                            .build()
            );
            val timeoffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annual").build(),
                    TimeoffTypeDBO.builder().id(2L).key("sick").build(),
                    TimeoffTypeDBO.builder().id(3L).key("sabbatical").build()
            );
            DefinitionEntity definitionEntity1 = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation1 = new TimeoffDefinitionValidationEntity(
                    14.0, null, 20.0, DAYS, List.of("ONBOARDING"), false
            );
            definitionEntity1.setId(750L);
            definitionEntity1.setValidations(Set.of(validation1));
            definitionEntity1.setRequired(true);

            DefinitionEntity definitionEntity2 = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation2 = new TimeoffDefinitionValidationEntity(
                    14.0, null, null, DAYS, List.of("ONBOARDING"), false
            );
            definitionEntity2.setId(751L);
            definitionEntity2.setValidations(Set.of(validation2));
            definitionEntity2.setRequired(false);

            DefinitionEntity definitionEntity3 = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation3 = new TimeoffDefinitionValidationEntity(
                    14.0, null, 20.0, DAYS, List.of("ONBOARDING"), false
            );
            definitionEntity3.setId(752L);
            definitionEntity3.setValidations(Set.of(validation1));
            definitionEntity3.setRequired(true);

            List<TimeOffDefinitionEntity> timeOffDefinitions = List.of(
                    new CountryDefinitionEntity(1L, 1L, definitionEntity3),
                    new CompanyDefinitionEntity(2L, 2L, definitionEntity1, companyId, null, null, new ArrayList<>()),
                    new CountryDefinitionEntity(3L, 3L, definitionEntity2)
            );

            // no keys for sabbatical -> Hence intention is to delete
            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("annual").value(21.0).unit(DAYS).build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("sick").value(10.0).unit(DAYS).build());

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L, 3L))).thenReturn(timeoffTypes);
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(timeOffDefinitions);
            when(definitionService.getTypeIdToEligibleCountryDefinitionMap(contract)).thenReturn(Collections.emptyMap());

            ValidationException ex = assertThrows(ValidationException.class, () ->
                    timeoffService.updateTimeOffEntitlements(contractId, inputs));
            assertEquals("Entitlement value must be in [min, max]=[null, 20.0]" + ". ContractID=" + contractId
                    + ". Input=" + inputs.get(0), ex.getMessage());
        }

        @Test
        void should_throw_when_eor_employee_try_to_add_entitlement_value_lower_than_the_limit() {
            val contractId = 100L;
            val companyId = 200L;
            val companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();
            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder()
                            .id(1000L)
                            .typeId(1L)
                            .definitionId(852L)
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .id(2000L)
                            .typeId(2L)
                            .definitionId(850L)
                            .build(),
                    TimeoffEntitlementDBO
                            .builder()
                            .id(3000L)
                            .typeId(3L)
                            .definitionId(851L)
                            .build()
            );
            val timeoffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annual").build(),
                    TimeoffTypeDBO.builder().id(2L).key("sick").build(),
                    TimeoffTypeDBO.builder().id(3L).key("sabbatical").build()
            );
            DefinitionEntity definitionEntity1 = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation1 = new TimeoffDefinitionValidationEntity(
                    14.0, 7.0, 20.0, DAYS, List.of("ONBOARDING"), false
            );
            definitionEntity1.setId(850L);
            definitionEntity1.setValidations(Set.of(validation1));
            definitionEntity1.setRequired(true);

            DefinitionEntity definitionEntity2 = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation2 = new TimeoffDefinitionValidationEntity(
                    14.0, null, null, DAYS, List.of("ONBOARDING"), false
            );
            definitionEntity2.setId(851L);
            definitionEntity2.setValidations(Set.of(validation2));
            definitionEntity2.setRequired(false);

            DefinitionEntity definitionEntity3 = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation3 = new TimeoffDefinitionValidationEntity(
                    14.0, 7.0, 20.0, DAYS, List.of("ONBOARDING"), false
            );
            definitionEntity3.setId(852L);
            definitionEntity3.setValidations(Set.of(validation3));
            definitionEntity3.setRequired(true);

            List<TimeOffDefinitionEntity> timeOffDefinitions = List.of(
                    new CountryDefinitionEntity(1L, 1L, definitionEntity3),
                    new CompanyDefinitionEntity(2L, 2L, definitionEntity1, companyId, null, null, new ArrayList<>()),
                    new CountryDefinitionEntity(3L, 3L, definitionEntity2)
            );

            // no keys for sabbatical -> Hence intention is to delete
            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("annual").value(16.0).unit(DAYS).build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("sick").value(6.0).unit(DAYS).build());

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L, 3L))).thenReturn(timeoffTypes);
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(timeOffDefinitions);
            when(definitionService.getTypeIdToEligibleCountryDefinitionMap(contract)).thenReturn(Collections.emptyMap());


            ValidationException ex = assertThrows(ValidationException.class, () ->
                    timeoffService.updateTimeOffEntitlements(contractId, inputs));
            assertEquals("Entitlement value must be in [min, max]=[7.0, 20.0]" + ". ContractID=" + contractId
                    + ". Input=" + inputs.get(1), ex.getMessage());
        }

        @Test
        void should_update_and_delete_entitlements_successfully_when_eor_employee_requested() {
            val contractId = 100L;
            val companyId = 200L;
            val companyUserId = 200L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder()
                            .id(1L)
                            .typeId(1L)
                            .definitionId(1000L)
                            .value(12.0)
                            .unit(DAYS)
                            .build(),
                    TimeoffEntitlementDBO.builder()
                            .id(3L)
                            .typeId(3L)
                            .definitionId(3000L)
                            .value(5.0)
                            .unit(DAYS)
                            .build()
            );

            val timeoffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annual").build(),
                    TimeoffTypeDBO.builder().id(2L).key("sick").build(),
                    TimeoffTypeDBO.builder().id(3L).key("sabbatical").build()
            );

            DefinitionEntity annualDefinition = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation1 = new TimeoffDefinitionValidationEntity(
                    14.0, null, 20.0, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
            );
            annualDefinition.setValidations(Set.of(validation1));
            annualDefinition.setRequired(true);
            annualDefinition.setId(1000L);

            DefinitionEntity sickDefinition = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation2 = new TimeoffDefinitionValidationEntity(
                    7.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
            );
            sickDefinition.setValidations(Set.of(validation2));
            sickDefinition.setRequired(false);
            sickDefinition.setId(2000L);

            DefinitionEntity sabbaticalDefinition = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation3 = new TimeoffDefinitionValidationEntity(
                    5.0, null, 20.0, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
            );
            sabbaticalDefinition.setValidations(Set.of(validation1));
            sabbaticalDefinition.setRequired(false);
            sabbaticalDefinition.setId(3000L);

            val annualCountryDefinition = new CountryDefinitionEntity(1L, 1L, annualDefinition);
            val sickCompanyDefinition = new CompanyDefinitionEntity(2L, 2L, sickDefinition, companyId, null, null, new ArrayList<>());
            val sabbaticalCountryDefinition = new CountryDefinitionEntity(3L, 3L, sabbaticalDefinition);
            List<TimeOffDefinitionEntity> timeOffDefinitions = List.of(annualCountryDefinition, sickCompanyDefinition, sabbaticalCountryDefinition);

            DefinitionEntity countrySickLeaveDefinition = new DefinitionEntity();
            TimeoffDefinitionValidationEntity validation = new TimeoffDefinitionValidationEntity(
                    14.0, null, null, DAYS, List.of("ONBOARDING"), false
            );
            countrySickLeaveDefinition.setValidations(Set.of(validation));
            countrySickLeaveDefinition.setRequired(false);
            val countryDefinitionForSickLeave = new CountryDefinitionEntity(142L, 2L, countrySickLeaveDefinition);

            Map<Long, CountryDefinitionEntity> countryDefinitionMap = Map.of(
                    1L, annualCountryDefinition,
                    2L, countryDefinitionForSickLeave,
                    3L, sabbaticalCountryDefinition
            );

            // no keys for sabbatical -> Hence intention is to delete
            List<ContractUpdateTimeOffEntitlementsInput> inputs = List.of(
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("annual").value(16.0).unit(TimeOffUnit.DAYS).build(),
                    ContractUpdateTimeOffEntitlementsInput.newBuilder().key("sick").value(10.0).unit(TimeOffUnit.DAYS).build());

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L, 3L))).thenReturn(timeoffTypes);
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(timeOffDefinitions);
            when(definitionService.getTypeIdToEligibleCountryDefinitionMap(contract)).thenReturn(countryDefinitionMap);

            timeoffService.updateTimeOffEntitlements(contractId, inputs);

            verify(timeoffEntitlementDBORepository).deleteAll(entitlementSetArgumentCaptor.capture());
            verify(summaryRepo).deleteAllByContractIdAndTypeIdIn(contractId, List.of(3L));
            verify(timeoffEntitlementDBORepository, times(2)).saveAndFlush(entitlementArgumentCaptor.capture());

            val updatedEntitlements = entitlementArgumentCaptor.getAllValues();
            val entitlementsToDelete = entitlementSetArgumentCaptor.getValue();

            assertEquals(1, entitlementsToDelete.size());
            assertEquals(3, entitlementsToDelete.stream().findFirst().get().typeId());
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream().anyMatch(ent -> ent.typeId().equals(1L) && ent.value().equals(16.0) && ent.definitionId().equals(1000L)));
            assertTrue(updatedEntitlements.stream().anyMatch(ent ->  ent.value().equals(10.0)));
        }
    }

    @Nested
    class saveAsDraft {

        @Test
        void when_contract_not_started_throws_exception() {
            long memberId = 1L;
            long contractId = 2L;
            TimeOffSaveAsDraftInput input = new TimeOffSaveAsDraftInput();
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStarted(false)
                    .build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId));
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract); // authorizing
            var ex = assertThrows(ValidationException.class,
                    () -> timeoffService.saveAsDraft(input, dfe));

            assertThat(ex.getMessage()).isEqualTo("Cannot save as draft because the contract has not started. Contract id=2");

            verifyNoInteractions(timeoffRepository);
        }

        @Test
        void when_member_saves_as_draft_then_success() {
            long memberId = 1L;
            long contractId = 2L;
            TimeOffSaveAsDraftInput input = new TimeOffSaveAsDraftInput();
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStarted(true)
                    .build();
            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);

            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId));
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);// authorize
            when(timeoffMapper.mapFrom(any(TimeOffSaveAsDraftInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.saveAsDraft(input, dfe);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
        }

        @Test
        void when_offboarding_member_saves_as_draft_then_success() {
            long memberId = 1L;
            long contractId = 2L;
            TimeOffSaveAsDraftInput input = new TimeOffSaveAsDraftInput();
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStarted(false)
                    .setStatus(ContractOuterClass.ContractStatus.OFFBOARDING)
                    .build();
            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);

            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId));
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);// authorize
            when(timeoffMapper.mapFrom(any(TimeOffSaveAsDraftInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.saveAsDraft(input, dfe);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
        }

        @Test
        void when_company_user_saves_as_draft_on_behalf_of_their_member_then_success() {
            long cuId = 1L;
            long companyId = 5L;
            long contractId = 2L;

            TimeOffSaveAsDraftInput input = TimeOffSaveAsDraftInput.newBuilder().contractId(contractId).build();
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setStarted(true)
                    .setCountry(String.valueOf(Country.GrpcCountryCode.ABW))
                    .build();
            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(cuId, companyId)); // user_id = 10L
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);// authorize

            when(timeoffMapper.mapFrom(any(TimeOffSaveAsDraftInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.saveAsDraft(input, dfe);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
        }

    }

    @Nested
    class submit {
        @Test
        void when_member_submits_a_new_timeoff_then_success() {
            long memberId = 1L;
            long contractId = 2L;
            TimeOffCreateInput input = new TimeOffCreateInput();

            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setMemberId(memberId)
                    .setStarted(true)
                    .build();
            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId));
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffMapper.mapFrom(any(TimeOffCreateInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.submit(dfe, null, input);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
            verify(timeoffSummaryService).onTimeOffChange(mappedEntity);
        }

        @Test
        void when_company_admin_submits_on_behalf_of_their_member_then_success() {
            long cuId = 1L;
            long companyId = 5L;
            long contractId = 2L;
            long memberId = 1L;
            TimeOffCreateInput input = new TimeOffCreateInput();

            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setMemberId(memberId)
                    .setStarted(true)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(cuId, companyId)); // user_id = 10L
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffMapper.mapFrom(any(TimeOffCreateInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.submit(dfe, contractId, input);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
            verify(timeoffSummaryService).onTimeOffChange(mappedEntity);
        }

        @Test
        void when_company_admin_submits_on_behalf_of_their_member_then_success_for_offboardingContract() {
            long cuId = 1L;
            long companyId = 5L;
            long contractId = 2L;
            TimeOffCreateInput input = new TimeOffCreateInput();

            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStarted(true)
                    .setCompanyId(companyId)
                    .build();
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(cuId, companyId)); // user_id = 10L
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(contractServiceAdapter.findContractByContractId(contractId))
                    .thenReturn(ContractOuterClass.Contract.newBuilder()
                            .setId(contractId)
                            .setStarted(true)
                            .setCompanyId(companyId)
                            .build()
                    );
            when(timeoffMapper.mapFrom(any(TimeOffCreateInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.submit(dfe, contractId, input);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
            verify(timeoffSummaryService).onTimeOffChange(mappedEntity);
        }

        @Test
        void when_company_manager_submits_on_behalf_of_their_member_then_success() {
            long cuId = 1L;
            long companyId = 5L;
            long contractId = 2L;
            TimeOffCreateInput input = new TimeOffCreateInput();

            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStarted(true)
                    .build();
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(cuId, companyId)); // user_id = 10L
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffMapper.mapFrom(any(TimeOffCreateInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.submit(dfe, contractId, input);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
            verify(timeoffSummaryService).onTimeOffChange(mappedEntity);
        }

        @Test
        void when_company_manager_submits_on_behalf_of_their_member_then_success_forOffboardingContract() {
            long cuId = 1L;
            long companyId = 5L;
            long contractId = 2L;
            TimeOffCreateInput input = new TimeOffCreateInput();

            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStarted(true)
                    .build();
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(cuId, companyId)); // user_id = 10L
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffMapper.mapFrom(any(TimeOffCreateInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.submit(dfe, contractId, input);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
            verify(timeoffSummaryService).onTimeOffChange(mappedEntity);
        }

        @Test
        void when_company_user_submits_a_draft_timeoff_but_contract_not_started_then_throws() {
            long contractId = 2L;
            long timeoffId = 6L;
            TimeOffCreateInput input = TimeOffCreateInput.newBuilder().id(timeoffId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStarted(false)
                    .build();
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10L
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffRepository.findById(timeoffId))
                    .thenReturn(Optional.of(TimeoffDBO.builder().id(timeoffId).status(TimeOffStatus.DRAFT).build()));

            var ex = assertThrows(ValidationException.class, () -> timeoffService.submit(dfe, contractId, input));

            assertEquals("Cannot submit because contract has not started. Contract id=2",
                    ex.getMessage());
        }

        @Test
        void when_member_submits_draft_timeoff_with_created_by_undefined_and_not_belongs_to_them_then_throw_validation() {
            long contractId = 2L;
            long memberId = 3L;
            long timeoffId = 6L;
            TimeOffCreateInput input = TimeOffCreateInput.newBuilder().id(timeoffId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .build();
            val draftTimeOff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(3L) // different contract id than the member
                    .status(TimeOffStatus.DRAFT)
                    .build();

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10L
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(draftTimeOff));

            var ex = assertThrows(ValidationException.class, () -> timeoffService.submit(dfe, contractId, input));

            assertEquals("Cannot submit the timeoff id = " + timeoffId + ", as it belongs to someone else",
                    ex.getMessage());
        }

        @Test
        void when_member_submits_draft_timeoff_with_created_by_is_a_different_user_then_throw_validation_error() {
            long contractId = 2L;
            long memberId = 3L;
            long timeoffId = 6L;
            TimeOffCreateInput input = TimeOffCreateInput.newBuilder().id(timeoffId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .build();
            val draftTimeOff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(2L) // different contract id than the member
                    .createdBy(500L) // different user id than 10
                    .createdByInfo(null)
                    .status(TimeOffStatus.DRAFT)
                    .build();

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10L
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(draftTimeOff));

            var ex = assertThrows(ValidationException.class, () -> timeoffService.submit(dfe, contractId, input));

            assertEquals("Cannot submit the timeoff id = " + timeoffId + ", as it was not created by you",
                    ex.getMessage());
        }

        @Test
        void when_member_submits_draft_timeoff_with_created_by_info_and_created_by_different_member_then_throw_validation_error() {
            long contractId = 2L;
            long memberId = 3L;
            long timeoffId = 6L;
            TimeOffCreateInput input = TimeOffCreateInput.newBuilder().id(timeoffId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .build();
            val createdByInfo = new AuditUser();
            createdByInfo.setUserId(500L); // different user id than 10 (10 = user id of current user)
            createdByInfo.setExperience("member");
            val draftTimeOff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(2L) // different contract id than the member
                    .createdBy(500L)
                    .createdByInfo(createdByInfo)
                    .status(TimeOffStatus.DRAFT)
                    .build();

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10L
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(draftTimeOff));

            var ex = assertThrows(ValidationException.class, () -> timeoffService.submit(dfe, contractId, input));

            assertEquals("Cannot submit the timeoff id = " + timeoffId + ", as it was created in Team/Company view OR not created by you",
                    ex.getMessage());
        }

        @Test
        void when_member_submits_draft_timeoff_with_created_by_info_and_created_by_the_company_user_then_throw_validation_error() {
            long contractId = 2L;
            long memberId = 3L;
            long timeoffId = 6L;
            TimeOffCreateInput input = TimeOffCreateInput.newBuilder().id(timeoffId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .build();
            val createdByInfo = new AuditUser();
            createdByInfo.setUserId(10L);
            createdByInfo.setExperience("company");
            val draftTimeOff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(3L) // different contract id than the member
                    .createdBy(10L)
                    .createdByInfo(createdByInfo)
                    .status(TimeOffStatus.DRAFT)
                    .build();

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10L
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(draftTimeOff));

            var ex = assertThrows(ValidationException.class, () -> timeoffService.submit(dfe, contractId, input));

            assertEquals("Cannot submit the timeoff id = " + timeoffId + ", as it was created in Team/Company view OR not created by you",
                    ex.getMessage());
        }

        @Test
        void member_should_be_able_to_submit_draft_timeoff_created_by_them() {
            long contractId = 2L;
            long memberId = 3L;
            long timeoffId = 6L;
            TimeOffCreateInput input = TimeOffCreateInput.newBuilder().id(timeoffId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .build();
            val createdByInfo = new AuditUser();
            createdByInfo.setUserId(10L);
            createdByInfo.setExperience("member");
            val draftTimeOff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(contractId)
                    .createdBy(10L)
                    .createdByInfo(createdByInfo)
                    .status(TimeOffStatus.DRAFT)
                    .build();
            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);


            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10L
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(draftTimeOff));
            when(timeoffMapper.mapFrom(any(TimeOffCreateInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.submit(dfe, contractId, input);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
            verify(timeoffSummaryService).onTimeOffChange(mappedEntity);
        }

        @Test
        void when_company_user_submit_draft_timeoff_created_by_other_company_user_should_throw_validation_error() {
            long contractId = 2L;
            long timeoffId = 6L;
            TimeOffCreateInput input = TimeOffCreateInput.newBuilder().id(timeoffId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .build();
            val createdByInfo = new AuditUser();
            createdByInfo.setUserId(11L); // different company user
            createdByInfo.setExperience("company");
            val draftTimeOff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(3L) // different contract id than the member
                    .createdBy(11L)
                    .createdByInfo(createdByInfo)
                    .status(TimeOffStatus.DRAFT)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10L
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(draftTimeOff));

            var ex = assertThrows(ValidationException.class, () -> timeoffService.submit(dfe, contractId, input));

            assertEquals("Cannot submit the timeoff id = " + timeoffId + ", as it was created in Personal/Member view OR not created by you",
                    ex.getMessage());
        }

        @Test
        void when_company_user_submit_draft_timeoff_created_by_other_company_user_should_throw_validation_error_2() {
            long contractId = 2L;
            long timeoffId = 6L;
            TimeOffCreateInput input = TimeOffCreateInput.newBuilder().id(timeoffId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .build();
            val draftTimeOff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(3L) // different contract id than the member
                    .createdBy(11L)
                    .status(TimeOffStatus.DRAFT)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10L
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(draftTimeOff));

            var ex = assertThrows(ValidationException.class, () -> timeoffService.submit(dfe, contractId, input));

            assertEquals("Cannot submit the timeoff id = " + timeoffId + ", as it was not created by you",
                    ex.getMessage());
        }

        @Test
        void when_company_user_submit_draft_timeoff_created_by_member_should_throw_validation_error() {
            long contractId = 2L;
            long timeoffId = 6L;
            TimeOffCreateInput input = TimeOffCreateInput.newBuilder().id(timeoffId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .build();
            val createdByInfo = new AuditUser();
            createdByInfo.setUserId(500L); // different company user
            createdByInfo.setExperience("member");

            val draftTimeOff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(3L) // different contract id than the member
                    .createdBy(500L)
                    .createdByInfo(createdByInfo)
                    .status(TimeOffStatus.DRAFT)
                    .build();


            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10L
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(draftTimeOff));

            var ex = assertThrows(ValidationException.class, () -> timeoffService.submit(dfe, contractId, input));

            assertEquals("Cannot submit the timeoff id = " + timeoffId + ", as it was created in Personal/Member view OR not created by you",
                    ex.getMessage());
        }

        @Test
        void company_user_should_be_able_to_submit_draft_timeoff_created_by_them() {
            long contractId = 2L;
            long timeoffId = 6L;
            TimeOffCreateInput input = TimeOffCreateInput.newBuilder().id(timeoffId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .build();
            val createdByInfo = new AuditUser();
            createdByInfo.setUserId(10L); // different company user
            createdByInfo.setExperience("company");
            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);

            val draftTimeOff = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(3L) // different contract id than the member
                    .createdBy(10L)
                    .createdByInfo(createdByInfo)
                    .status(TimeOffStatus.DRAFT)
                    .build();


            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10L
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(draftTimeOff));
            when(timeoffMapper.mapFrom(any(TimeOffCreateInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.submit(dfe, contractId, input);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
            verify(timeoffSummaryService).onTimeOffChange(mappedEntity);
        }
    }

    @Nested
    class submitV2 {

        @Test
        void when_submits_a_new_valid_timeoff_then_success() {
            long contractId = 1L;
            long memberId = 2L;
            long typeId = 1L;
            double noOfDays = 3.0;
            val expectedBreakdown = timeoffEntryList();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            ArgumentCaptor<TimeoffDBO> timeoffCaptor = ArgumentCaptor.forClass(TimeoffDBO.class);
            val expectedUsage = TimeOffUsageSummaryDTO.builder().build();

            stubAuthorization(contractId, memberId, dfe);
            stubWorkshiftAdaptor();
            stubValidations();
            stubTimeoffTypeRepository(typeId);
            stubTimeoffSummary(contractId, typeId);
            stubTimeoffBreakdownService(noOfDays, expectedBreakdown);
            stubTimeoffSummaryBreakdownService(expectedUsage);

            val startDate = buildInputDate("2025-10-01", TimeOffSession.MORNING);
            val endDate = buildInputDate("2025-10-03", TimeOffSession.AFTERNOON);
            val expectedTimeoff = buildExpectedTimeoff(noOfDays, typeId, startDate, endDate);
            val input = buildTimeoffInput(contractId, typeId, startDate, endDate);

            timeoffService.submitV2(dfe, input);

            verify(timeoffRepository).save(timeoffCaptor.capture());
            verify(summaryRepo).saveAll(anyList());

            TimeoffDBO capturedTimeoff = timeoffCaptor.getValue();
            List<TimeoffEntryDBO> capturedTimeoffEntries = timeoffCaptor.getValue().timeoffEntries();
            assertEquals(expectedTimeoff.startDate(), capturedTimeoff.startDate());
            assertEquals(expectedTimeoff.startSession(), capturedTimeoff.startSession());
            assertEquals(expectedTimeoff.endDate(), capturedTimeoff.endDate());
            assertEquals(expectedTimeoff.endSession(), capturedTimeoff.endSession());
            assertEquals(expectedTimeoff.noOfDays(), capturedTimeoff.noOfDays());
            assertEquals(expectedTimeoff.status(), capturedTimeoff.status());
            assertEquals(expectedBreakdown, capturedTimeoffEntries);

        }

        private void stubValidations() {
            doNothing().when(timeoffValidator).preValidateTimeoffCreateV2(any(), any(), any(), any());
            doNothing().when(timeoffValidator).throwIfBalanceIsInsufficient(any());
        }

        private void stubWorkshiftAdaptor() {
            WorkshiftDTO workshift = WorkshiftDTO.builder()
                .startDate(DayOfWeek.MONDAY)
                .endDate(DayOfWeek.FRIDAY).build();
            doReturn(workshift).when(workshiftServiceAdapter).getWorkshiftByContract(any());
        }

        private void stubTimeoffBreakdownService(Double noOfDays, List<TimeoffEntryDBO> breakdown) {
            val breakdownResult = Pair.of(breakdown, noOfDays);
            doReturn(breakdownResult).when(timeoffBreakdownService).generateTimeOffEntries(any(), any());
        }

        private void stubTimeoffSummaryBreakdownService(TimeOffUsageSummaryDTO usageSummaryDTO) {
            doReturn(usageSummaryDTO).when(timeoffSummaryBreakdownService).applyAndUpdateTimeoffSummaries(any(), any(), any());
        }

        private List<TimeoffEntryDBO> timeoffEntryList() {
            return List.of(
                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-01")).value(1.0).type(TimeOffEntryType.TIMEOFF).session(TimeOffSession.FULL_DAY).build(),
                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-02")).value(1.0).type(TimeOffEntryType.TIMEOFF).session(TimeOffSession.FULL_DAY).build(),
                TimeoffEntryDBO.builder().date(LocalDate.parse("2025-10-03")).value(1.0).type(TimeOffEntryType.TIMEOFF).session(TimeOffSession.FULL_DAY).build()
            );
        }

        private TimeoffDBO buildExpectedTimeoff(Double noOfDays, Long typeId, TimeOffDateInput startDate,
            TimeOffDateInput endDate) {
            return TimeoffDBO.builder()
                .typeId(typeId)
                .startDate(startDate.getDateOnly())
                .startSession(startDate.getSession())
                .endDate(endDate.getDateOnly())
                .endSession(endDate.getSession())
                .noOfDays(noOfDays)
                .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                .build();
        }

        ;

        private TimeOffDateInput buildInputDate(String date, TimeOffSession session) {
            return TimeOffDateInput.newBuilder()
                .dateOnly(LocalDate.parse(date))
                .session(session)
                .build();
        }

        private TimeOffCreateInputV2 buildTimeoffInput(
            Long contractId,
            Long typeId,
            TimeOffDateInput startDate,
            TimeOffDateInput endDate
        ) {
            return TimeOffCreateInputV2.newBuilder()
                .contractId(contractId)
                .typeId(typeId)
                .startDate(startDate)
                .endDate(endDate)
                .build();
        }

        private void stubTimeoffTypeRepository(Long typeId) {
            val timeOffType = TimeoffTypeDBO.builder().id(typeId).build();
            doReturn(timeOffType).when(timeoffTypeRepository).getReferenceById(any());
        }

        private void stubTimeoffSummary(Long contractId, Long typeId) {
            val currentSummary = TimeoffSummaryDBO.builder().id(1L).contractId(contractId).typeId(typeId).status(TimeOffSummaryStatus.ACTIVE).build();
            val nextSummary = TimeoffSummaryDBO.builder().id(2L).contractId(contractId).typeId(typeId).status(TimeOffSummaryStatus.UPCOMING).build();
            doReturn(new kotlin.Pair<>(currentSummary, nextSummary)).when(timeoffSummaryService).getCurrentAndNextSummaryByContractIdAndTypeId(contractId, typeId);
        }

        private void stubAuthorization(Long contractId, Long memberId, DgsDataFetchingEnvironment dfe) {
            val contract = ContractOuterClass.ContractBasicInfo.newBuilder()
                .setContractId(contractId)
                .setStarted(true)
                .build();

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId));
            when(contractServiceAdapter.getBasicContractById(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
        }

    }

    @Nested
    class update {
        @Test
        void when_member_updates_their_draft_timeoff_then_success() {
            long memberId = 1L;
            long contractId = 2L;
            long timeoffId = 5L;
            TimeOffUpdateInput input = mock(TimeOffUpdateInput.class);
            val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);
            Optional<TimeoffDBO> timeoffDBO = Optional.of(TimeoffDBO.builder()
                    .id(timeoffId)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(10L)
                    .build());

            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(timeoffDBO);
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffMapper.mapFrom(any(TimeOffUpdateInput.class), any(TimeoffDBO.class), any())).thenReturn(mappedEntity);

            timeoffService.update(timeoffId, input, dfe);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
            verify(timeoffSummaryService).onTimeOffChange(mappedEntity);
        }

        @Test
        void when_member_updates_a_submitted_timeoff_then_throws() {
            long contractId = 2L;
            long timeoffId = 5L;
            long memberId = 34L;
            TimeOffUpdateInput input = mock(TimeOffUpdateInput.class);
            val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
            Optional<TimeoffDBO> timeoffDBO = Optional.of(TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(contractId)
                    .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                    .build());
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
            when(timeoffRepository.findById(timeoffId)).thenReturn(timeoffDBO);
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);

            var ex = assertThrows(ValidationException.class, () -> timeoffService.update(timeoffId, input, dfe));
            assertEquals(
                    "The timeoff id = " + timeoffId + " is in status = " + TimeOffStatus.APPROVAL_IN_PROGRESS + ", so cannot update",
                    ex.getMessage()
            );
        }

        @Test
        void when_company_user_updates_their_draft_timeoff_then_success() {
            long contractId = 2L;
            long timeoffId = 5L;
            TimeOffUpdateInput input = mock(TimeOffUpdateInput.class);
            val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
            Optional<TimeoffDBO> timeoffDBO = Optional.of(TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(contractId)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(10L)
                    .build());
            TimeoffDBO mappedEntity = mock(TimeoffDBO.class);
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            when(timeoffRepository.findById(timeoffId)).thenReturn(timeoffDBO);
            mockAdminCompanyUser();
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffMapper.mapFrom(any(TimeOffUpdateInput.class), any(TimeoffDBO.class), any()))
                    .thenReturn(mappedEntity);

            timeoffService.update(timeoffId, input, dfe);

            verify(timeoffRepository).saveAndFlush(mappedEntity);
            verify(timeoffSummaryService).onTimeOffChange(mappedEntity);
        }

        @Test
        void when_company_user_updates_a_submitted_timeoff_then_throws() {
            long contractId = 2L;
            long timeoffId = 5L;
            TimeOffUpdateInput input = mock(TimeOffUpdateInput.class);
            val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
            Optional<TimeoffDBO> timeoffDBO = Optional.of(TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(contractId)
                    .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                    .build());
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
            when(timeoffRepository.findById(timeoffId)).thenReturn(timeoffDBO);
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);

            var ex = assertThrows(ValidationException.class, () -> timeoffService.update(timeoffId, input, dfe));
            assertEquals(
                    "The timeoff id = " + timeoffId + " is in status = " + TimeOffStatus.APPROVAL_IN_PROGRESS + ", so cannot update",
                    ex.getMessage()
            );
        }
    }

    @Nested
    class revoke {

        @Nested
        class MemberRevoke {

            @Test
            void when_member_not_authorized_should_throw_access_denied() {
                long contractId = 2L;
                long timeoffId = 5L;
                long memberId = 56L;
                TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                        .id(timeoffId)
                        .contractId(contractId)
                        .status(TimeOffStatus.DRAFT)
                        .build();
                ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
                DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

                when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
                when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
                doThrow(AccessDeniedException.class).when(authorizationService).authorize(dfe, contract);


                assertThrows(AccessDeniedException.class, () -> timeoffService.revoke(dfe, timeoffId));
            }

            @Test
            void when_member_revoke_draft_timeoffs_should_throw_error() {
                long contractId = 2L;
                long timeoffId = 5L;
                long memberId = 56L;
                TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                        .id(timeoffId)
                        .contractId(contractId)
                        .status(TimeOffStatus.DRAFT)
                        .build();
                ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
                DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

                doNothing().when(authorizationService).authorize(dfe, contract);
                when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
                when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);

                var ex = assertThrows(ValidationException.class, () -> timeoffService.revoke(dfe, timeoffId));
                assertEquals(
                        "Timeoff id = " + timeoffDBO.id() + " is already in DRAFT status",
                        ex.getMessage()
                );
            }

            @Test
            void when_member_revoke_taken_timeoffs_should_throw_error() {
                long contractId = 2L;
                long timeoffId = 5L;
                long memberId = 56L;
                TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                        .id(timeoffId)
                        .contractId(contractId)
                        .status(TimeOffStatus.TAKEN)
                        .build();
                ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
                DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

                doNothing().when(authorizationService).authorize(dfe, contract);
                when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
                when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);

                var ex = assertThrows(ValidationException.class, () -> timeoffService.revoke(dfe, timeoffId));
                assertEquals(
                        "The timeoff id = " + timeoffDBO.id() + " is in status = " + timeoffDBO.status() + ", so cannot revoke",
                        ex.getMessage()
                );
            }

            @Test
            void when_member_revoke_his_timeoff_but_created_by_company_exp_should_throw_error_when_only_created_by_is_present() {
                long memberId = 1L;
                long contractId = 2L;
                long timeoffId = 5L;
                long createdByMemberUserId = 23L;// cu
                TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                        .id(timeoffId)
                        .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                        .createdBy(createdByMemberUserId)
                        .build();
                ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
                DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

                when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
                when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
                doNothing().when(authorizationService).authorize(dfe, contract);

                var ex = assertThrows(ValidationException.class, () -> timeoffService.revoke(dfe, timeoffId));
                assertEquals(
                        "Cannot access the timeoff id = " + timeoffDBO.id() + ", as it was not created by you",
                        ex.getMessage()
                );
            }

            @Test
            void when_member_revoke_his_timeoff_but_created_by_company_exp_should_throw_error_when_created_by_info_is_present() {
                long memberId = 1L;
                long contractId = 2L;
                long timeoffId = 5L;
                long createdByMemberUserId = 23L;
                TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                        .id(timeoffId)
                        .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                        .createdBy(createdByMemberUserId)
                        .createdByInfo(new AuditUser(23L, "company"))
                        .build();
                ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
                DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

                when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
                when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
                doNothing().when(authorizationService).authorize(dfe, contract);

                var ex = assertThrows(ValidationException.class, () -> timeoffService.revoke(dfe, timeoffId));
                assertEquals(
                        "Cannot access the timeoff id = " + timeoffDBO.id() + ", as it was created in Team/Company view OR not created by you",
                        ex.getMessage()
                );
            }

            @Test
            void when_member_revoke_his_timeoff_but_created_by_ops_user_should_throw_error_when_created_by_info_is_present() {
                long memberId = 1L;
                long contractId = 2L;
                long timeoffId = 5L;
                long createdByMemberUserId = 23L;
                TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                        .id(timeoffId)
                        .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                        .createdBy(createdByMemberUserId)
                        .createdByInfo(new AuditUser(35L, "operations"))
                        .build();
                ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
                DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

                when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
                when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
                doNothing().when(authorizationService).authorize(dfe, contract);

                var ex = assertThrows(ValidationException.class, () -> timeoffService.revoke(dfe, timeoffId));
                assertEquals(
                        "Cannot access the timeoff id = " + timeoffDBO.id() + ", as it was created in Team/Company view OR not created by you",
                        ex.getMessage()
                );
            }

            @Test
            void when_member_revokes_their_submitted_timeoff_where_created_by_is_null_then_success() {
                long memberId = 1L;
                long contractId = 2L;
                long timeoffId = 5L;
                TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                        .id(timeoffId)
                        .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                        .build();
                ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();

                DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
                when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
                when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
                when(timeoffRepository.saveAndFlush(timeoffDBO)).thenReturn(timeoffDBO);
                doNothing().when(authorizationService).authorize(dfe, contract);

                timeoffService.revoke(dfe, timeoffId);

                verify(timeoffRepository).saveAndFlush(timeoffDBO);
                verify(timeoffSummaryService).onTimeOffChange(timeoffDBO);
            }

            @Test
            void when_member_revokes_their_submitted_timeoff_and_created_by_info_is_null_then_success() {
                long memberId = 1L;
                long contractId = 2L;
                long timeoffId = 5L;
                TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                        .id(timeoffId)
                        .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                        .createdBy(10L)
                        .build();
                ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
                DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

                when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
                when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
                when(timeoffRepository.saveAndFlush(timeoffDBO)).thenReturn(timeoffDBO);
                doNothing().when(authorizationService).authorize(dfe, contract);

                timeoffService.revoke(dfe, timeoffId);

                verify(timeoffRepository).saveAndFlush(timeoffDBO);
                verify(timeoffSummaryService).onTimeOffChange(timeoffDBO);
            }

            @Test
            void when_member_revokes_their_submitted_timeoff_and_created_by_info_is_not_null_then_success() {
                long memberId = 1L;
                long contractId = 2L;
                long timeoffId = 5L;
                TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                        .id(timeoffId)
                        .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                        .createdBy(10L)
                        .createdByInfo(new AuditUser(10L, "member"))
                        .build();
                ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
                DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

                when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
                when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
                when(timeoffRepository.saveAndFlush(timeoffDBO)).thenReturn(timeoffDBO);
                doNothing().when(authorizationService).authorize(dfe, contract);

                timeoffService.revoke(dfe, timeoffId);

                verify(timeoffRepository).saveAndFlush(timeoffDBO);
                verify(timeoffSummaryService).onTimeOffChange(timeoffDBO);
            }


        }

        @Nested
        class CompanyUserRevoke {

            @Test
            void when_company_user_revokes_a_draft_timeoff_then_throws() {
                long contractId = 2L;
                long timeoffId = 5L;
                TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                        .id(timeoffId)
                        .contractId(contractId)
                        .status(TimeOffStatus.DRAFT)
                        .build();
                val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();

                DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
                when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
                when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);

                var ex = assertThrows(ValidationException.class, () -> timeoffService.revoke(dfe, timeoffId));
                assertEquals(
                        "Timeoff id = " + timeoffDBO.id() + " is already in DRAFT status",
                        ex.getMessage()
                );
            }

            @Nested
            class AdminRevoke {
                @Test
                void when_admin_revoke_timeoff_created_by_other_company_user_success() {
                    long contractId = 2L;
                    long timeoffId = 5L;
                    TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                            .id(timeoffId)
                            .contractId(contractId)
                            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                            .createdBy(1L)
                            .createdByInfo(new AuditUser(1L, "company"))
                            .build();
                    val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();

                    DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
                    when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                    when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
                    when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
                    doNothing().when(authorizationService).authorize(dfe, contract);
                    when(timeoffRepository.saveAndFlush(timeoffDBO)).thenReturn(timeoffDBO);
                    mockAdminCompanyUser();

                    timeoffService.revoke(dfe, timeoffId);

                    verify(timeoffRepository).saveAndFlush(timeoffDBO);
                    verify(timeoffSummaryService).onTimeOffChange(timeoffDBO);
                }
            }

            @Nested
            class ManagerRevoke {
                @Test
                void when_manager_revokes_timeoff_belongs_to_his_member_with_created_by_is_null_success() {
                    long contractId = 2L;
                    long timeoffId = 5L;
                    TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                            .id(timeoffId)
                            .contractId(contractId)
                            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                            .createdBy(null)
                            .build();
                    val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();

                    DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
                    when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                    when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
                    when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
                    doNothing().when(authorizationService).authorize(dfe, contract); // authorize success since timeoff belongs to his subordinate
                    when(timeoffRepository.saveAndFlush(timeoffDBO)).thenReturn(timeoffDBO);
                    mockManagerCompanyUser();

                    timeoffService.revoke(dfe, timeoffId);

                    verify(timeoffRepository).saveAndFlush(timeoffDBO);
                    verify(timeoffSummaryService).onTimeOffChange(timeoffDBO);
                }

                @Test
                void when_manager_revokes_timeoff_created_by_other_company_user_and_created_by_info_is_null_should_throw_error() {
                    long contractId = 2L;
                    long timeoffId = 5L;
                    TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                            .id(timeoffId)
                            .contractId(contractId)
                            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                            .createdBy(15L)
                            .build();
                    val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();

                    DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
                    when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                    when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
                    when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
                    doNothing().when(authorizationService).authorize(dfe, contract); // authorize success since timeoff belongs to his subordinate
                    mockManagerCompanyUser();

                    var ex = assertThrows(ValidationException.class, () -> timeoffService.revoke(dfe, timeoffId));
                    assertEquals(
                            "Cannot access the timeoff id = " + timeoffDBO.id() + ", as it was not created by you",
                            ex.getMessage()
                    );
                }

                @Test
                void when_manager_revokes_timeoff_created_by_him_and_created_by_info_is_null_success() {
                    long contractId = 2L;
                    long timeoffId = 5L;
                    TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                            .id(timeoffId)
                            .contractId(contractId)
                            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                            .createdBy(10L)
                            .build();
                    val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();

                    when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                    when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
                    when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
                    when(timeoffRepository.saveAndFlush(timeoffDBO)).thenReturn(timeoffDBO);
                    mockManagerCompanyUser();

                    timeoffService.revoke(null, timeoffId);

                    verify(timeoffRepository).saveAndFlush(timeoffDBO);
                    verify(timeoffSummaryService).onTimeOffChange(timeoffDBO);
                }

                @Test
                void when_manager_revokes_timeoff_created_by_other_company_user_and_created_by_info_is_not_null_should_throw_error() {
                    long contractId = 2L;
                    long timeoffId = 5L;
                    TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                            .id(timeoffId)
                            .contractId(contractId)
                            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                            .createdBy(15L)
                            .createdByInfo(new AuditUser(15L, "company"))
                            .build();
                    val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();

                    DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
                    when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                    when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
                    when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
                    doNothing().when(authorizationService).authorize(dfe, contract); // authorize success since timeoff belongs to his subordinate
                    mockManagerCompanyUser();

                    var ex = assertThrows(ValidationException.class, () -> timeoffService.revoke(dfe, timeoffId));
                    assertEquals(
                            "Cannot access the timeoff id = " + timeoffDBO.id() + ", as it was created in Personal/Member view OR not created by you",
                            ex.getMessage()
                    );
                }

                @Test
                void when_manager_revokes_timeoff_created_by_member_and_created_by_info_is_not_null_should_throw_error() {
                    long contractId = 2L;
                    long timeoffId = 5L;
                    TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                            .id(timeoffId)
                            .contractId(contractId)
                            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                            .createdBy(15L)
                            .createdByInfo(new AuditUser(15L, "member"))
                            .build();
                    val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();

                    DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
                    when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                    when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
                    when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
                    doNothing().when(authorizationService).authorize(dfe, contract); // authorize success since timeoff belongs to his subordinate
                    mockManagerCompanyUser();

                    var ex = assertThrows(ValidationException.class, () -> timeoffService.revoke(dfe, timeoffId));
                    assertEquals(
                            "Cannot access the timeoff id = " + timeoffDBO.id() + ", as it was created in Personal/Member view OR not created by you",
                            ex.getMessage()
                    );
                }

                @Test
                void when_manager_revokes_timeoff_created_by_him_and_created_by_info_is_not_null_should_success() {
                    long contractId = 2L;
                    long timeoffId = 5L;
                    TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                            .id(timeoffId)
                            .contractId(contractId)
                            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                            .createdBy(10L)
                            .createdByInfo(new AuditUser(10L, "company"))
                            .build();
                    val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();

                    DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
                    when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                    when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
                    when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
                    doNothing().when(authorizationService).authorize(dfe, contract); // authorize success since timeoff belongs to his subordinate
                    when(timeoffRepository.saveAndFlush(timeoffDBO)).thenReturn(timeoffDBO);
                    mockManagerCompanyUser();

                    timeoffService.revoke(dfe, timeoffId);

                    verify(timeoffRepository).saveAndFlush(timeoffDBO);
                    verify(timeoffSummaryService).onTimeOffChange(timeoffDBO);
                }


            }

            @Nested
            class PayrollAdminRevoke {
                @Test
                void when_payroll_admin_revokes_timeoff_created_by_him_success() {
                    long contractId = 2L;
                    long timeoffId = 5L;
                    TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                            .id(timeoffId)
                            .contractId(contractId)
                            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                            .createdBy(10L)
                            .createdByInfo(new AuditUser(10L, "company"))
                            .build();
                    val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();

                    DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
                    when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
                    when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
                    when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
                    doNothing().when(authorizationService).authorize(dfe, contract); // authorize success since timeoff belongs to his subordinate
                    when(timeoffRepository.saveAndFlush(timeoffDBO)).thenReturn(timeoffDBO);
                    mockPayrollAdminCompanyUser(1L);

                    timeoffService.revoke(dfe, timeoffId);

                    verify(timeoffRepository).saveAndFlush(timeoffDBO);
                    verify(timeoffSummaryService).onTimeOffChange(timeoffDBO);
                }
            }
        }
    }

    @Nested
    class delete {
        @Test
        void when_member_deletes_their_draft_timeoff_then_success() {
            long memberId = 1L;
            long contractId = 2L;
            long timeoffId = 5L;
            TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                    .id(timeoffId)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(10L)
                    .build();
            val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId)); // user_id = 10
            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract); // authorize success since timeoff belongs to his subordinate

            timeoffService.delete(timeoffId, dfe);

            verify(timeoffSummaryService).onTimeOffChange(timeoffDBO);
        }

        @Test
        void when_member_deletes_a_non_draft_timeoff_then_throws() {
            long contractId = 2L;
            long timeoffId = 5L;
            long memberId = 9L;
            TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(contractId)
                    .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                    .build();
            val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
            when(currentUser.getContext()).thenReturn(getMemberUserDetails(9L)); // user_id = 10
            when(contractServiceAdapter.findContractByMemberId(memberId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract); // authorize success since timeoff belongs to his subordinate

            var ex = assertThrows(ValidationException.class, () -> timeoffService.delete(timeoffId, dfe));
            assertEquals(
                    "The timeoff id = " + timeoffDBO.id() + " is in status = " + timeoffDBO.status() + ", so cannot delete",
                    ex.getMessage()
            );
        }

        @Test
        void when_company_user_deletes_their_draft_timeoff_then_success() {
            long contractId = 2L;
            long timeoffId = 5L;
            TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(contractId)
                    .status(TimeOffStatus.DRAFT)
                    .createdBy(10L)
                    .build();
            val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(timeoffRepository.findById(timeoffId)).thenReturn(Optional.of(timeoffDBO));
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract); // authorize success since timeoff belongs to his subordinate
            mockManagerCompanyUser();
            timeoffService.delete(timeoffId, dfe);

            verify(timeoffSummaryService).onTimeOffChange(timeoffDBO);
        }

        @Test
        void when_company_user_deletes_a_non_draft_timeoff_then_throws() {
            long contractId = 2L;
            long timeoffId = 5L;
            TimeoffDBO timeoffDBO = TimeoffDBO.builder()
                    .id(timeoffId)
                    .contractId(contractId)
                    .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                    .build();
            val contract = ContractOuterClass.Contract.newBuilder().setId(contractId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(timeoffRepository.findById(timeoffId))
                    .thenReturn(Optional.of(timeoffDBO));
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails()); // user_id = 10
            when(contractServiceAdapter.findContractByContractId(contractId)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract); // authorize success since timeoff belongs to his subordinate

            var ex = assertThrows(ValidationException.class, () -> timeoffService.delete(timeoffId, dfe));
            assertEquals(
                    "The timeoff id = " + timeoffDBO.id() + " is in status = " + timeoffDBO.status() + ", so cannot delete",
                    ex.getMessage()
            );
        }
    }

    @Nested
    class getTimeOffsForPayroll {
        Set<Long> contractIds = Set.of(1L, 2L);
        Set<TimeOffStatus> statuses = Set.of(TimeOffStatus.APPROVED, TimeOffStatus.TAKEN);
        LocalDate startDate = LocalDate.of(2023, 2, 1);
        LocalDate endDate = LocalDate.of(2023, 2, 28);
        Instant approvedOnGreaterThanEqTo = startDate.atStartOfDay().toInstant(ZoneOffset.UTC);
        LocalDate approvedOnFromInclusive = LocalDate.of(2023, 1, 16);
        LocalDate approvedOnToExclusive = LocalDate.of(2023, 2, 15);


        @Test
        void returnsEmpty_whenRepositoryReturnsEmptyList() {
            when(timeoffRepository.findAllForPayroll(contractIds, statuses, startDate, endDate, approvedOnGreaterThanEqTo))
                    .thenReturn(Collections.emptyList());

            var res = timeoffService.getTimeOffsForPayroll(contractIds, statuses, startDate, endDate, approvedOnGreaterThanEqTo);

            assertThat(res).isEmpty();
        }

        @Test
        void returnsEmpty_whenNoTimeOffsFound() {
            when(timeoffRepository.findAllForPayrollCycle(contractIds, statuses, startDate, endDate, approvedOnFromInclusive, approvedOnToExclusive))
                    .thenReturn(Collections.emptyList());
            var res = timeoffService.getTimeOffsForPayrollCycle(contractIds, statuses, startDate, endDate, approvedOnFromInclusive, approvedOnToExclusive);
            assertThat(res).isEmpty();
        }

        @Test
        void should_return_timeoffs_for_payroll_cycle() {
            Set<Long> contractIdsForTest = Set.of(1L, 2L, 3L, 4L);
            val companyIdForHolidayByEntityOn = 800L;
            val companyIdForHolidayByEntityOff = 900L;
            /*
            * timeoff id = 1, 2023-02-01 -2023-02-03, holiday on 2023-02-02 => no of days 2
            * timeoff id = 2, 2023-02-02 -2023-02-03, holiday on 2023-02-03 => no of days 1
            * timeoff id = 3, 2023-02-01 -2023-02-03, holiday on 2023-02-03 => no of days 1
            * */
            List<TimeoffDBO> timeoffDBOs = List.of(
                    TimeoffDBO.builder().id(1L).contractId(1L).status(TimeOffStatus.APPROVED).startDate(LocalDate.of(2023,2,1)).endDate(LocalDate.of(2023,2,3)).startSession(TimeOffSession.MORNING).endSession(TimeOffSession.AFTERNOON).approvedOn(Instant.now()).build(),
                    TimeoffDBO.builder().id(2L).contractId(2L).status(TimeOffStatus.TAKEN).startDate(LocalDate.of(2023,2,2)).endDate(LocalDate.of(2023,2,3)).startSession(TimeOffSession.MORNING).endSession(TimeOffSession.AFTERNOON).approvedOn(Instant.now()).build(),
                    TimeoffDBO.builder().id(3L).contractId(3L).status(TimeOffStatus.TAKEN).startDate(LocalDate.of(2023,2,2)).endDate(LocalDate.of(2023,2,3)).startSession(TimeOffSession.MORNING).endSession(TimeOffSession.AFTERNOON).approvedOn(Instant.now()).build(),
                    TimeoffDBO.builder().id(4L).contractId(4L).status(TimeOffStatus.APPROVED).startDate(LocalDate.of(2023,2,1)).endDate(LocalDate.of(2023,2,3)).startSession(TimeOffSession.MORNING).endSession(TimeOffSession.AFTERNOON).approvedOn(Instant.now()).build(),
                    TimeoffDBO.builder().id(5L).contractId(5L).status(TimeOffStatus.TAKEN).approvedOn(Instant.now()).build() // no contract for this timeoff -> corrupted data
            );
            val contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(companyIdForHolidayByEntityOn).setCountry("LKA").build(),
                    ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(companyIdForHolidayByEntityOff).setCountry("IND").build(),
                    ContractOuterClass.Contract.newBuilder().setId(3L).setCompanyId(companyIdForHolidayByEntityOff).setCountry("LKA").build(),
                    ContractOuterClass.Contract.newBuilder().setId(4L).setCompanyId(companyIdForHolidayByEntityOn).setCountry("LKA").build()
            );
            val countryHolidays = List.of(
                    HolidayOuterClass.Holiday.newBuilder().setYear(2023).setMonth(2).setDate(3).setCountryCode(Country.GrpcCountryCode.IND).build(),
                    HolidayOuterClass.Holiday.newBuilder().setYear(2023).setMonth(2).setDate(3).setCountryCode(Country.GrpcCountryCode.LKA).build()
            );
            val entityHolidays = List.of(
                    LegalEntityHoliday.Holiday.newBuilder().setYear(2023).setMonth(2).setDate(2).addAllContractIds(List.of(1L, 10L, 4L)).build()
            );

            when(timeoffRepository.findAllForPayrollCycle(contractIdsForTest, statuses, startDate, endDate, approvedOnFromInclusive, approvedOnToExclusive))
                    .thenReturn(timeoffDBOs);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(contractIdsForTest, true)).thenReturn(contracts);
            holidayByEntityOn(companyIdForHolidayByEntityOn);
            holidayByEntityOff(companyIdForHolidayByEntityOff);
            when(holidayServiceAdapter.getHolidays(Set.of(Country.GrpcCountryCode.IND, Country.GrpcCountryCode.LKA), startDate.getYear(), null, null))
                    .thenReturn(countryHolidays);
            when(holidayServiceAdapter.getHolidays(Set.of(1L, 4L), 2023, null))
                    .thenReturn(entityHolidays);
            when(grpcTimeoffMapper.mapToGrpcTimeOffForPayroll(timeoffDBOs.get(1),1.0)).thenReturn(
                    GrpcTimeOffForPayroll.newBuilder().setId(2L).setNoOfDaysWithinCycle(1.0).build());
            when(grpcTimeoffMapper.mapToGrpcTimeOffForPayroll(timeoffDBOs.get(2),1.0)).thenReturn(
                    GrpcTimeOffForPayroll.newBuilder().setId(3L).setNoOfDaysWithinCycle(1.0).build());
            when(grpcTimeoffMapper.mapToGrpcTimeOffForPayroll(timeoffDBOs.get(0),2.0)).thenReturn(
                    GrpcTimeOffForPayroll.newBuilder().setId(1L).setNoOfDaysWithinCycle(2.0).build());
            when(grpcTimeoffMapper.mapToGrpcTimeOffForPayroll(timeoffDBOs.get(3),2.0)).thenReturn(
                    GrpcTimeOffForPayroll.newBuilder().setId(4L).setNoOfDaysWithinCycle(2.0).build());


            var res = timeoffService.getTimeOffsForPayrollCycle(contractIdsForTest, statuses, startDate, endDate, approvedOnFromInclusive, approvedOnToExclusive);

            assertThat(res).hasSize(4);
            assertTrue(res.stream().anyMatch(t -> t.getId() == 1L && t.getNoOfDaysWithinCycle() == 2.0));
            assertTrue(res.stream().anyMatch(t -> t.getId() == 2L && t.getNoOfDaysWithinCycle() == 1.0));
            assertTrue(res.stream().anyMatch(t -> t.getId() == 3L && t.getNoOfDaysWithinCycle() == 1.0));
            assertTrue(res.stream().anyMatch(t -> t.getId() == 4L && t.getNoOfDaysWithinCycle() == 2.0));
        }
    }

    @Nested
    class changeTimeOffDate {
        long id = 1L;
        LocalDate mockStartDate = mock(LocalDate.class);
        LocalDate mockEndDate = mock(LocalDate.class);

        /**
         * Defines the fields that should not be changed
         */
        void assertFieldsNotChanged(TimeoffDBO timeOffDBO) {
            assertNull(timeOffDBO.noOfDays());
            assertNull(timeOffDBO.startSession());
            assertNull(timeOffDBO.endSession());
            assertEquals(TimeOffStatus.APPROVAL_IN_PROGRESS, timeOffDBO.status());
        }

        @ParameterizedTest
        @ValueSource(booleans = {true, false})
        void should_throw_when_new_start_date_is_after_new_end_date_regardless_of_ignoresValidations(boolean ignoresValidations) {
            var newStartDate = LocalDate.of(2023, 7, 19);
            var newEndDate = newStartDate.minusDays(1);

            var ex = assertThrows(ValidationException.class, () -> timeoffService.changeTimeOffDate(id, newStartDate, newEndDate, ignoresValidations));

            assertEquals("Cannot change timeoff date because new start date is after new end date (bad input)", ex.getMessage());
        }

        @Test
        void should_change_the_date_when_ignoresValidations_is_true() {
            TimeoffDBO timeOffDBO = TimeoffDBO.builder().status(TimeOffStatus.APPROVAL_IN_PROGRESS).build();

            when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));

            timeoffService.changeTimeOffDate(id, mockStartDate, mockEndDate, true);

            assertEquals(mockStartDate, timeOffDBO.startDate());
            assertEquals(mockEndDate, timeOffDBO.endDate());
            assertFieldsNotChanged(timeOffDBO);
        }

        @ParameterizedTest
        @ValueSource(strings = {"REJECTED", "DRAFT", "DELETED"})
        void should_throw_when_not_ignore_and_current_timeoff_status_is_not_valid(String invalidStatus) {
            TimeoffDBO timeOffDBO = TimeoffDBO.builder().status(TimeOffStatus.valueOf(invalidStatus)).build();

            when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));

            var ex = assertThrows(ValidationException.class, () -> timeoffService.changeTimeOffDate(id, mockStartDate, mockEndDate, false));

            assertTrue(ex.getMessage().contains("Cannot change timeoff date because current timeoff status is invalid"), "Exception message is not expected, actual=" + ex.getMessage());
        }

        @Nested
        class when_old_date_range_is_within_a_month {
            @ParameterizedTest
            @CsvSource({
                    "2023-06-25, 2023-06-26",
                    "2023-06-30, 2023-07-01",
                    "2022-07-15, 2023-07-16", // year is not correct
                    "2023-07-31, 2023-08-01",
                    "2023-08-05, 2023-08-06"
            })
            void throw_when_new_date_range_is_before_or_after_the_month(String newStartDateStr, String newEndDateStr) {
                TimeoffDBO timeOffDBO = TimeoffDBO.builder()
                        .startDate(LocalDate.of(2023, 7, 27))
                        .endDate(LocalDate.of(2023, 7, 28))
                        .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                        .build();
                LocalDate newStartDate = LocalDate.parse(newStartDateStr);
                LocalDate newEndDate = LocalDate.parse(newEndDateStr);

                when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));

                var ex = assertThrows(ValidationException.class,() -> timeoffService.changeTimeOffDate(id, newStartDate, newEndDate, false));

                assertTrue(ex.getMessage().contains("Cannot change timeoff date because the new date range is invalid"), "Exception message is not expected, actual=" + ex.getMessage());
            }

            @Test
            void success_when_new_date_range_in_the_same_month_as_old_date_range() {
                TimeoffDBO timeOffDBO = TimeoffDBO.builder()
                        .startDate(LocalDate.of(2023, 7, 27))
                        .endDate(LocalDate.of(2023, 7, 28))
                        .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                        .build();
                // old and new date ranges are in same month, we're safe
                LocalDate newStartDate = LocalDate.of(2023, 7, 30);
                LocalDate newEndDate = LocalDate.of(2023, 7, 31);

                when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));

                timeoffService.changeTimeOffDate(id, newStartDate, newEndDate, false);

                assertEquals(newStartDate, timeOffDBO.startDate());
                assertEquals(newEndDate, timeOffDBO.endDate());
                assertFieldsNotChanged(timeOffDBO);
            }
        }

        @Nested
        class when_old_date_range_spans_across_2_months {
            @ParameterizedTest
            @CsvSource({
                    "2023-05-25, 2023-05-26",
                    "2023-05-31, 2023-06-01",
                    "2023-07-31, 2023-08-01",
                    "2023-08-05, 2023-08-06",
                    "2022-06-15, 2023-06-16", // year is not correct
                    "2022-06-15, 2023-07-16", // year is not correct
                    "2022-07-15, 2023-07-16", // year is not correct
            })
            void should_throw_when_new_date_range_is_before_or_after_the_2_months(String newStartDateStr, String newEndDateStr) {
                TimeoffDBO timeOffDBO = TimeoffDBO.builder()
                        .startDate(LocalDate.of(2023, 6, 30))// Fri
                        .endDate(LocalDate.of(2023, 7, 3)) // Mon
                        .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                        .build();
                LocalDate newStartDate = LocalDate.parse(newStartDateStr);
                LocalDate newEndDate = LocalDate.parse(newEndDateStr);

                when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));

                var ex = assertThrows(ValidationException.class, () -> timeoffService.changeTimeOffDate(id, newStartDate, newEndDate, false));

                assertTrue(ex.getMessage().contains("Cannot change timeoff date because the new date range is invalid"), "Exception message is not expected, actual=" + ex.getMessage());
            }

            @ParameterizedTest
            @CsvSource({
                    "2023-06-25, 2023-06-26",
                    "2023-06-30, 2023-07-01",
                    "2023-07-01, 2023-07-02"
            })
            void success_when_new_date_range_is_within_the_2_months(String newStartDateStr, String newEndDateStr) {
                TimeoffDBO timeOffDBO = TimeoffDBO.builder()
                        .startDate(LocalDate.of(2023, 6, 30))// Fri
                        .endDate(LocalDate.of(2023, 7, 3)) // Mon
                        .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                        .build();
                LocalDate newStartDate = LocalDate.parse(newStartDateStr);
                LocalDate newEndDate = LocalDate.parse(newEndDateStr);

                when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));

                timeoffService.changeTimeOffDate(id, newStartDate, newEndDate, false);

                assertEquals(newStartDate, timeOffDBO.startDate());
                assertEquals(newEndDate, timeOffDBO.endDate());
                assertFieldsNotChanged(timeOffDBO);
            }
        }
    }


    @Nested
    class ChangeTimeOffDateV2Tests {
        long id = 1L;
        LocalDate mockStartDate = mock(LocalDate.class);
        LocalDate mockEndDate = mock(LocalDate.class);

        void assertFieldsNotChanged(TimeoffDBO timeOffDBO) {
            assertNull(timeOffDBO.noOfDays());
            assertNull(timeOffDBO.startSession());
            assertNull(timeOffDBO.endSession());
            assertEquals(TimeOffStatus.APPROVAL_IN_PROGRESS, timeOffDBO.status());
        }

        @ParameterizedTest
        @ValueSource(booleans = {true, false})
        void should_throw_when_new_start_date_is_after_new_end_date_regardless_of_ignoresValidations(boolean ignoresValidations) {
            TimeOffChangeDateInput input = TimeOffChangeDateInput.newBuilder()
                    .id(id)
                    .startDate(LocalDate.of(2023, 7, 19))
                    .endDate(LocalDate.of(2023, 7, 18))
                    .ignoresValidations(true)
                    .build();
            var ex = assertThrows(ValidationException.class, () -> timeoffService.changeTimeOffDateV2(input));
            assertEquals("Cannot change timeoff date because new start date is after new end date (bad input)", ex.getMessage());
        }

        @Test
        void should_change_the_date_when_ignoresValidations_is_true() {
            TimeoffDBO timeOffDBO = TimeoffDBO.builder().status(TimeOffStatus.APPROVAL_IN_PROGRESS).build();
            when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));

            TimeOffChangeDateInput input = TimeOffChangeDateInput.newBuilder()
                    .id(id)
                    .startDate(mockStartDate)
                    .endDate(mockEndDate)
                    .ignoresValidations(true)
                    .build();
            timeoffService.changeTimeOffDateV2(input);

            assertEquals(mockStartDate, timeOffDBO.startDate());
            assertEquals(mockEndDate, timeOffDBO.endDate());
            assertFieldsNotChanged(timeOffDBO);
        }

        @ParameterizedTest
        @ValueSource(strings = {"REJECTED", "DRAFT", "DELETED"})
        void should_throw_when_not_ignore_and_current_timeoff_status_is_not_valid(String invalidStatus) {
            TimeoffDBO timeOffDBO = TimeoffDBO.builder().status(TimeOffStatus.valueOf(invalidStatus)).build();
            when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));

            TimeOffChangeDateInput input = TimeOffChangeDateInput.newBuilder()
                    .id(id)
                    .startDate(mockStartDate)
                    .endDate(mockEndDate)
                    .ignoresValidations(false)
                    .build();
            var ex = assertThrows(ValidationException.class, () -> timeoffService.changeTimeOffDateV2(input));

            assertTrue(ex.getMessage().contains("Cannot change timeoff date because current timeoff status is invalid"));
        }

        @Nested
        class WhenOldDateRangeIsWithinAMonth {
            @ParameterizedTest
            @CsvSource({
                    "2023-06-25, 2023-06-26",
                    "2023-06-30, 2023-07-01",
                    "2022-07-15, 2023-07-16",
                    "2023-07-31, 2023-08-01"
            })
            void throw_when_new_date_range_is_invalid(String newStartDateStr, String newEndDateStr) {
                TimeoffDBO timeOffDBO = TimeoffDBO.builder().startDate(LocalDate.of(2023, 7, 27)).endDate(LocalDate.of(2023, 7, 28)).status(TimeOffStatus.APPROVAL_IN_PROGRESS).build();
                LocalDate newStartDate = LocalDate.parse(newStartDateStr);
                LocalDate newEndDate = LocalDate.parse(newEndDateStr);

                when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));
                TimeOffChangeDateInput input = TimeOffChangeDateInput.newBuilder()
                        .id(id)
                        .startDate(newStartDate)
                        .endDate(newEndDate)
                        .ignoresValidations(false)
                        .build();
                var ex = assertThrows(ValidationException.class, () -> timeoffService.changeTimeOffDateV2(input));

                assertTrue(ex.getMessage().contains("Cannot change timeoff date because the new date range is invalid"));
            }
        }

        @Nested
        class WhenOldDateRangeSpansAcrossTwoMonths {
            @ParameterizedTest
            @CsvSource({
                    "2023-05-25, 2023-05-26",
                    "2023-05-31, 2023-06-01",
                    "2023-07-31, 2023-08-01"
            })
            void should_throw_when_new_date_range_is_invalid(String newStartDateStr, String newEndDateStr) {
                TimeoffDBO timeOffDBO = TimeoffDBO.builder().startDate(LocalDate.of(2023, 6, 30)).endDate(LocalDate.of(2023, 7, 3)).status(TimeOffStatus.APPROVAL_IN_PROGRESS).build();
                LocalDate newStartDate = LocalDate.parse(newStartDateStr);
                LocalDate newEndDate = LocalDate.parse(newEndDateStr);

                when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));
                TimeOffChangeDateInput input = TimeOffChangeDateInput.newBuilder()
                        .id(id)
                        .startDate(newStartDate)
                        .endDate(newEndDate)
                        .ignoresValidations(false)
                        .build();
                var ex = assertThrows(ValidationException.class, () -> timeoffService.changeTimeOffDateV2(input));

                assertTrue(ex.getMessage().contains("Cannot change timeoff date because the new date range is invalid"));
            }
        }

        @Test
        void shouldAddValidationMessageWhenValidationsPass() {
            // Create a mock TimeoffDBO object with a valid status
            TimeoffDBO timeOffDBO = TimeoffDBO.builder()
                    .startDate(LocalDate.of(2025, 2, 18))
                    .endDate(LocalDate.of(2025, 2, 22))
                    .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                    .build();

            when(timeoffRepository.findById(1L)).thenReturn(Optional.of(timeOffDBO));

            // Create a valid TimeOffChangeDateInput object
            TimeOffChangeDateInput input = new TimeOffChangeDateInput();
            input.setId(1L);
            input.setStartDate(LocalDate.of(2025, 2, 20));
            input.setEndDate(LocalDate.of(2025, 2, 25));
            input.setIgnoresValidations(false);

            // Call the method
            List<String> result = timeoffService.changeTimeOffDateV2(input);

            // Assertions
            assertTrue(result.contains("Validations passed. This request is safe to proceed."));
            verify(timeoffRepository, times(1)).save(any());
            verify(timeoffKafkaPublisher, times(1)).publishTimeoffUpdateEvent(any());
        }

        @Test
        void should_throw_when_noOfDays_is_greater_than_duration() {
            TimeoffDBO timeOffDBO = TimeoffDBO.builder()
                    .startDate(LocalDate.of(2025, 2, 20))
                    .endDate(LocalDate.of(2025, 2, 25))
                    .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                    .build();

            when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));

            TimeOffChangeDateInput input = new TimeOffChangeDateInput();
            input.setId(id);
            input.setStartDate(LocalDate.of(2025, 2, 20));
            input.setEndDate(LocalDate.of(2025, 2, 25));
            input.setNoOfDays(10.0); // Greater than the duration (6 days)
            input.setIgnoresValidations(false);

            var ex = assertThrows(ValidationException.class, () -> timeoffService.changeTimeOffDateV2(input));
            assertEquals("noOfDays cannot be greater than the duration between start and end date.", ex.getMessage());

            verify(timeoffRepository, never()).save(any());
            verify(timeoffKafkaPublisher, never()).publishTimeoffUpdateEvent(any());
        }

        @Test
        void should_update_noOfDays_when_within_valid_range() {
            TimeoffDBO timeOffDBO = TimeoffDBO.builder()
                    .startDate(LocalDate.of(2025, 2, 20))
                    .endDate(LocalDate.of(2025, 2, 25))
                    .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                    .build();

            when(timeoffRepository.findById(id)).thenReturn(Optional.of(timeOffDBO));

            TimeOffChangeDateInput input = new TimeOffChangeDateInput();
            input.setId(id);
            input.setStartDate(LocalDate.of(2025, 2, 20));
            input.setEndDate(LocalDate.of(2025, 2, 25));
            input.setNoOfDays(4.0); // Within valid range
            input.setIgnoresValidations(false);

            List<String> result = timeoffService.changeTimeOffDateV2(input);

            assertTrue(result.contains("noOfDays is valid and within the permissible range."));
            assertEquals(4.0, timeOffDBO.noOfDays());
            verify(timeoffRepository, times(1)).save(timeOffDBO);
            verify(timeoffKafkaPublisher, times(1)).publishTimeoffUpdateEvent(any());
        }

    }

    @Nested
    class bulkRevokeTimeOffs {

        @Captor
        ArgumentCaptor<List<TimeoffDBO>> timeoffDBOsCaptor;

        @Test
        void should_return_when_ids_is_empty() {
            TaskResponse actual = timeoffService.bulkRevokeTimeOffs(List.of());
            assertEquals(new TaskResponse(true, "Ids is empty. Nothing processed."), actual);
            verify(timeoffRepository, never()).findAllById(any());
            verify(timeoffRepository, never()).saveAllAndFlush(any());
            verify(timeoffSummaryService, never()).onTimeOffChange(any());
            verify(timeoffKafkaPublisher, never()).publishTimeoffUpdateEvent(any());
        }

        @Test
        void should_return_when_ids_is_null() {
            TaskResponse actual = timeoffService.bulkRevokeTimeOffs(null);
            assertEquals(new TaskResponse(true, "Ids is empty. Nothing processed."), actual);
            verify(timeoffRepository, never()).findAllById(any());
            verify(timeoffRepository, never()).saveAllAndFlush(any());
            verify(timeoffSummaryService, never()).onTimeOffChange(any());
            verify(timeoffKafkaPublisher, never()).publishTimeoffUpdateEvent(any());
        }

        @Test
        void should_throw_validation_exception_when_some_timeOffs_are_not_found() {
            List<Long> ids = List.of(1L, 2L, 3L);

            when(timeoffRepository.findAllById(ids)).thenReturn(List.of(
                    TimeoffDBO.builder().id(1L).build(),
                    TimeoffDBO.builder().id(2L).build()
            ));

            // when
            var ex = assertThrows(ValidationException.class, () -> timeoffService.bulkRevokeTimeOffs(ids));

            // then
            assertEquals("Unable to find some of the time offs by id: [3]", ex.getMessage());
            verify(timeoffRepository, never()).saveAllAndFlush(any());
            verify(timeoffSummaryService, never()).onTimeOffChange(any());
            verify(timeoffKafkaPublisher, never()).publishTimeoffUpdateEvent(any());
        }

        @Test
        void should_return_validation_exception_when_some_timeOffs_are_not_in_allowed_state() {
            List<Long> ids = List.of(1L, 2L, 3L, 4L, 5L, 6L);

            when(timeoffRepository.findAllById(ids)).thenReturn(List.of(
                    TimeoffDBO.builder().id(1L).status(TimeOffStatus.APPROVED).build(),
                    TimeoffDBO.builder().id(2L).status(TimeOffStatus.APPROVAL_IN_PROGRESS).build(),
                    TimeoffDBO.builder().id(3L).status(TimeOffStatus.DRAFT).build(),
                    TimeoffDBO.builder().id(4L).status(TimeOffStatus.DELETED).build(),
                    TimeoffDBO.builder().id(5L).status(TimeOffStatus.TAKEN).build(),
                    TimeoffDBO.builder().id(6L).status(TimeOffStatus.REJECTED).build()
            ));

            // when
            var ex = assertThrows(ValidationException.class, () -> timeoffService.bulkRevokeTimeOffs(ids));

            // then
            assertEquals("Some of the given time offs cannot be processed due to their current status: [{id: 3, status: DRAFT}, {id: 4, status: DELETED}]", ex.getMessage());
            verify(timeoffRepository, never()).saveAllAndFlush(any());
            verify(timeoffSummaryService, never()).onTimeOffChange(any());
            verify(timeoffKafkaPublisher, never()).publishTimeoffUpdateEvent(any());
        }

        @Test
        void should_return_successfully_when_all_timeOffs_are_in_allowed_state() {
            List<Long> ids = List.of(1L, 2L, 3L, 4L);

            when(timeoffRepository.findAllById(ids)).thenReturn(List.of(
                    TimeoffDBO.builder().id(1L).status(TimeOffStatus.APPROVAL_IN_PROGRESS).build(),
                    TimeoffDBO.builder().id(2L).status(TimeOffStatus.APPROVED).build(),
                    TimeoffDBO.builder().id(3L).status(TimeOffStatus.REJECTED).build(),
                    TimeoffDBO.builder().id(4L).status(TimeOffStatus.TAKEN).build()
            ));

            when(timeoffRepository.saveAllAndFlush(timeoffDBOsCaptor.capture())).thenAnswer(invocation -> invocation.getArgument(0));

            // when
            TaskResponse actual = timeoffService.bulkRevokeTimeOffs(ids);

            // then
            assertEquals(new TaskResponse(true, "Successfully Revoked 4 Time Offs"), actual);
            for(var timeoffDBO: timeoffDBOsCaptor.getValue()) {
                assertEquals(TimeOffStatus.DRAFT, timeoffDBO.status());
            }
            verify(timeoffSummaryService, times(4)).onTimeOffChange(any());
            verify(timeoffKafkaPublisher, times(4)).publishTimeoffUpdateEvent(any());
        }

    }

    @Nested
    class backFillEntitlementDefinitionIdsTest {


        @Test
        void should_backfill_entitlement_definition_ids() {
            val contractId1 = 600L;
            val contractId2 = 700L;
            val contractId3 = 800L;
            val contractId4 = 900L;
            val contractId5 = 1000L;
            val contractId6 = 1100L;
            val contractId7 = 1200L;
            val contractId8 = 1300L;
            val typeId1 = 1000L;
            val typeId2 = 2000L;
            val typeId3 = 3000L;
            val companyId1 = 123L;
            val companyId2 = 124L;

            val annualCompany1Definition1 = new DefinitionEntity();
            annualCompany1Definition1.setId(1L);
            annualCompany1Definition1.setCreatedOn(LocalDateTime.of(2024,03,01,10,10,10));

            val annualCompany1Definition2 = new DefinitionEntity();
            annualCompany1Definition2.setId(2L);
            annualCompany1Definition2.setCreatedOn(LocalDateTime.of(2024,03,02,10,10,10));

            val companyDefinition1ForCompany1 = new CompanyDefinitionEntity(100L, typeId1, annualCompany1Definition1, companyId1, null, null, new ArrayList<>());
            companyDefinition1ForCompany1.setCreatedOn(LocalDateTime.of(2024,03,01,10,10,10));

            val companyDefinition2ForCompany1 = new CompanyDefinitionEntity(101L, typeId1, annualCompany1Definition2, companyId1, null, null, new ArrayList<>());
            companyDefinition1ForCompany1.setCreatedOn(LocalDateTime.of(2024,03,02,10,10,10));

            val michiganDefinitionSLAnnualLeave = new DefinitionEntity();
            michiganDefinitionSLAnnualLeave.setId(3L);
            michiganDefinitionSLAnnualLeave.setCountryCode(CountryCode.USA);
            michiganDefinitionSLAnnualLeave.setStateCode("Michigan");

            val usaDefinitionSickLeave = new DefinitionEntity();
            usaDefinitionSickLeave.setId(4L);
            usaDefinitionSickLeave.setCountryCode(CountryCode.USA);

            val defaultAnnualLeave = new DefinitionEntity();
            usaDefinitionSickLeave.setId(5L);

            val defaultCasualLeave1 = new DefinitionEntity();
            usaDefinitionSickLeave.setId(6L);

            val defaultCasualLeave2 = new DefinitionEntity();
            usaDefinitionSickLeave.setId(7L);


            List<CompanyDefinitionEntity> companyDefinitionEntities = List.of(companyDefinition1ForCompany1, companyDefinition2ForCompany1);

            List<CountryDefinitionEntity> countryDefinitionEntities = List.of(
                    new CountryDefinitionEntity(101L, typeId2, michiganDefinitionSLAnnualLeave),
                    new CountryDefinitionEntity(102L, typeId2, usaDefinitionSickLeave),
                    new CountryDefinitionEntity(103L, typeId1, defaultAnnualLeave),
                    new CountryDefinitionEntity(104L, typeId3, defaultCasualLeave1),
                    new CountryDefinitionEntity(105L, typeId3, defaultCasualLeave2)
            );
            Specification<TimeoffEntitlementDBO> spec = Specification.<TimeoffEntitlementDBO>where(null);


            List<TimeoffEntitlementDBO> mockEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(100L).typeId(typeId1).contractId(contractId1).build(), // find by company definitions
                    TimeoffEntitlementDBO.builder().id(200L).typeId(typeId2).contractId(contractId2).build(), // find by country-state definitions
                    TimeoffEntitlementDBO.builder().id(300L).typeId(typeId2).contractId(contractId3).build(), // state definition not found => get country definition
                    TimeoffEntitlementDBO.builder().id(400L).typeId(typeId2).contractId(contractId4).build(), // find by country definitions
                    TimeoffEntitlementDBO.builder().id(500L).typeId(typeId1).contractId(contractId2).build(), // find by default definitions
                    TimeoffEntitlementDBO.builder().id(600L).typeId(typeId2).contractId(contractId5).build(), // find by default definitions
                    TimeoffEntitlementDBO.builder().id(700L).typeId(typeId2).contractId(contractId6).build(), // find by default definitions
                    TimeoffEntitlementDBO.builder().id(800L).typeId(typeId2).contractId(contractId7).build(), // fail to update since no definitions
                    TimeoffEntitlementDBO.builder().id(900L).typeId(typeId3).contractId(contractId8).build() // fail to update since multiple default definitions
            );

            Page<TimeoffEntitlementDBO> entitlementPage = new PageImpl<>(mockEntitlements);
            Page<TimeoffEntitlementDBO> emptyPage = new PageImpl<>(Collections.emptyList());

            List<ContractOuterClass.Contract> contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(contractId1).setCompanyId(companyId1).build(),
                    ContractOuterClass.Contract.newBuilder().setId(contractId2).setCompanyId(companyId2).setCountry("USA").setCountryStateCode("Michigan").build(),
                    ContractOuterClass.Contract.newBuilder().setId(contractId3).setCompanyId(companyId2).setCountry("USA").setCountryStateCode("NY").build(),
                    ContractOuterClass.Contract.newBuilder().setId(contractId4).setCompanyId(companyId2).setCountry("USA").build(),
                    ContractOuterClass.Contract.newBuilder().setId(contractId5).setCompanyId(companyId2).setCountry("LKA").build(),
                    ContractOuterClass.Contract.newBuilder().setId(contractId6).setCompanyId(companyId2).setCountry("LKA").setCountryStateCode("Colombo").build(),
                    ContractOuterClass.Contract.newBuilder().setId(contractId7).setCompanyId(companyId2).setCountry("IND").build(),
                    ContractOuterClass.Contract.newBuilder().setId(contractId8).setCompanyId(companyId2).setCountry("AFG").build()
            );

            when(countryDefinitionRepository.findAll()).thenReturn(countryDefinitionEntities);
            when(timeoffEntitlementSpecificationBuilder.buildForContractIdIn(anySet(), anySet())).thenReturn(spec);
            when(timeoffEntitlementDBORepository.findAll(eq(spec), any(Pageable.class)))
                    .thenReturn(entitlementPage)
                    .thenReturn(emptyPage);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(anySet(), eq(true))).thenReturn(contracts);
            when(companyDefinitionRepository.findAllNonDeletedByTypeIdInAndCompanyIdIn(anySet(), anySet()))
                    .thenReturn(companyDefinitionEntities);

            timeoffService.backFillDefinitionIdsForEntitlements(null);

            verify(timeoffEntitlementDBORepository).saveAllAndFlush(anyList());

        }
    }

    @Nested
    class getTimeOffEntitlementsForContract {

        @Test
        void should_return_entitlements_for_contract() {
            val contractId = 100L;
            val companyId = 300L;
            val entitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(1L).definitionId(1L).value(14.0).unit(DAYS).contractId(contractId).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(2L).definitionId(2L).value(7.0).unit(DAYS).contractId(contractId).build(),
                    TimeoffEntitlementDBO.builder().id(3L).typeId(3L).definitionId(3L).value(24.0).unit(TimeOffUnit.WEEKS).contractId(contractId).build()
            );
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setCountry("India")
                    .build();
            val timeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("Annual Leave").build(),
                    TimeoffTypeDBO.builder().id(2L).key("Sick Leave").build(),
                    TimeoffTypeDBO.builder().id(3L).key("Maternity Leave").build(),
                    TimeoffTypeDBO.builder().id(4L).key("Paternity Leave").build()
            );
            val definitions = List.of(
                    new DefinitionEntity(1L, null, "", false, "Annual Leave For All Indian employees", "", "ANNUAL",Collections.emptySet(), null, TimeOffTypeDefinitionStatus.ACTIVE, "Indian Annual Leave Policy"),
                    new DefinitionEntity(2L, CountryCode.IND, "", true, "Sick Leave (India)", "", "ANNUAL",Collections.emptySet(), null, TimeOffTypeDefinitionStatus.ACTIVE, ""),
                    new DefinitionEntity(3L, null, "", false, "Maternity Leave For All employees", "", "ANNUAL",Collections.emptySet(), null, TimeOffTypeDefinitionStatus.ACTIVE, "Maternity Leave Policy")
            );
            DefinitionEntity definitionEntity1 = new DefinitionEntity();
            definitionEntity1.setRequired(true);
            CountryDefinitionEntity annualCountryDefinition = new CountryDefinitionEntity(1L, 1L, definitionEntity1);

            CountryDefinitionEntity sickCountryDefinition = new CountryDefinitionEntity(2L, 2L, definitions.get(1));
            CountryDefinitionEntity paternityCountryDefinition = new CountryDefinitionEntity(3L, 4L, null);


            val countryDefinitionsMap = Map.of(
                    1L, annualCountryDefinition,
                    2L, sickCountryDefinition,
                    4L, paternityCountryDefinition
            );

            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(contract);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(entitlements);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L, 3L))).thenReturn(timeOffTypes);
            when(definitionEntityRepository.findAllById(Set.of(1L, 2L, 3L))).thenReturn(definitions);
            when(definitionService.getTypeIdToEligibleCountryDefinitionMap(contract)).thenReturn(countryDefinitionsMap);

            val results = timeoffService.getTimeOffEntitlementsForContract(contractId);

            assertTrue(results.stream().anyMatch(ent -> ent.getIsMandatory() && ent.getValue() == 14.0));
            assertTrue(results.stream().anyMatch(ent -> ent.getIsMandatory() && ent.getValue() == 7.0));
            assertTrue(results.stream().anyMatch(ent -> !ent.getIsMandatory() && ent.getValue() == 24.0));

        }


    }

    @Nested
    class reallocateTimeoff {

        @Test
        void should_skip_reallocation_when_future_leaves_feature_enabled() {
            // Given
            LocalDate expiryTime = LocalDate.now().minusDays(1);
            TimeoffSummaryDBO summary = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .contractId(100L)
                    .typeId(1L)
                    .build();
            ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                    .setId(100L)
                    .setCompanyId(200L)
                    .build();

            when(timeoffSummaryService.getLatestSummariesExpiredBeforeOrOn(expiryTime)).thenReturn(List.of(summary));
            when(contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(List.of(100L))).thenReturn(List.of(contract));
            when(featureFlagService.isOn(FeatureFlags.FUTURE_LEAVES, Map.of("company", 200L))).thenReturn(true);

            // When
            timeoffService.reallocateTimeoff(expiryTime);

            // Then
            verify(timeoffSummaryService, never()).legacyReallocateTimeoff(any(), any());
        }

        @Test
        void should_proceed_with_reallocation_when_future_leaves_feature_disabled() {
            // Given
            LocalDate expiryTime = LocalDate.now().minusDays(1);
            TimeoffSummaryDBO summary = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .contractId(100L)
                    .typeId(1L)
                    .build();
            ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                    .setId(100L)
                    .setCompanyId(200L)
                    .build();

            when(timeoffSummaryService.getLatestSummariesExpiredBeforeOrOn(expiryTime)).thenReturn(List.of(summary));
            when(contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(List.of(100L))).thenReturn(List.of(contract));
            when(featureFlagService.isOn(FeatureFlags.FUTURE_LEAVES, Map.of("company", 200L))).thenReturn(false);

            // When
            timeoffService.reallocateTimeoff(expiryTime);

            // Then
            verify(timeoffSummaryService).legacyReallocateTimeoff(summary, contract);
        }

        @Test
        void should_handle_mixed_companies_with_different_feature_flag_states() {
            // Given
            LocalDate expiryTime = LocalDate.now().minusDays(1);
            TimeoffSummaryDBO summary1 = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .contractId(100L)
                    .typeId(1L)
                    .build();
            TimeoffSummaryDBO summary2 = TimeoffSummaryDBO.builder()
                    .id(2L)
                    .contractId(101L)
                    .typeId(1L)
                    .build();
            ContractOuterClass.Contract contract1 = ContractOuterClass.Contract.newBuilder()
                    .setId(100L)
                    .setCompanyId(200L)
                    .build();
            ContractOuterClass.Contract contract2 = ContractOuterClass.Contract.newBuilder()
                    .setId(101L)
                    .setCompanyId(201L)
                    .build();

            when(timeoffSummaryService.getLatestSummariesExpiredBeforeOrOn(expiryTime)).thenReturn(List.of(summary1, summary2));
            when(contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(List.of(100L, 101L))).thenReturn(List.of(contract1, contract2));
            when(featureFlagService.isOn(FeatureFlags.FUTURE_LEAVES, Map.of("company", 200L))).thenReturn(true);
            when(featureFlagService.isOn(FeatureFlags.FUTURE_LEAVES, Map.of("company", 201L))).thenReturn(false);

            // When
            timeoffService.reallocateTimeoff(expiryTime);

            // Then
            verify(timeoffSummaryService, never()).legacyReallocateTimeoff(summary1, contract1);
            verify(timeoffSummaryService).legacyReallocateTimeoff(summary2, contract2);
        }

        @Test
        void should_skip_reallocation_when_contract_not_found() {
            // Given
            LocalDate expiryTime = LocalDate.now().minusDays(1);
            TimeoffSummaryDBO summary = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .contractId(100L)
                    .typeId(1L)
                    .build();

            when(timeoffSummaryService.getLatestSummariesExpiredBeforeOrOn(expiryTime)).thenReturn(List.of(summary));
            when(contractServiceAdapter.getNonDeletedNonEndedContractsByIdsInChunks(List.of(100L))).thenReturn(Collections.emptyList());

            // When
            timeoffService.reallocateTimeoff(expiryTime);

            // Then
            verify(timeoffSummaryService, never()).legacyReallocateTimeoff(any(), any());
            verify(featureFlagService, never()).isOn(any(), any());
        }
    }

    @Nested
    class getTimeOffContractRequirements {

        @Test
        void should_throw_validation_exception_if_contract_not_found() {
            val contractId = 100L;
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(null);

            assertThrows(ValidationException.class, () -> timeoffService.getTimeOffContractRequirements(contractId, dfe));
        }

        @Test
        void should_throw_access_denied_exception_when_company_tries_to_get_other_company_contract_data() {
            val contractId = 100L;
            val companyId = 350L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(contract);
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(300L, 2345L));
            doThrow(ValidationException.class).when(authorizationService).authorize(dfe, contract);
            assertThrows(ValidationException.class, () -> timeoffService.getTimeOffContractRequirements(contractId, dfe));
        }


        @Test
        void should_throw_error_response_when_requested_for_freelancer() {
            val contractId = 100L;
            val memberId = 1000L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setType(ContractOuterClass.ContractType.FREELANCER)
                    .setMemberId(memberId)
                    .build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(contract);
            when(currentUser.getContext()).thenReturn(getMemberUserDetails(1000L));

            val ex = assertThrows(ValidationException.class, ()-> timeoffService.getTimeOffContractRequirements(contractId, dfe));

            assertEquals("Contract type is not eligible for timeoffs entitlements (id = 100, type = FREELANCER)", ex.getMessage());
        }

        @Test
        void should_throw_error_response_when_requested_for_contractor() {
            val contractId = 100L;
            val memberId = 1000L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setType(ContractOuterClass.ContractType.CONTRACTOR)
                    .setMemberId(memberId)
                    .build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(contract);
            when(currentUser.getContext()).thenReturn(getMemberUserDetails(1000L));

            val ex = assertThrows(ValidationException.class, ()-> timeoffService.getTimeOffContractRequirements(contractId, dfe));

            assertEquals("Contract type is not eligible for timeoffs entitlements (id = 100, type = CONTRACTOR)", ex.getMessage());
        }

        @Test
        void should_send_requirements_for_hr_member() {
            val contractId = 100L;
            val companyId = 300L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setCountry("INDIA")
                    .setId(contractId)
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .setCompanyId(companyId)
                    .build();
            val sickCompanyDefinition = new DefinitionEntity(1L, null, "", false, "", "", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(14.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "N&N Global Sick Leave Policy");

            val festivalCompanyLeaveDefinition = new DefinitionEntity(3L, null, "", false, "", "", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(3.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "N&N Festival Leave Policy");


            // HR Member only have company definitions
            List<TimeOffDefinitionEntity> allDefinitions = List.of(
                    new CompanyDefinitionEntity(1L, 2L, sickCompanyDefinition, companyId, null, null, new ArrayList<>()), // Sick Leave
                    new CompanyDefinitionEntity(2L, 3L, festivalCompanyLeaveDefinition, companyId, null, null, new ArrayList<>()) // Festival Leave :  eligible definition -> but no existing entitlements
            );


            val timeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annualLeave").build(),
                    TimeoffTypeDBO.builder().id(2L).key("sickLeave").build(),
                    TimeoffTypeDBO.builder().id(3L).key("festivalLeave").build(),
                    TimeoffTypeDBO.builder().id(14L).key("unpaidLeave").build()
            );

            val existingEntitlements = List.of(
                    // existing annual leave entitlement from default annual leave definition
                    TimeoffEntitlementDBO.builder().id(1L).contractId(contractId).typeId(1L).definitionId(null).value(10.0).unit(DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(1L).contractId(contractId).typeId(2L).definitionId(1L).value(14.0).unit(DAYS).build(),
                    // existing unpaid leave entitlement from default annual leave definition
                    TimeoffEntitlementDBO.builder().id(14L).contractId(contractId).typeId(14L).definitionId(null).value(365.0).unit(DAYS).build()
            );
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(contract);
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(300L, companyId));
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(allDefinitions);
            when(timeoffTypeRepository.findAllById(Set.of(1L,2L, 3L, 14L))).thenReturn(timeOffTypes);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);


            val result = timeoffService.getTimeOffContractRequirements(contractId, dfe);

            assertTrue(result.getClause().isEmpty());
            assertEquals(4, result.getAvailableEntitlements().size());
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(0.0) && !ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(14.0) && !ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(3.0) && !ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(365.0) && !ent.getIsMandatory()));

            assertEquals(3, result.getAssignedEntitlements().size());
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(10.0) && !ent.getIsMandatory()));
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(14.0) && !ent.getIsMandatory()));
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(365.0) && !ent.getIsMandatory()));

        }

        @Test
        void should_return_contract_requirements_for_eor() {
            val contractId = 100L;
            val companyId = 300L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setCountry("INDIA")
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .build();
            val annualCompanyDefinition = new DefinitionEntity(1L, null, "", false, "", "", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(14.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "N&N Global Annual Leave Policy");

            val annualCountryDefinition = new DefinitionEntity(10L, CountryCode.IND, "", true, "Annual Leave For All Indian employees", "", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(14.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "");

            val sickCountryDefinition = new DefinitionEntity(2L, CountryCode.IND, "", true, "Sick Leave For All Indian employees", "Indian minimum timeoff requirements", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(7.0, null, null, DAYS, List.of(), false)),
                            null, TimeOffTypeDefinitionStatus.ACTIVE, "");

            val usSickDefinition = new DefinitionEntity(205L, null, "", true, "Sick Leave For All USA employees", "USA minimum timeoff requirements", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(15.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "");

            val festivalCompanyLeaveDefinition = new DefinitionEntity(3L, null, "", false, "", "", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(3.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "N&N Festival Leave Policy");

            val personalCountryLeaveDefinition = new DefinitionEntity(4L, CountryCode.IND, "", false, "Personal Leave For All Indian employees", "Indian minimum timeoff requirements", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(0.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "");
            val birthdayDefinition = new DefinitionEntity(102L, CountryCode.IND, "", false, "Birthday Leave For All Indian employees", "Indian minimum timeoff requirements", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(10.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "");
            val birthdayCompanyDefinition = new CompanyDefinitionEntity(50L, 10L, birthdayDefinition, companyId, null, null, new ArrayList<>());
            val usSickCompanyDefinition = new CompanyDefinitionEntity(502L, 2L, usSickDefinition, companyId, null, null, new ArrayList<>());

            val allDefinitions = List.of(
                    new CompanyDefinitionEntity(1L, 1L, annualCompanyDefinition, companyId, null, null, new ArrayList<>()), // Mandatory Annual Leave - company definition
                    new CountryDefinitionEntity(2L, 2L, sickCountryDefinition), // Mandatory Sick Leave - country definition
                    new CompanyDefinitionEntity(2L, 3L, festivalCompanyLeaveDefinition, companyId, null, null, new ArrayList<>()), // Festival Leave - company definition
                    new CountryDefinitionEntity(3L, 4L, personalCountryLeaveDefinition) // Personal Leave - country definition
            );

            val allCountryDefinitions = Map.of(
                    1L, new CountryDefinitionEntity(1L, 1L, annualCountryDefinition),
                    2L, new CountryDefinitionEntity(2L, 2L, sickCountryDefinition),
                    4L, new CountryDefinitionEntity(3L, 4L, personalCountryLeaveDefinition)
            );

            val timeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annualLeave").build(),
                    TimeoffTypeDBO.builder().id(2L).key("sickLeave").build(),
                    TimeoffTypeDBO.builder().id(3L).key("festivalLeave").build(),
                    TimeoffTypeDBO.builder().id(4L).key("personalLeave").build(),
                    TimeoffTypeDBO.builder().id(10L).key("birthdayLeave").build()
            );

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).contractId(contractId).typeId(1L).definitionId(1L).value(21.0).unit(DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).contractId(contractId).typeId(2L).definitionId(205L).value(20.0).unit(DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(3L).contractId(contractId).typeId(3L).definitionId(3L).value(9.0).unit(DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(4L).contractId(contractId).typeId(10L).definitionId(102L).value(19.0).unit(DAYS).build() // entitlement assigned from non eligible definition
            );
            Map<Long, TimeOffDefinitionEntity> missingDefinitionMap = Map.of(
                    102L, birthdayCompanyDefinition,
                    205L, usSickCompanyDefinition
            );
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);


            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(contract);
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(300L, companyId));
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(allDefinitions);
            when(definitionService.findAllDefinitionsByIds(companyId, Set.of(102L, 205L))).thenReturn(missingDefinitionMap);
            when(timeoffTypeRepository.findAllById(Set.of(1L,2L,3L,4L, 10L))).thenReturn(timeOffTypes);
            when(definitionService.getTypeIdToEligibleCountryDefinitionMap(contract)).thenReturn(allCountryDefinitions);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);


            val result = timeoffService.getTimeOffContractRequirements(contractId, dfe);

            assertEquals(5, result.getAvailableEntitlements().size());
            assertEquals(4, result.getAssignedEntitlements().size());
            assertEquals(sickCountryDefinition.getClause(), result.getClause());
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(21.0) && ent.getIsMandatory()));
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(20.0) && ent.getIsMandatory()));
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(9.0) && !ent.getIsMandatory()));
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(19.0) && !ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(14.0) && ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(15.0) && ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(3.0) && !ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(0.0) && !ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(10.0) && !ent.getIsMandatory()));

        }

        @Test
        void should_return_contract_requirements_for_eor_when_requested_by_ops_user() {
            val contractId = 100L;
            val companyId = 300L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setCountry("INDIA")
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .build();
            val annualCompanyDefinition = new DefinitionEntity(1L, null, "", false, "", "", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(14.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "N&N Global Annual Leave Policy");

            val annualCountryDefinition = new DefinitionEntity(10L, CountryCode.IND, "", true, "Annual Leave For All Indian employees", "", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(14.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "");

            val sickCountryDefinition = new DefinitionEntity(2L, CountryCode.IND, "", true, "Sick Leave For All Indian employees", "Indian minimum timeoff requirements", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(7.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "");

            val usSickDefinition = new DefinitionEntity(205L, null, "", true, "Sick Leave For All USA employees", "USA minimum timeoff requirements", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(15.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "");

            val festivalCompanyLeaveDefinition = new DefinitionEntity(3L, null, "", false, "", "", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(3.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "N&N Festival Leave Policy");

            val personalCountryLeaveDefinition = new DefinitionEntity(4L, CountryCode.IND, "", false, "Personal Leave For All Indian employees", "Indian minimum timeoff requirements", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(0.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "");
            val birthdayDefinition = new DefinitionEntity(102L, CountryCode.IND, "", false, "Birthday Leave For All Indian employees", "Indian minimum timeoff requirements", "ANNUAL",
                    Set.of(new TimeoffDefinitionValidationEntity(10.0, null, null, DAYS, List.of(), false)),
                    null, TimeOffTypeDefinitionStatus.ACTIVE, "");
            val birthdayCompanyDefinition = new CompanyDefinitionEntity(50L, 10L, birthdayDefinition, companyId, null, null, new ArrayList<>());
            val usSickCompanyDefinition = new CompanyDefinitionEntity(502L, 2L, usSickDefinition, companyId, null, null, new ArrayList<>());

            val allDefinitions = List.of(
                    new CompanyDefinitionEntity(1L, 1L, annualCompanyDefinition, companyId, null, null, new ArrayList<>()), // Mandatory Annual Leave - company definition
                    new CountryDefinitionEntity(2L, 2L, sickCountryDefinition), // Mandatory Sick Leave - country definition
                    new CompanyDefinitionEntity(2L, 3L, festivalCompanyLeaveDefinition, companyId, null, null, new ArrayList<>()), // Festival Leave - company definition
                    new CountryDefinitionEntity(3L, 4L, personalCountryLeaveDefinition) // Personal Leave - country definition
            );

            val allCountryDefinitions = Map.of(
                    1L, new CountryDefinitionEntity(1L, 1L, annualCountryDefinition),
                    2L, new CountryDefinitionEntity(2L, 2L, sickCountryDefinition),
                    4L, new CountryDefinitionEntity(3L, 4L, personalCountryLeaveDefinition)
            );

            val timeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annualLeave").build(),
                    TimeoffTypeDBO.builder().id(2L).key("sickLeave").build(),
                    TimeoffTypeDBO.builder().id(3L).key("festivalLeave").build(),
                    TimeoffTypeDBO.builder().id(4L).key("personalLeave").build(),
                    TimeoffTypeDBO.builder().id(10L).key("birthdayLeave").build()
            );

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).contractId(contractId).typeId(1L).definitionId(1L).value(21.0).unit(DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).contractId(contractId).typeId(2L).definitionId(205L).value(20.0).unit(DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(3L).contractId(contractId).typeId(3L).definitionId(3L).value(9.0).unit(DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(4L).contractId(contractId).typeId(10L).definitionId(102L).value(19.0).unit(DAYS).build() // entitlement assigned from non eligible definition
            );
            Map<Long, TimeOffDefinitionEntity> missingDefinitionMap = Map.of(
                    102L, birthdayCompanyDefinition,
                    205L, usSickCompanyDefinition
            );
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);


            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(contract);
            when(currentUser.getContext()).thenReturn(getOpsUserDetails());
            when(definitionService.findAllDefinitionsForContract(contract)).thenReturn(allDefinitions);
            when(definitionService.findAllDefinitionsByIds(companyId, Set.of(102L, 205L))).thenReturn(missingDefinitionMap);
            when(timeoffTypeRepository.findAllById(Set.of(1L,2L,3L,4L, 10L))).thenReturn(timeOffTypes);
            when(definitionService.getTypeIdToEligibleCountryDefinitionMap(contract)).thenReturn(allCountryDefinitions);
            when(timeoffEntitlementDBORepository.findAllByContractId(contractId)).thenReturn(existingEntitlements);


            val result = timeoffService.getTimeOffContractRequirements(contractId, dfe);

            assertEquals(5, result.getAvailableEntitlements().size());
            assertEquals(4, result.getAssignedEntitlements().size());
            assertEquals(sickCountryDefinition.getClause(), result.getClause());
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(21.0) && ent.getIsMandatory()));
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(20.0) && ent.getIsMandatory()));
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(9.0) && !ent.getIsMandatory()));
            assertTrue(result.getAssignedEntitlements().stream().anyMatch(ent -> ent.getValue().equals(19.0) && !ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(14.0) && ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(15.0) && ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(3.0) && !ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(0.0) && !ent.getIsMandatory()));
            assertTrue(result.getAvailableEntitlements().stream().anyMatch(ent -> ent.getValue().equals(10.0) && !ent.getIsMandatory()));

        }
    }

    private UserContext getMemberUserDetails(long memberId) {
        return new UserContext(
                10L,
                "username",
                "member",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        memberId,
                        null,
                        null,
                        null,
                        null,
                        null,
                        false
                ),
                "user",
                List.of()
        );
    }

    private UserContext getCompanyUserUserDetails(Long companyUserId, Long companyId) {
        return new UserContext(
                10L,
                "username",
                "company",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        null,
                        companyId,
                        null,
                        companyUserId,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

    private UserContext getOpsUserDetails() {
        return new UserContext(
                10L,
                "username",
                "operations",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

    /**
     * Ignore scope
     *
     * @return with user_id = 10
     */
    private UserContext getCompanyUserUserDetails() {
        return getCompanyUserUserDetails(null, null);
    }

    private void mockManagerCompanyUser() {
        mockManagerCompanyUser(0L);
    }

    private void mockManagerCompanyUser(long cuId) {
        var companyUser = CompanyOuterClass.CompanyUser.newBuilder().setIsManager(true).setId(cuId).build();
        when(companyServiceAdapter.getCompanyUser(any())).thenReturn(companyUser);
    }

    private void mockPayrollAdminCompanyUser(long cuId) {
        var companyUser = CompanyOuterClass.CompanyUser.newBuilder().setIsManager(false)
                .addAllRoles(List.of(CompanyOuterClass.CompanyUserRole.PAYROLL_ADMIN))
                .setId(cuId).build();
        when(companyServiceAdapter.getCompanyUser(any())).thenReturn(companyUser);
    }

    private void mockAdminCompanyUser() {
        var companyUser = CompanyOuterClass.CompanyUser.newBuilder().setIsAdmin(true).build();
        when(companyServiceAdapter.getCompanyUser(any())).thenReturn(companyUser);
    }

    private void holidayByEntityOn(Long companyId) {
        when(featureFlagService.feature(FeatureFlags.HOLIDAYS_BY_ENTITY, Map.of("company", companyId))).thenReturn(createFeatureFlag(true));
    }

    private void holidayByEntityOff(Long companyId) {
        when(featureFlagService.feature(FeatureFlags.HOLIDAYS_BY_ENTITY, Map.of("company", companyId))).thenReturn(createFeatureFlag(false));
    }

    private static GBFeatureResult createFeatureFlag(boolean isOn) {
        return new GBFeatureResult(null, isOn, !isOn, GBFeatureSource.defaultValue, null, null);
    }

    private DefinitionEntity buildDefinitionEntity(double entitledCount, boolean isUnlimitedLeaves) {
        return new DefinitionEntity(
                100L, null, null, false, null, null, null,
                Set.of(
                        new TimeoffDefinitionValidationEntity(
                                isUnlimitedLeaves ? 365.0 : entitledCount, // default value
                                null, // minimum value
                                null, // maximum value
                                TimeOffUnit.DAYS,
                                List.of(ContractStatus.ONBOARDING.name(), ContractStatus.OFFBOARDING.name(), ContractStatus.ACTIVE.name()),
                                isUnlimitedLeaves // is unlimited leaves allowed
                        ))
                , null
                , TimeOffTypeDefinitionStatus.ACTIVE
                , "Policy"

        );
    }
}
