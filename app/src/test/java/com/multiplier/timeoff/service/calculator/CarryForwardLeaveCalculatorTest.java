package com.multiplier.timeoff.service.calculator;

import com.multiplier.timeoff.types.CarryForwardConfig;
import com.multiplier.timeoff.types.CarryForwardLimit;
import com.multiplier.timeoff.types.LimitValueType;
import com.multiplier.timeoff.types.TimeOffUnit;
import lombok.val;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

public class CarryForwardLeaveCalculatorTest {


    @Nested
    class GetMinMaxCarryForwardLeaves_withFixedType {

        @Test
        void emptyConfig_then_noCarryForwardLeaves() {
            val minMax = CarryForwardLeaveCalculator.getMinMaxCarryForwardLeaves(12, null);
            Assertions.assertEquals(0, minMax.getLeft());
            Assertions.assertEquals(0, minMax.getRight());
        }

        @Test
        void given_singleConfig_entitledCount_12_with_min_5_shouldReturnMinMax_5_infinitive() {

            val minConfig = new CarryForwardLimit(LimitValueType.FIXED, 5D, TimeOffUnit.DAYS);
            val config = new CarryForwardConfig(true, minConfig, null, null);

            val minMax = CarryForwardLeaveCalculator.getMinMaxCarryForwardLeaves(12, config);
            Assertions.assertEquals(minConfig.getValue().doubleValue(), minMax.getLeft().doubleValue());
            Assertions.assertTrue(minMax.getRight() > 366);
        }

        @Test
        void given_singleConfig_entitledCount_12_with_max_10_shouldReturnMinMax_0_10() {

            val maxConfig = new CarryForwardLimit(LimitValueType.FIXED, 10D, TimeOffUnit.DAYS);
            val config = new CarryForwardConfig(true, null, maxConfig, null);

            val minMax = CarryForwardLeaveCalculator.getMinMaxCarryForwardLeaves(12, config);
            Assertions.assertEquals(maxConfig.getValue().doubleValue(), minMax.getRight().doubleValue());
            Assertions.assertEquals(0, (double) minMax.getLeft());
        }

        @Test
        void given_singleConfig_entitledCount_12_with_min_max_5_10_shouldReturnMinMax_5_10() {

            val minConfig = new CarryForwardLimit(LimitValueType.FIXED, 5D, TimeOffUnit.DAYS);
            val maxConfig = new CarryForwardLimit(LimitValueType.FIXED, 10D, TimeOffUnit.DAYS);
            val config = new CarryForwardConfig(true, minConfig, maxConfig, null);

            val minMax = CarryForwardLeaveCalculator.getMinMaxCarryForwardLeaves(12, config);
            Assertions.assertEquals(minConfig.getValue().doubleValue(), minMax.getLeft().doubleValue());
            Assertions.assertEquals(maxConfig.getValue().doubleValue(), minMax.getRight().doubleValue());

        }

    }

    @Nested
    class GetMinMaxCarryForwardLeaves_withPercentageType {

        @Test
        void given_singleConfig_entitledCount_12_with_min_max_percentage_type() {

            val minConfig = new CarryForwardLimit(LimitValueType.PERCENTAGE, 30D, null);
            val maxConfig = new CarryForwardLimit(LimitValueType.PERCENTAGE, 100D, null);
            val config = new CarryForwardConfig(true, minConfig, maxConfig, null);

            val minMax = CarryForwardLeaveCalculator.getMinMaxCarryForwardLeaves(12, config);
            Assertions.assertEquals(3.5, minMax.getLeft().doubleValue());
            Assertions.assertEquals(12, minMax.getRight().doubleValue());
        }

    }


    @Nested
    class GetCarryForwardCount_byMinMaxCarryForwardConfig {

        @Test
        void getCarryForwardCount_multipleCase() {
            Assertions.assertEquals(0, CarryForwardLeaveCalculator.getCarryForwardCount(0, Pair.of(0d, 12d)));
            Assertions.assertEquals(10, CarryForwardLeaveCalculator.getCarryForwardCount(10, Pair.of(0d, 12d)));
            Assertions.assertEquals(5, CarryForwardLeaveCalculator.getCarryForwardCount(5d, Pair.of(0d, 5d)));
            Assertions.assertEquals(5, CarryForwardLeaveCalculator.getCarryForwardCount(5d, Pair.of(3d, 5d)));
            Assertions.assertEquals(0, CarryForwardLeaveCalculator.getCarryForwardCount(2d, Pair.of(3d, 5d)));
        }

    }

}
