package com.multiplier.timeoff.service;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.WorkshiftServiceAdapter;
import com.multiplier.timeoff.core.common.dto.WorkshiftDTO;
import com.multiplier.timeoff.core.common.exceptionhandler.MPLErrorType;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.exception.TimeoffValidationException;
import com.multiplier.timeoff.repository.TimeoffEntitlementDBORepository;
import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.service.exception.DataCorruptionException;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.TimeOffInputMapper;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.Builder;
import lombok.Data;
import lombok.val;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class TimeoffSummaryBreakdownServiceTest {
    @Mock
    private AuthorizationService authorizationService;
    @Mock
    private ContractServiceAdapter contractServiceAdapter;
    @Mock
    private TimeoffSummaryService timeoffSummaryService;
    @Mock
    private TimeoffBreakdownService timeoffBreakdownService;
    @Mock
    private CarryForwardLeaveService carryForwardLeaveService;
    @Mock
    private TimeoffEntitlementDBORepository entitlementDBORepository;
    @Mock
    private WorkshiftServiceAdapter workshiftServiceAdapter;
    @Mock
    private DgsDataFetchingEnvironment mockDfe;
    @Mock
    private TimeOffInputMapper timeOffInputMapper;

    @InjectMocks
    private TimeoffSummaryBreakdownService timeoffSummaryBreakdownService;

    @Nested
    class GetSummaryBreakdownTest {
        static final Long CONTRACT_ID = 134L;
        static final Long TYPE_ID = 23L;

        @Test
        void should_throw_validation_error_for_invalid_inputs() {
            val input = TimeOffSummaryBreakdownInput.newBuilder()
                    .contractId(0L)
                    .typeId(0L)
                    .build();

            val ex = assertThrows(ValidationException.class, () -> timeoffSummaryBreakdownService.getSummaryBreakdown(mockDfe, input));

            assertEquals("Invalid contract id : 0; Invalid type id : 0; Time off start date is required.; Time off end date is required.", ex.getMessage());
        }

        @Test
        void should_throw_validation_error_for_start_date_is_after_end_date() {
            val timeoffStartDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2025, 2, 3))
                    .build();
            val timeoffEndDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2025, 2, 1))
                    .build();
            val input = TimeOffSummaryBreakdownInput.newBuilder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .timeOffStartDate(timeoffStartDate)
                    .timeOffEndDate(timeoffEndDate)
                    .build();

            val ex = assertThrows(ValidationException.class, () -> timeoffSummaryBreakdownService.getSummaryBreakdown(mockDfe, input));

            assertEquals("Start date must not be after end date. start date :2025-02-03 end date : 2025-02-01", ex.getMessage());
        }

        @Test
        void should_throw_validation_error_when_entitlement_not_found() {
            val timeoffStartDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2025, 2, 1))
                    .build();
            val timeoffEndDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2025, 2, 12))
                    .build();
            val input = TimeOffSummaryBreakdownInput.newBuilder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .timeOffStartDate(timeoffStartDate)
                    .timeOffEndDate(timeoffEndDate)
                    .build();
            val contract = ContractOuterClass.ContractBasicInfo.newBuilder()
                    .setContractId(CONTRACT_ID)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .build();
            when(contractServiceAdapter.getBasicContractById(CONTRACT_ID)).thenReturn(contract);
            when(entitlementDBORepository.findByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(Optional.empty());

            val ex = assertThrows(ValidationException.class, () -> timeoffSummaryBreakdownService.getSummaryBreakdown(mockDfe, input));

            assertEquals("Entitlement not found for contract id : " + CONTRACT_ID + " and type id : " + TYPE_ID, ex.getMessage());
        }

        @Test
        void should_throw_error_when_current_summary_is_not_found() {
            val timeoffStartDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2025, 2, 3))
                    .build();
            val timeoffEndDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2025, 2, 10))
                    .build();
            val input = TimeOffSummaryBreakdownInput.newBuilder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .timeOffStartDate(timeoffStartDate)
                    .timeOffEndDate(timeoffEndDate)
                    .build();
            val contract = ContractOuterClass.ContractBasicInfo.newBuilder()
                    .setContractId(CONTRACT_ID)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .build();
            val definition = getDefinitionEntity(false, false, true, false, null);
            val entitlement = TimeoffEntitlementDBO.builder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .definition(definition)
                    .build();
            when(contractServiceAdapter.getBasicContractById(CONTRACT_ID)).thenReturn(contract);
            when(entitlementDBORepository.findByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(Optional.of(entitlement));
            when(timeoffSummaryService.getCurrentAndNextSummaryByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(new kotlin.Pair<>(null, null));

            val ex = assertThrows(DataCorruptionException.class, () -> timeoffSummaryBreakdownService.getSummaryBreakdown(mockDfe, input));

            assertEquals("Current summary is not found", ex.getMessage());

        }

        @Test
        void should_throw_error_when_past_timeoff_pass_active_summary_start_date() {
            val timeoffStartDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2024, 12, 23))
                    .build();
            val timeoffEndDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2025, 2, 10))
                    .build();
            val input = TimeOffSummaryBreakdownInput.newBuilder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .timeOffStartDate(timeoffStartDate)
                    .timeOffEndDate(timeoffEndDate)
                    .build();
            val contract = ContractOuterClass.ContractBasicInfo.newBuilder()
                    .setContractId(CONTRACT_ID)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .build();
            val entitlement = TimeoffEntitlementDBO.builder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .definition(getDefinitionEntity(true, true, true, true, null))
                    .build();

            val currentSummary = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffSummaryStatus.ACTIVE)
                    .typeId(TYPE_ID)
                    .periodStart(LocalDate.of(2025, 1, 1))
                    .periodEnd(LocalDate.of(2025, 12, 31))
                    .allocatedCount(25.0)
                    .carryForwardCount(0.0)
                    .carryForwardExpiredCount(0.0)
                    .usedFromAllocatedCount(13.5)
                    .usedFromCarryForwardCount(0.0)
                    .usedFromLapsableCount(6.0)
                    .usedFromNextCycleCarryForwardCount(0.0)
                    .build();
            val nextSummary = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffSummaryStatus.UPCOMING)
                    .typeId(TYPE_ID)
                    .periodStart(LocalDate.of(2026, 1, 1))
                    .periodEnd(LocalDate.of(2026, 12, 31))
                    .allocatedCount(25.0)
                    .carryForwardCount(0.0)
                    .carryForwardExpiredCount(0.0)
                    .usedFromAllocatedCount(0.0)
                    .usedFromCarryForwardCount(0.0)
                    .usedFromLapsableCount(0.0)
                    .usedFromNextCycleCarryForwardCount(0.0)
                    .build();
            when(contractServiceAdapter.getBasicContractById(CONTRACT_ID)).thenReturn(contract);
            when(entitlementDBORepository.findByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(Optional.of(entitlement));
            when(timeoffSummaryService.getCurrentAndNextSummaryByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(new kotlin.Pair<>(currentSummary, nextSummary));

            val ex = assertThrows(TimeoffValidationException.class, () -> timeoffSummaryBreakdownService.getSummaryBreakdown(mockDfe, input));

            assertEquals(MPLErrorType.MPL0523_INVALID_PAST_TIMEOFF.getCode(), ex.getMplError().getErrorType().getCode());
            assertEquals(MPLErrorType.MPL0523_INVALID_PAST_TIMEOFF.getMessage(), ex.getMplError().getErrorType().getMessage());

        }

        @Test
        void should_throw_error_when_future_leaves_enabled_but_no_future_summaries_found() {
            val timeoffStartDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2025, 12, 23))
                    .build();
            val timeoffEndDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2026, 2, 10))
                    .build();
            val input = TimeOffSummaryBreakdownInput.newBuilder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .timeOffStartDate(timeoffStartDate)
                    .timeOffEndDate(timeoffEndDate)
                    .build();
            val contract = ContractOuterClass.ContractBasicInfo.newBuilder()
                    .setContractId(CONTRACT_ID)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .build();
            val entitlement = TimeoffEntitlementDBO.builder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .definition(getDefinitionEntity(true, true, true, true, null))
                    .build();

            val currentSummary = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffSummaryStatus.ACTIVE)
                    .typeId(TYPE_ID)
                    .periodStart(LocalDate.of(2025, 1, 1))
                    .periodEnd(LocalDate.of(2025, 12, 31))
                    .allocatedCount(25.0)
                    .carryForwardCount(0.0)
                    .carryForwardExpiredCount(0.0)
                    .usedFromAllocatedCount(13.5)
                    .usedFromCarryForwardCount(0.0)
                    .usedFromLapsableCount(6.0)
                    .usedFromNextCycleCarryForwardCount(0.0)
                    .build();
            when(contractServiceAdapter.getBasicContractById(CONTRACT_ID)).thenReturn(contract);
            when(entitlementDBORepository.findByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(Optional.of(entitlement));
            when(timeoffSummaryService.getCurrentAndNextSummaryByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(new kotlin.Pair<>(currentSummary, null));

            val ex = assertThrows(DataCorruptionException.class, () -> timeoffSummaryBreakdownService.getSummaryBreakdown(mockDfe, input));

            assertEquals("Future leaves enabled but no future leaves can be found", ex.getMessage());
        }

        @Test
        void should_throw_error_when_future_leaves_not_enabled_but_applying_for_future_cycle() {
            val timeoffStartDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2025, 12, 23))
                    .build();
            val timeoffEndDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.of(2026, 2, 10))
                    .build();
            val input = TimeOffSummaryBreakdownInput.newBuilder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .timeOffStartDate(timeoffStartDate)
                    .timeOffEndDate(timeoffEndDate)
                    .build();
            val contract = ContractOuterClass.ContractBasicInfo.newBuilder()
                    .setContractId(CONTRACT_ID)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .build();
            val entitlement = TimeoffEntitlementDBO.builder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .definition(getDefinitionEntity(true, true, false, true, null))
                    .build();

            val currentSummary = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .contractId(CONTRACT_ID)
                    .status(TimeOffSummaryStatus.ACTIVE)
                    .typeId(TYPE_ID)
                    .periodStart(LocalDate.of(2025, 1, 1))
                    .periodEnd(LocalDate.of(2025, 12, 31))
                    .allocatedCount(25.0)
                    .carryForwardCount(0.0)
                    .carryForwardExpiredCount(0.0)
                    .usedFromAllocatedCount(13.5)
                    .usedFromCarryForwardCount(0.0)
                    .usedFromLapsableCount(6.0)
                    .usedFromNextCycleCarryForwardCount(0.0)
                    .build();
            when(contractServiceAdapter.getBasicContractById(CONTRACT_ID)).thenReturn(contract);
            when(entitlementDBORepository.findByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(Optional.of(entitlement));
            when(timeoffSummaryService.getCurrentAndNextSummaryByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(new kotlin.Pair<>(currentSummary, null));

            val ex = assertThrows(TimeoffValidationException.class, () -> timeoffSummaryBreakdownService.getSummaryBreakdown(mockDfe, input));

            assertEquals(MPLErrorType.MPL0524_INVALID_FUTURE_TIMEOFF.getCode(), ex.getMplError().getErrorType().getCode());
            assertEquals(MPLErrorType.MPL0524_INVALID_FUTURE_TIMEOFF.getMessage(), ex.getMplError().getErrorType().getMessage());
        }

        @ParameterizedTest
        @MethodSource("provideCurrentSummaryTimeoffsTestCases")
        void should_return_summary_breakdowns_test(TestCase testCase) {
            val input = TimeOffSummaryBreakdownInput.newBuilder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .timeOffStartDate(testCase.timeoffStartDate)
                    .timeOffEndDate(testCase.timeoffEndDate)
                    .build();
            val contract = ContractOuterClass.ContractBasicInfo.newBuilder()
                    .setContractId(CONTRACT_ID)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .build();
            val entitlement = TimeoffEntitlementDBO.builder()
                    .contractId(CONTRACT_ID)
                    .typeId(TYPE_ID)
                    .definition(testCase.definitionEntity)
                    .build();
            val workshiftDTO = WorkshiftDTO.builder()
                    .startDate(DayOfWeek.MONDAY)
                    .endDate(DayOfWeek.FRIDAY)
                    .build();
            val startDate = TimeOffDate.newBuilder()
                    .dateOnly(input.getTimeOffStartDate().getDateOnly())
                    .session(input.getTimeOffStartDate().getSession())
                    .build();
            val endDate = TimeOffDate.newBuilder().
                    dateOnly(input.getTimeOffEndDate().getDateOnly())
                    .session(input.getTimeOffEndDate().getSession())
                    .build();
            val currentAndNexSummary = new kotlin.Pair<>(testCase.currentSummary, testCase.nextSummary);
            when(contractServiceAdapter.getBasicContractById(CONTRACT_ID)).thenReturn(contract);
            when(entitlementDBORepository.findByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(Optional.of(entitlement));
            when(timeoffSummaryService.getCurrentAndNextSummaryByContractIdAndTypeId(input.getContractId(), input.getTypeId()))
                    .thenReturn(currentAndNexSummary);
            when(workshiftServiceAdapter.getWorkshiftByContractId(CONTRACT_ID)).thenReturn(workshiftDTO);
            when(timeoffBreakdownService.calculateTimeoffEntriesAndApplyingDays(input.getContractId(), startDate, endDate, workshiftDTO))
                    .thenReturn(getTimeoffBreakDown(input.getTimeOffStartDate(), input.getTimeOffEndDate()));
            when(timeOffInputMapper.map(input.getTimeOffStartDate())).thenReturn(startDate);
            when(timeOffInputMapper.map(input.getTimeOffEndDate())).thenReturn(endDate);
            // a call to carry forward service only happens if carry forward expiry enabled
            if (isCarryForwardEnabled(testCase)) {
                if (isCarryForwardExpiryEnabled(testCase)) {
                    if (testCase.testType == TestType.NEXT_SUMMARY) {
                        when(carryForwardLeaveService.getLastValidDate(eq(testCase.nextSummary.periodStart()), any()))
                                .thenReturn(LocalDate.of(2026, 3, 31));
                        when(carryForwardLeaveService.getLastValidDate(eq(testCase.nextSummary.periodEnd().plusDays(1)), any()))
                                .thenReturn(LocalDate.of(2027, 3, 31));
                    } else {
                        when(carryForwardLeaveService.getLastValidDate(eq(testCase.currentSummary.periodStart()), any()))
                                .thenReturn(LocalDate.of(2025, 3, 31));
                        when(carryForwardLeaveService.getLastValidDate(eq(testCase.nextSummary.periodStart()), any()))
                                .thenReturn(LocalDate.of(2026, 3, 31));
                        when(carryForwardLeaveService.getLastValidDate(eq(testCase.nextSummary.periodEnd().plusDays(1)), any()))
                                .thenReturn(LocalDate.of(2027, 3, 31));
                    }
                } else {
                    if (testCase.testType == TestType.NEXT_SUMMARY) {
                        when(carryForwardLeaveService.getLastValidDate(eq(testCase.nextSummary.periodStart()), any()))
                                .thenReturn(LocalDate.of(2026, 12, 31));
                        when(carryForwardLeaveService.getLastValidDate(eq(testCase.nextSummary.periodEnd().plusDays(1)), any()))
                                .thenReturn(LocalDate.of(2027, 12, 31));
                    } else  {
                        when(carryForwardLeaveService.getLastValidDate(eq(testCase.currentSummary.periodStart()), any()))
                                .thenReturn(LocalDate.of(2025, 12, 31));
                        when(carryForwardLeaveService.getLastValidDate(eq(testCase.nextSummary.periodStart()), any()))
                                .thenReturn(LocalDate.of(2026, 12, 31));
                        when(carryForwardLeaveService.getLastValidDate(eq(testCase.nextSummary.periodEnd().plusDays(1)), any()))
                                .thenReturn(LocalDate.of(2027, 12, 31));
                    }
                }
            }

            var response = timeoffSummaryBreakdownService.getSummaryBreakdown(mockDfe, input);

            assertEquals(testCase.expectedValues.breakdowns.size(), response.size());

            if (testCase.expectedValues.breakdowns.size() == 1) {
                assertResults(testCase.expectedValues.breakdowns.get(0), response.get(0));
            } else if (testCase.expectedValues.breakdowns.size() == 2) {
                assertResults(testCase.expectedValues.breakdowns.get(0), response.get(0));
                assertResults(testCase.expectedValues.breakdowns.get(1), response.get(1));
            }
        }

        private boolean isCarryForwardExpiryEnabled(TestCase testCase) {
            return testCase.definitionEntity != null
                    && testCase.definitionEntity.getConfigurations() != null
                    && testCase.definitionEntity.getConfigurations().getCarryForwardConfig() != null
                    && testCase.definitionEntity.getConfigurations().getCarryForwardConfig().getEnabled()
                    && testCase.definitionEntity.getConfigurations().getCarryForwardConfig().getExpiry() != null;
        }

        private boolean isCarryForwardEnabled(TestCase testCase) {
            return testCase.definitionEntity != null
                    && testCase.definitionEntity.getConfigurations() != null
                    && testCase.definitionEntity.getConfigurations().getCarryForwardConfig() != null
                    && testCase.definitionEntity.getConfigurations().getCarryForwardConfig().getEnabled();
        }

        private void assertResults(TimeOffSummaryBreakdown expected, TimeOffSummaryBreakdown actual) {
            assertEquals(expected.getSummaryStartDate(), actual.getSummaryStartDate());
            assertEquals(expected.getSummaryEndDate(), actual.getSummaryEndDate());
            assertEquals(expected.getEntitledDays(), actual.getEntitledDays());
            assertEquals(expected.getApplyingDays(), actual.getApplyingDays());
            // assert for used days
            assertEquals(expected.getUsedDays().getTotal(), actual.getUsedDays().getTotal());
            assertEquals(expected.getUsedDays().getAllocated(), actual.getUsedDays().getAllocated());
            assertEquals(expected.getUsedDays().getCarryForward(), actual.getUsedDays().getCarryForward());
            assertEquals(expected.getUsedDays().getLapsable(), actual.getUsedDays().getLapsable());
            assertEquals(expected.getUsedDays().getNextCycleCarryForward(), actual.getUsedDays().getNextCycleCarryForward());
            // assert for balance
            assertEquals(expected.getBalance().getTotalRemainingDays(), actual.getBalance().getTotalRemainingDays());
            assertEquals(expected.getBalance().getCarryForwardDays(), actual.getBalance().getCarryForwardDays());
            assertEquals(expected.getBalance().getCarryForwardExpiryDate(), actual.getBalance().getCarryForwardExpiryDate());

            assertEquals(expected.getErrors(), actual.getErrors());
        }


        // Method source for parameterized test
        private static Stream<TestCase> provideCurrentSummaryTimeoffsTestCases() {
            return Stream.of(
                    CurrentSummaryTimeoffTestCases.get_carry_forward_disabled_success_test_case(),
                    CurrentSummaryTimeoffTestCases.get_carry_forward_disabled_failure_test_case(),
                    CurrentSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_disabled_test_case(),
                    CurrentSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_enabled_test_case(),
                    CurrentSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_enabled_with_cf_max_limit_test_case(),
                    CurrentSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_enabled_with_cf_max_limit_insufficient_balance_test_case(),

                    // next summary timeoff test cases
                    NextSummaryTimeoffTestCases.get_carry_forward_disabled_lapsable_disabled_test_case(),
                    NextSummaryTimeoffTestCases.get_carry_forward_disabled_lapsable_enabled_using_from_lapsable_test_case(),
                    NextSummaryTimeoffTestCases.get_carry_forward_disabled_lapsable_enabled_not_using_from_lapsable_test_case(),
                    NextSummaryTimeoffTestCases.get_carry_forward_enabled_lapsable_disabled_test_case(),
                    NextSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_enabled_lapsable_disabled_test_case(),
                    NextSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_enabled_lapsable_enabled_test_case(),
                    NextSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_enabled_lapsable_enabled_insufficient_balance_test_case(),

                    // overlapping summary test cases
                    OverlappingSummaryTimeoffTestCases.get_carry_forward_disabled_lapsable_disabled_test_case(),
                    OverlappingSummaryTimeoffTestCases.get_carry_forward_disabled_lapsable_disabled_insufficient_balance_test_case(),
                    OverlappingSummaryTimeoffTestCases.get_carry_forward_disabled_lapsable_enabled_using_from_lapsable_test_case(),
                    OverlappingSummaryTimeoffTestCases.get_carry_forward_disabled_lapsable_enabled_not_using_from_lapsable_test_case(),
                    OverlappingSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_disabled_lapsable_disabled_test_case(),
                    OverlappingSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_enabled_lapsable_disabled_test_case(),
                    OverlappingSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_enabled_lapsable_enabled_test_case(),
                    OverlappingSummaryTimeoffTestCases.get_carry_forward_enabled_expiry_enabled_lapsable_enabled_insufficient_balance_test_case()
            );

        }

        static class CurrentSummaryTimeoffTestCases {
            private static TestCase get_carry_forward_disabled_success_test_case() {
                return TestCase.builder()
                        .testType(TestType.CURRENT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 2, 3))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 2, 4))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .definitionEntity(getDefinitionEntity(false, false, true, false, null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(13.5)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(6.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(0.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(1.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(21.0)
                                                                .allocated(15.0)
                                                                .carryForward(0.0)
                                                                .lapsable(6.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(4.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(0.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(0.0)
                                                                .allocated(0.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(25.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                ).build()
                        ).build();
            }

            private static TestCase get_carry_forward_disabled_failure_test_case() {
                return TestCase.builder()
                        .testType(TestType.CURRENT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 2, 3))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 2, 20))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(false, false, true, false, null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(13.5)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(6.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(0.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(14.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(33.5)
                                                                .allocated(27.5)
                                                                .carryForward(0.0)
                                                                .lapsable(6.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(-8.5)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of("Insufficient leave balance. Please reduce the number of leaves you're applying for,or get in touch with your manager"))
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(0.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(0.0)
                                                                .allocated(0.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(25.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                ).build()
                        ).build();
            }

            private static TestCase get_carry_forward_enabled_expiry_disabled_test_case() {
                return TestCase.builder()
                        .testType(TestType.CURRENT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 3, 28))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 4, 10))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(true, false, true, false, null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(5.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(5.0)
                                .usedFromCarryForwardCount(2.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(4.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(0.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(30.0)
                                                        .applyingDays(10.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(21.0)
                                                                .allocated(12.0)
                                                                .carryForward(5.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(4.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(9.0)
                                                                .carryForwardDays(9.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 12, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(0.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(0.0)
                                                                .allocated(0.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(25.0)
                                                                .carryForwardDays(25.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 12, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_expiry_enabled_test_case() {
                return TestCase.builder()
                        .testType(TestType.CURRENT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 3, 28))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 4, 10))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(true, true, true, false, null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(5.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(5.0)
                                .usedFromCarryForwardCount(2.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(4.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(0.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(30.0)
                                                        .applyingDays(10.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(21.0)
                                                                .allocated(13.0)
                                                                .carryForward(4.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(4.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(9.0)
                                                                .carryForwardDays(9.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(0.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(0.0)
                                                                .allocated(0.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(25.0)
                                                                .carryForwardDays(25.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_expiry_enabled_with_cf_max_limit_test_case() {
                return TestCase.builder()
                        .testType(TestType.CURRENT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 3, 28))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 4, 10))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                true,
                                true,
                                true,
                                false,
                                new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 5.0, TimeOffUnit.DAYS)))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(5.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(5.0)
                                .usedFromCarryForwardCount(2.0)
                                .usedFromLapsableCount(3.0)
                                .usedFromNextCycleCarryForwardCount(4.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(0.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(30.0)
                                                        .applyingDays(10.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(24.0)
                                                                .allocated(13.0)
                                                                .carryForward(4.0)
                                                                .lapsable(3.0)
                                                                .nextCycleCarryForward(4.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(6.0)
                                                                .carryForwardDays(1.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(0.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(0.0)
                                                                .allocated(0.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(25.0)
                                                                .carryForwardDays(5.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_expiry_enabled_with_cf_max_limit_insufficient_balance_test_case() {
                return TestCase.builder()
                        .testType(TestType.CURRENT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 4, 1))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 4, 10))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                true,
                                true,
                                true,
                                false,
                                new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 5.0, TimeOffUnit.DAYS)))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(5.0)
                                .carryForwardExpiredCount(3.0)
                                .usedFromAllocatedCount(5.0)
                                .usedFromCarryForwardCount(2.0)
                                .usedFromLapsableCount(15.0)
                                .usedFromNextCycleCarryForwardCount(4.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(0.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(27.0)
                                                        .applyingDays(8.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(34.0)
                                                                .allocated(13.0)
                                                                .carryForward(2.0)
                                                                .lapsable(15.0)
                                                                .nextCycleCarryForward(4.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(-7.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of("Insufficient leave balance. Please reduce the number of leaves you're applying for,or get in touch with your manager"))
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(0.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(0.0)
                                                                .allocated(0.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(25.0)
                                                                .carryForwardDays(5.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

        }

        static class NextSummaryTimeoffTestCases {

            private static TestCase get_carry_forward_disabled_lapsable_disabled_test_case() {
                return TestCase.builder()
                        .testType(TestType.NEXT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 2, 2))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 2, 4))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                false,
                                false,
                                true,
                                false,
                                null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(5.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(3.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(0.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(5.0)
                                                                .allocated(5.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(20.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(2.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(5.5)
                                                                .allocated(5.5)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(19.5)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_disabled_lapsable_enabled_using_from_lapsable_test_case() {
                return TestCase.builder()
                        .testType(TestType.NEXT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 2, 2))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 2, 6))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                false,
                                false,
                                true,
                                true,
                                null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(5.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(18.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(4.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(2.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(25.0)
                                                                .allocated(5.0)
                                                                .carryForward(0.0)
                                                                .lapsable(20.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(0.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(2.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(6.5)
                                                                .allocated(6.5)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(18.5)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_disabled_lapsable_enabled_not_using_from_lapsable_test_case() {
                return TestCase.builder()
                        .testType(TestType.NEXT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 2, 2))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 2, 6))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                false,
                                false,
                                true,
                                true,
                                null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(7.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(18.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(4.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(0.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(25.0)
                                                                .allocated(7.0)
                                                                .carryForward(0.0)
                                                                .lapsable(18.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(0.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(4.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(8.5)
                                                                .allocated(8.5)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(16.5)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_lapsable_disabled_test_case() {
                return TestCase.builder()
                        .testType(TestType.NEXT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 2, 2))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 2, 6))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                true,
                                false,
                                true,
                                false,
                                new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 5.0, TimeOffUnit.DAYS)))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(5.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(7.0)
                                .usedFromCarryForwardCount(2.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(1.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(4.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(30.0)
                                                        .applyingDays(4.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(14.0)
                                                                .allocated(7.0)
                                                                .carryForward(2.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(5.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(16.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 12, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(0.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(4.5)
                                                                .allocated(4.5)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(20.5)
                                                                .carryForwardDays(5.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 12, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_expiry_enabled_lapsable_disabled_test_case() {
                return TestCase.builder()
                        .testType(TestType.NEXT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 3, 30))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 4, 3))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                true,
                                true,
                                true,
                                false,
                                new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 5.0, TimeOffUnit.DAYS)))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(5.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(7.0)
                                .usedFromCarryForwardCount(2.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(1.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(4.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(30.0)
                                                        .applyingDays(1.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(11.5)
                                                                .allocated(7.0)
                                                                .carryForward(2.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(2.5)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(18.5)
                                                                .carryForwardDays(2.5)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(3.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(7.0)
                                                                .allocated(7.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(18.0)
                                                                .carryForwardDays(5.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_expiry_enabled_lapsable_enabled_test_case() {
                return TestCase.builder()
                        .testType(TestType.NEXT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 3, 30))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 4, 3))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                true,
                                true,
                                true,
                                true,
                                new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 5.0, TimeOffUnit.DAYS)))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(5.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(13.0)
                                .usedFromCarryForwardCount(2.0)
                                .usedFromLapsableCount(8.0)
                                .usedFromNextCycleCarryForwardCount(1.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(4.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(30.0)
                                                        .applyingDays(3.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(27.5)
                                                                .allocated(13.0)
                                                                .carryForward(2.0)
                                                                .lapsable(10.0)
                                                                .nextCycleCarryForward(2.5)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(2.5)
                                                                .carryForwardDays(2.5)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(1.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(5.0)
                                                                .allocated(5.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(20.0)
                                                                .carryForwardDays(5.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_expiry_enabled_lapsable_enabled_insufficient_balance_test_case() {
                return TestCase.builder()
                        .testType(TestType.NEXT_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 3, 30))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 4, 3))
                                .session(TimeOffSession.AFTERNOON)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                true,
                                true,
                                true,
                                true,
                                new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 5.0, TimeOffUnit.DAYS)))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(5.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(13.0)
                                .usedFromCarryForwardCount(2.0)
                                .usedFromLapsableCount(8.0)
                                .usedFromNextCycleCarryForwardCount(1.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(24.5)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(30.0)
                                                        .applyingDays(3.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(27.5)
                                                                .allocated(13.0)
                                                                .carryForward(2.0)
                                                                .lapsable(10.0)
                                                                .nextCycleCarryForward(2.5)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(2.5)
                                                                .carryForwardDays(2.5)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(1.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(25.5)
                                                                .allocated(25.5)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(-0.5)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of("Insufficient leave balance. Please reduce the number of leaves you're applying for,or get in touch with your manager"))
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

        }

        static class OverlappingSummaryTimeoffTestCases {

            private static TestCase get_carry_forward_disabled_lapsable_disabled_test_case() {
                return TestCase.builder()
                        .testType(TestType.OVERLAPPING_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 12, 25))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 1, 4))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                false,
                                false,
                                true,
                                false,
                                null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(5.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(3.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(5.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(10.0)
                                                                .allocated(10.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(15.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(2.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(5.0)
                                                                .allocated(5.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(20.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_disabled_lapsable_disabled_insufficient_balance_test_case() {
                return TestCase.builder()
                        .testType(TestType.OVERLAPPING_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 12, 25))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 1, 4))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                false,
                                false,
                                true,
                                false,
                                null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(23.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(25.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(5.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(28.0)
                                                                .allocated(28.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(-3.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of("Insufficient leave balance. Please reduce the number of leaves you're applying for,or get in touch with your manager"))
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(2.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(27.0)
                                                                .allocated(27.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(-2.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of("Insufficient leave balance. Please reduce the number of leaves you're applying for,or get in touch with your manager"))
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_disabled_lapsable_enabled_using_from_lapsable_test_case() {
                return TestCase.builder()
                        .testType(TestType.OVERLAPPING_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 12, 25))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 1, 6))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                false,
                                false,
                                true,
                                true,
                                null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(13.5)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(4.5)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(13.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(7.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(25.0)
                                                                .allocated(18.5)
                                                                .carryForward(0.0)
                                                                .lapsable(6.5)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(0.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(1.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(14.5)
                                                                .allocated(14.5)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(10.5)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .errors(List.of())
                                                        .isCurrentSummary(false)
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_disabled_lapsable_enabled_not_using_from_lapsable_test_case() {
                return TestCase.builder()
                        .testType(TestType.OVERLAPPING_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 12, 25))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 1, 6))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                false,
                                false,
                                true,
                                true,
                                null))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(13.5)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(6.5)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(13.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(5.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(25.0)
                                                                .allocated(18.5)
                                                                .carryForward(0.0)
                                                                .lapsable(6.5)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(0.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(3.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(16.5)
                                                                .allocated(16.5)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(8.5)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(null)
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_expiry_disabled_lapsable_disabled_test_case() {
                return TestCase.builder()
                        .testType(TestType.OVERLAPPING_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 12, 25))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 1, 6))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                true,
                                false,
                                true,
                                false,
                                new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 5.0, TimeOffUnit.DAYS)))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(13.5)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(3.5)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(25.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(13.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(6.5)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(23.5)
                                                                .allocated(18.5)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(5.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(1.5)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 12, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(25.0)
                                                        .applyingDays(2.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(15.0)
                                                                .allocated(15.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(10.0)
                                                                .carryForwardDays(5.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 12, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_expiry_enabled_lapsable_disabled_test_case() {
                return TestCase.builder()
                        .testType(TestType.OVERLAPPING_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 12, 25))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 4, 5))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                true,
                                true,
                                true,
                                false,
                                new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 75.0, TimeOffUnit.DAYS)))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(100.0)
                                .carryForwardCount(75.0)
                                .carryForwardExpiredCount(12.0)
                                .usedFromAllocatedCount(10.0)
                                .usedFromCarryForwardCount(63.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(5.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(100.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(25.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(163.0)
                                                        .applyingDays(69.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(147.0)
                                                                .allocated(15.0)
                                                                .carryForward(63.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(69.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(16.0)
                                                                .carryForwardDays(6.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(100.0)
                                                        .applyingDays(3.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(28.0)
                                                                .allocated(28.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(72.0)
                                                                .carryForwardDays(72.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_expiry_enabled_lapsable_enabled_test_case() {
                return TestCase.builder()
                        .testType(TestType.OVERLAPPING_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 12, 25))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 4, 5))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                true,
                                true,
                                true,
                                true,
                                new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 75.0, TimeOffUnit.DAYS)))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(100.0)
                                .carryForwardCount(75.0)
                                .carryForwardExpiredCount(12.0)
                                .usedFromAllocatedCount(1.0)
                                .usedFromCarryForwardCount(63.0)
                                .usedFromLapsableCount(10.0)
                                .usedFromNextCycleCarryForwardCount(5.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(100.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(25.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(163.0)
                                                        .applyingDays(72.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(151.0)
                                                                .allocated(6.0)
                                                                .carryForward(63.0)
                                                                .lapsable(19.0)
                                                                .nextCycleCarryForward(63.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(12.0)
                                                                .carryForwardDays(12.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of())
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(100.0)
                                                        .applyingDays(0.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(25.0)
                                                                .allocated(25.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(75.0)
                                                                .carryForwardDays(75.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of())
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }

            private static TestCase get_carry_forward_enabled_expiry_enabled_lapsable_enabled_insufficient_balance_test_case() {
                return TestCase.builder()
                        .testType(TestType.OVERLAPPING_SUMMARY)
                        .timeoffStartDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2025, 12, 25))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .timeoffEndDate(TimeOffDateInput.newBuilder()
                                .dateOnly(LocalDate.of(2026, 4, 5))
                                .session(TimeOffSession.MORNING)
                                .build())
                        .definitionEntity(getDefinitionEntity(
                                true,
                                true,
                                true,
                                true,
                                new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 75.0, TimeOffUnit.DAYS)))
                        .currentSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .status(TimeOffSummaryStatus.ACTIVE)
                                .typeId(TYPE_ID)
                                .periodStart(LocalDate.of(2025, 1, 1))
                                .periodEnd(LocalDate.of(2025, 12, 31))
                                .allocatedCount(100.0)
                                .carryForwardCount(75.0)
                                .carryForwardExpiredCount(12.0)
                                .usedFromAllocatedCount(23.0)
                                .usedFromCarryForwardCount(63.0)
                                .usedFromLapsableCount(1.0)
                                .usedFromNextCycleCarryForwardCount(72.0)
                                .build())
                        .nextSummary(TimeoffSummaryDBO.builder()
                                .id(1L)
                                .contractId(CONTRACT_ID)
                                .typeId(TYPE_ID)
                                .status(TimeOffSummaryStatus.UPCOMING)
                                .periodStart(LocalDate.of(2026, 1, 1))
                                .periodEnd(LocalDate.of(2026, 12, 31))
                                .allocatedCount(100.0)
                                .carryForwardCount(0.0)
                                .carryForwardExpiredCount(0.0)
                                .usedFromAllocatedCount(50.0)
                                .usedFromCarryForwardCount(0.0)
                                .usedFromLapsableCount(0.0)
                                .usedFromNextCycleCarryForwardCount(0.0)
                                .build())
                        .expectedValues(TestCase.ExpectedValues
                                .builder()
                                .breakdowns(
                                        List.of(
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2025, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2025, 12, 31))
                                                        .entitledDays(163.0)
                                                        .applyingDays(5.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(164.0)
                                                                .allocated(28.0)
                                                                .carryForward(63.0)
                                                                .lapsable(1.0)
                                                                .nextCycleCarryForward(72.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(-1.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2026, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(true)
                                                        .errors(List.of("Insufficient leave balance. Please reduce the number of leaves you're applying for,or get in touch with your manager"))
                                                        .build(),
                                                TimeOffSummaryBreakdown.newBuilder()
                                                        .summaryStartDate(LocalDate.of(2026, 1, 1))
                                                        .summaryEndDate(LocalDate.of(2026, 12, 31))
                                                        .entitledDays(100.0)
                                                        .applyingDays(67.0)
                                                        .usedDays(TimeOffUsedDays.newBuilder()
                                                                .total(117.0)
                                                                .allocated(117.0)
                                                                .carryForward(0.0)
                                                                .lapsable(0.0)
                                                                .nextCycleCarryForward(0.0)
                                                                .build())
                                                        .balance(TimeOffBalance.newBuilder()
                                                                .totalRemainingDays(-17.0)
                                                                .carryForwardDays(0.0)
                                                                .carryForwardExpiryDate(LocalDate.of(2027, 3, 31))
                                                                .build())
                                                        .isCurrentSummary(false)
                                                        .errors(List.of("Insufficient leave balance. Please reduce the number of leaves you're applying for,or get in touch with your manager"))
                                                        .build()
                                        )
                                )
                                .build())
                        .build();
            }


        }

        enum TestType {
            CURRENT_SUMMARY,
            NEXT_SUMMARY,
            OVERLAPPING_SUMMARY
        }


        @Builder
        record TestCase(TestType testType, TimeoffSummaryDBO currentSummary, TimeoffSummaryDBO nextSummary,
                        TimeOffDateInput timeoffStartDate, TimeOffDateInput timeoffEndDate,
                        DefinitionEntity definitionEntity,
                        GetSummaryBreakdownTest.TestCase.ExpectedValues expectedValues, double expectedEntitledDays,
                        double expectedApplyingDays, double expectedUsedDaysTotal) {

            @Data
            @Builder
            static class ExpectedValues {
                List<TimeOffSummaryBreakdown> breakdowns;
            }
        }

    }

    private Pair<List<TimeoffEntryDBO>, Double> getTimeoffBreakDown(TimeOffDateInput startDate, TimeOffDateInput endDate) {
        val timeOffEntries = new ArrayList<TimeoffEntryDBO>();
        double noOfDays = 0.0;
        for (var date = startDate.getDateOnly(); date.isBefore(endDate.getDateOnly().plusDays(1)); date = date.plusDays(1)) {
            if (date.getDayOfWeek().equals(java.time.DayOfWeek.SATURDAY) || date.getDayOfWeek().equals(DayOfWeek.SUNDAY)) {
                timeOffEntries.add(TimeoffEntryDBO.builder()
                        .date(date)
                        .type(TimeOffEntryType.HOLIDAY)
                        .build());
                continue;
            }
            var startSession = date.equals(startDate.getDateOnly()) ? startDate.getSession() : TimeOffSession.MORNING;
            var endSession = date.equals(endDate.getDateOnly()) ? endDate.getSession() : TimeOffSession.AFTERNOON;
            var session = startSession.equals(endSession) ? endSession : TimeOffSession.FULL_DAY;
            timeOffEntries.add(TimeoffEntryDBO.builder()
                    .date(date)
                    .session(session)
                    .type(TimeOffEntryType.TIMEOFF)
                    .value(session.equals(TimeOffSession.FULL_DAY) ? 1.0 : 0.5)
                    .build());
            noOfDays += session.equals(TimeOffSession.FULL_DAY) ? 1.0 : 0.5;
        }

        return Pair.of(timeOffEntries, noOfDays);
    }

    private static DefinitionEntity getDefinitionEntity(boolean isCarryForwardEnabled,
                                                        boolean isCarryForwardExpired,
                                                        boolean isFutureLeaveEnabled,
                                                        boolean isLapsableLeaveEnabled,
                                                        CarryForwardLimitEntity carryForwardMaxLimit) {
        val definitionEntity = new DefinitionEntity();

        val carryForwardExpiry = isCarryForwardExpired ? new CarryForwardExpiryEntity(3.0, TimeOffUnit.MONTHS) : null;
        val carryForwardConfig = new CarryForwardConfigEntity(isCarryForwardEnabled, null, carryForwardMaxLimit, carryForwardExpiry);

        val lapsableExpiry = new LapsableExpiryEntity(1.0, TimeOffUnit.YEARS);
        val futureLeaveConfig = new FutureLeaveConfigEntity(isFutureLeaveEnabled, 1, new LapsableLeaveConfigEntity(isLapsableLeaveEnabled, lapsableExpiry));
        definitionEntity.setConfigurations(new TimeoffDefinitionConfigEntity(null, carryForwardConfig, futureLeaveConfig));
        return definitionEntity;
    }
}
