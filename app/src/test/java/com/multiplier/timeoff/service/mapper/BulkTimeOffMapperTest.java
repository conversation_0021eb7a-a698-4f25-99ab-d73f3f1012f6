package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.core.common.dto.BulkUpsertTimeOffResult;
import com.multiplier.timeoff.core.common.dto.BulkValidateTimeOffResult;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class BulkTimeOffMapperTest {

    private final BulkTimeOffMapperImpl mapper = new BulkTimeOffMapperImpl();

    @Test
    void testMapItem() {
        BulkValidateTimeOffResult.BulkValidateTimeOffResultItem input = BulkValidateTimeOffResult.BulkValidateTimeOffResultItem.builder()
                .externalTimeOffId("testId")
                .errors(Arrays.asList("error1", "error2"))
                .build();

        BulkUpsertTimeOffResult.Item result = mapper.map(input);

        assertNotNull(result);
        assertEquals("testId", result.getExternalTimeOffId());
        assertEquals(Arrays.asList("error1", "error2"), result.getErrors());
    }

    @Test
    void testMapItem_with_null_input() {
        BulkValidateTimeOffResult.BulkValidateTimeOffResultItem input = null;
        BulkUpsertTimeOffResult.Item result = mapper.map(input);

        assertNull(result);
    }

    @Test
    void testMapList() {
        BulkValidateTimeOffResult.BulkValidateTimeOffResultItem input1 = BulkValidateTimeOffResult.BulkValidateTimeOffResultItem.builder()
                .externalTimeOffId("testId1")
                .errors(Arrays.asList("error1", "error2"))
                .build();

        BulkValidateTimeOffResult.BulkValidateTimeOffResultItem input2 = BulkValidateTimeOffResult.BulkValidateTimeOffResultItem.builder()
                .externalTimeOffId("testId2")
                .errors(Arrays.asList("error3", "error4"))
                .build();

        List<BulkUpsertTimeOffResult.Item> result = mapper.map(Arrays.asList(input1, input2));

        assertNotNull(result);
        assertEquals(2, result.size());

        assertEquals("testId1", result.get(0).getExternalTimeOffId());
        assertEquals(Arrays.asList("error1", "error2"), result.get(0).getErrors());

        assertEquals("testId2", result.get(1).getExternalTimeOffId());
        assertEquals(Arrays.asList("error3", "error4"), result.get(1).getErrors());
    }

    @Test
    void testMapList_with_null_input() {
        List<BulkValidateTimeOffResult.BulkValidateTimeOffResultItem> input = null;
        List<BulkUpsertTimeOffResult.Item> result = mapper.map(input);

        assertNull(result);
    }
}
