package com.multiplier.timeoff.service;


import com.google.protobuf.Int32Value;
import com.google.type.Date;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.repository.model.CarryForwardConfigEntity;
import com.multiplier.timeoff.repository.model.TimeoffDefinitionConfigEntity;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.repository.*;
import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.service.builder.TimoffSpecificationBuilder;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.types.*;
import lombok.val;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.jpa.domain.Specification;

import java.lang.reflect.Field;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.multiplier.timeoff.types.TimeOffSummaryStatus.ACTIVE;
import static com.multiplier.timeoff.types.TimeOffSummaryStatus.UPCOMING;
import static com.multiplier.timeoff.types.TimeOffUnit.DAYS;
import static graphql.Assert.assertTrue;
import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class TimeOffSummaryServiceTest {

    @Mock
    TimeoffSummaryRepository timeoffSummaryRepository;

    @Mock
    TimeOffConfigurationService timeOffConfigurationService;

    @Mock
    TimeoffEntitlementDBORepository entitlementDBORepository;

    @Mock
    EntitlementChangeRecordRepository entitlementChangeRecordRepository;

    @InjectMocks
    private TimeoffSummaryService timeOffSummaryService;

    @Mock
    private TimeoffRepository timeoffRepo;

    @Mock
    private TimoffSpecificationBuilder timeoffSpecBuilder;

    @Mock
    private FeatureFlagService featureFlagService;

    @Mock
    private DefinitionEntityRepository definitionEntityRepository;

    @Mock
    private TimeoffTypeService timeoffTypeService;
    @Mock
    private CompanyServiceAdapter companyServiceAdapter;
    @Mock
    private Clock clock;
    @Mock
    private ContractServiceAdapter contractServiceAdapter;

    @Captor
    private ArgumentCaptor<List<TimeoffSummaryDBO>> summaryCaptor;
    @Captor
    private ArgumentCaptor<List<EntitlementChangeRecordEntity>> changeRecordCaptor;


    @Nested
    class OnTimeOffEntitlementChangesForBulk {
        @Nested
        class FutureLeaveFlagOff {

            @BeforeEach
            void setUp() {
                futureLeaveFlagOff();
            }

            @Test
            void should_update_timeoff_summary_when_timeoff_entitlement_changes_for_bulk() {
                // given
                val contractId1 = 100L;
                val contractId2 = 200L;

                val annualLeaveType = TimeoffTypeDBO.builder()
                        .id(1L)
                        .key("annual")
                        .label("Annual Leave")
                        .build();
                val sickLeaveType = TimeoffTypeDBO.builder()
                        .id(2L)
                        .key("sick")
                        .label("Sick Leave")
                        .build();
                val annualLeaveDefinition = new DefinitionEntity();
                val sickLeaveDefinition = new DefinitionEntity();
                val entitlementDBO1 = TimeoffEntitlementDBO.builder()
                        .id(1L)
                        .typeId(1L)
                        .contractId(contractId1)
                        .type(annualLeaveType)
                        .definition(annualLeaveDefinition)
                        .build();
                val entitlementDBO2 = TimeoffEntitlementDBO.builder()
                        .id(2L)
                        .typeId(2L)
                        .contractId(contractId2)
                        .type(sickLeaveType)
                        .definition(sickLeaveDefinition)
                        .build();

                val contract1 = ContractOuterClass.Contract.newBuilder()
                        .setId(contractId1)
                        .build();
                val contract2 = ContractOuterClass.Contract.newBuilder()
                        .setId(contractId2)
                        .build();
                List<TimeoffEntitlementDBO> changedEntitlementDBOs = List.of(entitlementDBO1, entitlementDBO2);
                Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(contractId1, contract1, contractId2, contract2);

                TimeoffSummaryDBO summaryDBO1 = TimeoffSummaryDBO.builder()
                        .id(1L)
                        .contractId(contractId1)
                        .typeId(1L)
                        .build();
                TimeoffSummaryDBO summaryDBO2 = TimeoffSummaryDBO.builder()
                        .id(2L)
                        .contractId(contractId1)
                        .typeId(2L)
                        .build();
                TimeoffSummaryDBO summaryDBO3 = TimeoffSummaryDBO.builder()
                        .id(3L)
                        .contractId(contractId2)
                        .typeId(1L)
                        .build();
                TimeoffSummaryDBO summaryDBO4 = TimeoffSummaryDBO.builder()
                        .id(4L)
                        .contractId(contractId2)
                        .typeId(2L)
                        .build();

                val summaries = List.of(summaryDBO1, summaryDBO2, summaryDBO3, summaryDBO4);

                when(timeOffConfigurationService.validateAndMutateSummaries(eq(changedEntitlementDBOs), anyMap(), eq(idToContractMap), eq(true)))
                        .thenReturn(summaries);

                // when
                timeOffSummaryService.onTimeOffEntitlementsResetBulk(changedEntitlementDBOs, idToContractMap);

                // then
                verify(timeoffSummaryRepository).saveAllAndFlush(summaries);
            }
        }

        @Nested
        class FutureLeaveFlagOn {
            @BeforeEach
            void setUp() {
                futureLeaveFlagOn();
                Instant fixedInstant = Instant.parse("2025-06-04T00:00:00Z");
                ZoneId zoneId = ZoneOffset.UTC;

                // Configure the injected mock clock to return a fixed instant and zone
                when(clock.instant()).thenReturn(fixedInstant);
                when(clock.getZone()).thenReturn(zoneId);
            }

            @Test
            void should_generate_summaries_for_entitlements_reset() {
                List<TimeoffEntitlementDBO> entitlements = List.of(
                        // timeoff type not exist => should skip generating summaries
                        TimeoffEntitlementDBO.builder().id(1L).typeId(100L).value(15.0).definitionId(1L).unit(DAYS).contractId(1L).build(),

                        // contract not exist => should skip generating summaries
                        TimeoffEntitlementDBO.builder().id(2L).typeId(1L).value(15.0).unit(DAYS).contractId(103L).build(),

                        // definition not exist => should skip generating summaries
                        TimeoffEntitlementDBO.builder().id(3L).typeId(12L).value(15.0).unit(DAYS).contractId(1L).build(),

                        // contract starts before current cycle start => should generate expired, active & future summaries (financial year Apr - Mar)
                        TimeoffEntitlementDBO.builder().id(4L).typeId(1L).definitionId(1L).value(14.0).unit(DAYS).contractId(1L).build(),

                        // contract starts same as current date => should generate active & future summaries
                        TimeoffEntitlementDBO.builder().id(5L).typeId(2L).definitionId(2L).value(7.0).unit(DAYS).contractId(2L).build(),

                        // future leaves disabled => should only create a active summary
                        TimeoffEntitlementDBO.builder().id(6L).typeId(1L).definitionId(3L).value(16.0).unit(DAYS).contractId(2L).build()

                );

                // contract starts before current financial year start
                val contract1StartDate = Date.newBuilder()
                        .setYear(2024)
                        .setMonth(9)
                        .setDay(3)
                        .build();
                val contract2StartDate = Date.newBuilder()
                        .setYear(2025)
                        .setMonth(6)
                        .setDay(4)
                        .build();
                Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                        1L, ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(100L).setStartOn(contract1StartDate).build(),
                        2L, ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(102L).setStartOn(contract2StartDate).build()
                );
                List<DefinitionEntity> definitions = List.of(
                        getDefinitionEntity(1L, true, true),
                        getDefinitionEntity(2L, false, true),
                        getDefinitionEntity(3L, false, false)
                );
                List<TimeoffTypeDBO> types = List.of(
                        TimeoffTypeDBO.builder().id(1L).key("annual").build(),
                        TimeoffTypeDBO.builder().id(2L).key("sick").build(),
                        TimeoffTypeDBO.builder().id(12L).key("paternity").build()
                );
                List<CompanyOuterClass.Company> companies = List.of(
                        CompanyOuterClass.Company.newBuilder().setId(100L).setFinancialYear(Int32Value.of(Month.APRIL.getValue())).build(),
                        CompanyOuterClass.Company.newBuilder().setId(102L).setFinancialYear(Int32Value.of(Month.JANUARY.getValue())).build()
                );

                when(definitionEntityRepository.findAllById(Set.of(1L, 2L, 3L))).thenReturn(definitions);
                when(timeoffTypeService.findAllTypeDBOsByIds(Set.of(1L, 2L, 100L, 12L))).thenReturn(types);
                when(companyServiceAdapter.getCompanyByIds(Set.of(100L, 102L))).thenReturn(companies);

                timeOffSummaryService.onTimeOffEntitlementsResetBulk(entitlements, idToContractMap);

                verify(timeoffSummaryRepository).saveAll(summaryCaptor.capture());
                verify(entitlementChangeRecordRepository).saveAll(changeRecordCaptor.capture());

                val savedSummaries = summaryCaptor.getValue();

                assertEquals(6, savedSummaries.size());
                // There should be 3 summaries generated for entitlement id = 4
                // 2024-09-03 to 2025-03-31
                // 2025-04-01 to 2026-03-31
                // 2026-04-01 to 2027-03-31
                val expectedStartDate1 = LocalDate.of(2024, 9, 3);
                val expectedEndDate1 = LocalDate.of(2025, 3, 31);

                val expectedStartDate2 = LocalDate.of(2025, 4, 1);
                val expectedEndDate2 = LocalDate.of(2026, 3, 31);

                val expectedStartDate3 = LocalDate.of(2026, 4, 1);
                val expectedEndDate3 = LocalDate.of(2027, 3, 31);

                isSummaryMatched(savedSummaries, 1L, 1L, expectedStartDate1, expectedEndDate1, TimeOffSummaryStatus.EXPIRED, 8.0);
                isSummaryMatched(savedSummaries, 1L, 1L, expectedStartDate2, expectedEndDate2, ACTIVE, 14.0);
                isSummaryMatched(savedSummaries, 1L, 1L, expectedStartDate3, expectedEndDate3, UPCOMING, 14.0);

                // There should be 2 summaries generated for entitlement id = 5
                // 2025-06-04 to 2025-12-31
                // 2026-01-01 to 2026-12-31
                val expectedStartDate4 = LocalDate.of(2025, 6, 4);
                val expectedEndDate4 = LocalDate.of(2025, 12, 31);

                val expectedStartDate5 = LocalDate.of(2026, 1, 1);
                val expectedEndDate5 = LocalDate.of(2026, 12, 31);

                // There should be a 1 summary generated for entitlement id = 6
                // 2025-06-04 to 2025-12-31
                val expectedStartDate6 = LocalDate.of(2025, 6, 4);
                val expectedEndDate6 = LocalDate.of(2025, 12, 31);

                isSummaryMatched(savedSummaries, 2L, 1L, expectedStartDate6, expectedEndDate6, ACTIVE, 16.0);


                val savedChangeRecords = changeRecordCaptor.getValue();
                assertEquals(5, savedChangeRecords.size());
                // There should be 2 ECR for entitlement id = 4
                assertTrue(isECRMatched(savedChangeRecords, 1L, 1L, expectedStartDate2, expectedEndDate2, EntitlementChangeCategory.ALLOCATION, 14.0));
                assertTrue(isECRMatched(savedChangeRecords, 1L, 1L, expectedStartDate3, expectedEndDate3, EntitlementChangeCategory.ALLOCATION, 14.0));

                // There should be 2 ECR for entitlement id = 5
                assertTrue(isECRMatched(savedChangeRecords, 2L, 2L, expectedStartDate4, expectedEndDate4, EntitlementChangeCategory.ALLOCATION, 7.0));
                assertTrue(isECRMatched(savedChangeRecords, 2L, 2L, expectedStartDate5, expectedEndDate5, EntitlementChangeCategory.ALLOCATION, 7.0));

                // There should be a 1 ECR for entitlement id = 6
                assertTrue(isECRMatched(savedChangeRecords, 2L, 1L, expectedStartDate6, expectedEndDate6, EntitlementChangeCategory.ALLOCATION, 16.0));


            }
        }

    }

    @Nested
    class UpdateSummaryOnDefinitionAssignment {
        @Nested
        class FutureLeaveFlagOn {

            @BeforeEach
            void setUp() {
                futureLeaveFlagOn();
                Instant fixedInstant = Instant.parse("2025-06-04T00:00:00Z");
                ZoneId zoneId = ZoneOffset.UTC;

                // Configure the injected mock clock to return a fixed instant and zone
                when(clock.instant()).thenReturn(fixedInstant);
                when(clock.getZone()).thenReturn(zoneId);
            }

            @Test
            void should_update_summaries_when_definition_is_assigned_to_contracts() {
                Long typeId = 2L;
                long companyId = 250L;

                TimeoffTypeDBO timeoffType = TimeoffTypeDBO.builder().id(typeId).key("custom").build();
                List<TimeoffEntitlementDBO> entitlements = List.of(
                        // new policy assignment => should generate new summaries
                        TimeoffEntitlementDBO.builder().id(1L).contractId(1L).typeId(typeId).type(timeoffType).value(15.0).definitionId(1L).unit(DAYS).build(),

                        // switching policy for same leave type assignment => should update  summaries
                        TimeoffEntitlementDBO.builder().id(2L).contractId(2L).typeId(typeId).type(timeoffType).value(15.0).definitionId(1L).unit(DAYS).build()
                );

                val contract1StartDate = Date.newBuilder()
                        .setYear(2024)
                        .setMonth(9)
                        .setDay(3)
                        .build();
                val contract2StartDate = Date.newBuilder()
                        .setYear(2023)
                        .setMonth(9)
                        .setDay(3)
                        .build();
                Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                        1L, ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(companyId).setStartOn(contract1StartDate).build(),
                        2L, ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(companyId).setStartOn(contract2StartDate).build()
                );
                val definition = getDefinitionEntity(1L, true, true);
                CompanyDefinitionEntity companyDefinition = new CompanyDefinitionEntity(1L, typeId, definition, companyId, EntityType.EOR_PARTNER_ENTITY, -1L, null);

                List<TimeoffSummaryDBO> summaries = List.of(
                        TimeoffSummaryDBO.builder().id(1L).contractId(2L).typeId(typeId)
                                .periodStart(LocalDate.parse("2025-01-01"))
                                .periodEnd(LocalDate.parse("2025-12-31"))
                                .timeoffType(timeoffType)
                                .status(ACTIVE)
                                .allocatedCount(4.0)
                                .totalEntitledCount(6.0)
                                .carryForwardCount(2.0)
                                .usedFromAllocatedCount(1.0)
                                .build(),
                        TimeoffSummaryDBO.builder().id(2L).contractId(2L).typeId(typeId)
                                .periodStart(LocalDate.parse("2026-01-01"))
                                .periodEnd(LocalDate.parse("2026-12-31"))
                                .timeoffType(timeoffType)
                                .status(UPCOMING)
                                .allocatedCount(4.0)
                                .totalEntitledCount(4.0)
                                .build()
                );

                CompanyOuterClass.Company company = CompanyOuterClass.Company.newBuilder()
                        .setId(companyId)
                        .setFinancialYear(Int32Value.of(Month.APRIL.getValue()))
                        .build();

                when(timeoffSummaryRepository.findAllByContractIdInAndTypeIdAndStatusIn(Set.of(1L, 2L), typeId, Set.of(ACTIVE, UPCOMING)))
                        .thenReturn(summaries);
                when(companyServiceAdapter.getCompany(companyId)).thenReturn(company);

                timeOffSummaryService.updateSummaryOnDefinitionAssignment(entitlements, idToContractMap, companyDefinition);

                verify(timeoffSummaryRepository).saveAll(summaryCaptor.capture());
                verify(entitlementChangeRecordRepository).saveAll(changeRecordCaptor.capture());

                val savedSummaries = summaryCaptor.getValue();
                assertEquals(4, savedSummaries.size());
                // There should be 2 summaries generated for entitlement id = 1
                // 2025-04-01 to 2026-03-31
                // 2026-04-01 to 2027-03-31

                val expectedStartDate1 = LocalDate.of(2025, 4, 1);
                val expectedEndDate1 = LocalDate.of(2026, 3, 31);

                val expectedStartDate2 = LocalDate.of(2026, 4, 1);
                val expectedEndDate2 = LocalDate.of(2027, 3, 31);

                isSummaryMatched(savedSummaries, 1L, typeId, expectedStartDate1, expectedEndDate1, ACTIVE, 15.0);
                isSummaryMatched(savedSummaries, 1L, typeId, expectedStartDate2, expectedEndDate2, UPCOMING, 15.0);

                // There should be 2 summaries updated for entitlement id = 2

                isSummaryMatched(savedSummaries, 2L, typeId, summaries.get(0).periodStart(), summaries.get(1).periodEnd(), ACTIVE, 15.0);
                isSummaryMatched(savedSummaries, 2L, typeId, summaries.get(1).periodStart(), summaries.get(1).periodEnd(), UPCOMING, 15.0);

                val savedChangeRecords = changeRecordCaptor.getValue();
                assertEquals(2, savedChangeRecords.size());

                // There should be 2 ECR for entitlement id = 4
                assertTrue(isECRMatched(savedChangeRecords, 1L, typeId, expectedStartDate1, expectedEndDate1, EntitlementChangeCategory.ALLOCATION, 15.0));
                assertTrue(isECRMatched(savedChangeRecords, 1L, typeId, expectedStartDate2, expectedEndDate2, EntitlementChangeCategory.ALLOCATION, 15.0));





            }
        }
    }

    @Nested
    class TimeOffSummaryUpdateTests {

        @Test
        void shouldUpdateTimeOffSummarySuccessfully() {
            Long summaryId = 1L;
            Long contractId = 101L;
            Long typeId = 201L;

            TimeoffSummaryDBO summary = TimeoffSummaryDBO.builder()
                    .id(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .allocatedCount(5.0)
                    .carriedCount(2.0)
                    .totalEntitledCount(7.0)
                    .periodStart(LocalDate.of(2025, 1, 1))
                    .periodEnd(LocalDate.of(2025, 12, 31))
                    .build();

            UpdateTimeOffSummaryInput input = UpdateTimeOffSummaryInput.newBuilder()
                    .summaryId(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .allocatedDaysCount(5.0)
                    .entitledDaysCount(7.0)
                    .carriedDaysCount(2.0)
                    .build();

            List<TimeoffDBO> timeOffs = createTimeOffEntries(contractId, typeId, 3.0, 2.0);

            when(timeoffSummaryRepository.findAllById(anySet())).thenReturn(List.of(summary));

            Specification<TimeoffDBO> mockSpec = mock(Specification.class);
            when(timeoffSpecBuilder.specBuilderByContractIdAndTypeId(any())).thenReturn(mockSpec);
            when(timeoffRepo.findAll(mockSpec)).thenReturn(timeOffs);

            assertDoesNotThrow(() -> timeOffSummaryService.updateTimeOffSummary(List.of(input)));

            verify(timeoffSummaryRepository).saveAll(anyList());
        }

        @Test
        void shouldThrowValidationExceptionForInvalidEntitlement() {
            Long summaryId = 2L;
            Long contractId = 102L;
            Long typeId = 202L;

            TimeoffSummaryDBO summary = TimeoffSummaryDBO.builder()
                    .id(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .allocatedCount(6.0)
                    .carriedCount(2.0)
                    .totalEntitledCount(7.0)
                    .periodStart(LocalDate.of(2025, 1, 1))
                    .periodEnd(LocalDate.of(2025, 12, 31))
                    .build();

            UpdateTimeOffSummaryInput input = UpdateTimeOffSummaryInput.newBuilder()
                    .summaryId(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .allocatedDaysCount(8.0) // Invalid allocation
                    .build();

            when(timeoffSummaryRepository.findAllById(anySet())).thenReturn(List.of(summary));

            var inputList = List.of(input);
            var ex = assertThrows(ValidationException.class, () -> timeOffSummaryService.updateTimeOffSummary(inputList));

            assertEquals("Validation failed for summaryId 2: Allocated count cannot be greater than entitled count.; Allocated count + Carried count must equal Entitled count.", ex.getMessage());
        }

        @Test
        void shouldThrowValidationExceptionForMismatchedAllocationAndCarriedDays() {
            Long summaryId = 3L;
            Long contractId = 103L;
            Long typeId = 203L;

            TimeoffSummaryDBO summary = TimeoffSummaryDBO.builder()
                    .id(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .allocatedCount(4.0)
                    .carriedCount(3.0)
                    .totalEntitledCount(7.0)
                    .periodStart(LocalDate.of(2025, 1, 1))
                    .periodEnd(LocalDate.of(2025, 12, 31))
                    .build();

            UpdateTimeOffSummaryInput input = UpdateTimeOffSummaryInput.newBuilder()
                    .summaryId(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .allocatedDaysCount(5.0) // Incorrect sum
                    .carriedDaysCount(3.0)
                    .entitledDaysCount(7.0)
                    .build();

            when(timeoffSummaryRepository.findAllById(anySet())).thenReturn(List.of(summary));

            var inputList = List.of(input);
            var ex = assertThrows(ValidationException.class, () -> timeOffSummaryService.updateTimeOffSummary(inputList));

            assertEquals("Validation failed for summaryId 3: Allocated count + Carried count must equal Entitled count.", ex.getMessage());
        }

        @Test
        void shouldThrowValidationExceptionWhenSummaryNotFound() {
            Long summaryId = 4L;
            Long contractId = 104L;
            Long typeId = 204L;

            UpdateTimeOffSummaryInput input = UpdateTimeOffSummaryInput.newBuilder()
                    .summaryId(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .build();

            when(timeoffSummaryRepository.findAllById(anySet())).thenReturn(List.of()); // No summaries found

            var inputList = List.of(input);
            var ex = assertThrows(ValidationException.class, () -> timeOffSummaryService.updateTimeOffSummary(inputList));

            assertEquals("Summary not found for ID: 4", ex.getMessage());
        }

        @Test
        void shouldCapturePendingAndTakenCountCorrectly() {
            Long summaryId = 10L;
            Long contractId = 110L;
            Long typeId = 210L;

            TimeoffSummaryDBO summary = TimeoffSummaryDBO.builder()
                    .id(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .allocatedCount(5.0)
                    .carriedCount(2.0)
                    .totalEntitledCount(7.0)
                    .periodStart(LocalDate.of(2025, 1, 1))
                    .periodEnd(LocalDate.of(2025, 12, 31))
                    .build();

            UpdateTimeOffSummaryInput input = UpdateTimeOffSummaryInput.newBuilder()
                    .summaryId(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .allocatedDaysCount(5.0)
                    .carriedDaysCount(2.0)
                    .entitledDaysCount(7.0)
                    .build();

            // Create mock timeoff entries
            List<TimeoffDBO> timeOffs = List.of(
                    TimeoffDBO.builder()
                            .id(summaryId)
                            .contractId(contractId)
                            .typeId(typeId)
                            .noOfDays(3.0)
                            .status(TimeOffStatus.TAKEN)
                            .build(),

                    TimeoffDBO.builder()
                            .id(summaryId)
                            .contractId(contractId)
                            .typeId(typeId)
                            .noOfDays(2.0)
                            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                            .build()
            );


            when(timeoffSummaryRepository.findAllById(anySet())).thenReturn(List.of(summary));

            Specification<TimeoffDBO> mockSpec = mock(Specification.class);
            when(timeoffSpecBuilder.specBuilderByContractIdAndTypeId(any())).thenReturn(mockSpec);
            when(timeoffRepo.findAll(mockSpec)).thenReturn(timeOffs);

            timeOffSummaryService.updateTimeOffSummary(List.of(input));

            // Capture the saved summary
            verify(timeoffSummaryRepository).saveAll(summaryCaptor.capture());
            List<TimeoffSummaryDBO> capturedSummaries = summaryCaptor.getValue();

            assertEquals(1, capturedSummaries.size());
            TimeoffSummaryDBO updatedSummary = capturedSummaries.get(0);

            // Expected values
            double expectedTakenCount = 3.0;  // TAKEN
            double expectedPendingCount = 2.0; // APPROVAL_IN_PROGRESS

            assertEquals(expectedTakenCount, updatedSummary.takenCount());
            assertEquals(expectedPendingCount, updatedSummary.pendingCount());
        }

        @Test
        void shouldThrowExceptionWhenTakenAndPendingExceedsEntitled() {
            Long summaryId = 10L;
            Long contractId = 110L;
            Long typeId = 210L;

            TimeoffSummaryDBO summary = TimeoffSummaryDBO.builder()
                    .id(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .allocatedCount(5.0)
                    .carriedCount(2.0)
                    .totalEntitledCount(7.0)  // Entitled days = 7
                    .periodStart(LocalDate.of(2025, 1, 1))
                    .periodEnd(LocalDate.of(2025, 12, 31))
                    .build();

            UpdateTimeOffSummaryInput input = UpdateTimeOffSummaryInput.newBuilder()
                    .summaryId(summaryId)
                    .contractId(contractId)
                    .typeId(typeId)
                    .allocatedDaysCount(5.0)
                    .carriedDaysCount(2.0)
                    .entitledDaysCount(7.0)
                    .build();

            // Create mock timeoff entries where taken + pending > entitled
            List<TimeoffDBO> timeOffs = List.of(
                    TimeoffDBO.builder()
                            .id(summaryId)
                            .contractId(contractId)
                            .typeId(typeId)
                            .noOfDays(5.0) // TAKEN
                            .status(TimeOffStatus.TAKEN)
                            .build(),

                    TimeoffDBO.builder()
                            .id(summaryId)
                            .contractId(contractId)
                            .typeId(typeId)
                            .noOfDays(3.0) // APPROVAL_IN_PROGRESS (Pending)
                            .status(TimeOffStatus.APPROVAL_IN_PROGRESS)
                            .build()
            );

            when(timeoffSummaryRepository.findAllById(anySet())).thenReturn(List.of(summary));

            Specification<TimeoffDBO> mockSpec = mock(Specification.class);
            when(timeoffSpecBuilder.specBuilderByContractIdAndTypeId(any())).thenReturn(mockSpec);
            when(timeoffRepo.findAll(mockSpec)).thenReturn(timeOffs);

            ValidationException exception = assertThrows(ValidationException.class,
                    () -> timeOffSummaryService.updateTimeOffSummary(List.of(input)));

            assertTrue(exception.getMessage().contains("Pending count + Taken count cannot exceed Entitled count."));
        }

    }

    private List<TimeoffDBO> createTimeOffEntries(Long contractId, Long typeId, double takenDays, double pendingDays) {
        return List.of(
                TimeoffDBO.builder().contractId(contractId).typeId(typeId).noOfDays(takenDays).status(TimeOffStatus.TAKEN).build(),
                TimeoffDBO.builder().contractId(contractId).typeId(typeId).noOfDays(pendingDays).status(TimeOffStatus.APPROVAL_IN_PROGRESS).build()
        );
    }

    @Nested
    class CalendarDateRangeTests {
        private static final Long CONTRACT_ID = 1L;

        @Test
        void getEarliestActiveSummaryStartDate_ShouldReturnEarliestDate_WhenMultipleSummariesExist() {
            // Given
            LocalDate earliestDate = LocalDate.of(2023, 1, 1);

            when(timeoffSummaryRepository.findEarliestActiveSummaryStartDate(CONTRACT_ID)).thenReturn(earliestDate);

            // When
            LocalDate result = timeOffSummaryService.getEarliestActiveSummaryStartDate(CONTRACT_ID);

            // Then
            assertEquals(earliestDate, result);
        }


        @Test
        void getFurthestSummaryEndDate_ShouldReturnFurthestDate_WhenMultipleSummariesExist() {
            // Given
            LocalDate furthestDate = LocalDate.of(2026, 1, 1);

            when(timeoffSummaryRepository.findFurthestSummaryEndDate(CONTRACT_ID)).thenReturn(furthestDate);

            // When
            LocalDate result = timeOffSummaryService.getFurthestSummaryEndDate(CONTRACT_ID);

            // Then
            assertEquals(furthestDate, result);
        }
    }

    @Nested
    class ActivateNextTimeoffSummariesTests {

        private final LocalDate currentDate = LocalDate.of(2025, 5, 20);
        private final int batchSize = 100;

        @BeforeEach
        void setUp() throws Exception {
            setActivationBatchSize();
        }

        @Test
        void should_skip_when_no_active_summaries_with_past_period_end() {
            // Given
            when(timeoffSummaryRepository.countActiveSummariesWithPastPeriodEnd(currentDate)).thenReturn(0L);

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).countActiveSummariesWithPastPeriodEnd(currentDate);
            verify(timeoffSummaryRepository, never()).findBatchOfActiveSummariesWithPastPeriodEnd(any(), anyInt());
            verify(timeoffSummaryRepository, never()).saveAllAndFlush(anyList());
            verify(entitlementChangeRecordRepository, never()).saveAllAndFlush(anyList());
        }

        @Test
        void should_skip_when_count_is_null() {
            // Given
            when(timeoffSummaryRepository.countActiveSummariesWithPastPeriodEnd(currentDate)).thenReturn(null);

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).countActiveSummariesWithPastPeriodEnd(currentDate);
            verify(timeoffSummaryRepository, never()).findBatchOfActiveSummariesWithPastPeriodEnd(any(), anyInt());
        }

        @Test
        void should_skip_when_no_upcoming_summaries_found() {
            // Given
            val contractId = 100L;
            val typeId = 1L;
            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");

            List<TimeoffSummaryDBO> activeSummaries = List.of(
                    createActiveSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31))
            );

            when(timeoffSummaryRepository.countActiveSummariesWithPastPeriodEnd(currentDate)).thenReturn(1L);
            when(timeoffSummaryRepository.findBatchOfActiveSummariesWithPastPeriodEnd(currentDate, batchSize)).thenReturn(activeSummaries);
            when(timeoffSummaryRepository.findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(Set.of(contractId), Set.of(typeId), currentDate)).thenReturn(List.of());

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).countActiveSummariesWithPastPeriodEnd(currentDate);
            verify(timeoffSummaryRepository).findBatchOfActiveSummariesWithPastPeriodEnd(currentDate, batchSize);
            verify(timeoffSummaryRepository).findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(Set.of(contractId), Set.of(typeId), currentDate);
            verify(timeoffSummaryRepository, never()).saveAllAndFlush(anyList());
        }

        @Test
        void should_activate_next_summary_successfully_with_carry_forward_enabled() {
            // Given
            val contractId = 100L;
            val typeId = 1L;
            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");

            TimeoffSummaryDBO activeSummary = createActiveSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31));
            activeSummary.allocatedCount(20.0).usedFromAllocatedCount(5.0).carryForwardCount(3.0);

            TimeoffSummaryDBO upcomingSummary = createUpcomingSummary(2L, contractId, typeId, timeoffType, LocalDate.of(2025, 1, 1), LocalDate.of(2025, 12, 31));

            TimeoffEntitlementDBO entitlement = createTimeoffEntitlement(contractId, typeId, 20.0, DAYS, timeoffType);
            entitlement.definitionId(1L);

            DefinitionEntity definition = createDefinitionWithCarryForward(true);

            ContractOuterClass.ContractBasicInfo contract = createActiveContract(contractId);

            setupMocksForSuccessfulActivation(activeSummary, upcomingSummary, entitlement, definition, contract);

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());
            verify(entitlementChangeRecordRepository).saveAllAndFlush(changeRecordCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(2, savedSummaries.size());

            // Verify active summary is marked as EXPIRED
            TimeoffSummaryDBO expiredSummary = savedSummaries.stream()
                    .filter(s -> s.id().equals(1L))
                    .findFirst().orElseThrow();
            assertEquals(TimeOffSummaryStatus.EXPIRED, expiredSummary.status());

            // Verify upcoming summary is marked as ACTIVE with carry forward
            TimeoffSummaryDBO activatedSummary = savedSummaries.stream()
                    .filter(s -> s.id().equals(2L))
                    .findFirst().orElseThrow();
            assertEquals(TimeOffSummaryStatus.ACTIVE, activatedSummary.status());
            assertTrue(activatedSummary.carryForwardCount() > 0);

            // Verify carry forward record is created
            List<EntitlementChangeRecordEntity> carryForwardRecords = changeRecordCaptor.getValue();
            assertEquals(1, carryForwardRecords.size());
            assertEquals(EntitlementChangeCategory.CARRY_FORWARD, carryForwardRecords.get(0).getCategory());
        }

        @Test
        void should_activate_next_summary_successfully_with_carry_forward_disabled() {
            // Given
            val contractId = 100L;
            val typeId = 1L;
            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");

            TimeoffSummaryDBO activeSummary = createActiveSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31));
            TimeoffSummaryDBO upcomingSummary = createUpcomingSummary(2L, contractId, typeId, timeoffType, LocalDate.of(2025, 1, 1), LocalDate.of(2025, 12, 31));

            TimeoffEntitlementDBO entitlement = createTimeoffEntitlement(contractId, typeId, 20.0, DAYS, timeoffType);
            entitlement.definitionId(1L);

            DefinitionEntity definition = createDefinitionWithCarryForward(false);
            ContractOuterClass.ContractBasicInfo contract = createActiveContract(contractId);

            setupMocksForSuccessfulActivation(activeSummary, upcomingSummary, entitlement, definition, contract);

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());
            // When carry forward is disabled, no carry forward records are created, so saveAllAndFlush is not called
            verify(entitlementChangeRecordRepository, never()).saveAllAndFlush(anyList());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(2, savedSummaries.size());

            // Verify upcoming summary is activated with zero carry forward
            TimeoffSummaryDBO activatedSummary = savedSummaries.stream()
                    .filter(s -> s.id().equals(2L))
                    .findFirst().orElseThrow();
            assertEquals(TimeOffSummaryStatus.ACTIVE, activatedSummary.status());
            assertEquals(0.0, activatedSummary.carryForwardCount());
        }

        @Test
        void should_handle_contract_ended_scenario() {
            // Given
            val contractId = 100L;
            val typeId = 1L;
            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");

            TimeoffSummaryDBO activeSummary = createActiveSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31));
            TimeoffSummaryDBO upcomingSummary = createUpcomingSummary(2L, contractId, typeId, timeoffType, LocalDate.of(2025, 1, 1), LocalDate.of(2025, 12, 31));

            TimeoffEntitlementDBO entitlement = createTimeoffEntitlement(contractId, typeId, 20.0, DAYS, timeoffType);

            // Contract is ended
            ContractOuterClass.ContractBasicInfo endedContract = createEndedContract(contractId);

            // For ended contracts, no definitions are fetched, so use legacy data setup
            setupMocksForLegacyData(activeSummary, upcomingSummary, entitlement, endedContract);

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(2, savedSummaries.size());

            // Both summaries should be marked as EXPIRED
            savedSummaries.forEach(summary -> assertEquals(TimeOffSummaryStatus.EXPIRED, summary.status()));

            // No carry forward records should be created for ended contracts
            verify(entitlementChangeRecordRepository, never()).saveAllAndFlush(anyList());
        }

        @Test
        void should_handle_legacy_data_with_null_definition_id_for_annual_leave() {
            // Given
            val contractId = 100L;
            val typeId = 1L; // Annual leave type ID
            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");

            TimeoffSummaryDBO activeSummary = createActiveSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31));
            activeSummary.allocatedCount(20.0).usedFromAllocatedCount(5.0);

            TimeoffSummaryDBO upcomingSummary = createUpcomingSummary(2L, contractId, typeId, timeoffType, LocalDate.of(2025, 1, 1), LocalDate.of(2025, 12, 31));

            // Legacy entitlement with null definition ID
            TimeoffEntitlementDBO entitlement = createTimeoffEntitlement(contractId, typeId, 20.0, DAYS, timeoffType);
            entitlement.definitionId(null);

            ContractOuterClass.ContractBasicInfo contract = createActiveContract(contractId);

            setupMocksForLegacyData(activeSummary, upcomingSummary, entitlement, contract);

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());
            verify(entitlementChangeRecordRepository).saveAllAndFlush(changeRecordCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(2, savedSummaries.size());

            // Verify upcoming summary is activated with carry forward (legacy annual leave has carry forward enabled)
            TimeoffSummaryDBO activatedSummary = savedSummaries.stream()
                    .filter(s -> s.id().equals(2L))
                    .findFirst().orElseThrow();
            assertEquals(TimeOffSummaryStatus.ACTIVE, activatedSummary.status());
            assertTrue(activatedSummary.carryForwardCount() > 0);

            // Verify carry forward record is created for legacy annual leave
            List<EntitlementChangeRecordEntity> carryForwardRecords = changeRecordCaptor.getValue();
            assertEquals(1, carryForwardRecords.size());
        }

        @Test
        void should_handle_legacy_data_with_null_definition_id_for_non_annual_leave() {
            // Given
            val contractId = 100L;
            val typeId = 2L; // Non-annual leave type ID
            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "sick", "Sick Leave");

            TimeoffSummaryDBO activeSummary = createActiveSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31));
            TimeoffSummaryDBO upcomingSummary = createUpcomingSummary(2L, contractId, typeId, timeoffType, LocalDate.of(2025, 1, 1), LocalDate.of(2025, 12, 31));

            // Legacy entitlement with null definition ID for non-annual leave
            TimeoffEntitlementDBO entitlement = createTimeoffEntitlement(contractId, typeId, 20.0, DAYS, timeoffType);
            entitlement.definitionId(null);

            ContractOuterClass.ContractBasicInfo contract = createActiveContract(contractId);

            setupMocksForLegacyData(activeSummary, upcomingSummary, entitlement, contract);

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(2, savedSummaries.size());

            // Verify upcoming summary is activated without carry forward (legacy non-annual leave has no carry forward)
            TimeoffSummaryDBO activatedSummary = savedSummaries.stream()
                    .filter(s -> s.id().equals(2L))
                    .findFirst().orElseThrow();
            assertEquals(TimeOffSummaryStatus.ACTIVE, activatedSummary.status());
            assertEquals(0.0, activatedSummary.carryForwardCount());

            // No carry forward records for non-annual leave legacy data
            verify(entitlementChangeRecordRepository, never()).saveAllAndFlush(anyList());
        }

        @Test
        void should_handle_multiple_summaries_activation() {
            // Given
            val contractId1 = 100L;
            val contractId2 = 200L;
            val typeId1 = 1L;
            val typeId2 = 2L;

            TimeoffTypeDBO timeoffType1 = createTimeoffType(typeId1, "annual", "Annual Leave");
            TimeoffTypeDBO timeoffType2 = createTimeoffType(typeId2, "sick", "Sick Leave");

            List<TimeoffSummaryDBO> activeSummaries = List.of(
                    createActiveSummary(1L, contractId1, typeId1, timeoffType1, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31)),
                    createActiveSummary(3L, contractId2, typeId2, timeoffType2, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31))
            );

            List<TimeoffSummaryDBO> upcomingSummaries = List.of(
                    createUpcomingSummary(2L, contractId1, typeId1, timeoffType1, LocalDate.of(2025, 1, 1), LocalDate.of(2025, 12, 31)),
                    createUpcomingSummary(4L, contractId2, typeId2, timeoffType2, LocalDate.of(2025, 1, 1), LocalDate.of(2025, 12, 31))
            );

            TimeoffEntitlementDBO entitlement1 = createTimeoffEntitlement(contractId1, typeId1, 20.0, DAYS, timeoffType1);
            entitlement1.definitionId(1L); // Set definition ID for first entitlement

            TimeoffEntitlementDBO entitlement2 = createTimeoffEntitlement(contractId2, typeId2, 15.0, DAYS, timeoffType2);
            entitlement2.definitionId(2L); // Set definition ID for second entitlement

            List<TimeoffEntitlementDBO> entitlements = List.of(entitlement1, entitlement2);

            Map<Long, DefinitionEntity> definitionMap = Map.of(
                    1L, createDefinitionWithCarryForward(1L, true),
                    2L, createDefinitionWithCarryForward(2L, false)
            );

            Map<Long, ContractOuterClass.ContractBasicInfo> contractMap = Map.of(
                    contractId1, createActiveContract(contractId1),
                    contractId2, createActiveContract(contractId2)
            );

            setupMocksForMultipleSummaries(activeSummaries, upcomingSummaries, entitlements, definitionMap, contractMap);

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(4, savedSummaries.size());

            // Verify all active summaries are expired
            long expiredCount = savedSummaries.stream()
                    .filter(s -> s.status() == TimeOffSummaryStatus.EXPIRED)
                    .count();
            assertEquals(2, expiredCount);

            // Verify all upcoming summaries are activated
            long activatedCount = savedSummaries.stream()
                    .filter(s -> s.status() == TimeOffSummaryStatus.ACTIVE)
                    .count();
            assertEquals(2, activatedCount);
        }

        @Test
        void should_handle_exception_gracefully() {
            // Given
            when(timeoffSummaryRepository.countActiveSummariesWithPastPeriodEnd(currentDate))
                    .thenThrow(new RuntimeException("Database error"));

            // When & Then - Should not throw exception due to try-catch in method
            assertDoesNotThrow(() -> timeOffSummaryService.activateNextTimeoffSummaries(currentDate));

            verify(timeoffSummaryRepository).countActiveSummariesWithPastPeriodEnd(currentDate);
            verify(timeoffSummaryRepository, never()).saveAllAndFlush(anyList());
        }

        @Test
        void should_handle_no_matching_upcoming_summaries_for_active_pairs() {
            // Given
            val contractId = 100L;
            val typeId = 1L;
            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");

            List<TimeoffSummaryDBO> activeSummaries = List.of(
                    createActiveSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31))
            );

            // Upcoming summary for different contract/type pair
            List<TimeoffSummaryDBO> upcomingSummaries = List.of(
                    createUpcomingSummary(2L, 999L, 999L, timeoffType, LocalDate.of(2025, 1, 1), LocalDate.of(2025, 12, 31))
            );

            when(timeoffSummaryRepository.countActiveSummariesWithPastPeriodEnd(currentDate)).thenReturn(1L);
            when(timeoffSummaryRepository.findBatchOfActiveSummariesWithPastPeriodEnd(currentDate, batchSize)).thenReturn(activeSummaries);
            when(timeoffSummaryRepository.findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(Set.of(contractId), Set.of(typeId), currentDate)).thenReturn(upcomingSummaries);

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).countActiveSummariesWithPastPeriodEnd(currentDate);
            verify(timeoffSummaryRepository).findBatchOfActiveSummariesWithPastPeriodEnd(currentDate, batchSize);
            verify(timeoffSummaryRepository).findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(Set.of(contractId), Set.of(typeId), currentDate);
            verify(timeoffSummaryRepository, never()).saveAllAndFlush(anyList());
        }

        @Test
        void should_handle_missing_entitlement_gracefully() {
            // Given
            val contractId = 100L;
            val typeId = 1L;
            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");

            TimeoffSummaryDBO activeSummary = createActiveSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31));
            TimeoffSummaryDBO upcomingSummary = createUpcomingSummary(2L, contractId, typeId, timeoffType, LocalDate.of(2025, 1, 1), LocalDate.of(2025, 12, 31));

            ContractOuterClass.ContractBasicInfo contract = createActiveContract(contractId);

            when(timeoffSummaryRepository.countActiveSummariesWithPastPeriodEnd(currentDate)).thenReturn(1L);
            when(timeoffSummaryRepository.findBatchOfActiveSummariesWithPastPeriodEnd(currentDate, batchSize)).thenReturn(List.of(activeSummary));
            when(timeoffSummaryRepository.findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(Set.of(contractId), Set.of(typeId), currentDate)).thenReturn(List.of(upcomingSummary));
            when(contractServiceAdapter.getBasicContractsByIds(Set.of(contractId))).thenReturn(Map.of(contractId, contract));
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Set.of(contractId), Set.of(typeId))).thenReturn(List.of()); // No entitlements found

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(2, savedSummaries.size());

            // Should still activate the summary but without carry forward
            TimeoffSummaryDBO activatedSummary = savedSummaries.stream()
                    .filter(s -> s.id().equals(2L))
                    .findFirst().orElseThrow();
            assertEquals(TimeOffSummaryStatus.ACTIVE, activatedSummary.status());
            assertEquals(0.0, activatedSummary.carryForwardCount());
        }

        @Test
        void should_handle_null_contract_gracefully() {
            // Given
            val contractId = 100L;
            val typeId = 1L;
            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");

            TimeoffSummaryDBO activeSummary = createActiveSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31));
            TimeoffSummaryDBO upcomingSummary = createUpcomingSummary(2L, contractId, typeId, timeoffType, LocalDate.of(2025, 1, 1), LocalDate.of(2025, 12, 31));

            TimeoffEntitlementDBO entitlement = createTimeoffEntitlement(contractId, typeId, 20.0, DAYS, timeoffType);

            when(timeoffSummaryRepository.countActiveSummariesWithPastPeriodEnd(currentDate)).thenReturn(1L);
            when(timeoffSummaryRepository.findBatchOfActiveSummariesWithPastPeriodEnd(currentDate, batchSize)).thenReturn(List.of(activeSummary));
            when(timeoffSummaryRepository.findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(Set.of(contractId), Set.of(typeId), currentDate)).thenReturn(List.of(upcomingSummary));
            when(contractServiceAdapter.getBasicContractsByIds(Set.of(contractId))).thenReturn(Map.of()); // No contract found
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Set.of(contractId), Set.of(typeId))).thenReturn(List.of(entitlement));

            // When
            timeOffSummaryService.activateNextTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(2, savedSummaries.size());

            // Both summaries should be marked as EXPIRED when contract is null
            savedSummaries.forEach(summary -> assertEquals(TimeOffSummaryStatus.EXPIRED, summary.status()));
        }

        // Helper methods for creating test objects
        private void setActivationBatchSize() throws Exception {
            Field batchSizeField = TimeoffSummaryService.class.getDeclaredField("timeoffNextSummaryActivationBatchSize");
            batchSizeField.setAccessible(true);
            batchSizeField.set(timeOffSummaryService, batchSize);
        }

        private TimeoffSummaryDBO createActiveSummary(Long id, Long contractId, Long typeId, TimeoffTypeDBO timeoffType, LocalDate periodStart, LocalDate periodEnd) {
            return TimeoffSummaryDBO.builder()
                    .id(id)
                    .contractId(contractId)
                    .typeId(typeId)
                    .timeoffType(timeoffType)
                    .periodStart(periodStart)
                    .periodEnd(periodEnd)
                    .allocatedCount(20.0)
                    .takenCount(0.0)
                    .pendingCount(0.0)
                    .carryForwardCount(0.0)
                    .totalEntitledCount(20.0)
                    .usedFromAllocatedCount(0.0)
                    .usedFromCarryForwardCount(0.0)
                    .usedFromNextCycleCarryForwardCount(0.0)
                    .status(TimeOffSummaryStatus.ACTIVE)
                    .build();
        }

        private TimeoffSummaryDBO createUpcomingSummary(Long id, Long contractId, Long typeId, TimeoffTypeDBO timeoffType, LocalDate periodStart, LocalDate periodEnd) {
            return TimeoffSummaryDBO.builder()
                    .id(id)
                    .contractId(contractId)
                    .typeId(typeId)
                    .timeoffType(timeoffType)
                    .periodStart(periodStart)
                    .periodEnd(periodEnd)
                    .allocatedCount(20.0)
                    .takenCount(0.0)
                    .pendingCount(0.0)
                    .carryForwardCount(0.0)
                    .totalEntitledCount(20.0)
                    .usedFromAllocatedCount(0.0)
                    .usedFromCarryForwardCount(0.0)
                    .usedFromNextCycleCarryForwardCount(0.0)
                    .status(TimeOffSummaryStatus.UPCOMING)
                    .build();
        }

        private ContractOuterClass.ContractBasicInfo createActiveContract(Long contractId) {
            return ContractOuterClass.ContractBasicInfo.newBuilder()
                    .setContractId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .build();
        }

        private ContractOuterClass.ContractBasicInfo createEndedContract(Long contractId) {
            return ContractOuterClass.ContractBasicInfo.newBuilder()
                    .setContractId(contractId)
                    .setStatus(ContractOuterClass.ContractStatus.ENDED)
                    .build();
        }

        private DefinitionEntity createDefinitionWithCarryForward(Long id, boolean enabled) {
            DefinitionEntity definition = mock(DefinitionEntity.class);
            TimeoffDefinitionConfigEntity configurations = mock(TimeoffDefinitionConfigEntity.class);
            CarryForwardConfigEntity carryForwardConfig = mock(CarryForwardConfigEntity.class);

            when(definition.getId()).thenReturn(id);
            when(definition.getConfigurations()).thenReturn(configurations);
            when(configurations.getCarryForwardConfig()).thenReturn(carryForwardConfig);
            when(carryForwardConfig.getEnabled()).thenReturn(enabled);

            if (enabled) {
                // Set up carry forward config for enabled case
                when(carryForwardConfig.getMaxLimit()).thenReturn(null); // No limit
            }

            return definition;
        }

        private DefinitionEntity createDefinitionWithCarryForward(boolean enabled) {
            return createDefinitionWithCarryForward(1L, enabled);
        }

        private void setupMocksForSuccessfulActivation(TimeoffSummaryDBO activeSummary, TimeoffSummaryDBO upcomingSummary,
                                                       TimeoffEntitlementDBO entitlement, DefinitionEntity definition,
                                                       ContractOuterClass.ContractBasicInfo contract) {
            when(timeoffSummaryRepository.countActiveSummariesWithPastPeriodEnd(currentDate)).thenReturn(1L);
            when(timeoffSummaryRepository.findBatchOfActiveSummariesWithPastPeriodEnd(currentDate, batchSize)).thenReturn(List.of(activeSummary));
            when(timeoffSummaryRepository.findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(
                    Set.of(activeSummary.contractId()), Set.of(activeSummary.typeId()), currentDate)).thenReturn(List.of(upcomingSummary));
            when(contractServiceAdapter.getBasicContractsByIds(Set.of(activeSummary.contractId()))).thenReturn(Map.of(activeSummary.contractId(), contract));
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Set.of(activeSummary.contractId()), Set.of(activeSummary.typeId()))).thenReturn(List.of(entitlement));
            when(definitionEntityRepository.findAllById(anySet())).thenReturn(List.of(definition));
        }

        private void setupMocksForLegacyData(TimeoffSummaryDBO activeSummary, TimeoffSummaryDBO upcomingSummary,
                                             TimeoffEntitlementDBO entitlement, ContractOuterClass.ContractBasicInfo contract) {
            when(timeoffSummaryRepository.countActiveSummariesWithPastPeriodEnd(currentDate)).thenReturn(1L);
            when(timeoffSummaryRepository.findBatchOfActiveSummariesWithPastPeriodEnd(currentDate, batchSize)).thenReturn(List.of(activeSummary));
            when(timeoffSummaryRepository.findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(
                    Set.of(activeSummary.contractId()), Set.of(activeSummary.typeId()), currentDate)).thenReturn(List.of(upcomingSummary));
            when(contractServiceAdapter.getBasicContractsByIds(Set.of(activeSummary.contractId()))).thenReturn(Map.of(activeSummary.contractId(), contract));
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Set.of(activeSummary.contractId()), Set.of(activeSummary.typeId()))).thenReturn(List.of(entitlement));
            // For legacy data, definitionEntityRepository.findAllById() is not called because definition ID is null
        }

        private void setupMocksForMultipleSummaries(List<TimeoffSummaryDBO> activeSummaries, List<TimeoffSummaryDBO> upcomingSummaries,
                                                    List<TimeoffEntitlementDBO> entitlements, Map<Long, DefinitionEntity> definitionMap,
                                                    Map<Long, ContractOuterClass.ContractBasicInfo> contractMap) {
            Set<Long> contractIds = activeSummaries.stream().map(TimeoffSummaryDBO::contractId).collect(Collectors.toSet());
            Set<Long> typeIds = activeSummaries.stream().map(TimeoffSummaryDBO::typeId).collect(Collectors.toSet());

            when(timeoffSummaryRepository.countActiveSummariesWithPastPeriodEnd(currentDate)).thenReturn((long) activeSummaries.size());
            when(timeoffSummaryRepository.findBatchOfActiveSummariesWithPastPeriodEnd(currentDate, batchSize)).thenReturn(activeSummaries);
            when(timeoffSummaryRepository.findUpcomingSummariesByContractIdsAndTypeIdsWhereDateIsWithinPeriod(contractIds, typeIds, currentDate)).thenReturn(upcomingSummaries);
            when(contractServiceAdapter.getBasicContractsByIds(contractIds)).thenReturn(contractMap);
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(contractIds, typeIds)).thenReturn(entitlements);
            when(definitionEntityRepository.findAllById(anySet())).thenReturn(new ArrayList<>(definitionMap.values()));
        }

        private TimeoffTypeDBO createTimeoffType(Long id, String key, String label) {
            return TimeoffTypeDBO.builder()
                    .id(id)
                    .key(key)
                    .label(label)
                    .build();
        }

        private TimeoffEntitlementDBO createTimeoffEntitlement(Long contractId, Long typeId, Double value, TimeOffUnit unit, TimeoffTypeDBO timeoffType) {
            return TimeoffEntitlementDBO.builder()
                    .id(1L)
                    .contractId(contractId)
                    .typeId(typeId)
                    .value(value)
                    .unit(unit)
                    .type(timeoffType)
                    .build();
        }
    }

    @Nested
    class GenerateFutureTimeoffSummariesTests {

        private final LocalDate currentDate = LocalDate.of(2025, 5, 20);
        private final int batchSize = 100;

        @BeforeEach
        void setUp() throws Exception {
            setBatchSize();
        }

        @Test
        void should_skip_when_no_summaries_need_future_entries() {
            // Given
            when(timeoffSummaryRepository.countSummariesNeedingFutureEntries(currentDate)).thenReturn(0L);

            // When
            timeOffSummaryService.generateFutureTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).countSummariesNeedingFutureEntries(currentDate);
            verify(timeoffSummaryRepository, never()).findBatchOfSummariesNeedingFutureEntries(any(LocalDate.class), anyInt());
            verify(timeoffSummaryRepository, never()).saveAllAndFlush(anyList());
            verify(entitlementChangeRecordRepository, never()).saveAllAndFlush(anyList());
        }

        @Test
        void should_process_summaries_and_create_future_entries_successfully() {
            // Given
            val contractId1 = 100L;
            val contractId2 = 200L;
            val typeId1 = 1L;
            val typeId2 = 2L;

            TimeoffTypeDBO timeoffType1 = createTimeoffType(typeId1, "annual", "Annual Leave");
            TimeoffTypeDBO timeoffType2 = createTimeoffType(typeId2, "sick", "Sick Leave");

            List<TimeoffSummaryDBO> summariesToProcess = List.of(
                    createTimeoffSummary(1L, contractId1, typeId1, timeoffType1, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31)),
                    createTimeoffSummary(2L, contractId2, typeId2, timeoffType2, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31))
            );

            List<TimeoffEntitlementDBO> entitlements = List.of(
                    createTimeoffEntitlement(contractId1, typeId1, 24.0, DAYS, timeoffType1),
                    createTimeoffEntitlement(contractId2, typeId2, 10.0, DAYS, timeoffType2)
            );

            when(timeoffSummaryRepository.countSummariesNeedingFutureEntries(currentDate)).thenReturn(2L);
            when(timeoffSummaryRepository.findBatchOfSummariesNeedingFutureEntries(currentDate, batchSize))
                    .thenReturn(summariesToProcess);
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(
                    Set.of(contractId1, contractId2), Set.of(typeId1, typeId2)))
                    .thenReturn(entitlements);

            // When
            timeOffSummaryService.generateFutureTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).countSummariesNeedingFutureEntries(currentDate);
            verify(timeoffSummaryRepository).findBatchOfSummariesNeedingFutureEntries(currentDate, batchSize);
            verify(entitlementDBORepository).findSimplifiedEntitlementsByContractIdInAndTypeIdIn(
                    Set.of(contractId1, contractId2), Set.of(typeId1, typeId2));

            summaryCaptor = ArgumentCaptor.forClass(List.class);
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(2, savedSummaries.size());

            // Verify first summary
            TimeoffSummaryDBO savedSummary1 = savedSummaries.stream()
                    .filter(s -> s.contractId() == contractId1)
                    .findFirst().orElseThrow();
            assertEquals(contractId1, savedSummary1.contractId());
            assertEquals(typeId1, savedSummary1.typeId());
            assertEquals(24.0, savedSummary1.allocatedCount());
            assertEquals(24.0, savedSummary1.totalEntitledCount());
            assertEquals(0.0, savedSummary1.takenCount());
            assertEquals(0.0, savedSummary1.pendingCount());
            assertEquals(0.0, savedSummary1.carriedCount());
            assertEquals(LocalDate.of(2025, 1, 1), savedSummary1.periodStart());
            assertEquals(LocalDate.of(2025, 12, 31), savedSummary1.periodEnd());
            assertEquals(UPCOMING, savedSummary1.status());

            // Verify allocation records were saved
            ArgumentCaptor<List<EntitlementChangeRecordEntity>> recordCaptor = ArgumentCaptor.forClass(List.class);
            verify(entitlementChangeRecordRepository).saveAllAndFlush(recordCaptor.capture());

            List<EntitlementChangeRecordEntity> savedRecords = recordCaptor.getValue();
            assertEquals(2, savedRecords.size());
        }

        @Test
        void should_skip_summaries_without_entitlements() {
            // Given
            val contractId = 100L;
            val typeId = 1L;

            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");
            List<TimeoffSummaryDBO> summariesToProcess = List.of(
                    createTimeoffSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31))
            );

            when(timeoffSummaryRepository.countSummariesNeedingFutureEntries(currentDate)).thenReturn(1L);
            when(timeoffSummaryRepository.findBatchOfSummariesNeedingFutureEntries(currentDate, batchSize)).thenReturn(summariesToProcess);
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Set.of(contractId), Set.of(typeId))).thenReturn(List.of()); // No entitlements found

            // When
            timeOffSummaryService.generateFutureTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).countSummariesNeedingFutureEntries(currentDate);
            verify(timeoffSummaryRepository).findBatchOfSummariesNeedingFutureEntries(currentDate, batchSize);
            verify(entitlementDBORepository).findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Set.of(contractId), Set.of(typeId));

            // Should not save any summaries since no entitlements found
            verify(timeoffSummaryRepository, never()).saveAllAndFlush(anyList());
            verify(entitlementChangeRecordRepository, never()).saveAllAndFlush(anyList());
        }

        @Test
        void should_handle_different_time_off_units_correctly() {
            // Given
            val contractId1 = 100L;
            val contractId2 = 200L;
            val typeId1 = 1L;
            val typeId2 = 2L;

            TimeoffTypeDBO timeoffType1 = createTimeoffType(typeId1, "annual", "Annual Leave");
            TimeoffTypeDBO timeoffType2 = createTimeoffType(typeId2, "sick", "Sick Leave");

            List<TimeoffSummaryDBO> summariesToProcess = List.of(
                    createTimeoffSummary(1L, contractId1, typeId1, timeoffType1, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31)),
                    createTimeoffSummary(2L, contractId2, typeId2, timeoffType2, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31))
            );

            List<TimeoffEntitlementDBO> entitlements = List.of(
                    createTimeoffEntitlement(contractId1, typeId1, 2.0, TimeOffUnit.WEEKS, timeoffType1), // 14 days
                    createTimeoffEntitlement(contractId2, typeId2, 1.0, TimeOffUnit.MONTHS, timeoffType2) // 31 days
            );

            when(timeoffSummaryRepository.countSummariesNeedingFutureEntries(currentDate)).thenReturn(2L);
            when(timeoffSummaryRepository.findBatchOfSummariesNeedingFutureEntries(currentDate, batchSize)).thenReturn(summariesToProcess);
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Set.of(contractId1, contractId2), Set.of(typeId1, typeId2))).thenReturn(entitlements);

            // When
            timeOffSummaryService.generateFutureTimeoffSummaries(currentDate);

            // Then
            summaryCaptor = ArgumentCaptor.forClass(List.class);
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(2, savedSummaries.size());

            // Verify weeks conversion (2 weeks = 14 days)
            TimeoffSummaryDBO weeksSummary = savedSummaries.stream()
                    .filter(s -> s.contractId() == contractId1)
                    .findFirst().orElseThrow();
            assertEquals(14.0, weeksSummary.allocatedCount());
            assertEquals(14.0, weeksSummary.totalEntitledCount());

            // Verify months conversion (1 month = 31 days)
            TimeoffSummaryDBO monthsSummary = savedSummaries.stream()
                    .filter(s -> s.contractId() == contractId2)
                    .findFirst().orElseThrow();
            assertEquals(31.0, monthsSummary.allocatedCount());
            assertEquals(31.0, monthsSummary.totalEntitledCount());
        }

        @Test
        void should_handle_exception_gracefully() {
            // Given
            when(timeoffSummaryRepository.countSummariesNeedingFutureEntries(currentDate)).thenThrow(new RuntimeException("Database error"));

            // When & Then - Should not throw exception due to try-catch in method
            assertDoesNotThrow(() -> timeOffSummaryService.generateFutureTimeoffSummaries(currentDate));

            verify(timeoffSummaryRepository).countSummariesNeedingFutureEntries(currentDate);
            verify(timeoffSummaryRepository, never()).saveAllAndFlush(anyList());
        }

        @Test
        void should_process_batch_size_correctly() {
            // Given
            val contractIds = List.of(100L, 200L, 300L, 400L, 500L);
            val typeId = 1L;

            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");

            List<TimeoffSummaryDBO> summariesToProcess = contractIds.stream()
                    .map(contractId -> createTimeoffSummary(contractId, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31)))
                    .toList();

            List<TimeoffEntitlementDBO> entitlements = contractIds.stream()
                    .map(contractId -> createTimeoffEntitlement(contractId, typeId, 24.0, DAYS, timeoffType))
                    .toList();

            when(timeoffSummaryRepository.countSummariesNeedingFutureEntries(currentDate)).thenReturn(5L);
            when(timeoffSummaryRepository.findBatchOfSummariesNeedingFutureEntries(currentDate, batchSize)).thenReturn(summariesToProcess);
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Set.copyOf(contractIds), Set.of(typeId))).thenReturn(entitlements);

            // When
            timeOffSummaryService.generateFutureTimeoffSummaries(currentDate);

            // Then
            summaryCaptor = ArgumentCaptor.forClass(List.class);
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(5, savedSummaries.size()); // All summaries should be processed
        }

        @Test
        void should_verify_future_summary_period_dates_calculation() {
            // Given
            val contractId = 100L;
            val typeId = 1L;

            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");
            List<TimeoffSummaryDBO> summariesToProcess = List.of(
                    createTimeoffSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 3, 1), LocalDate.of(2025, 2, 28)) // Custom period
            );

            List<TimeoffEntitlementDBO> entitlements = List.of(
                    createTimeoffEntitlement(contractId, typeId, 20.0, DAYS, timeoffType)
            );

            when(timeoffSummaryRepository.countSummariesNeedingFutureEntries(currentDate)).thenReturn(1L);
            when(timeoffSummaryRepository.findBatchOfSummariesNeedingFutureEntries(currentDate, batchSize)).thenReturn(summariesToProcess);
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Set.of(contractId), Set.of(typeId))).thenReturn(entitlements);

            // When
            timeOffSummaryService.generateFutureTimeoffSummaries(currentDate);

            // Then
            summaryCaptor = ArgumentCaptor.forClass(List.class);
            verify(timeoffSummaryRepository).saveAllAndFlush(summaryCaptor.capture());

            List<TimeoffSummaryDBO> savedSummaries = summaryCaptor.getValue();
            assertEquals(1, savedSummaries.size());

            TimeoffSummaryDBO futureSummary = savedSummaries.get(0);

            // Next period should start the day after current period ends.
            assertEquals(LocalDate.of(2025, 3, 1), futureSummary.periodStart());

            // Next period should end one year later minus one day.
            assertEquals(LocalDate.of(2026, 2, 28), futureSummary.periodEnd());
            assertEquals(UPCOMING, futureSummary.status());
        }

        @Test
        void should_verify_allocation_records_creation() {
            // Given
            val contractId = 100L;
            val typeId = 1L;

            TimeoffTypeDBO timeoffType = createTimeoffType(typeId, "annual", "Annual Leave");
            List<TimeoffSummaryDBO> summariesToProcess = List.of(
                    createTimeoffSummary(1L, contractId, typeId, timeoffType, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31))
            );

            List<TimeoffEntitlementDBO> entitlements = List.of(
                    createTimeoffEntitlement(contractId, typeId, 25.0, DAYS, timeoffType)
            );

            when(timeoffSummaryRepository.countSummariesNeedingFutureEntries(currentDate)).thenReturn(1L);
            when(timeoffSummaryRepository.findBatchOfSummariesNeedingFutureEntries(currentDate, batchSize)).thenReturn(summariesToProcess);
            when(entitlementDBORepository.findSimplifiedEntitlementsByContractIdInAndTypeIdIn(Set.of(contractId), Set.of(typeId))).thenReturn(entitlements);

            // When
            timeOffSummaryService.generateFutureTimeoffSummaries(currentDate);

            // Then
            ArgumentCaptor<List<EntitlementChangeRecordEntity>> recordCaptor = ArgumentCaptor.forClass(List.class);
            verify(entitlementChangeRecordRepository).saveAllAndFlush(recordCaptor.capture());

            List<EntitlementChangeRecordEntity> savedRecords = recordCaptor.getValue();
            assertEquals(1, savedRecords.size());

            EntitlementChangeRecordEntity entitlementChangeRecordEntity = savedRecords.get(0);
            assertEquals(typeId, entitlementChangeRecordEntity.getTypeId());
            assertEquals(contractId, entitlementChangeRecordEntity.getContractId());
            assertEquals(EntitlementChangeCategory.ALLOCATION, entitlementChangeRecordEntity.getCategory());
            assertEquals(25.0, entitlementChangeRecordEntity.getCount());
            assertEquals(LocalDate.of(2025, 1, 1), entitlementChangeRecordEntity.getValidFrom());
            assertEquals(LocalDate.of(2025, 12, 31), entitlementChangeRecordEntity.getValidToInclusive());
        }

        @Test
        void should_handle_empty_summaries_list_in_group() {
            // Given

            // Create a summary but mock the grouping to return empty list
            List<TimeoffSummaryDBO> summariesToProcess = List.of();

            when(timeoffSummaryRepository.countSummariesNeedingFutureEntries(currentDate)).thenReturn(0L);

            // When
            timeOffSummaryService.generateFutureTimeoffSummaries(currentDate);

            // Then
            verify(timeoffSummaryRepository).countSummariesNeedingFutureEntries(currentDate);
            verify(entitlementDBORepository, never()).findSimplifiedEntitlementsByContractIdInAndTypeIdIn(anySet(), anySet());
            verify(timeoffSummaryRepository, never()).saveAllAndFlush(anyList());
            verify(entitlementChangeRecordRepository, never()).saveAllAndFlush(anyList());
        }

        // Helper methods for creating test objects
        private void setBatchSize() throws Exception {
            // Set the batch size using reflection since it's a private field with @Value annotation
            Field batchSizeField = TimeoffSummaryService.class.getDeclaredField("timeoffFutureSummaryGenerationBatchSize");
            batchSizeField.setAccessible(true);
            batchSizeField.set(timeOffSummaryService, batchSize);
        }

        private TimeoffSummaryDBO createTimeoffSummary(Long id, Long contractId, Long typeId, TimeoffTypeDBO timeoffType, LocalDate periodStart, LocalDate periodEnd) {
            return TimeoffSummaryDBO.builder()
                    .id(id)
                    .contractId(contractId)
                    .typeId(typeId)
                    .timeoffType(timeoffType)
                    .periodStart(periodStart)
                    .periodEnd(periodEnd)
                    .allocatedCount(20.0)
                    .takenCount(5.0)
                    .pendingCount(2.0)
                    .carriedCount(3.0)
                    .totalEntitledCount(23.0)
                    .status(ACTIVE)
                    .build();
        }

        private TimeoffTypeDBO createTimeoffType(Long id, String key, String label) {
            return TimeoffTypeDBO.builder()
                    .id(id)
                    .key(key)
                    .label(label)
                    .build();
        }

        private TimeoffEntitlementDBO createTimeoffEntitlement(Long contractId, Long typeId, Double value, TimeOffUnit unit, TimeoffTypeDBO type) {
            return TimeoffEntitlementDBO.builder()
                    .contractId(contractId)
                    .typeId(typeId)
                    .value(value)
                    .unit(unit)
                    .type(type)
                    .build();
        }
    }

    private boolean isSummaryMatched(List<TimeoffSummaryDBO> summaries, Long contractId, Long typeId, LocalDate periodStart, LocalDate periodEnd, TimeOffSummaryStatus status, Double allocatedCount) {
        return summaries.stream().anyMatch(summary ->
                summary.periodStart().equals(periodStart)
                        && summary.periodEnd().equals(periodEnd)
                        && summary.contractId() == contractId
                        && summary.timeoffType().id().equals(typeId)
                        && Objects.equals(summary.allocatedCount(), allocatedCount)
                        && summary.status() == status);
    }

    private boolean isECRMatched(List<EntitlementChangeRecordEntity> changeRecords, Long contractId, Long typeId, LocalDate validFrom, LocalDate validToInclusive, EntitlementChangeCategory category, Double count) {
        return changeRecords.stream().anyMatch(c ->
                c.getContractId() == contractId
                        && c.getTypeId() == typeId
                        && c.getValidFrom().equals(validFrom)
                        && c.getValidToInclusive().equals(validToInclusive)
                        && c.getCategory() == category
                        && c.getCount() == count);
    }


    private void futureLeaveFlagOn() {
        when(featureFlagService.isOn(eq(FeatureFlags.FUTURE_LEAVES), Mockito.anyMap())).thenReturn(true);
    }

    private void futureLeaveFlagOff() {
        when(featureFlagService.isOn(eq(FeatureFlags.FUTURE_LEAVES), Mockito.anyMap())).thenReturn(false);
    }

    private static DefinitionEntity getDefinitionEntity(Long id,
                                                        boolean isProrated,
                                                        boolean isFutureLeavesEnabled) {
        val definitionEntity = new DefinitionEntity();
        definitionEntity.setId(id);
        val allocationConfig = new AllocationConfigEntity("ANNUALLY", isProrated);

        val carryForwardConfig = new CarryForwardConfigEntity(false, null, null, null);

        val futureLeaveConfig = new FutureLeaveConfigEntity(isFutureLeavesEnabled, 1, new LapsableLeaveConfigEntity());
        definitionEntity.setConfigurations(new TimeoffDefinitionConfigEntity(allocationConfig, carryForwardConfig, futureLeaveConfig));
        return definitionEntity;
    }


}