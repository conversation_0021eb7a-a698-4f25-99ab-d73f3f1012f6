package com.multiplier.timeoff.service;
import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.common.transport.user.UserScopes;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.core.schema.platform.Approval;
import com.multiplier.timeoff.adapters.ApprovalServiceAdapter;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.CompanyUserFilters;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.core.common.db.AuditUser;
import com.multiplier.timeoff.core.common.db.BaseEntity;
import com.multiplier.timeoff.core.common.util.PageRequestHelper;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.repository.TimeoffRepository;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.service.builder.TimeoffFilters;
import com.multiplier.timeoff.service.builder.TimoffSpecificationBuilder;
import com.multiplier.timeoff.service.mapper.TimeoffMapper;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import java.util.*;
import java.util.stream.Collectors;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@Slf4j
@ExtendWith(MockitoExtension.class)
class TimeoffQueryTest {
    @Mock
    PageRequestHelper pageRequestHelper;

    @Mock
    TimeoffRepository timeoffRepo;

    @Mock
    ContractServiceAdapter contractServiceAdapter;

    @Mock
    TimoffSpecificationBuilder timeoffSpecBuilder;

    @Mock
    TimeoffMapper mapper;

    @Mock
    CurrentUser currentUser;

    @Mock
    CompanyServiceAdapter companyServiceAdapter;

    @Mock
    ApprovalServiceAdapter approvalServiceAdapter;

    @InjectMocks
    TimeoffQuery timeoffQuery;

    @Mock
    DgsDataFetchingEnvironment dfe;

    @Mock
    private FeatureFlagService featureFlagService;

    @Nested
    class GetAllForCompanyTests{

        @Test
        void shouldReturnMappedTimeOffsWhenExperienceIsCompany() {
            TimeoffFilters filters = new TimeoffFilters();
            List<TimeoffDBO> allTimeOffsForCompany = List.of(
                    TimeoffDBO.builder().id(1L).status(TimeOffStatus.SUBMITTED).build(),
                    TimeoffDBO.builder().id(2L).status(TimeOffStatus.APPROVED).build()
            );

            // Mocking repository
            when(timeoffRepo.findAll(isNull(Specification.class))).thenReturn(allTimeOffsForCompany);

            // Mocking company user
            val cuId=2L;
            val companyUser  = CompanyOuterClass.CompanyUser.newBuilder().setId(cuId).build();
            when(companyServiceAdapter.getCompanyUser(any(CompanyUserFilters.class))).thenReturn(companyUser);

            //Mocking approvalServiceAdapter
            val assignedItemIds = Approval.ListOfIdsResponse.newBuilder().addAllIds(List.of(1L, 2L)).build();
            when(approvalServiceAdapter.getAssignedItems(cuId))
                    .thenReturn(assignedItemIds);

            // Mocking mapper response
            when(mapper.map(anyList())).thenAnswer(invocation -> {
                List<TimeoffDBO> timeOffs = invocation.getArgument(0);
                return timeOffs.stream()
                        .map(dbo -> {
                            TimeOff timeOff = new TimeOff();
                            timeOff.setId(dbo.id());
                            timeOff.setStatus(dbo.status());
                            return timeOff;
                        })
                        .toList();
            });

            // Mocking current user and user context

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(null, 5L));
            List<TimeOff> results = timeoffQuery.getAllForCompany(dfe, filters);

            // Assert
            assertThat(results).hasSize(2);
            assertTrue(results.stream().anyMatch( r -> r.getId() == 1L && r.getStatus() == TimeOffStatus .SUBMITTED));
            assertTrue(results.stream().anyMatch( r -> r.getId() == 2L && r.getStatus() == TimeOffStatus .APPROVED));
            assertThat(results).extracting("status").containsExactly(TimeOffStatus.SUBMITTED, TimeOffStatus.APPROVED);
        }

        @Test
        void shouldReturnMappedTimeOffsWhenExperienceIsNotCompanyAndUserIsAdmin() throws Exception {
            TimeoffFilters filters = new TimeoffFilters();
            List<TimeoffDBO> allTimeOffsForCompany = List.of(
                    TimeoffDBO.builder().id(1L).status(TimeOffStatus.SUBMITTED).build(),
                    TimeoffDBO.builder().id(2L).status(TimeOffStatus.APPROVED).build()
            );

            // Mocking repository
            when(timeoffRepo.findAll(isNull(Specification.class))).thenReturn(allTimeOffsForCompany);

            // Mocking company user (with admin privileges)
            val cuId = 2L;
            val companyUser = CompanyOuterClass.CompanyUser.newBuilder()
                    .setId(cuId)
                    .setIsAdmin(true)
                    .build();
            when(companyServiceAdapter.getCompanyUser(any(CompanyUserFilters.class))).thenReturn(companyUser);

            // Mocking assigned items
            val assignedItemIds = Approval.ListOfIdsResponse.newBuilder()
                    .addAllIds(List.of(1L, 2L))
                    .build();
            when(approvalServiceAdapter.getAssignedItems(cuId)).thenReturn(assignedItemIds);

            // Mocking mapper response
            when(mapper.map(anyList())).thenAnswer(invocation -> {
                List<TimeoffDBO> timeOffs = invocation.getArgument(0);
                return timeOffs.stream()
                        .map(dbo -> {
                            TimeOff timeOff = new TimeOff();
                            timeOff.setId(dbo.id());
                            timeOff.setStatus(dbo.status());
                            return timeOff;
                        })
                        .toList();
            });

            // Mocking current user and user context (Experience is not 'company' here)
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(null, 5L));

            val results = timeoffQuery.getAllForCompany(dfe, filters);

            // Assert
            assertThat(results).hasSize(2);
            assertTrue(results.stream().anyMatch(r -> r.getId() == 1L && r.getStatus() == TimeOffStatus.SUBMITTED));
            assertTrue(results.stream().anyMatch(r -> r.getId() == 2L && r.getStatus() == TimeOffStatus.APPROVED));
            assertThat(results).extracting("status").containsExactly(TimeOffStatus.SUBMITTED, TimeOffStatus.APPROVED);
        }

        @Test
        void shouldThrowRuntimeExceptionWhenCompanyIdIsNotFoundInUserContext() {
            TimeoffFilters filters = new TimeoffFilters();

            // Mocking current user and user context
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(null, null));
            val ex = assertThrows(RuntimeException.class, () -> timeoffQuery.getAllForCompany(dfe, filters));
            assertEquals("Cannot find the company ID from the current user", ex.getMessage());
        }
    }

    @Nested
    class GetAllTests {

        @Test
        void shouldReturnEmptyListWhenContractIdsEmpty() {
            // Initialized with no contractIds
            TimeoffFilters filters = new TimeoffFilters();
            List<TimeOff> result = timeoffQuery.getAll(filters);
            // This will expect an empty list
            assertThat(result).isEmpty();
        }

        @Test
        void shouldReturnMappedTimeOffsWhenValidFiltersAreProvided() {
            // Setup filters with valid contractIds
            TimeoffFilters filters = new TimeoffFilters();
            filters.contractIds(Set.of(1L, 2L));

            // Create sample TimeoffDBO objects
            TimeoffDBO timeoffDBO1 = TimeoffDBO.builder()
                    .status(TimeOffStatus.SUBMITTED)
                    .contractId(1L)
                    .build();
            TimeoffDBO timeoffDBO2 = TimeoffDBO.builder()
                    .status(TimeOffStatus.APPROVED)
                    .contractId(2L)
                    .build();
            List<TimeoffDBO> timeoffDBOs = List.of(timeoffDBO1, timeoffDBO2);

            // Mock the behavior of the repository method
            Specification<TimeoffDBO> spec = Specification.where(null);
            when(timeoffSpecBuilder.build(filters)).thenReturn(spec);
            when(timeoffRepo.findAll(Mockito.eq(spec), any(Sort.class))).thenReturn(timeoffDBOs);

            // Creating TimeOff objects
            TimeOff timeOff1 = TimeOff.newBuilder()
                    .id(1L)
                    .status(TimeOffStatus.SUBMITTED)
                    .build();
            TimeOff timeOff2 = TimeOff.newBuilder()
                    .id(2L)
                    .status(TimeOffStatus.APPROVED)
                    .build();
            when(mapper.map(timeoffDBOs)).thenReturn(List.of(timeOff1, timeOff2));

            // Call the method to test
            List<TimeOff> result = timeoffQuery.getAll(filters);

            // Assertions
            assertTrue(result.stream().anyMatch(r -> r.getId() == 1L && r.getStatus() == TimeOffStatus.SUBMITTED));
            assertTrue(result.stream().anyMatch(r -> r.getId() == 2L && r.getStatus() == TimeOffStatus.APPROVED));
        }

        @Test
        void shouldReturnEmptyListWhenNoTimeOffsMatchFilters() {
            // Setup filters with contractIds that don't match any records
            TimeoffFilters filters = new TimeoffFilters();
            filters.contractIds(Set.of(99L));

            List<TimeoffDBO> timeoffDBOs = Collections.emptyList();

            // Mock the repository method
            Specification<TimeoffDBO> spec = Specification.where(null);
            when(timeoffSpecBuilder.build(filters)).thenReturn(spec);
            when(timeoffRepo.findAll(Mockito.eq(spec), any(Sort.class))).thenReturn(timeoffDBOs);

            // Call the method to test
            List<TimeOff> result = timeoffQuery.getAll(filters);

            // Assertions: should return an empty list as no records match the filter
            assertThat(result).isEmpty();
        }

        @Test
        void shouldReturnMappedTimeOffsExcludingDraftsNotCreatedByUser() {
            // Setup filters with valid contractIds
            TimeoffFilters filters = new TimeoffFilters();
            filters.contractIds(Set.of(1L, 2L));

            // Create sample DRAFT TimeoffDBO objects with different createdByUserId
            TimeoffDBO timeoffDBO1 = TimeoffDBO.builder()
                    .status(TimeOffStatus.DRAFT)
                    .contractId(1L)
                    .createdByInfo(new AuditUser(100L, "Experience")) // Not current user
                    .build();
            TimeoffDBO timeoffDBO2 = TimeoffDBO.builder()
                    .status(TimeOffStatus.DRAFT)
                    .contractId(2L)
                    .createdByInfo(new AuditUser(10L, "Experience")) // Created by current user
                    .build();

            List<TimeoffDBO> timeoffDBOs = List.of(timeoffDBO1, timeoffDBO2);

            // Mock repository and mapper
            Specification<TimeoffDBO> spec = Specification.where(null);
            when(timeoffSpecBuilder.build(filters)).thenReturn(spec);
            when(timeoffRepo.findAll(Mockito.eq(spec), any(Sort.class))).thenReturn(timeoffDBOs);

            // Stub for mapper.map() with filtered list (only timeoffDBO2 should be returned)
            TimeOff timeOff2 = TimeOff.newBuilder()
                    .id(2L)
                    .status(TimeOffStatus.DRAFT)
                    .build();
            when(mapper.map(anyList())).thenReturn(List.of(timeOff2));

            // Call the method to test
            List<TimeOff> result = timeoffQuery.getAll(filters);

            // Assertions: should return only the time-off created by the current user
            assertTrue(result.stream().anyMatch(r -> r.getId() == 2L && r.getStatus() == TimeOffStatus.DRAFT));
        }
    }

    @Nested
    class GetTimeOffsByContractIdsTests {

        @Test
        void testGetTimeOffsByContractIds_withValidContractIds_returnsMappedTimeOffs() {
            // Setup contractIds for the test
            List<Long> contractIds = List.of(1L, 2L, 3L);

            // Create mock TimeoffDBO objects
            TimeoffDBO timeoffDBO1 = TimeoffDBO.builder().contractId(1L).status(TimeOffStatus.SUBMITTED).build();
            TimeoffDBO timeoffDBO2 = TimeoffDBO.builder().contractId(2L).status(TimeOffStatus.APPROVED).build();
            TimeoffDBO timeoffDBO3 = TimeoffDBO.builder().contractId(3L).status(TimeOffStatus.REJECTED).build();

            List<TimeoffDBO> timeoffDBOs = List.of(timeoffDBO1, timeoffDBO2, timeoffDBO3);

            // Create expected TimeOff objects
            TimeOff timeOff1 = TimeOff.newBuilder().id(1L).status(TimeOffStatus.SUBMITTED).build();
            TimeOff timeOff2 = TimeOff.newBuilder().id(2L).status(TimeOffStatus.APPROVED).build();
            TimeOff timeOff3 = TimeOff.newBuilder().id(3L).status(TimeOffStatus.REJECTED).build();
            List<TimeOff> timeOffs = List.of(timeOff1, timeOff2, timeOff3);

            // Mock repository and mapper behavior
            when(timeoffRepo.findAllByContractIdIn(contractIds)).thenReturn(timeoffDBOs);
            when(mapper.map(timeoffDBOs)).thenReturn(timeOffs);

            List<TimeOff> result = timeoffQuery.getTimeOffsByContractIds(contractIds);

            // Assert
            assertTrue(result.stream().anyMatch(r -> r.getId() == 1L && r.getStatus() == TimeOffStatus.SUBMITTED));
            assertTrue(result.stream().anyMatch(r -> r.getId() == 2L && r.getStatus() == TimeOffStatus.APPROVED));
            assertTrue(result.stream().anyMatch(r -> r.getId() == 3L && r.getStatus() == TimeOffStatus.REJECTED));

            // Verify interactions with mocks
            verify(timeoffRepo, times(1)).findAllByContractIdIn(contractIds);
            verify(mapper, times(1)).map(timeoffDBOs);
        }
    }

    @Nested
    class GetTimeoffByIdTests {
        @Test
        void testGetTimeoffById_withValidId_returnsMappedTimeOff() {
            Long id = 1L;

            // Mocking the TimeoffDBO and TimeOff objects
            TimeoffDBO timeoffDBO = TimeoffDBO.builder().id(id).status(TimeOffStatus.SUBMITTED).build();
            TimeOff timeOff = TimeOff.newBuilder().id(id).status(TimeOffStatus.SUBMITTED).build();

            // Mock the repository and mapper behavior
            when(timeoffRepo.findById(id)).thenReturn(Optional.of(timeoffDBO));
            when(mapper.map(timeoffDBO)).thenReturn(timeOff);

            TimeOff result = timeoffQuery.getTimeoffById(id);

            // Assert
            assertEquals(timeOff, result); // Ensure the result matches the expected TimeOff object
            verify(timeoffRepo, times(1)).findById(id); // Verify that findById was called once
            verify(mapper, times(1)).map(timeoffDBO); // Verify that the mapper was called once
        }
    }

    @Nested
    class GetApproveByMeTimeoffsTests {

        @Test
        void getApproveByMeTimeoffs_whenUserNotPayrollAdmin_returnsEmptyList() {
            // Mock a regular user with HR_ADMIN role (not PAYROLL_ADMIN)
            CompanyOuterClass.CompanyUser regularUser = CompanyOuterClass.CompanyUser.newBuilder()
                    .addRoles(CompanyOuterClass.CompanyUserRole.HR_ADMIN) // HR_ADMIN instead of PAYROLL_ADMIN
                    .build();

            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(regularUser); // Mock company user service

            List<TimeOff> timeOffRequests = List.of(
                    new TimeOff.Builder().id(1L).build(),
                    new TimeOff.Builder().id(2L).build()
            );

            List<TimeOff> result = timeoffQuery.getApproveByMeTimeoffs(timeOffRequests);

            // Assert
            assertTrue(result.isEmpty()); // The result should be empty for a non-PAYROLL_ADMIN user
            verify(approvalServiceAdapter, never()).getAssignedItems(anyLong()); // Ensure getAssignedItems is not called
        }

        @Test
        void getApproveByMeTimeoffs_whenUserIsPayrollAdmin_returnsAssignedTimeoffs() {
            // Mock a payroll admin user
            CompanyOuterClass.CompanyUser payrollAdminUser = CompanyOuterClass.CompanyUser.newBuilder()
                    .addRoles(CompanyOuterClass.CompanyUserRole.PAYROLL_ADMIN)
                    .build();

            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(payrollAdminUser); // Mock payroll admin user service

            // Mock the assigned items for the payroll admin
            Approval.ListOfIdsResponse listOfIdsResponse = Approval.ListOfIdsResponse.newBuilder()
                    .addIds(1L)
                    .addIds(2L)
                    .build(); // Mocking the response with assigned IDs

            when(approvalServiceAdapter.getAssignedItems(anyLong())).thenReturn(listOfIdsResponse);

            List<TimeOff> timeOffRequests = List.of(
                    new TimeOff.Builder().id(1L).build(),
                    new TimeOff.Builder().id(2L).build(),
                    new TimeOff.Builder().id(3L).build()
            );

            List<TimeOff> result = timeoffQuery.getApproveByMeTimeoffs(timeOffRequests);


            // Assert
            assertEquals(2, result.size()); // Ensure two timeoffs are returned, those with IDs 1L and 2L
            assertTrue(result.stream().allMatch(timeOff -> timeOff.getId() == 1L || timeOff.getId() == 2L)); // Check if returned timeoffs are the correct ones
            verify(approvalServiceAdapter).getAssignedItems(anyLong()); // Ensure getAssignedItems is called once
        }
    }

    @Nested
    class GetAllForOperationsWithPagination {
        @Test
        void buildsPageRequest_queriesRepo_returnsResult() {
            var graphPageRequest = new com.multiplier.timeoff.types.PageRequest();
            var filters = new TimeOffFilter();
            var springPageRequest = PageRequest.of(1, 1);
            var spec = Specification.<TimeoffDBO>where(null);
            when(pageRequestHelper.toSpringPageRequestWithDefaultSort(graphPageRequest, "id"))
                    .thenReturn(springPageRequest);
            when(timeoffSpecBuilder.build(any(TimeoffFilters.class))).thenReturn(spec);

            when(timeoffRepo.findAll(spec))
                    .thenReturn(List.of());

            when(mapper.map(Collections.emptyList())).thenReturn(Collections.emptyList());

            when(timeoffRepo.countAllByStatusIsNot(TimeOffStatus.DELETED)).thenReturn(0);
            when(timeoffRepo.countAllByStatusIsIn(List.of(TimeOffStatus.SUBMITTED, TimeOffStatus.APPROVAL_IN_PROGRESS)))
                    .thenReturn(0);
            when(timeoffRepo.countAllByStatusIsIn(List.of(TimeOffStatus.APPROVED)))
                    .thenReturn(0);

            UserContext context = mock(UserContext.class);
            when(currentUser.getContext()).thenReturn(context);
            UserScopes scopes = mock(UserScopes.class);
            when(context.getScopes()).thenReturn(scopes);
            when(scopes.isOperationsTestUser()).thenReturn(false);

            var result = timeoffQuery.getAllForOperationsWithPagination(filters, graphPageRequest);

            assertThat(result).isNotNull();
            assertThat(result.getTimeOffs()).isEmpty();
            assertThat(result.getPage()).isNotNull();
            assertThat(result.getAllCount()).isEqualTo(0);
            assertThat(result.getPendingCount()).isEqualTo(0);
            assertThat(result.getApprovedCount()).isEqualTo(0);
        }

        @Test
        void filtersOnContractAttributes() {
            var timeoffdbo = new TimeoffDBO();
            timeoffdbo.contractId(1L);
            var graphPageRequest = new com.multiplier.timeoff.types.PageRequest();
            var filters = new TimeOffFilter();
            filters.setContractStatus(ContractStatus.ACTIVE);
            filters.setContractCountry(CountryCode.USA);

            var springPageRequest = PageRequest.of(0, 1);
            var spec = Specification.<TimeoffDBO>where(null);
            when(pageRequestHelper.toSpringPageRequestWithDefaultSort(graphPageRequest, "id"))
                    .thenReturn(springPageRequest);
            when(timeoffSpecBuilder.build(any(TimeoffFilters.class))).thenReturn(spec);

            when(timeoffRepo.findAll(spec))
                    .thenReturn(List.of(timeoffdbo));

            UserContext context = mock(UserContext.class);
            when(currentUser.getContext()).thenReturn(context);
            UserScopes scopes = mock(UserScopes.class);
            when(context.getScopes()).thenReturn(scopes);
            when(scopes.isOperationsTestUser()).thenReturn(false);

            when(contractServiceAdapter.getContractIdsMatchingFilters(ContractOuterClass.ContractFilters.newBuilder()
                    .addAllContractIds(List.of(1L))
                    .setCountryCode("USA")
                    .setContractStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setContractType(ContractOuterClass.ContractType.NULL_CONTRACT_TYPE)
                    .setIsTest(false)
                    .build())).thenReturn(List.of(1L));

            var timeoff = new TimeOff();
            when(mapper.map(List.of(timeoffdbo))).thenReturn(List.of(timeoff));

            when(timeoffRepo.countAllByStatusIsNot(TimeOffStatus.DELETED)).thenReturn(0);
            when(timeoffRepo.countAllByStatusIsIn(List.of(TimeOffStatus.SUBMITTED, TimeOffStatus.APPROVAL_IN_PROGRESS)))
                    .thenReturn(0);
            when(timeoffRepo.countAllByStatusIsIn(List.of(TimeOffStatus.APPROVED)))
                    .thenReturn(1);

            var result = timeoffQuery.getAllForOperationsWithPagination(filters, graphPageRequest);

            assertThat(result).isNotNull();
            assertThat(result.getTimeOffs()).hasSize(1);
            assertThat(result.getPage()).isNotNull();
            assertThat(result.getAllCount()).isEqualTo(0);
            assertThat(result.getPendingCount()).isEqualTo(0);
            assertThat(result.getApprovedCount()).isEqualTo(1);
        }
    }


    // userId = 10
    private UserContext getCompanyUserUserDetails(Long companyUserId, Long companyId) {
        return new UserContext(
                10L,
                "username",
                "company",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        null,
                        companyId,
                        null,
                        companyUserId,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }
}
