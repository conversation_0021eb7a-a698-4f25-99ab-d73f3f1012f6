package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.repository.model.TimeoffEntryDBO;
import com.multiplier.timeoff.types.TimeOffEntry;
import com.multiplier.timeoff.types.TimeOffEntryType;
import com.multiplier.timeoff.types.TimeOffSession;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class TimeoffEntryMapperTest {

    private final TimeoffEntryMapper mapper = new TimeoffEntryMapperImpl();

    @Test
    void map_should_map_all_fields_correctly() {
        LocalDate timeoffEntryDate = LocalDate.of(2023, 1, 1);
        TimeoffEntryDBO entryDBO = TimeoffEntryDBO.builder()
                .timeoffId(456L)
                .date(timeoffEntryDate)
                .value(1.0)
                .session(TimeOffSession.FULL_DAY)
                .type(TimeOffEntryType.TIMEOFF)
                .build();

        // Act
        TimeOffEntry result = mapper.map(entryDBO);

        // Assert
        assertNotNull(result);
        assertEquals(timeoffEntryDate, result.getDate());
        assertEquals(1.0, result.getValue());
        assertEquals(TimeOffSession.FULL_DAY, result.getSession());
        assertEquals(TimeOffEntryType.TIMEOFF, result.getType());
    }

    @Test
    void map_should_handle_null_entry() {
        // Act
        TimeOffEntry result = mapper.map((TimeoffEntryDBO) null);

        // Assert
        assertNull(result);
    }

    @Test
    void map_list_should_map_all_entries() {
        // Arrange
        LocalDate timeoffEntryDate1 = LocalDate.of(2023, 1, 1);
        LocalDate timeoffEntryDate2 = LocalDate.of(2023, 1, 2);
        
        TimeoffEntryDBO entry1 = TimeoffEntryDBO.builder()
                .timeoffId(123L)
                .date(timeoffEntryDate1)
                .value(0.5)
                .session(TimeOffSession.MORNING)
                .type(TimeOffEntryType.TIMEOFF)
                .build();
                
        TimeoffEntryDBO entry2 = TimeoffEntryDBO.builder()
                .timeoffId(456L)
                .date(timeoffEntryDate2)
                .value(0.0)
                .session(TimeOffSession.FULL_DAY)
                .type(TimeOffEntryType.HOLIDAY)
                .build();
        
        List<TimeoffEntryDBO> entries = List.of(entry1, entry2);

        // Act
        List<TimeOffEntry> results = mapper.map(entries);

        // Assert
        assertNotNull(results);
        assertEquals(2, results.size());
        
        // Check first entry
        assertEquals(timeoffEntryDate1, results.get(0).getDate());
        assertEquals(0.5, results.get(0).getValue());
        assertEquals(TimeOffSession.MORNING, results.get(0).getSession());
        assertEquals(TimeOffEntryType.TIMEOFF, results.get(0).getType());
        
        // Check second entry
        assertEquals(timeoffEntryDate2, results.get(1).getDate());
        assertEquals(0.0, results.get(1).getValue());
        assertEquals(TimeOffSession.FULL_DAY, results.get(1).getSession());
        assertEquals(TimeOffEntryType.HOLIDAY, results.get(1).getType());
    }

    @Test
    void map_list_should_handle_null_list() {
        // Act
        List<TimeOffEntry> results = mapper.map((List<TimeoffEntryDBO>) null);

        // Assert
        assertNull(results);
    }

    @Test
    void map_list_should_handle_empty_list() {
        // Act
        List<TimeOffEntry> results = mapper.map(List.of());

        // Assert
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }
}