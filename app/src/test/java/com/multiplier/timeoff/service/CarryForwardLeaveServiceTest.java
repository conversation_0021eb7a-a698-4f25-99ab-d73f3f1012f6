package com.multiplier.timeoff.service;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.service.dto.CarryForwardResult;
import com.multiplier.timeoff.types.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import java.time.LocalDate;
import java.util.Collections;


@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class CarryForwardLeaveServiceTest {

    @Mock
    private ContractServiceAdapter contractServiceAdapter;

    @Mock
    private TimeOffRequirementsService requirementsService;

    @InjectMocks
    private CarryForwardLeaveService carryForwardLeaveService;

    @BeforeEach
    void setUp() {
        carryForwardLeaveService = new CarryForwardLeaveService(contractServiceAdapter, requirementsService);
    }

    @Test
    void carry_forward_leaves_success() {
        // Test case for carryForwardLeaves method

        //Arrange
        TimeoffEntitlementDBO entitlement = new TimeoffEntitlementDBO();
        entitlement.value(10.0);
        entitlement.unit(TimeOffUnit.DAYS);

        TimeoffSummaryDBO previousSummaryDBO = new TimeoffSummaryDBO();
        previousSummaryDBO.typeId(1L);
        previousSummaryDBO.totalEntitledCount(15.0);
        previousSummaryDBO.takenCount(5.0);
        previousSummaryDBO.pendingCount(2.0);

        TimeoffSummaryDBO newSummaryDBO = new TimeoffSummaryDBO();
        newSummaryDBO.periodStart(LocalDate.now());

        CarryForwardConfig config = new CarryForwardConfig();
        config.setEnabled(true);
        config.setMinForEligibility(CarryForwardLimit.newBuilder().type(LimitValueType.PERCENTAGE).value(10.0).unit(TimeOffUnit.DAYS).build());
        config.setMaxLimit(CarryForwardLimit.newBuilder().type(LimitValueType.PERCENTAGE).value(50.0).unit(TimeOffUnit.DAYS).build());
        config.setExpiry(new TimeOffDuration(1.0, TimeOffUnit.YEARS));

        ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(1L).setCountry("IND").setCountryStateCode("KAR").build();

        TimeOffRequirements timeoffRequirments = TimeOffRequirements.newBuilder().definitions(Collections.singletonList(TimeOffTypeDefinition.newBuilder().typeId(1L).configuration(TimeoffConfiguration.newBuilder().carryForward(config).build()).build())).build();

        when(requirementsService.getCompanyTimeOffRequirements(any())).thenReturn(timeoffRequirments);

        // Act
        CarryForwardResult result = carryForwardLeaveService.carryForwardLeaves(entitlement, previousSummaryDBO, newSummaryDBO, contract);

        // Assert
        assertNotNull(result);
        assertEquals(5.0, result.getCarriedCount());
        assertEquals(LocalDate.now().plusYears(1).minusDays(1), result.getLastValidDate());

    }

}
