package com.multiplier.timeoff.service.bulk;

import com.multiplier.grpc.common.bulkupload.v1.*;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.core.common.dto.*;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.service.TimeoffTypeService;
import com.multiplier.timeoff.service.exception.ValidationException;
import lombok.val;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BulkTimeOffServiceV2Test {

    @Mock
    private BulkTimeOffService bulkTimeOffService;

    @Mock
    private TimeoffTypeService timeoffTypeService;

    @Mock
    private CompanyServiceAdapter companyServiceAdapter;

    @InjectMocks
    private BulkTimeOffServiceV2 bulkTimeOffServiceV2;

    @Test
    void validateBulkTimeOffsTest() {
        val externalTimeOffId = "300";
        val contractId = 456L;
        val companyId = 123L;
        val entityId = 789L;

        // Data for both formats
        Map<String, String> dataMapOldFormat = new HashMap<>();
        dataMapOldFormat.put("externalTimeOffId", externalTimeOffId);
        dataMapOldFormat.put("timeOffType", "annual");
        dataMapOldFormat.put("noOfDays", "2");
        dataMapOldFormat.put("timeOffStartDate", "27/11/2023");
        dataMapOldFormat.put("timeOffEndDate", "29/11/2023");
        dataMapOldFormat.put("description", "description");
        dataMapOldFormat.put("employeeId", "123456");
        dataMapOldFormat.put("employeeFullName", "abc xyz");

        Map<String, String> dataMapNewFormat = new HashMap<>();
        dataMapNewFormat.put("externalTimeOffId", externalTimeOffId);
        dataMapNewFormat.put("timeOffType", "annual");
        dataMapNewFormat.put("noOfDays", "2");
        dataMapNewFormat.put("timeOffStartDate", "2023-11-27");
        dataMapNewFormat.put("timeOffEndDate", "2023-11-29");
        dataMapNewFormat.put("description", "description");
        dataMapNewFormat.put("employeeId", "123456");
        dataMapNewFormat.put("employeeFullName", "abc xyz");

        PlatformKeys platformKeys = PlatformKeys.newBuilder()
                .setCompanyId(companyId)
                .setContractId(contractId)
                .setEntityId(entityId)
                .build();

        // Input for old format
        ValidateUpsertInputRequest inputOldFormat = ValidateUpsertInputRequest.newBuilder()
                .setInputId("1")
                .setKeys(platformKeys)
                .putAllData(dataMapOldFormat)
                .build();

        // Input for new format
        ValidateUpsertInputRequest inputNewFormat = ValidateUpsertInputRequest.newBuilder()
                .setInputId("2")
                .setKeys(platformKeys)
                .putAllData(dataMapNewFormat)
                .build();
        ValidateUpsertInputBulkRequest req = ValidateUpsertInputBulkRequest.newBuilder()
                .addAllInputs(Arrays.asList(inputOldFormat, inputNewFormat))
                .build();

        BulkValidateTimeOffResult.BulkValidateTimeOffResultItem item = BulkValidateTimeOffResult.BulkValidateTimeOffResultItem.builder()
                .externalTimeOffId(externalTimeOffId)
                .errors(Collections.emptyList())
                .build();
        BulkValidateTimeOffResult result = BulkValidateTimeOffResult.builder()
                .success(true)
                .items(Collections.singletonList(item))
                .build();

        val expectedExternalTimeOffId = contractId + "-" + externalTimeOffId;

        when(bulkTimeOffService.validateBulkTimeOffs(any())).thenReturn(result);
        when(companyServiceAdapter.getLegalEntityIdsByCompanyIds(anySet()))
                .thenReturn(Map.of(companyId, List.of(entityId)));

        ValidateUpsertInputBulkResponse actual = bulkTimeOffServiceV2.validateBulkTimeOffs(req);

        assertEquals(expectedExternalTimeOffId, actual.getResults(0).getValidatedInputDataMap().get("externalTimeOffId"));
        verify(bulkTimeOffService, times(1)).validateBulkTimeOffs(any());
    }

    @Test
    void validateBulkTimeOffsTest_successFalseScenario() {
        val externalTimeOffId = "300";
        val contractId = 456L;
        val companyId = 123L;
        val entityId = 789L;

        // Data for both formats
        Map<String, String> dataMapOldFormat = new HashMap<>();
        dataMapOldFormat.put("externalTimeOffId", externalTimeOffId);
        dataMapOldFormat.put("timeOffType", "annual");
        dataMapOldFormat.put("noOfDays", "2");
        dataMapOldFormat.put("timeOffStartDate", "27/11/2023");
        dataMapOldFormat.put("timeOffEndDate", "29/11/2023");
        dataMapOldFormat.put("description", "description");

        Map<String, String> dataMapNewFormat = new HashMap<>();
        dataMapNewFormat.put("externalTimeOffId", externalTimeOffId);
        dataMapNewFormat.put("timeOffType", "annual");
        dataMapNewFormat.put("noOfDays", "2");
        dataMapNewFormat.put("timeOffStartDate", "2023-11-27");
        dataMapNewFormat.put("timeOffEndDate", "2023-11-29");
        dataMapNewFormat.put("description", "description");

        val errorMessage1 = "Start date Should be in format of YYYY-MM-DD.";
        val errorMessage2 = "Start date Should be in format of YYYY-MM-DD.";

        PlatformKeys platformKeys = PlatformKeys.newBuilder()
                .setCompanyId(companyId)
                .setContractId(contractId)
                .setEntityId(entityId)
                .build();

        ValidateUpsertInputRequest inputOldFormat = ValidateUpsertInputRequest.newBuilder()
                .setInputId("1")
                .setKeys(platformKeys)
                .putAllData(dataMapOldFormat)
                .build();

        ValidateUpsertInputRequest inputNewFormat = ValidateUpsertInputRequest.newBuilder()
                .setInputId("2")
                .setKeys(platformKeys)
                .putAllData(dataMapNewFormat)
                .build();
        ValidateUpsertInputBulkRequest req = ValidateUpsertInputBulkRequest.newBuilder()
                .addAllInputs(Arrays.asList(inputOldFormat, inputNewFormat))
                .build();

        val expectedExternalTimeOffId = contractId + "-" + externalTimeOffId;

        BulkValidateTimeOffResult.BulkValidateTimeOffResultItem item = BulkValidateTimeOffResult.BulkValidateTimeOffResultItem.builder()
                .externalTimeOffId(expectedExternalTimeOffId)
                .errors(List.of(errorMessage1, errorMessage2))
                .build();
        BulkValidateTimeOffResult result = BulkValidateTimeOffResult.builder()
                .success(false)
                .items(Collections.singletonList(item))
                .build();

        when(bulkTimeOffService.validateBulkTimeOffs(any())).thenReturn(result);
        when(companyServiceAdapter.getLegalEntityIdsByCompanyIds(anySet()))
                .thenReturn(Map.of(companyId, List.of(entityId)));

        ValidateUpsertInputBulkResponse actual = bulkTimeOffServiceV2.validateBulkTimeOffs(req);

        val actualResult = actual.getResults(0);

        assertEquals(Collections.emptyMap(), actualResult.getValidatedInputDataMap());
        assertEquals(2, actualResult.getMessagesCount());
        assertEquals(errorMessage1, actualResult.getMessages(0).getErrors(0));
        assertEquals(errorMessage2, actualResult.getMessages(1).getErrors(0));
        verify(bulkTimeOffService, times(1)).validateBulkTimeOffs(any());
    }

    @Test
    void validateBulkTimeOffsTest_throwsException() {
        val companyId = 123L;
        val entityId = 789L;

        // Data for both formats
        Map<String, String> dataMapOldFormat = new HashMap<>();
        dataMapOldFormat.put("externalTimeOffId", "300");
        dataMapOldFormat.put("type", "annual");
        dataMapOldFormat.put("noOfDays", "2");
        dataMapOldFormat.put("startDate", "27/11/2023");
        dataMapOldFormat.put("endDate", "29/11/2023");
        dataMapOldFormat.put("description", "description");

        Map<String, String> dataMapNewFormat = new HashMap<>();
        dataMapNewFormat.put("externalTimeOffId", "300");
        dataMapNewFormat.put("type", "annual");
        dataMapNewFormat.put("noOfDays", "2");
        dataMapNewFormat.put("startDate", "2023-11-27");
        dataMapNewFormat.put("endDate", "2023-11-29");
        dataMapNewFormat.put("description", "description");

        PlatformKeys platformKeys = PlatformKeys.newBuilder()
                .setCompanyId(companyId)
                .setContractId(456L)
                .setEntityId(entityId)
                .build();

        // Input for old format
        ValidateUpsertInputRequest inputOldFormat = ValidateUpsertInputRequest.newBuilder()
                .setInputId("1")
                .setKeys(platformKeys)
                .putAllData(dataMapOldFormat)
                .build();

        // Input for new format
        ValidateUpsertInputRequest inputNewFormat = ValidateUpsertInputRequest.newBuilder()
                .setInputId("2")
                .setKeys(platformKeys)
                .putAllData(dataMapNewFormat)
                .build();

        ValidateUpsertInputBulkRequest req = ValidateUpsertInputBulkRequest.newBuilder()
                .addAllInputs(Arrays.asList(inputOldFormat, inputNewFormat))
                .build();

        when(bulkTimeOffService.validateBulkTimeOffs(any())).thenThrow(new RuntimeException("Test exception"));
        when(companyServiceAdapter.getLegalEntityIdsByCompanyIds(anySet()))
                .thenReturn(Map.of(companyId, List.of(entityId)));

        assertThrows(RuntimeException.class, () -> bulkTimeOffServiceV2.validateBulkTimeOffs(req));
        verify(bulkTimeOffService, times(1)).validateBulkTimeOffs(any());
    }

    @Test
    void bulkUpsertTimeOffsTest_bothFormats() {
        val inputId = "1";
        val externalTimeOffId = "300";
        val contractId = 456L;
        val companyId = 123L;
        val entityId = 789L;

        // Data for both date formats
        Map<String, String> dataMapOldFormat = new HashMap<>();
        dataMapOldFormat.put("externalTimeOffId", externalTimeOffId);
        dataMapOldFormat.put("type", "annual");
        dataMapOldFormat.put("noOfDays", "2");
        dataMapOldFormat.put("startDate", "27/11/2023");
        dataMapOldFormat.put("endDate", "29/11/2023");
        dataMapOldFormat.put("description", "description");
        dataMapOldFormat.put("companyId", String.valueOf(companyId));
        dataMapOldFormat.put("contractId", String.valueOf(contractId));
        dataMapOldFormat.put("entityId", String.valueOf(entityId));

        Map<String, String> dataMapNewFormat = new HashMap<>();
        dataMapNewFormat.put("externalTimeOffId", externalTimeOffId);
        dataMapNewFormat.put("type", "annual");
        dataMapNewFormat.put("noOfDays", "2");
        dataMapNewFormat.put("startDate", "2023-11-27");
        dataMapNewFormat.put("endDate", "2023-11-29");
        dataMapNewFormat.put("description", "description");
        dataMapNewFormat.put("companyId", String.valueOf(companyId));
        dataMapNewFormat.put("contractId", String.valueOf(contractId));
        dataMapNewFormat.put("entityId", String.valueOf(entityId));

        // Input for both formats
        UpsertRequest inputOldFormat = UpsertRequest.newBuilder()
                .setInputId(inputId)
                .putAllData(dataMapOldFormat)
                .build();

        UpsertRequest inputNewFormat = UpsertRequest.newBuilder()
                .setInputId(inputId)
                .putAllData(dataMapNewFormat)
                .build();
        UpsertBulkRequest req = UpsertBulkRequest.newBuilder()
                .addAllInputs(Arrays.asList(inputOldFormat, inputNewFormat))
                .build();

        BulkUpsertTimeOffResult.Item item = BulkUpsertTimeOffResult.Item.builder()
                .externalTimeOffId(externalTimeOffId)
                .build();
        BulkUpsertTimeOffResult result = BulkUpsertTimeOffResult.builder()
                .success(true)
                .items(Collections.singletonList(item))
                .build();

        when(bulkTimeOffService.bulkUpsertTimeOffs(any())).thenReturn(result);
        when(companyServiceAdapter.getLegalEntityIdsByCompanyIds(anySet()))
                .thenReturn(Map.of(companyId, List.of(entityId)));

        UpsertBulkResponse actual = bulkTimeOffServiceV2.bulkUpsertTimeOffs(req);

        assertEquals(inputId, actual.getResults(0).getInputId());
        assertTrue(actual.getResults(0).getSuccess());
        verify(bulkTimeOffService, times(1)).bulkUpsertTimeOffs(any());
    }

    @Test
    void getFieldRequirementsTest() {
        val companyId = 123L;
        val entityId = 789L;

        FieldRequirementsRequest request = FieldRequirementsRequest.newBuilder()
                .setCompanyId(companyId)
                .setEntityId(entityId)
                .build();

        TimeoffTypeDBO timeoffTypeDBO1 = TimeoffTypeDBO.builder()
                .key("annual")
                .label("Annual")
                .build();

        TimeoffTypeDBO timeoffTypeDBO2 = TimeoffTypeDBO.builder()
                .key("unpaid")
                .label("Unpaid")
                .build();

        when(timeoffTypeService.fetchEntityTimeOffTypes(anyLong(), anyLong(), any())).thenReturn(List.of(timeoffTypeDBO1, timeoffTypeDBO2));
        when(companyServiceAdapter.getLegalEntityIdsByCompanyIds(anySet()))
                .thenReturn(Map.of(companyId, List.of(entityId)));

        FieldRequirementsResponse actual = bulkTimeOffServiceV2.getFieldRequirements(request);

        assertEquals(8, actual.getFieldRequirementsCount());
        assertEquals(List.of("Annual", "Unpaid"), actual.getFieldRequirements(3).getAllowedValuesList());
    }

    @Test
    void getFieldRequirementsTest_throwsException_sc1() {
        val companyId = 123L;
        val entityId = 789L;

        FieldRequirementsRequest request = FieldRequirementsRequest.newBuilder()
                .setCompanyId(companyId)
                .setEntityId(entityId)
                .build();

        when(companyServiceAdapter.getLegalEntityIdsByCompanyIds(anySet()))
                .thenReturn(Collections.emptyMap());

        val exception = assertThrows(ValidationException.class, () ->
                bulkTimeOffServiceV2.getFieldRequirements(request));

        assertTrue(exception.getMessage().contains("[validateEntityId] Entity id 789 does not belong to company id 123"));
    }

    @Test
    void getFieldRequirementsTest_throwsException_sc2() {
        val companyId = 123L;
        val entityId = 789L;

        FieldRequirementsRequest request = FieldRequirementsRequest.newBuilder()
                .setCompanyId(companyId)
                .setEntityId(entityId)
                .build();

        when(companyServiceAdapter.getLegalEntityIdsByCompanyIds(anySet()))
                .thenReturn(Map.of(companyId, Collections.emptyList()));

        val exception = assertThrows(ValidationException.class, () ->
                bulkTimeOffServiceV2.getFieldRequirements(request));

        assertTrue(exception.getMessage().contains("[validateEntityId] Entity id 789 does not belong to company id 123"));
    }
}