package com.multiplier.timeoff.service;

import com.google.protobuf.Int32Value;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.repository.model.DefinitionEntity;
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.validation.TimeoffDefinitionAllocator;
import com.multiplier.timeoff.validation.TimeoffDefinitionConfigRequest;
import lombok.val;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

import java.time.Month;
import java.util.List;
import java.util.Map;
import java.util.Set;


@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class TimeOffConfigurationServiceTest {
    @Mock
    TimeoffDefinitionAllocator timeoffDefinitionAllocator;
    @Mock
    CompanyServiceAdapter companyServiceAdapter;
    @Mock
    private ContractServiceAdapter contractServiceAdapter;
    @InjectMocks
    TimeOffConfigurationService timeOffConfigurationService;

    @Nested
    class validateAndMutateSummaries {

        @Captor
        private ArgumentCaptor<TimeoffDefinitionConfigRequest> timeoffDefinitionConfigRequestCaptor;

        @Test
        void should_validate_and_mutate_summaries() {
            // given
            val annualLeaveType = TimeoffTypeDBO.builder()
                    .id(1L)
                    .key("annual")
                    .label("Annual Leave")
                    .build();
            val annualLeaveDefinition = new DefinitionEntity();

            val sickLeaveType = TimeoffTypeDBO.builder()
                    .id(2L)
                    .key("sick")
                    .label("Sick Leave")
                    .build();
            val sickLeaveDefinition = new DefinitionEntity();

            val personalLeaveType = TimeoffTypeDBO.builder()
                    .id(5L)
                    .key("personal")
                    .label("Personal Leave")
                    .build();
            val maternityLeaveType = TimeoffTypeDBO.builder()
                    .id(3L)
                    .key("maternity")
                    .label("Maternity Leave")
                    .build();
            val happyLeaveType = TimeoffTypeDBO.builder()
                    .id(13L)
                    .key("happy")
                    .label("Happy Leave")
                    .build();


            val entitlementDBO1 = TimeoffEntitlementDBO.builder()
                    .contractId(1L)
                    .value(23.0)
                    .type(annualLeaveType)
                    .definition(annualLeaveDefinition)
                    .build();
            val entitlementDBO2 = TimeoffEntitlementDBO.builder()
                    .contractId(2L)
                    .value(10.0)
                    .type(sickLeaveType)
                    .definition(sickLeaveDefinition)
                    .build();
            val entitlementDBO3 = TimeoffEntitlementDBO.builder()
                    .contractId(2L)
                    .type(personalLeaveType)
                    .build();
            val entitlementDBO4 = TimeoffEntitlementDBO.builder()
                    .contractId(2L)
                    .type(maternityLeaveType)
                    .build();
            val entitlementDBO5 = TimeoffEntitlementDBO.builder()
                    .contractId(3L)
                    .type(maternityLeaveType)
                    .value(10.0)
                    .build();
            val entitlementDBO6 = TimeoffEntitlementDBO.builder()
                    .contractId(2L)
                    .type(happyLeaveType)
                    .value(10.0)
                    .build();
            val entitlementDBOS = List.of(entitlementDBO1, entitlementDBO2, entitlementDBO3,
                    entitlementDBO4, entitlementDBO5, entitlementDBO6);

            val annualLeaveSummary = TimeoffSummaryDBO.builder().id(1L).contractId(1L).typeId(1L).build();
            val sickLeaveSummary = TimeoffSummaryDBO.builder().id(2L).contractId(2L).typeId(2L).build();
            val maternityLeaveSummary = TimeoffSummaryDBO.builder().id(3L).contractId(2L).typeId(3L).build();
            val personalLeaveSummary = TimeoffSummaryDBO.builder().id(4L).contractId(3L).typeId(5L).build();
            val happyLeaveSummary = TimeoffSummaryDBO.builder().id(5L).contractId(2L).typeId(14L).build();
            val contractIdTypeIdToSummaryMap = Map.of(
                    "1_1", annualLeaveSummary,
                    "2_2", sickLeaveSummary,
                    "2_3", maternityLeaveSummary,
                    "3_5", personalLeaveSummary,
                    "2_13", happyLeaveSummary);
            val contract1 = ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(100L).build();
            val contract2 = ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(200L).build();
            val idToContractMap = Map.of(1L, contract1, 2L, contract2);

            val companies = List.of(
                    CompanyOuterClass.Company.newBuilder().setId(100L).setFinancialYear(Int32Value.of(1)).build(),
                    CompanyOuterClass.Company.newBuilder().setId(200L).setFinancialYear(Int32Value.of(4)).build()
            );

            when(companyServiceAdapter.getCompanyByIds(Set.of(100L, 200L))).thenReturn(companies);

            // when
            timeOffConfigurationService.validateAndMutateSummaries(entitlementDBOS, contractIdTypeIdToSummaryMap, idToContractMap, true);

            // then
            verify(timeoffDefinitionAllocator, times(2)).allocate(timeoffDefinitionConfigRequestCaptor.capture());
            val timeoffDefinitionConfigRequest = timeoffDefinitionConfigRequestCaptor.getAllValues();

            assertEquals(2, timeoffDefinitionConfigRequest.size());
            assertTrue(timeoffDefinitionConfigRequest.stream().anyMatch(r -> isConfigRequestMatched(r, entitlementDBO1, annualLeaveSummary, annualLeaveDefinition, contract1, Month.DECEMBER)));
            assertTrue(timeoffDefinitionConfigRequest.stream().anyMatch(r -> isConfigRequestMatched(r, entitlementDBO2, sickLeaveSummary, sickLeaveDefinition, contract2, Month.MARCH)));
        }

        private boolean isConfigRequestMatched(TimeoffDefinitionConfigRequest request,
                                       TimeoffEntitlementDBO entitlementDBO,
                                       TimeoffSummaryDBO summary,
                                       DefinitionEntity definition,
                                       ContractOuterClass.Contract contract,
                                       Month companyFinancialYearEndMonth) {
            return request.timeoffEntitlementDBO().equals(entitlementDBO) &&
                    request.contract().equals(contract) &&
                    request.timeoffSummaryDBO().equals(summary) &&
                    request.definitionEntity().equals(definition) &&
                    request.isAllocation() &&
                    request.companyFinancialYearEndMonth().equals(companyFinancialYearEndMonth);
        }
    }

    @Nested
    class validateAndMutateSummary {

        @Test
        void should_return_null_when_contract_is_ended() {
            // given
            val summaryDBO = TimeoffSummaryDBO.builder().id(1L).contractId(1L).typeId(1L).build();
            val entitlementDBO = TimeoffEntitlementDBO.builder()
                    .contractId(1L)
                    .value(23.0)
                    .type(TimeoffTypeDBO.builder().id(1L).build())
                    .build();
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .setStatus(ContractOuterClass.ContractStatus.ENDED)
                    .setCompanyId(100L)
                    .build();

            when(contractServiceAdapter.getContractByIdAnyStatus(1L)).thenReturn(contract);

            // when
            val result = timeOffConfigurationService.validateAndMutateSummary(summaryDBO, entitlementDBO, true);

            // then
            assertNull(result);
            verify(contractServiceAdapter).getContractByIdAnyStatus(1L);
            verifyNoMoreInteractions(contractServiceAdapter, companyServiceAdapter, timeoffDefinitionAllocator);
        }

        @Test
        void should_validate_and_mutate_summary_successfully() {
            // given
            val summaryDBO = TimeoffSummaryDBO.builder().id(1L).contractId(1L).typeId(1L).build();
            val entitlementDBO = TimeoffEntitlementDBO.builder()
                    .contractId(1L)
                    .value(23.0)
                    .type(TimeoffTypeDBO.builder().id(1L).build())
                    .definition(new DefinitionEntity())
                    .build();
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setCompanyId(100L)
                    .build();
            val company = CompanyOuterClass.Company.newBuilder()
                    .setId(100L)
                    .setFinancialYear(Int32Value.of(1))
                    .build();

            when(contractServiceAdapter.getContractByIdAnyStatus(1L)).thenReturn(contract);
            when(companyServiceAdapter.getCompany(100L)).thenReturn(company);
            doAnswer(invocation -> null).when(timeoffDefinitionAllocator).allocate(any());

            // when
            val result = timeOffConfigurationService.validateAndMutateSummary(summaryDBO, entitlementDBO, true);

            // then
            assertNotNull(result);
            assertEquals(summaryDBO, result);
            verify(contractServiceAdapter).getContractByIdAnyStatus(1L);
            verify(companyServiceAdapter).getCompany(100L);
            verify(timeoffDefinitionAllocator).allocate(any());
        }

        @Test
        void should_return_null_when_exception_occurs() {
            // given
            val summaryDBO = TimeoffSummaryDBO.builder().id(1L).contractId(1L).typeId(1L).build();
            val entitlementDBO = TimeoffEntitlementDBO.builder()
                    .contractId(1L)
                    .value(23.0)
                    .type(TimeoffTypeDBO.builder().id(1L).build())
                    .definition(new DefinitionEntity())
                    .build();

            when(contractServiceAdapter.getContractByIdAnyStatus(1L)).thenThrow(new RuntimeException("Test exception"));

            // when
            val result = timeOffConfigurationService.validateAndMutateSummary(summaryDBO, entitlementDBO, true);

            // then
            assertNull(result);
            verify(contractServiceAdapter).getContractByIdAnyStatus(1L);
            verifyNoMoreInteractions(contractServiceAdapter, companyServiceAdapter, timeoffDefinitionAllocator);
        }
    }

    @Nested
    class fixTimeoffAllocation {
        @Test
        void should_return_null_when_definition_not_found() {
            // given
            val summaryDBO = TimeoffSummaryDBO.builder().id(1L).contractId(1L).typeId(1L).build();
            val entitlementDBO = TimeoffEntitlementDBO.builder()
                    .contractId(1L)
                    .type(TimeoffTypeDBO.builder().id(1L).build())
                    .build();
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .setCompanyId(100L)
                    .build();

            // when
            val result = timeOffConfigurationService.fixTimeoffAllocation(summaryDBO, entitlementDBO, contract);

            // then
            assertNull(result);
            verifyNoInteractions(timeoffDefinitionAllocator, companyServiceAdapter);
        }

        @Test
        void should_return_null_when_entitlement_value_is_null() {
            // given
            val summaryDBO = TimeoffSummaryDBO.builder().id(1L).contractId(1L).typeId(1L).build();
            val entitlementDBO = TimeoffEntitlementDBO.builder()
                    .contractId(1L)
                    .type(TimeoffTypeDBO.builder().id(1L).build())
                    .definition(new DefinitionEntity())
                    .build();
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .setCompanyId(100L)
                    .build();

            // when
            val result = timeOffConfigurationService.fixTimeoffAllocation(summaryDBO, entitlementDBO, contract);

            // then
            assertNull(result);
            verifyNoInteractions(timeoffDefinitionAllocator, companyServiceAdapter);
        }

        @Test
        void should_fix_timeoff_allocation_successfully() {
            // given
            val summaryDBO = TimeoffSummaryDBO.builder().id(1L).contractId(1L).typeId(1L).build();
            val entitlementDBO = TimeoffEntitlementDBO.builder()
                    .contractId(1L)
                    .value(23.0)
                    .type(TimeoffTypeDBO.builder().id(1L).build())
                    .definition(new DefinitionEntity())
                    .build();
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .setCompanyId(100L)
                    .build();
            val company = CompanyOuterClass.Company.newBuilder()
                    .setId(100L)
                    .setFinancialYear(Int32Value.of(1))
                    .build();

            when(companyServiceAdapter.getCompany(100L)).thenReturn(company);

            // when
            val result = timeOffConfigurationService.fixTimeoffAllocation(summaryDBO, entitlementDBO, contract);

            // then
            assertNotNull(result);
            assertEquals(summaryDBO, result);
            verify(companyServiceAdapter).getCompany(100L);
            verify(timeoffDefinitionAllocator).fixTimeoffAllocation(any());
        }

        @Test
        void should_return_null_when_exception_occurs() {
            // given
            val summaryDBO = TimeoffSummaryDBO.builder().id(1L).contractId(1L).typeId(1L).build();
            val entitlementDBO = TimeoffEntitlementDBO.builder()
                    .contractId(1L)
                    .value(23.0)
                    .type(TimeoffTypeDBO.builder().id(1L).build())
                    .definition(new DefinitionEntity())
                    .build();
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .setCompanyId(100L)
                    .build();

            when(companyServiceAdapter.getCompany(100L)).thenThrow(new RuntimeException("Test exception"));

            // when
            val result = timeOffConfigurationService.fixTimeoffAllocation(summaryDBO, entitlementDBO, contract);

            // then
            assertNull(result);
            verify(companyServiceAdapter).getCompany(100L);
            verifyNoMoreInteractions(companyServiceAdapter, timeoffDefinitionAllocator);
        }
    }
}
