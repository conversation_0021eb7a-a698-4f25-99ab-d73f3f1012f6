package com.multiplier.timeoff.service.notifications;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.common.transport.user.UserScopes;
import com.multiplier.core.schema.common.Common;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.core.common.constant.Keys;
import com.multiplier.timeoff.types.*;
import lombok.val;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.time.LocalDate;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class TimeoffNotificationHelperTest {

    @Mock
    CurrentUser currentUser;

    @Mock
    PigeonNotificationService pigeonNotificationService;

    @Mock
    CompanyServiceAdapter companyServiceAdapter;

    @InjectMocks
    TimeoffNotificationHelper timeoffNotificationHelper;

    @Nested
    class sendTimeoffTypeCreatedEmail {

        @Captor
        ArgumentCaptor<PigeonEmailNotificationData> pigeonEmailNotificationDataArgumentCaptor;

        @Test
        void should_successfully_send_timeoff_type_created_email() {
            when(currentUser.getContext()).thenReturn(
                    new UserContext(
                            101L,
                            "<EMAIL>",
                            "Ops Experience",
                            null,
                            null,
                            "Russell",
                            "Hall",
                            null,
                            new UserScopes(101L, 1L, 102L, null, null, null, false),
                            null,
                            List.of()
                    )
            );

            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(
                    CompanyOuterClass.CompanyUser.newBuilder()
                            .setCompanyId(1L)
                            .build()
            );

            when(companyServiceAdapter.getCompanyAdmins(1L)).thenReturn(
                    List.of(
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("David ")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build(),
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("Frank")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build()
                    )
            );

            timeoffNotificationHelper.sendTimeoffTypeCreatedEmail("Annual Leave");

            verify(pigeonNotificationService, times(2)).sendEmail(pigeonEmailNotificationDataArgumentCaptor.capture());
            val notificationArguments = pigeonEmailNotificationDataArgumentCaptor.getAllValues();

            assertEquals(NotificationType.TimeoffTypeCreatedEmailToAdmin, notificationArguments.get(0).getType());
            assertEquals("<EMAIL>", notificationArguments.get(0).getTo());
            assertEquals("David", notificationArguments.get(0).getData().get("recipientName"));
            assertEquals("Annual Leave", notificationArguments.get(0).getData().get("timeOffTypeName"));
            assertEquals("Russell Hall", notificationArguments.get(0).getData().get("createdBy"));
            assertEquals(LocalDate.now().toString(), notificationArguments.get(0).getData().get("createdOn"));

            assertEquals(NotificationType.TimeoffTypeCreatedEmailToAdmin, notificationArguments.get(1).getType());
            assertEquals("<EMAIL>", notificationArguments.get(1).getTo());
            assertEquals("Frank", notificationArguments.get(1).getData().get("recipientName"));
            assertEquals("Annual Leave", notificationArguments.get(1).getData().get("timeOffTypeName"));
            assertEquals("Russell Hall", notificationArguments.get(1).getData().get("createdBy"));
            assertEquals(LocalDate.now().toString(), notificationArguments.get(1).getData().get("createdOn"));
        }
    }

    @Nested
    class sendTimeoffTypeUpdatedEmail {

        @Captor
        ArgumentCaptor<PigeonEmailNotificationData> pigeonEmailNotificationDataArgumentCaptor;

        @Test
        void should_successfully_send_timeoff_type_updated_email() {
            when(currentUser.getContext()).thenReturn(
                    new UserContext(
                            101L,
                            "<EMAIL>",
                            "Ops Experience",
                            null,
                            null,
                            "Russell",
                            null,
                            null,
                            new UserScopes(101L, 1L, 102L, null, null, null, false),
                            null,
                            List.of()
                    )
            );

            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(
                    CompanyOuterClass.CompanyUser.newBuilder()
                            .setCompanyId(1L)
                            .build()
            );

            when(companyServiceAdapter.getCompanyAdmins(1L)).thenReturn(
                    List.of(
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("David ")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build(),
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("Frank")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build()
                    )
            );

            timeoffNotificationHelper.sendTimeoffTypeUpdatedEmail("Annual Leave");
            verify(pigeonNotificationService, times(2)).sendEmail(pigeonEmailNotificationDataArgumentCaptor.capture());
            val notificationArguments = pigeonEmailNotificationDataArgumentCaptor.getAllValues();

            assertEquals(NotificationType.TimeoffTypeUpdatedEmailToAdmin, notificationArguments.get(0).getType());
            assertEquals("<EMAIL>", notificationArguments.get(0).getTo());
            assertEquals("David", notificationArguments.get(0).getData().get("recipientName"));
            assertEquals("Annual Leave", notificationArguments.get(0).getData().get("timeoffTypeOldName"));
            assertEquals("Russell", notificationArguments.get(0).getData().get("updatedBy"));
            assertEquals(LocalDate.now().toString(), notificationArguments.get(0).getData().get("updatedAt"));

            assertEquals(NotificationType.TimeoffTypeUpdatedEmailToAdmin, notificationArguments.get(1).getType());
            assertEquals("<EMAIL>", notificationArguments.get(1).getTo());
            assertEquals("Frank", notificationArguments.get(1).getData().get("recipientName"));
            assertEquals("Annual Leave", notificationArguments.get(1).getData().get("timeoffTypeOldName"));
            assertEquals("Russell", notificationArguments.get(1).getData().get("updatedBy"));
            assertEquals(LocalDate.now().toString(), notificationArguments.get(1).getData().get("updatedAt"));

        }

    }

    @Nested
    class sendTimeoffTypeDeletedEmail {

        @Captor
        ArgumentCaptor<PigeonEmailNotificationData> pigeonEmailNotificationDataArgumentCaptor;

        @Test
        void should_successfully_send_timeoff_type_deleted_email() {
            when(currentUser.getContext()).thenReturn(
                    new UserContext(
                            101L,
                            "<EMAIL>",
                            "Ops Experience",
                            null,
                            null,
                            null,
                            "Hall",
                            null,
                            new UserScopes(101L, 1L, 102L, null, null, null, false),
                            null,
                            List.of()
                    )
            );

            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(
                    CompanyOuterClass.CompanyUser.newBuilder()
                            .setCompanyId(1L)
                            .build()
            );

            when(companyServiceAdapter.getCompanyAdmins(1L)).thenReturn(
                    List.of(
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("David ")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build(),
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("Frank")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build()
                    )
            );

            timeoffNotificationHelper.sendTimeoffTypeDeletedMail("Annual Leave");
            verify(pigeonNotificationService, times(2)).sendEmail(pigeonEmailNotificationDataArgumentCaptor.capture());
            val notificationArguments = pigeonEmailNotificationDataArgumentCaptor.getAllValues();

            assertEquals(NotificationType.TimeoffTypeDeletedEmailToAdmin, notificationArguments.get(0).getType());
            assertEquals("<EMAIL>", notificationArguments.get(0).getTo());
            assertEquals("David", notificationArguments.get(0).getData().get("recipientName"));
            assertEquals("Annual Leave", notificationArguments.get(0).getData().get("timeOffTypeName"));
            assertEquals("Hall", notificationArguments.get(0).getData().get("deletedBy"));
            assertEquals(LocalDate.now().toString(), notificationArguments.get(0).getData().get("deletedOn"));

            assertEquals(NotificationType.TimeoffTypeDeletedEmailToAdmin, notificationArguments.get(1).getType());
            assertEquals("<EMAIL>", notificationArguments.get(1).getTo());
            assertEquals("Frank", notificationArguments.get(1).getData().get("recipientName"));
            assertEquals("Annual Leave", notificationArguments.get(1).getData().get("timeOffTypeName"));
            assertEquals("Hall", notificationArguments.get(1).getData().get("deletedBy"));
            assertEquals(LocalDate.now().toString(), notificationArguments.get(1).getData().get("deletedOn"));
        }

    }

    @Nested
    class sendTimeoffPolicyCreatedNotification {

        @Captor
        ArgumentCaptor<PigeonEmailNotificationData> pigeonEmailNotificationDataArgumentCaptor;

        @Test
        void should_successfully_send_timeoff_policy_created_notification() {
            when(currentUser.getContext()).thenReturn(
                    new UserContext(
                            101L,
                            "<EMAIL>",
                            "Ops Experience",
                            null,
                            null,
                            "Russell",
                            "Hall",
                            null,
                            new UserScopes(101L, 1L, 102L, null, null, null, false),
                            null,
                            List.of()
                    )
            );

            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(
                    CompanyOuterClass.CompanyUser.newBuilder()
                            .setCompanyId(1L)
                            .build()
            );

            when(companyServiceAdapter.getCompanyAdmins(1L)).thenReturn(
                    List.of(
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("David ")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build(),
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("Frank")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build()
                    )
            );

            timeoffNotificationHelper.sendTimeoffPolicyCreatedNotification(1L);

            verify(pigeonNotificationService, times(2)).sendEmail(pigeonEmailNotificationDataArgumentCaptor.capture());

            val notificationArguments = pigeonEmailNotificationDataArgumentCaptor.getAllValues();

            assertEquals(NotificationType.TimeoffPolicyCreatedEmailToAdmin, notificationArguments.get(0).getType());
            assertEquals("<EMAIL>", notificationArguments.get(0).getTo());
            assertEquals("David", notificationArguments.get(0).getData().get("recipientName"));
            assertEquals("Russell Hall", notificationArguments.get(0).getData().get("createdBy"));
            assertTrue(notificationArguments.get(0).getData().get("link").endsWith("/organization-settings/company-policies/time-off/time-off-policy/1"));

            assertEquals(NotificationType.TimeoffPolicyCreatedEmailToAdmin, notificationArguments.get(1).getType());
            assertEquals("<EMAIL>", notificationArguments.get(1).getTo());
            assertEquals("Frank", notificationArguments.get(1).getData().get("recipientName"));
            assertEquals("Russell Hall", notificationArguments.get(1).getData().get("createdBy"));
            assertTrue(notificationArguments.get(1).getData().get("link").endsWith("/organization-settings/company-policies/time-off/time-off-policy/1"));
        }

    }

    @Nested
    class sendTimeoffPolicyUpdatedNotification {

        @Captor
        ArgumentCaptor<PigeonEmailNotificationData> pigeonEmailNotificationDataArgumentCaptor;

        @Test
        void should_successfully_send_timeoff_policy_updated_notification() {
            when(currentUser.getContext()).thenReturn(
                    new UserContext(
                            101L,
                            "<EMAIL>",
                            "Ops Experience",
                            null,
                            null,
                            "Russell",
                            "Hall",
                            null,
                            new UserScopes(101L, 1L, 102L, null, null, null, false),
                            null,
                            List.of()
                    )
            );

            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(
                    CompanyOuterClass.CompanyUser.newBuilder()
                            .setCompanyId(1L)
                            .build()
            );

            when(companyServiceAdapter.getCompanyAdmins(1L)).thenReturn(
                    List.of(
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("David ")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build(),
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("Frank")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build()
                    )
            );

            timeoffNotificationHelper.sendTimeoffPolicyUpdatedNotification(1L, "Annual Leave Policy");

            verify(pigeonNotificationService, times(2)).sendEmail(pigeonEmailNotificationDataArgumentCaptor.capture());

            val notificationArguments = pigeonEmailNotificationDataArgumentCaptor.getAllValues();

            assertEquals(NotificationType.TimeoffPolicyUpdatedEmailToAdmin, notificationArguments.get(0).getType());
            assertEquals("<EMAIL>", notificationArguments.get(0).getTo());
            assertEquals("David", notificationArguments.get(0).getData().get("recipientName"));
            assertEquals("Russell Hall", notificationArguments.get(0).getData().get("updatedBy"));
            assertEquals("Annual Leave Policy", notificationArguments.get(0).getData().get("leavePolicyName"));
            assertTrue(notificationArguments.get(0).getData().get("link").endsWith("/organization-settings/company-policies/time-off/time-off-policy/1"));

            assertEquals(NotificationType.TimeoffPolicyUpdatedEmailToAdmin, notificationArguments.get(1).getType());
            assertEquals("<EMAIL>", notificationArguments.get(1).getTo());
            assertEquals("Frank", notificationArguments.get(1).getData().get("recipientName"));
            assertEquals("Russell Hall", notificationArguments.get(1).getData().get("updatedBy"));
            assertEquals("Annual Leave Policy", notificationArguments.get(1).getData().get("leavePolicyName"));
            assertTrue(notificationArguments.get(1).getData().get("link").endsWith("/organization-settings/company-policies/time-off/time-off-policy/1"));

        }

    }

    @Nested
    class sendTimeoffPolicyDeletedNotification {

        @Captor
        ArgumentCaptor<PigeonEmailNotificationData> pigeonEmailNotificationDataArgumentCaptor;

        @Test
        void should_successfully_send_timeoff_policy_deleted_notification() {
            when(currentUser.getContext()).thenReturn(
                    new UserContext(
                            101L,
                            "<EMAIL>",
                            "Ops Experience",
                            null,
                            null,
                            "Russell",
                            "Hall",
                            null,
                            new UserScopes(101L, 1L, 102L, null, null, null, false),
                            null,
                            List.of()
                    )
            );

            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(
                    CompanyOuterClass.CompanyUser.newBuilder()
                            .setCompanyId(1L)
                            .build()
            );

            when(companyServiceAdapter.getCompanyAdmins(1L)).thenReturn(
                    List.of(
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("David ")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build(),
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("Frank")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build()
                    )
            );

            timeoffNotificationHelper.sendTimeoffPolicyDeletedNotification(1L, "Annual Leave Policy");

            verify(pigeonNotificationService, times(2)).sendEmail(pigeonEmailNotificationDataArgumentCaptor.capture());

            val notificationArguments = pigeonEmailNotificationDataArgumentCaptor.getAllValues();

            assertEquals(NotificationType.TimeoffPolicyDeletedEmailToAdmin, notificationArguments.get(0).getType());
            assertEquals("<EMAIL>", notificationArguments.get(0).getTo());
            assertEquals("David", notificationArguments.get(0).getData().get("recipientName"));
            assertEquals("Russell Hall", notificationArguments.get(0).getData().get("deletedBy"));
            assertEquals("Annual Leave Policy", notificationArguments.get(0).getData().get("leavePolicyName"));
            assertTrue(notificationArguments.get(0).getData().get("link").endsWith("/organization-settings/company-policies/time-off/time-off-policy/1"));

            assertEquals(NotificationType.TimeoffPolicyDeletedEmailToAdmin, notificationArguments.get(1).getType());
            assertEquals("<EMAIL>", notificationArguments.get(1).getTo());
            assertEquals("Frank", notificationArguments.get(1).getData().get("recipientName"));
            assertEquals("Russell Hall", notificationArguments.get(1).getData().get("deletedBy"));
            assertEquals("Annual Leave Policy", notificationArguments.get(1).getData().get("leavePolicyName"));
            assertTrue(notificationArguments.get(1).getData().get("link").endsWith("/organization-settings/company-policies/time-off/time-off-policy/1"));

        }

    }

    @Nested
    class sendTimeoffPolicyAssignmentNotification {

        @Captor
        ArgumentCaptor<PigeonEmailNotificationData> pigeonEmailNotificationDataArgumentCaptor;

        @Test
        void should_successfully_send_timeoff_policy_assignment_notification_when_rule_type_is_all() {
            when(currentUser.getContext()).thenReturn(
                    new UserContext(
                            101L,
                            "<EMAIL>",
                            "Ops Experience",
                            null,
                            null,
                            "Russell",
                            "Hall",
                            null,
                            new UserScopes(101L, 1L, 102L, null, null, null, false),
                            null,
                            List.of()
                    )
            );

            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(
                    CompanyOuterClass.CompanyUser.newBuilder()
                            .setCompanyId(1L)
                            .build()
            );

            when(companyServiceAdapter.getCompanyAdmins(1L)).thenReturn(
                    List.of(
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("David ")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build(),
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("Frank")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build()
                    )
            );

            timeoffNotificationHelper.sendTimeoffPolicyAssignmentNotification(1L, "Annual Leave Policy", 5, RuleInput.newBuilder().type(RuleType.ALL).build());

            verify(pigeonNotificationService, times(2)).sendEmail(pigeonEmailNotificationDataArgumentCaptor.capture());

            val notificationArguments = pigeonEmailNotificationDataArgumentCaptor.getAllValues();

            assertEquals(NotificationType.TimeoffPolicyAssignmentEmailToAdmin, notificationArguments.get(0).getType());
            assertEquals("<EMAIL>", notificationArguments.get(0).getTo());
            assertEquals("David", notificationArguments.get(0).getData().get("recipientName"));
            assertEquals("Annual Leave Policy", notificationArguments.get(0).getData().get("leavePolicyName"));
            assertEquals(LocalDate.now().toString(), notificationArguments.get(0).getData().get("effectiveFrom"));
            assertEquals("5", notificationArguments.get(0).getData().get("applicableEmployees"));
            assertEquals("All employees", notificationArguments.get(0).getData().get("applicableTo"));
            assertTrue(notificationArguments.get(0).getData().get("link").endsWith("/organization-settings/company-policies/time-off/time-off-policy/1"));

            assertEquals(NotificationType.TimeoffPolicyAssignmentEmailToAdmin, notificationArguments.get(1).getType());
            assertEquals("<EMAIL>", notificationArguments.get(1).getTo());
            assertEquals("Frank", notificationArguments.get(1).getData().get("recipientName"));
            assertEquals("Annual Leave Policy", notificationArguments.get(1).getData().get("leavePolicyName"));
            assertEquals(LocalDate.now().toString(), notificationArguments.get(1).getData().get("effectiveFrom"));
            assertEquals("5", notificationArguments.get(1).getData().get("applicableEmployees"));
            assertEquals("All employees", notificationArguments.get(1).getData().get("applicableTo"));
            assertTrue(notificationArguments.get(1).getData().get("link").endsWith("/organization-settings/company-policies/time-off/time-off-policy/1"));
        }

        @Test
        void should_successfully_send_timeoff_policy_assignment_notification_when_rule_type_is_by_condition() {
            when(currentUser.getContext()).thenReturn(
                    new UserContext(
                            101L,
                            "<EMAIL>",
                            "Ops Experience",
                            null,
                            null,
                            "Russell",
                            "Hall",
                            null,
                            new UserScopes(101L, 1L, 102L, null, null, null, false),
                            null,
                            List.of()
                    )
            );

            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(
                    CompanyOuterClass.CompanyUser.newBuilder()
                            .setCompanyId(1L)
                            .build()
            );

            when(companyServiceAdapter.getCompanyAdmins(1L)).thenReturn(
                    List.of(
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("David ")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build(),
                            CompanyOuterClass.CompanyUser.newBuilder()
                                    .setFirstName("Frank")
                                    .addEmails(0, Common.EmailAddress.newBuilder().setType(Keys.JSON_COLLECTION.PRIMARY).setEmail("<EMAIL>"))
                                    .build()
                    )
            );

            timeoffNotificationHelper.sendTimeoffPolicyAssignmentNotification(
                    1L,
                    "Annual Leave Policy",
                    5,
                    RuleInput.newBuilder()
                            .type(RuleType.BY_CONDITION)
                            .conditions(List.of(
                                    ConditionInput.newBuilder()
                                            .key(ConditionKey.DEPARTMENT)
                                            .operator(ConditionOperator.EQUALS)
                                            .values(List.of("Engineering", "Product"))
                                            .build(),
                                    ConditionInput.newBuilder()
                                            .key(ConditionKey.COUNTRY)
                                            .operator(ConditionOperator.NOT_EQUALS)
                                            .values(List.of("Canada", "Germany"))
                                            .build()
                            ))
                            .build()
            );

            verify(pigeonNotificationService, times(2)).sendEmail(pigeonEmailNotificationDataArgumentCaptor.capture());

            val notificationArguments = pigeonEmailNotificationDataArgumentCaptor.getAllValues();

            assertEquals(NotificationType.TimeoffPolicyAssignmentEmailToAdmin, notificationArguments.get(0).getType());
            assertEquals("<EMAIL>", notificationArguments.get(0).getTo());
            assertEquals("David", notificationArguments.get(0).getData().get("recipientName"));
            assertEquals("Annual Leave Policy", notificationArguments.get(0).getData().get("leavePolicyName"));
            assertEquals(LocalDate.now().toString(), notificationArguments.get(0).getData().get("effectiveFrom"));
            assertEquals("5", notificationArguments.get(0).getData().get("applicableEmployees"));
            assertEquals("Department is Engineering, Product & Country is not Canada, Germany", notificationArguments.get(0).getData().get("applicableTo"));
            assertTrue(notificationArguments.get(0).getData().get("link").endsWith("/organization-settings/company-policies/time-off/time-off-policy/1"));

            assertEquals(NotificationType.TimeoffPolicyAssignmentEmailToAdmin, notificationArguments.get(1).getType());
            assertEquals("<EMAIL>", notificationArguments.get(1).getTo());
            assertEquals("Frank", notificationArguments.get(1).getData().get("recipientName"));
            assertEquals("Annual Leave Policy", notificationArguments.get(1).getData().get("leavePolicyName"));
            assertEquals(LocalDate.now().toString(), notificationArguments.get(1).getData().get("effectiveFrom"));
            assertEquals("5", notificationArguments.get(1).getData().get("applicableEmployees"));
            assertEquals("Department is Engineering, Product & Country is not Canada, Germany", notificationArguments.get(1).getData().get("applicableTo"));
            assertTrue(notificationArguments.get(1).getData().get("link").endsWith("/organization-settings/company-policies/time-off/time-off-policy/1"));

        }

    }
}
