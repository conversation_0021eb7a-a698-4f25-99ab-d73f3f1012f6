package com.multiplier.timeoff.service

import com.multiplier.timeoff.repository.model.CountryDefinitionEntity
import com.multiplier.timeoff.types.CountryCode
import com.multiplier.timeoff.types.TimeOffUnit
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class CountryTimeOffDefinitionFileRowKtTest {

    @Test
    fun `should map country code`() {
        val input = createRow(countryCode = "CHE")

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.countryCode).isEqualTo(CountryCode.CHE)
    }

    @Test
    fun `should map country code to null if not present`() {
        val input = createRow(countryCode = "")

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.countryCode).isNull()
    }

    @Test
    fun `should map state code`() {
        val input = createRow(stateCode = "TX")

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.stateCode).isEqualTo("TX")
    }

    @Test
    fun `should map state to null if not present`() {
        val input = createRow(stateCode = "")

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.stateCode).isNull()
    }

    @Test
    fun `should map allocation basis`() {
        val input = createRow(allocationBasis = "MONTHLY")

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.basis).isEqualTo("MONTHLY")
    }

    @Test
    fun `should map allocation basis to ANNUAL if not present`() {
        val input = createRow(allocationBasis = "")

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.basis).isEqualTo("ANNUAL")
    }

    @Test
    fun `should map validation`() {
        val input = createRow(
            allocationUnit = "DAYS",
            allocationDefault = "7",
            allocationRange = "1:10",
            allocationAllowedContractStatuses = "ONBOARDING,ACTIVE"
        )

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>().definition?.validations?.first()

        assertThat(result?.unit).isEqualTo(TimeOffUnit.DAYS)
        assertThat(result?.defaultValue).isEqualTo(7.0)
        assertThat(result?.minimumValue).isEqualTo(1.0)
        assertThat(result?.maximumValue).isEqualTo(10.0)
        assertThat(result?.allowedContractStatuses).containsExactlyInAnyOrder("ONBOARDING", "ACTIVE")
    }

    @Test
    fun `should set minimum to null when it is -1`() {
        val input = createRow(
            allocationRange = "-1:10",
        )

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>().definition?.validations?.first()

        assertThat(result?.minimumValue).isNull()
        assertThat(result?.maximumValue).isEqualTo(10.0)
    }

    @Test
    fun `should set maximum to null when it is -1`() {
        val input = createRow(
            allocationRange = "10:-1",
        )

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>().definition?.validations?.first()

        assertThat(result?.minimumValue).isEqualTo(10.0)
        assertThat(result?.maximumValue).isEqualTo(null)
    }

    @Test
    fun `should map is required`() {
        val input = createRow(isMandatory = "true")

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.isRequired).isTrue()
    }

    @Test
    fun `should map is required to false if not present`() {
        val input = createRow(isMandatory = "")

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.isRequired).isFalse()
    }

    @Test
    fun `should map allocation config`() {
        val input = createRow(
            configAllocationBasis = "MONTHLY",
            configAllocationIsProrated = "true"
        )

        val result =
            input.toTimeOffDefinitionEntity<CountryDefinitionEntity>().definition?.configurations?.allocationConfig

        assertThat(result?.basis).isEqualTo("MONTHLY")
        assertThat(result?.prorated).isTrue()
    }

    @Test
    fun `should map default allocation config if values are not present`() {
        val input = createRow(
            configAllocationBasis = "",
            configAllocationIsProrated = ""
        )

        val result =
            input.toTimeOffDefinitionEntity<CountryDefinitionEntity>().definition?.configurations?.allocationConfig

        assertThat(result?.basis).isEqualTo("ANNUAL")
        assertThat(result?.prorated).isFalse()
    }

    @Test
    fun `should map description`() {
        val input = createRow(description = "description")

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.description).isEqualTo("description")
    }

    @Test
    fun `should map clause`() {
        val input = createRow(clause = "santa clause")

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.clause).isEqualTo("santa clause")
    }


    @Test
    fun `given columns, when carry-forward are missing, then map to null`() {
        val input = createRow().sliceArray(0..13)

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.configurations?.carryForwardConfig).isNull()
    }

    @Test
    fun `given columns, when carry-forward are present, then map to instance`() {
        val input = createRow(
            config_carry_enabled = "TRUE",
            config_carry_type = "PERCENTAGE",
            config_carry_unit = "DAYS",
            config_carry_eligibility = "15",
            config_carry_limit = "50",
            config_carry_expiry = "6",
            config_carry_expiry_unit = "MONTHS",
            )

        val result = input.toTimeOffDefinitionEntity<CountryDefinitionEntity>()

        assertThat(result.definition?.configurations?.carryForwardConfig).isNotNull
    }


    private fun createRow(
        countryCode: String = "",
        stateCode: String = "",
        leaveType: String = "",
        leaveTypeId: String = "",
        allocationBasis: String = "",
        allocationUnit: String = "",
        allocationDefault: String = "",
        allocationRange: String = "",
        allocationAllowedContractStatuses: String = "",
        isMandatory: String = "",
        configAllocationBasis: String = "",
        configAllocationIsProrated: String = "",
        description: String = "",
        clause: String = "",
        config_carry_enabled: String = "",
        config_carry_type: String = "",
        config_carry_unit: String = "",
        config_carry_eligibility: String = "",
        config_carry_limit: String = "",
        config_carry_expiry: String = "",
        config_carry_expiry_unit: String = "",

        ): Array<String> = arrayOf(

        countryCode,
        stateCode,
        leaveType,
        leaveTypeId,
        allocationBasis,
        allocationUnit,
        allocationDefault,
        allocationRange,
        allocationAllowedContractStatuses,
        isMandatory,
        configAllocationBasis,
        configAllocationIsProrated,
        description,
        clause,
        config_carry_enabled,
        config_carry_type,
        config_carry_unit,
        config_carry_eligibility,
        config_carry_limit,
        config_carry_expiry,
        config_carry_expiry_unit,
    )
}