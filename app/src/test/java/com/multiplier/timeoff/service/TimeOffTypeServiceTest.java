package com.multiplier.timeoff.service;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.common.transport.user.UserScopes;
import com.multiplier.timeoff.repository.CompanyDefinitionRepository;
import com.multiplier.timeoff.repository.TimeoffTypeRepository;
import com.multiplier.timeoff.repository.model.CompanyDefinitionEntity;
import com.multiplier.timeoff.repository.model.DefinitionEntity;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.TimeoffTypeMapper;
import com.multiplier.timeoff.service.notifications.TimeoffNotificationHelper;
import com.multiplier.timeoff.types.*;
import lombok.val;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.access.AccessDeniedException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class TimeOffTypeServiceTest {

    @Mock
    private CurrentUser currentUser;
    @Mock
    TimeoffTypeRepository timeoffTypeRepository;
    @Mock
    TimeoffTypeMapper timeoffTypeMapper;
    @Mock
    CompanyDefinitionRepository companyDefinitionRepository;
    @Mock
    TimeoffNotificationHelper timeoffNotificationHelper;
    @InjectMocks
    private TimeoffTypeService timeoffTypeService;

    @Nested
    class CreateTimeOffTypeTest {

        @Captor
        private ArgumentCaptor<TimeoffTypeDBO> timeoffTypeDBOArgumentCaptor;

        @Test
        void should_throw_validation_exception_when_current_user_is_not_a_company_user() {
            TimeOffTypeCreateInput timeOffTypeCreateInput = TimeOffTypeCreateInput.newBuilder()
                    .name("Annual leave")
                    .isPaidLeave(true)
                    .build();
            val memberId = 101L;

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId));

            val ex = assertThrows(ValidationException.class, () ->
                    timeoffTypeService.createTimeOffType(timeOffTypeCreateInput));

            assertTrue(ex.getMessage().contains("Cannot find company id of the current user"));
        }

        @Test
        void should_return_null_when_feature_off() {
            TimeOffTypeCreateInput timeOffTypeCreateInput = TimeOffTypeCreateInput.newBuilder()
                    .name("Annual leave")
                    .isPaidLeave(true)
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));

            val result = timeoffTypeService.createTimeOffType(timeOffTypeCreateInput);
            assertNull(result);
        }

        @Test
        void should_throw_illegal_argument_error_when_timeoff_type_name_is_empty() {
            TimeOffTypeCreateInput timeOffTypeCreateInput = TimeOffTypeCreateInput.newBuilder()
                    .name("")
                    .isPaidLeave(true)
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));

            var ex = assertThrows(IllegalArgumentException.class, () -> timeoffTypeService.createTimeOffType(timeOffTypeCreateInput));
            assertTrue(ex.getMessage().contains("Can not create time-off types with empty name"));
        }

        @Test
        void should_throw_validation_error_when_timeoff_type_already_exists() {
            TimeOffTypeCreateInput timeOffTypeCreateInput = TimeOffTypeCreateInput.newBuilder()
                    .name("Annual leave")
                    .isPaidLeave(true)
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeRepository.existsByKeyAndCompanyId("annualLeave", companyId))
                    .thenReturn(true);

            val ex = assertThrows(ValidationException.class, () -> timeoffTypeService.createTimeOffType(timeOffTypeCreateInput));
            assertTrue(ex.getMessage().contains("Can not create time-off type Annual leave for company id 100, since its already exist"));
        }

        @Test
        void should_create_time_off_type_successfully() {
            TimeOffTypeCreateInput timeOffTypeCreateInput = TimeOffTypeCreateInput.newBuilder()
                    .name("Annual leave")
                    .isPaidLeave(true)
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;


            val userContext = getCompanyUserUserDetails(companyUserId, companyId);
            when(currentUser.getContext()).thenReturn(userContext);
            when(timeoffTypeRepository.existsByKeyAndCompanyId("annualLeave", companyId))
                    .thenReturn(false);
            when(timeoffTypeRepository.save(any(TimeoffTypeDBO.class))).thenReturn(TimeoffTypeDBO.builder().build());

            timeoffTypeService.createTimeOffType(timeOffTypeCreateInput);

            verify(timeoffTypeRepository).save(timeoffTypeDBOArgumentCaptor.capture());
            verify(timeoffTypeMapper).mapToGraph(any(TimeoffTypeDBO.class));
            verify(timeoffNotificationHelper).sendTimeoffTypeCreatedEmail("Annual leave");
            val createdType = timeoffTypeDBOArgumentCaptor.getValue();

            assertEquals("annualLeave", createdType.key());
            assertEquals("Annual leave", createdType.label());
            assertEquals(createdType.companyId(), companyId);
            assertEquals(TimeOffTypeStatus.ACTIVE, createdType.status());
            assertTrue(createdType.isPaidLeave());
            assertEquals(createdType.createdBy(), userContext.getId());

        }
    }

    @Nested
    class DeleteTimeOffTypeTest {

        @Captor
        private ArgumentCaptor<TimeoffTypeDBO> timeoffTypeDBOArgumentCaptor;

        @Test
        void should_return_null_when_feature_off() {
            val companyUserId = 101L;
            val companyId = 100L;
            val typeId = 1L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));

            val result = timeoffTypeService.deleteTimeOffType(typeId);
            assertNull(result);
        }

        @Test
        void should_return_null_when_time_off_type_not_present_to_delete() {
            val companyUserId = 101L;
            val companyId = 100L;
            val typeId = 1L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeRepository.findByIdAndCompanyIdAndStatusNotIn(typeId, companyId, Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(null);
            val result = timeoffTypeService.deleteTimeOffType(typeId);
            assertNull(result);
        }

        @Test
        void should_throw_validation_error_when_try_to_delete_global_time_off_type() {
            val companyUserId = 101L;
            val companyId = 100L;
            val typeId = 1L;
            val timeOffTypeDBO = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(null) // global leave type, not belongs to any company
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeRepository.findByIdAndCompanyIdAndStatusNotIn(typeId, companyId, Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(timeOffTypeDBO);
            val ex = assertThrows(ValidationException.class, () -> timeoffTypeService.deleteTimeOffType(typeId));
            assertTrue(ex.getMessage().contains("Can not delete global time-off type id: 1"));
        }

        @Test
        void should_throw_validation_error_when_try_to_delete_time_off_type_belongs_to_another_company() {
            val companyUserId = 101L;
            val companyId = 100L;
            val typeId = 1L;
            val timeOffTypeDBO = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(101L)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeRepository.findByIdAndCompanyIdAndStatusNotIn(typeId, companyId, Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(timeOffTypeDBO);
            val ex = assertThrows(ValidationException.class, () -> timeoffTypeService.deleteTimeOffType(typeId));
            assertTrue(ex.getMessage().contains("Can not delete time-off type id : 1, company id 100 since it belongs to company id : 101"));
        }

        @Test
        void should_throw_validation_error_when_try_to_delete_time_off_type_with_active_policies() {
            val companyUserId = 101L;
            val companyId = 100L;
            val typeId = 1L;
            val timeOffTypeDBO = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(companyId)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeRepository.findByIdAndCompanyIdAndStatusNotIn(typeId, companyId, Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(timeOffTypeDBO);
            when(companyDefinitionRepository.countNonDeletedByTypeId(typeId)).thenReturn(1L);
            val ex = assertThrows(ValidationException.class, () -> timeoffTypeService.deleteTimeOffType(typeId));
            assertTrue(ex.getMessage().contains("Can not delete time-off type id : 1 since, there are existing policies associated with it"));
        }

        @Test
        void should_delete_time_off_type_successfully() {
            val companyUserId = 101L;
            val companyId = 100L;
            val typeId = 1L;
            val timeOffTypeDBO = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(companyId)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeRepository.findByIdAndCompanyIdAndStatusNotIn(typeId, companyId, Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(timeOffTypeDBO);
            when(companyDefinitionRepository.countNonDeletedByTypeId(typeId)).thenReturn(0L);
            when(timeoffTypeRepository.save(any(TimeoffTypeDBO.class))).thenReturn(timeOffTypeDBO);

            timeoffTypeService.deleteTimeOffType(typeId);

            verify(timeoffTypeRepository).save(timeoffTypeDBOArgumentCaptor.capture());
            verify(timeoffTypeMapper).mapToGraph(any(TimeoffTypeDBO.class));
            verify(timeoffNotificationHelper).sendTimeoffTypeDeletedMail(any());

            val createdType = timeoffTypeDBOArgumentCaptor.getValue();

            assertEquals(TimeOffTypeStatus.DELETED, createdType.status());
        }


    }

    @Nested
    class FindCompanyTimeOffTypeTest {

        @Test
        void should_return_empty_list_when_feature_off() {
            TimeOffTypeFilter timeOffTypeFilter = TimeOffTypeFilter.newBuilder()
                    .ids(List.of(100L))
                    .isPaidLeave(true)
                    .build();
            val companyId = 100L;
            val result = timeoffTypeService.findCompanyTimeOffTypes(companyId, timeOffTypeFilter);
            assertTrue(result.isEmpty());
        }

        @Test
        void should_return_all_company_time_off_types_when_filter_is_null() {
            val companyId = 100L;
            when(timeoffTypeRepository.findAllForCompany(companyId))
                    .thenReturn(List.of(TimeoffTypeDBO.builder().build()));

            timeoffTypeService.findCompanyTimeOffTypes(companyId, null);

            verify(timeoffTypeRepository).findAllForCompany(companyId);
            verify(timeoffTypeRepository, never()).findAllByCompanyIdAndIsPaidLeaveAndStatusNotIn(eq(companyId), anyBoolean(), anyCollection());
            verify(timeoffTypeRepository, never()).findByIdInAndCompanyIdAndStatusNotIn(any(), any(), any());

        }

        @Test
        void should_return_time_off_types_by_id_when_filter_id_is_populated() {
            TimeOffTypeFilter timeOffTypeFilter = TimeOffTypeFilter.newBuilder()
                    .ids(List.of(100L))
                    .isPaidLeave(true)
                    .build();

            val companyId = 100L;

            when(timeoffTypeRepository.findByIdInAndCompanyIdAndStatusNotIn(List.of(100L), companyId, Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(List.of(TimeoffTypeDBO.builder().build()));
            when(timeoffTypeMapper.mapToGraph(anyList())).thenReturn(List.of(TimeOffTypeInfo.newBuilder().build()));

            timeoffTypeService.findCompanyTimeOffTypes(companyId, timeOffTypeFilter);

            verify(timeoffTypeRepository).findByIdInAndCompanyIdAndStatusNotIn(List.of(100L), companyId, Set.of(TimeOffTypeStatus.DELETED));
            verify(timeoffTypeRepository, never()).findAllForCompany(companyId);
            verify(timeoffTypeRepository, never()).findAllByCompanyIdAndIsPaidLeaveAndStatusNotIn(eq(companyId), anyBoolean(), anyCollection());
        }

        @Test
        void should_return_time_off_types_by_is_paid_leave_when_filter_id_is__not_populated() {
            TimeOffTypeFilter timeOffTypeFilter = TimeOffTypeFilter.newBuilder()
                    .isPaidLeave(true)
                    .build();

            val companyId = 100L;

            when(timeoffTypeRepository.findAllByCompanyIdAndIsPaidLeaveAndStatusNotIn(companyId, true, Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(List.of(TimeoffTypeDBO.builder().build()));
            when(timeoffTypeMapper.mapToGraph(anyList())).thenReturn(List.of(TimeOffTypeInfo.newBuilder().build()));

            timeoffTypeService.findCompanyTimeOffTypes(companyId, timeOffTypeFilter);

            verify(timeoffTypeRepository, never()).findByIdInAndCompanyIdAndStatusNotIn(any(), any(), any());
            verify(timeoffTypeRepository, never()).findAllForCompany(companyId);
            verify(timeoffTypeRepository).findAllByCompanyIdAndIsPaidLeaveAndStatusNotIn(companyId, true, Set.of(TimeOffTypeStatus.DELETED));
        }

    }

    @Nested
    class UpdateTimeOffTypeTest {

        @Captor
        private ArgumentCaptor<TimeoffTypeDBO> timeoffTypeDBOArgumentCaptor;

        @Test
        void should_throw_validation_exception_when_current_user_is_not_a_company_user() {
            TimeOffTypeUpdateInput timeOffTypeUpdateInput = TimeOffTypeUpdateInput.newBuilder()
                    .name("Annual leave update")
                    .build();
            val memberId = 101L;

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId));

            val ex = assertThrows(ValidationException.class, () ->
                    timeoffTypeService.updateTimeOffType(timeOffTypeUpdateInput));

            assertTrue(ex.getMessage().contains("Cannot find company id of the current user"));
        }

        @Test
        void should_return_null_when_feature_off() {
            TimeOffTypeUpdateInput timeOffTypeUpdateInput = TimeOffTypeUpdateInput.newBuilder()
                    .name("Annual leave update")
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));

            val result = timeoffTypeService.updateTimeOffType(timeOffTypeUpdateInput);
            assertNull(result);
        }

        @Test
        void should_return_null_when_no_time_off_type_exists_to_update() {
            TimeOffTypeUpdateInput timeOffTypeUpdateInput = TimeOffTypeUpdateInput.newBuilder()
                    .typeId(100L)
                    .name("Annual leave update")
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeRepository.findByIdAndStatusNotIn(timeOffTypeUpdateInput.getTypeId(), Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(null);

            val result = timeoffTypeService.updateTimeOffType(timeOffTypeUpdateInput);
            assertNull(result);
        }

        @Test
        void should_throw_validation_error_when_try_to_update_other_company_time_off_type() {
            val typeId = 100L;
            TimeOffTypeUpdateInput timeOffTypeUpdateInput = TimeOffTypeUpdateInput.newBuilder()
                    .typeId(typeId)
                    .name("Annual leave update")
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;

            val timeOffTypeDBO = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(200L)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeRepository.findByIdAndStatusNotIn(timeOffTypeUpdateInput.getTypeId(), Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(timeOffTypeDBO);

            val ex = assertThrows(ValidationException.class, () ->
                    timeoffTypeService.updateTimeOffType(timeOffTypeUpdateInput));
            assertTrue(ex.getMessage().contains("Can not update time off types id:100 of company id:200 by company user in company id:100"));
        }

        @Test
        void should_throw_validation_error_when_try_to_update_global_time_off_type() {
            val typeId = 100L;
            TimeOffTypeUpdateInput timeOffTypeUpdateInput = TimeOffTypeUpdateInput.newBuilder()
                    .typeId(typeId)
                    .name("Annual leave update")
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;

            val timeOffTypeDBO = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(null)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeRepository.findByIdAndStatusNotIn(timeOffTypeUpdateInput.getTypeId(), Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(timeOffTypeDBO);

            val ex = assertThrows(ValidationException.class, () ->
                    timeoffTypeService.updateTimeOffType(timeOffTypeUpdateInput));
            assertTrue(ex.getMessage().contains("Can not update time off types id:100 of company id:null by company user in company id:100"));
        }

        @Test
        void should_update_company_time_off_type_successfully() {
            val typeId = 100L;
            TimeOffTypeUpdateInput timeOffTypeUpdateInput = TimeOffTypeUpdateInput.newBuilder()
                    .typeId(typeId)
                    .name("Annual leave update")
                    .description("For unit testing")
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;

            val timeOffTypeDBO = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .key("AnnualLeave")
                    .label("Annual Leave")
                    .description("Annual leaves for all employees")
                    .status(TimeOffTypeStatus.ACTIVE)
                    .companyId(companyId)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeRepository.findByIdAndStatusNotIn(timeOffTypeUpdateInput.getTypeId(), Set.of(TimeOffTypeStatus.DELETED)))
                    .thenReturn(timeOffTypeDBO);
            when(timeoffTypeRepository.save(any(TimeoffTypeDBO.class))).thenReturn(TimeoffTypeDBO.builder().build());

            timeoffTypeService.updateTimeOffType(timeOffTypeUpdateInput);

            verify(timeoffTypeRepository).save(timeoffTypeDBOArgumentCaptor.capture());
            verify(timeoffTypeMapper).mapToGraph(any(TimeoffTypeDBO.class));
            verify(timeoffNotificationHelper).sendTimeoffTypeUpdatedEmail(any());

            val updatedType = timeoffTypeDBOArgumentCaptor.getValue();
            assertEquals("annualLeaveUpdate", updatedType.key());
            assertEquals( "Annual leave update", updatedType.label());
            assertEquals(companyId, updatedType.companyId());
            assertEquals(TimeOffTypeStatus.ACTIVE, updatedType.status());
        }


    }

    @Nested
    class findTimeOffTypeDBOsForCompanyTest {

       @Test
       void should_return_company_timeoffs_for_system_call(){
           val companyId = 100L;
           List<TimeoffTypeDBO> timeoffs = List.of(TimeoffTypeDBO.builder().id(1L).companyId(companyId).build());
           when(timeoffTypeRepository.findAllForCompany(companyId)).thenReturn(timeoffs);

           val result = timeoffTypeService.findTimeOffTypeDBOsForCompany(companyId);

           assertEquals(1, result.size());
           assertEquals(1L, result.get(0).id());
       }

        @Test
        void should_return_company_timeoffs_for_ops_user(){
            val companyId = 100L;
            when(currentUser.getContext()).thenReturn(getOpsUserDetails(101L));
            List<TimeoffTypeDBO> timeoffs = List.of(TimeoffTypeDBO.builder().id(1L).companyId(companyId).build());
            when(timeoffTypeRepository.findAllForCompany(companyId)).thenReturn(timeoffs);

            val result = timeoffTypeService.findTimeOffTypeDBOsForCompany(companyId);

            assertEquals(1, result.size());
            assertEquals(1L, result.get(0).id());
        }

        @Test
        void should_return_company_timeoffs_for_company_user(){
            val companyId = 100L;
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(101L, companyId));
            List<TimeoffTypeDBO> timeoffs = List.of(TimeoffTypeDBO.builder().id(1L).companyId(companyId).build());
            when(timeoffTypeRepository.findAllForCompany(companyId)).thenReturn(timeoffs);

            val result = timeoffTypeService.findTimeOffTypeDBOsForCompany(companyId);

            assertEquals(1, result.size());
            assertEquals(1L, result.get(0).id());
        }

        @Test
        void should_throw_error_when_company_user_try_to_fetch_other_company_data(){
            val companyId = 100L;
            val companyUsercompanyId = 400L;
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(234L, companyUsercompanyId));

            val ex = assertThrows(AccessDeniedException.class, () ->
                    timeoffTypeService.findTimeOffTypeDBOsForCompany(companyId));

            assertTrue(ex.getMessage().contains("Access denied for user id : 10 to access/modify data of company id : 100"));
        }

    }

    @Nested
    class FetchEntityTimeOffTypes {
        @Test
        void fetchEntityTimeOffTypes_shouldReturnTimeOffTypes() {
            Long companyId = 1L;
            Long entityId = 2L;

            val definition1 = new DefinitionEntity();
            val definition2 = new DefinitionEntity();
            val allCompanyDefinitions = List.of(
                    new CompanyDefinitionEntity(100L, 200L, definition1, companyId, null, null, new ArrayList<>()),
                    new CompanyDefinitionEntity(101L, 201L, definition2, companyId, null, null, new ArrayList<>())
            );
            val typesList = List.of(
                    TimeoffTypeDBO.builder().id(200L).key("annualLeave").build(),
                    TimeoffTypeDBO.builder().id(201L).key("sickLeave").build(),
                    TimeoffTypeDBO.builder().id(14L).key("unpaidLeave").build()
            );

            when(companyDefinitionRepository.findAllNonDeletedByCompanyIdAndEntityIdAndEntityType(companyId, entityId, EntityType.COMPANY_ENTITY))
                    .thenReturn(allCompanyDefinitions);
            when(timeoffTypeRepository.findAllById(List.of(200L, 201L, 14L)))
                    .thenReturn(typesList);

            List<TimeoffTypeDBO> actualTimeOffTypes = timeoffTypeService.fetchEntityTimeOffTypes(companyId, entityId, EntityType.COMPANY_ENTITY);

            assertEquals(typesList, actualTimeOffTypes);
            assertEquals(2, allCompanyDefinitions.size());
            assertEquals(3, actualTimeOffTypes.size());
        }
    }

    private UserContext getCompanyUserUserDetails(Long companyUserId, Long companyId) {
        return new UserContext(
                10L,
                "username",
                "company",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        null,
                        companyId,
                        null,
                        companyUserId,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

    private UserContext getMemberUserDetails(long memberId) {
        return new UserContext(
                10L,
                "username",
                "member",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        memberId,
                        null,
                        null,
                        null,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

    private UserContext getOpsUserDetails(Long opsUserId) {
        return new UserContext(
                10L,
                "username",
                "operations",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        null,
                        null,
                        null,
                        null,
                        null,
                        opsUserId,
                        false
                ),
                "type",
                List.of()
        );
    }
}
