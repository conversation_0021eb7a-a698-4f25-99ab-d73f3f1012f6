package com.multiplier.timeoff.service.builder;

import com.multiplier.timeoff.core.common.builder.SortDefinition;
import com.multiplier.timeoff.core.common.builder.SortDirection;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import org.junit.jupiter.api.Test;
import org.springframework.data.jpa.domain.Specification;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class TimeoffSummarySpecificationBuilderTest {
    private final TimoffSummarySpecificationBuilder builder = new TimoffSummarySpecificationBuilder();

    @Test
    void testBuildSorting_withNullSpecAndValues() {
        Specification<TimeoffSummaryDBO> spec = null;
        Specification<TimeoffSummaryDBO> result = builder.buildSorting(spec, null);

        assertNull(result);
    }

    @Test
    void testBuildSorting_withNonNullSpecAndEmptyValues() {
        Specification<TimeoffSummaryDBO> spec = Specification.where(null);
        Specification<TimeoffSummaryDBO> result = builder.buildSorting(spec, Collections.emptyList());

        assertNotNull(result);
    }

    @Test
    void testBuildSorting_withNonNullSpecAndValues() {
        Specification<TimeoffSummaryDBO> spec = Specification.where(null);
        SortDefinition sortDefinition = SortDefinition.builder()
                .columns(Set.of("column1", "column2"))
                .direction(SortDirection.ASCENDING)
                .build();

        Specification<TimeoffSummaryDBO> result = builder.buildSorting(spec, Arrays.asList(sortDefinition));

        assertNotNull(result);
    }
}
