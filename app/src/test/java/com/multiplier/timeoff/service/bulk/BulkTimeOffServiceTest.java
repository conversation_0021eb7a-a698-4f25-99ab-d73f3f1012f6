package com.multiplier.timeoff.service.bulk;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.common.transport.user.UserScopes;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ApprovalServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.core.common.dto.*;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.processor.TimeoffValidator;
import com.multiplier.timeoff.repository.EntitlementChangeRecordRepository;
import com.multiplier.timeoff.repository.TimeoffRepository;
import com.multiplier.timeoff.repository.model.EntitlementChangeRecordEntity;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.service.TimeoffSummaryService;
import com.multiplier.timeoff.service.TimeoffTypeService;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.BulkTimeOffMapper;
import com.multiplier.growthbook.sdk.model.GBFeatureResult;
import com.multiplier.growthbook.sdk.model.GBFeatureSource;
import com.multiplier.timeoff.service.mapper.TimeoffMapper;
import com.multiplier.timeoff.types.EntityType;
import com.multiplier.timeoff.types.TimeOffStatus;
import lombok.val;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.access.AccessDeniedException;


import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.never;


@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class BulkTimeOffServiceTest {

    @Mock
    private CurrentUser currentUser;
    @Mock
    private BulkTimeOffValidator bulkTimeOffValidator;
    @Mock
    private TimeoffRepository timeoffRepository;
    @Mock
    private BulkTimeOffMapper bulkTimeOffMapper;
    @Mock
    private TimeoffTypeService timeoffTypeService;
    @Mock
    private ContractServiceAdapter contractServiceAdapter;
    @Mock
    private TimeoffSummaryService timeoffSummaryService;
    @Mock
    private EntitlementChangeRecordRepository entitlementChangeRecordRepository;
    @Mock
    private FeatureFlagService featureFlagService;
    @Mock
    private BulkTimeOffHelper bulkTimeOffHelper;
    @Mock
    private ApprovalServiceAdapter approvalServiceAdapter;
    @Mock
    private TimeoffMapper timeoffMapper;
    @InjectMocks
    private BulkTimeOffService bulkTimeOffService;


    @Nested
    class BulkUpsertTimeOffTest {

        @Captor
        private ArgumentCaptor<List<TimeoffDBO>> timeOffListArgumentCaptor;

        @Test
        void should_fail_when_try_to_upsert_for_not_other_company() {
            Long companyId = 100L;
            BulkTimeOffRequest input = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .inputs(List.of())
                    .build();

          when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(200L));
          val result = bulkTimeOffService.bulkUpsertTimeOffs(input);
          assertFalse(result.isSuccess());
          assertEquals("Bulk upsert process failed due to an exception (Access denied for user id : 10 to modify data of company id : 100)", result.getMessage());
        }

        @Test
        void should_send_success_response_when_no_items_to_upsert() {
            Long companyId = 100L;
            BulkTimeOffRequest input = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .inputs(List.of())
                    .build();
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            val result = bulkTimeOffService.bulkUpsertTimeOffs(input);
            assertTrue(result.isSuccess());
            assertEquals("Bulk upsert process succeeded", result.getMessage());
            assertTrue(result.getItems().isEmpty());
            verify(approvalServiceAdapter, never()).bulkStartApprovalAndAutoApprove(eq(companyId), anyMap());
        }

        @Test
        void should_send_failure_when_validation_fails() {
            Long companyId = 100L;
            Long entityId = 12345L;
            // Inputs with different date formats
            List<BulkTimeOffRequest.Input> bulkItems = List.of(
                    BulkTimeOffRequest.Input.builder()
                            .externalTimeOffId("001")
                            .type("annual")
                            .contractId(1L)
                            .noOfDays("2")
                            .startDate("27/11/2023")
                            .endDate("29/11/2023")
                            .build(),
                    BulkTimeOffRequest.Input.builder()
                            .externalTimeOffId("002")
                            .type("sick")
                            .contractId(2L)
                            .noOfDays("2")
                            .startDate("2023-11-25")
                            .endDate("2023-11-27")
                            .build()
            );
            BulkTimeOffRequest input = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .entityId(entityId)
                    .inputs(bulkItems)
                    .build();

            Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                    1L, ContractOuterClass.Contract.newBuilder().setId(1L).setStarted(true).setCompanyId(companyId).build(),
                    2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(companyId).build()
            );
            List<TimeoffTypeDBO> companyTimeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).label("annual").companyId(companyId).build(),
                    TimeoffTypeDBO.builder().id(2L).label("sick").companyId(companyId).build(),
                    TimeoffTypeDBO.builder().id(3L).label("maternity").companyId(companyId).build()
            );

            Set<Long> typeIds = Set.of(1L, 2L);
            List<TimeoffSummaryDBO> timeOffSummaries = List.of();
            Map<String, List<TimeoffDBO>> timeOffs = Map.of();
            List<EntitlementChangeRecordEntity> allocationRecords = List.of();
            List<EntitlementChangeRecordEntity> allDeductionECRs = List.of();

            // mocks
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            mockBulkDataLoadingMethods(companyId, entityId, typeIds,
                    idToContractMap, companyTimeOffTypes, timeOffSummaries, timeOffs, allocationRecords, allDeductionECRs, true);
            when(bulkTimeOffValidator.validate(eq(bulkItems.get(0)), any()))
                    .thenReturn(List.of("Start date Should be in format of YYYY-MM-DD."));
            when(bulkTimeOffValidator.validate(eq(bulkItems.get(1)), any()))
                    .thenReturn(List.of("Start date Should be in format of YYYY-MM-DD."));

            // when
            val result = bulkTimeOffService.bulkUpsertTimeOffs(input);

            // then
            verify(approvalServiceAdapter, never()).bulkStartApprovalAndAutoApprove(eq(companyId), anyMap());
            assertEquals("Validation errors found", result.getMessage());
            assertFalse(result.isSuccess());
        }

        @Test
        void should_upsert_bulk_timeOffs_successfully() {
            Long companyId = 100L;
            Long entityId = 12345L;
            // Inputs with different date formats
            List<BulkTimeOffRequest.Input> bulkItems = List.of(
                    BulkTimeOffRequest.Input.builder()
                            .externalTimeOffId("001")
                            .contractId(1L)
                            .type("annual")
                            .noOfDays("2")
                            .startDate("27/11/2023")
                            .endDate("29/11/2023")
                            .build(),
                    BulkTimeOffRequest.Input.builder()
                            .externalTimeOffId("002")
                            .contractId(2L)
                            .type("sick")
                            .noOfDays("2")
                            .startDate("2023-11-25")
                            .endDate("2023-11-27")
                            .build()
            );
            BulkTimeOffRequest input = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .entityId(entityId)
                    .inputs(bulkItems)
                    .build();
            List<TimeoffTypeDBO> companyTimeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).label("annual").companyId(companyId).build(),
                    TimeoffTypeDBO.builder().id(2L).label("sick").companyId(companyId).build(),
                    TimeoffTypeDBO.builder().id(3L).label("maternity").companyId(companyId).build()
            );
            Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                    1L, ContractOuterClass.Contract.newBuilder().setId(1L).setStarted(true).setCompanyId(companyId).build(),
                    2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(companyId).build()
            );

            Set<Long> typeIds = Set.of(1L, 2L);
            List<TimeoffSummaryDBO> timeOffSummaries = List.of();
            List<EntitlementChangeRecordEntity> allocationRecords = List.of();
            List<EntitlementChangeRecordEntity> allDeductionECRs = List.of();

            val externalIds = bulkItems.stream()
                    .map(BulkTimeOffRequest.Input::getExternalTimeOffId)
                    .collect(Collectors.toSet());
            List<TimeoffDBO> timeOffs = List.of(
                    TimeoffDBO.builder().id(1L).externalId("001").type(companyTimeOffTypes.get(1)).noOfDays(3.0).build()
            );
            Map<String, List<TimeoffDBO>> timeOffForContractIdAndTypeId = Map.of();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            mockBulkDataLoadingMethods(companyId, entityId, typeIds,
                    idToContractMap, companyTimeOffTypes, timeOffSummaries, timeOffForContractIdAndTypeId, allocationRecords, allDeductionECRs, true);
            when(bulkTimeOffValidator.validate(any(), any()))
                    .thenReturn(List.of());
            when(timeoffRepository.findAllByExternalIdIn(externalIds))
                    .thenReturn(timeOffs);
            when(timeoffRepository.saveAllAndFlush(any())).thenReturn(List.of());
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(1L, 2L), true))
                    .thenReturn(new ArrayList<>(idToContractMap.values()));

            // when
            val result = bulkTimeOffService.bulkUpsertTimeOffs(input);

            // then
            verify(timeoffRepository).saveAllAndFlush(timeOffListArgumentCaptor.capture());
            verify(approvalServiceAdapter).bulkStartApprovalAndAutoApprove(eq(companyId), anyMap());

            val savedTimeOffs = timeOffListArgumentCaptor.getValue();
            assertTrue(result.isSuccess());
            assertEquals("Bulk upsert process succeeded", result.getMessage());
            assertEquals(2, savedTimeOffs.size());
        }
    }

    @Nested
    class BulkValidateTimeOffTest {

        @Test
        void should_throw_error_when_current_user_is_from_different_company() {
            Long companyId = 100L;
            BulkTimeOffRequest input = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .inputs(List.of())
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(200L));
            assertThrows(AccessDeniedException.class, () -> bulkTimeOffService.validateBulkTimeOffs(input));
        }

        @Test
        void should_return_success_response_when_input_list_is_empty() {
            Long companyId = 100L;
            BulkTimeOffRequest request = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .inputs(List.of())
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));

            // when
            val result = bulkTimeOffService.validateBulkTimeOffs(request);

            assertTrue(result.isSuccess());
            assertTrue(result.getItems().isEmpty());
        }

        @Test
        void should_throw_error_when_item_ids_are_duplicated_in_bulk_list() {
            Long companyId = 500L;
            Long entityId = 12345L;
            List<BulkTimeOffRequest.Input> bulkItems = List.of(
                    BulkTimeOffRequest.Input.builder().externalTimeOffId("005").contractId(2L).type("annual").build(),
                    BulkTimeOffRequest.Input.builder().externalTimeOffId("005").contractId(3L).type("annual").build()
            );

            BulkTimeOffRequest request = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .entityId(entityId)
                    .inputs(bulkItems)
                    .build();

            Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                    2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(600L).build(),
                    3L, ContractOuterClass.Contract.newBuilder().setId(3L).setStarted(true).setCompanyId(companyId).build()
            );
            List<TimeoffTypeDBO> companyTimeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).label("annual").companyId(companyId).build()
            );
            Set<Long> typeIds = Set.of(1L);
            List<TimeoffSummaryDBO> timeOffSummaries = List.of(
                    TimeoffSummaryDBO.builder().contractId(2L).typeId(1L).allocatedCount(7.0).takenCount(5.0).pendingCount(1.0).build(),
                    TimeoffSummaryDBO.builder().contractId(3L).typeId(1L).allocatedCount(7.0).takenCount(2.0).pendingCount(1.0).build(),
                    TimeoffSummaryDBO.builder().contractId(3L).typeId(2L).allocatedCount(7.0).takenCount(2.0).pendingCount(1.0).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            mockBulkDataLoadingMethods(companyId, entityId, typeIds,
                    idToContractMap, companyTimeOffTypes, timeOffSummaries, Map.of(), List.of(), List.of(), false);

            // when
            val result = bulkTimeOffService.validateBulkTimeOffs(request);

            assertFalse(result.isSuccess());
            assertEquals(2, result.getItems().size());
            assertEquals("External id is duplicated", result.getItems().get(0).getErrors().get(0));
            assertEquals("External id is duplicated", result.getItems().get(1).getErrors().get(0));
        }

        @Test
        void should_return_fail_items_when_validate_bulk_inputs_have_invalid_data() {
            Long companyId = 500L;
            Long entityId = 12345L;
            List<BulkTimeOffRequest.Input> bulkItems = List.of(
                    BulkTimeOffRequest.Input.builder().externalTimeOffId("005").contractId(2L).type("annual").noOfDays("X").build(), // invalid -> no of days is not a number
                    BulkTimeOffRequest.Input.builder().externalTimeOffId("013").contractId(3L).type("sick").noOfDays("2").startDate("25/11/2023").endDate("27/11/2023").build()  //valid
            );

            BulkTimeOffRequest request = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .entityId(entityId)
                    .inputs(bulkItems)
                    .build();

            Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                    2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(600L).build(),
                    3L, ContractOuterClass.Contract.newBuilder().setId(3L).setStarted(true).setCompanyId(companyId).build()
            );
            List<TimeoffTypeDBO> companyTimeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).label("annual").companyId(companyId).build(),
                    TimeoffTypeDBO.builder().id(2L).label("sick").companyId(companyId).build(),
                    TimeoffTypeDBO.builder().id(3L).label("maternity").companyId(companyId).build()
            );
            Set<Long> typeIds = Set.of(1L, 2L);
            List<TimeoffSummaryDBO> timeOffSummaries = List.of(
                    TimeoffSummaryDBO.builder().contractId(2L).typeId(1L).allocatedCount(7.0).takenCount(5.0).pendingCount(1.0).build(),
                    TimeoffSummaryDBO.builder().contractId(3L).typeId(1L).allocatedCount(7.0).takenCount(2.0).pendingCount(1.0).build(),
                    TimeoffSummaryDBO.builder().contractId(3L).typeId(2L).allocatedCount(7.0).takenCount(2.0).pendingCount(1.0).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            mockBulkDataLoadingMethods(companyId, entityId, typeIds,
                    idToContractMap, companyTimeOffTypes, timeOffSummaries, Map.of(), List.of(), List.of(), false);
            when(bulkTimeOffValidator.validate(eq(bulkItems.get(0)), any()))
                    .thenReturn(List.of("Employee ID is invalid. Either the employee ID is not present or the employee is not activated"));
            when(bulkTimeOffValidator.validate(eq(bulkItems.get(1)), any()))
                    .thenReturn(List.of());

            // when
            val result = bulkTimeOffService.validateBulkTimeOffs(request);

            assertFalse(result.isSuccess());
            assertNotNull(result.getItems());
            assertEquals(1, result.getItems().size());
            assertEquals("Employee ID is invalid. Either the employee ID is not present or the employee is not activated", result.getItems().get(0).getErrors().get(0));

        }

        @Test
        void should_return_empty_item_list_when_all_data_are_valid_old_format() {
            Long companyId = 500L;
            Long entityId = 12345L;
            List<BulkTimeOffRequest.Input> bulkItems = List.of(
                    BulkTimeOffRequest.Input.builder().externalTimeOffId("005").contractId(2L).type("annual").noOfDays("1").build(),
                    BulkTimeOffRequest.Input.builder().externalTimeOffId("013").contractId(3L).type("sick").noOfDays("2").startDate("25/11/2023").endDate("27/11/2023").build()  //valid
            );

            BulkTimeOffRequest request = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .entityId(entityId)
                    .inputs(bulkItems)
                    .build();

            Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                    2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(600L).build(),
                    3L, ContractOuterClass.Contract.newBuilder().setId(3L).setStarted(true).setCompanyId(companyId).build()
            );
            List<TimeoffTypeDBO> companyTimeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).label("annual").companyId(companyId).build(),
                    TimeoffTypeDBO.builder().id(2L).label("sick").companyId(companyId).build(),
                    TimeoffTypeDBO.builder().id(3L).label("maternity").companyId(companyId).build()
            );
            Set<Long> typeIds = Set.of(1L, 2L);
            List<TimeoffSummaryDBO> timeOffSummaries = List.of(
                    TimeoffSummaryDBO.builder().contractId(2L).typeId(1L).allocatedCount(7.0).takenCount(5.0).pendingCount(1.0).build(),
                    TimeoffSummaryDBO.builder().contractId(3L).typeId(1L).allocatedCount(7.0).takenCount(2.0).pendingCount(1.0).build(),
                    TimeoffSummaryDBO.builder().contractId(3L).typeId(2L).allocatedCount(7.0).takenCount(2.0).pendingCount(1.0).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            mockBulkDataLoadingMethods(companyId, entityId, typeIds,
                    idToContractMap, companyTimeOffTypes, timeOffSummaries, Map.of(), List.of(), List.of(), false);
            when(bulkTimeOffValidator.validate(eq(bulkItems.get(0)), any()))
                    .thenReturn(List.of());
            when(bulkTimeOffValidator.validate(eq(bulkItems.get(1)), any()))
                    .thenReturn(List.of());

            // when
            val result = bulkTimeOffService.validateBulkTimeOffs(request);

            assertTrue(result.isSuccess());
            assertNotNull(result.getItems());
            assertTrue(result.getItems().isEmpty());
        }

        @Test
        void should_return_empty_item_list_when_all_data_are_valid_new_format() {
            Long companyId = 500L;
            Long entityId = 12345L;
            List<BulkTimeOffRequest.Input> bulkItems = List.of(
                    BulkTimeOffRequest.Input.builder().externalTimeOffId("005").contractId(2L).type("annual").noOfDays("1").build(),
                    BulkTimeOffRequest.Input.builder().externalTimeOffId("013").contractId(3L).type("sick").noOfDays("2").startDate("2023-11-25").endDate("2023-11-27").build()  //valid
            );

            BulkTimeOffRequest request = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .entityId(entityId)
                    .inputs(bulkItems)
                    .build();

            Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                    2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(600L).build(),
                    3L, ContractOuterClass.Contract.newBuilder().setId(3L).setStarted(true).setCompanyId(companyId).build()
            );
            List<TimeoffTypeDBO> companyTimeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).label("annual").companyId(companyId).build(),
                    TimeoffTypeDBO.builder().id(2L).label("sick").companyId(companyId).build(),
                    TimeoffTypeDBO.builder().id(3L).label("maternity").companyId(companyId).build()
            );
            Set<Long> typeIds = Set.of(1L, 2L);
            List<TimeoffSummaryDBO> timeOffSummaries = List.of(
                    TimeoffSummaryDBO.builder().contractId(2L).typeId(1L).allocatedCount(7.0).takenCount(5.0).pendingCount(1.0).build(),
                    TimeoffSummaryDBO.builder().contractId(3L).typeId(1L).allocatedCount(7.0).takenCount(2.0).pendingCount(1.0).build(),
                    TimeoffSummaryDBO.builder().contractId(3L).typeId(2L).allocatedCount(7.0).takenCount(2.0).pendingCount(1.0).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            mockBulkDataLoadingMethods(companyId, entityId, typeIds,
                    idToContractMap, companyTimeOffTypes, timeOffSummaries, Map.of(), List.of(), List.of(), false);
            when(bulkTimeOffValidator.validate(eq(bulkItems.get(0)), any()))
                    .thenReturn(List.of());
            when(bulkTimeOffValidator.validate(eq(bulkItems.get(1)), any()))
                    .thenReturn(List.of());

            // when
            val result = bulkTimeOffService.validateBulkTimeOffs(request);

            assertTrue(result.isSuccess());
            assertNotNull(result.getItems());
            assertTrue(result.getItems().isEmpty());
        }

        @Test
        void should_throw_error_when_combinations_of_items_are_duplicated_in_bulk_list() {
            Long companyId = 500L;
            Long entityId = 12345L;
            List<BulkTimeOffRequest.Input> bulkItems = new ArrayList<>(List.of(
                    BulkTimeOffRequest.Input.builder().externalTimeOffId("001").contractId(2L).type("annual").employeeId("123").build(),
                    BulkTimeOffRequest.Input.builder().externalTimeOffId("005").contractId(2L).type("annual").employeeId("123").build()
            ));

            BulkTimeOffRequest request = BulkTimeOffRequest.builder()
                    .companyId(companyId)
                    .entityId(entityId)
                    .inputs(bulkItems)
                    .build();

            Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                    2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(600L).build()
            );
            List<TimeoffTypeDBO> companyTimeOffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).label("annual").companyId(companyId).build()
            );
            Set<Long> typeIds = Set.of(1L);
            List<TimeoffSummaryDBO> timeOffSummaries = List.of(
                    TimeoffSummaryDBO.builder().contractId(2L).typeId(1L).allocatedCount(7.0).takenCount(5.0).pendingCount(1.0).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            mockBulkDataLoadingMethods(companyId, entityId, typeIds,
                    idToContractMap, companyTimeOffTypes, timeOffSummaries, Map.of(), List.of(), List.of(), false);

            List<String> dateFormats = List.of("25/11/2023", "2023-11-25");

            for (String dateFormat : dateFormats) {
                bulkItems.set(0, BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("001")
                        .contractId(2L)
                        .type("annual")
                        .startDate(dateFormat)
                        .endDate("26/11/2023")
                        .employeeId("123")
                        .build());

                bulkItems.set(1, BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("005")
                        .contractId(2L)
                        .type("annual")
                        .startDate(dateFormat)
                        .endDate("26/11/2023")
                        .employeeId("123")
                        .build());

                // when
                val result = bulkTimeOffService.validateBulkTimeOffs(request);

                assertFalse(result.isSuccess());
                assertEquals(2, result.getItems().size());
                assertEquals("Duplicated entries found for the combination of [Employee ID, Type, Start Date and End Date] with employee id: 123", result.getItems().get(0).getErrors().get(0));
                assertEquals("Duplicated entries found for the combination of [Employee ID, Type, Start Date and End Date] with employee id: 123", result.getItems().get(1).getErrors().get(0));
            }
        }
    }

    @Nested
    class BulkRevokeTimeOffsTest {

        @Captor
        ArgumentCaptor<List<TimeoffDBO>> timeoffDBOArgumentCaptor;

        @Test
        void should_throw_error_when_current_user_is_from_different_company() {
            // Given
            long companyId = 100L;
            long otherCompanyId = 200L;
            UserContext companyUserDetails = getCompanyUserUserDetails(otherCompanyId);
            when(currentUser.getContext()).thenReturn(companyUserDetails);
            assertThrows(AccessDeniedException.class,
                    () -> bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(companyId, List.of()));
        }

        @Test
        void should_throw_validation_error_when_company_user_try_to_revoke_invalid_timeoffs() {
            val companyId = 100L;
            val externalIds = List.of("001", "002", "003");
            val timeOffs = List.of(
                    TimeoffDBO.builder().id(1L).contractId(100L).externalId("001").status(TimeOffStatus.APPROVAL_IN_PROGRESS).build(),
                    TimeoffDBO.builder().id(2L).contractId(200L).externalId("002").status(TimeOffStatus.SUBMITTED).build(),
                    TimeoffDBO.builder().id(3L).contractId(300L).externalId("003").status(TimeOffStatus.DRAFT).build()
            );
            val contractIdToCompanyIdMap = Map.of(
                    100L, companyId,
                    200L, companyId,
                    300L, companyId
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            when(timeoffRepository.findAllByExternalIdIn(externalIds)).thenReturn(timeOffs);
            when(contractServiceAdapter.getContractIdToCompanyIdMap(Set.of(100L, 200L, 300L))).thenReturn(contractIdToCompanyIdMap);

            // when
            val ex = assertThrows(ValidationException.class, () -> bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(companyId, externalIds));

            // then
            assertEquals("Some of the given time offs cannot be processed due to their current status: [{id: 2, status: SUBMITTED}, {id: 3, status: DRAFT}]", ex.getMessage());
        }

        @Test
        void should_throw_validation_error_when_company_user_try_to_revoke_timeoffs_belongs_to_multiple_companies() {
            val companyId = 100L;
            val externalIds = List.of("001", "002", "003");
            val timeOffs = List.of(
                    TimeoffDBO.builder().id(1L).contractId(100L).externalId("001").status(TimeOffStatus.APPROVAL_IN_PROGRESS).build(),
                    TimeoffDBO.builder().id(2L).contractId(200L).externalId("002").status(TimeOffStatus.APPROVED).build(),
                    TimeoffDBO.builder().id(3L).contractId(300L).externalId("003").status(TimeOffStatus.TAKEN).build()
            );
            val contractIdToCompanyIdMap = Map.of(
                    100L, 200L,
                    200L, 300L,
                    300L, 400L
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            when(timeoffRepository.findAllByExternalIdIn(externalIds)).thenReturn(timeOffs);
            when(contractServiceAdapter.getContractIdToCompanyIdMap(Set.of(100L, 200L, 300L))).thenReturn(contractIdToCompanyIdMap);

            // when
            val ex = assertThrows(ValidationException.class, () -> bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(companyId, externalIds));

            // then
            assertTrue(ex.getMessage().contains("Found timeoffs from multiple companies :"));
        }

        @Test
        void should_throw_validation_error_when_company_user_try_to_revoke_timeoffs_of_another_company() {
            val companyId = 100L;
            val externalIds = List.of("001", "002", "003");
            val timeOffs = List.of(
                    TimeoffDBO.builder().id(1L).contractId(100L).externalId("001").status(TimeOffStatus.APPROVAL_IN_PROGRESS).build(),
                    TimeoffDBO.builder().id(2L).contractId(200L).externalId("002").status(TimeOffStatus.APPROVED).build(),
                    TimeoffDBO.builder().id(3L).contractId(300L).externalId("003").status(TimeOffStatus.TAKEN).build()
            );
            val contractIdToCompanyIdMap = Map.of(
                    100L, 200L,
                    200L, 200L,
                    300L, 200L
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            when(timeoffRepository.findAllByExternalIdIn(externalIds)).thenReturn(timeOffs);
            when(contractServiceAdapter.getContractIdToCompanyIdMap(Set.of(100L, 200L, 300L))).thenReturn(contractIdToCompanyIdMap);

            // when
            val ex = assertThrows(ValidationException.class, () -> bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(companyId, externalIds));

            // then
            assertEquals("Company user from company id 100 can not revoke timeoffs of company : 200", ex.getMessage());
        }

        @Test
        void company_user_should_revoke_timeoffs_in_bulk() {
            val companyId = 100L;
            val externalIds = List.of("001", "002", "003");
            val timeOffs = List.of(
                    TimeoffDBO.builder().id(1L).contractId(100L).externalId("001").status(TimeOffStatus.APPROVAL_IN_PROGRESS).build(),
                    TimeoffDBO.builder().id(2L).contractId(200L).externalId("002").status(TimeOffStatus.APPROVED).build(),
                    TimeoffDBO.builder().id(3L).contractId(300L).externalId("003").status(TimeOffStatus.TAKEN).build()
            );
            val contractIdToCompanyIdMap = Map.of(
                    100L, companyId,
                    200L, companyId,
                    300L, companyId
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyId));
            when(timeoffRepository.findAllByExternalIdIn(externalIds)).thenReturn(timeOffs);
            when(contractServiceAdapter.getContractIdToCompanyIdMap(Set.of(100L, 200L, 300L))).thenReturn(contractIdToCompanyIdMap);

            bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(companyId, externalIds);

            verify(timeoffRepository).saveAllAndFlush(timeoffDBOArgumentCaptor.capture());
            val results = timeoffDBOArgumentCaptor.getValue();

            assertEquals(3, results.size());
            assertTrue(results.stream().allMatch(timeoff -> timeoff.status() == TimeOffStatus.DRAFT));
        }

        @Test
        void should_throw_validation_error_when_ops_user_try_to_revoke_invalid_timeoffs() {
            val companyId = 100L;
            val externalIds = List.of("001", "002", "003");
            val timeOffs = List.of(
                    TimeoffDBO.builder().id(1L).contractId(100L).externalId("001").status(TimeOffStatus.APPROVAL_IN_PROGRESS).build(),
                    TimeoffDBO.builder().id(2L).contractId(200L).externalId("002").status(TimeOffStatus.SUBMITTED).build(),
                    TimeoffDBO.builder().id(3L).contractId(300L).externalId("003").status(TimeOffStatus.DRAFT).build()
            );
            val contractIdToCompanyIdMap = Map.of(
                    100L, companyId,
                    200L, companyId,
                    300L, companyId
            );

            when(currentUser.getContext()).thenReturn(getOpsUserDetails());
            when(timeoffRepository.findAllByExternalIdIn(externalIds)).thenReturn(timeOffs);
            when(contractServiceAdapter.getContractIdToCompanyIdMap(Set.of(100L, 200L, 300L))).thenReturn(contractIdToCompanyIdMap);

            // when
            val ex = assertThrows(ValidationException.class, () -> bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(companyId, externalIds));

            // then
            assertEquals("Some of the given time offs cannot be processed due to their current status: [{id: 2, status: SUBMITTED}, {id: 3, status: DRAFT}]", ex.getMessage());
        }


        @Test
        void ops_user_should_revoke_timeoffs_in_bulk() {
            val companyId = 100L;
            val externalIds = List.of("001", "002", "003");
            val timeOffs = List.of(
                    TimeoffDBO.builder().id(1L).contractId(100L).externalId("001").status(TimeOffStatus.APPROVAL_IN_PROGRESS).build(),
                    TimeoffDBO.builder().id(2L).contractId(200L).externalId("002").status(TimeOffStatus.APPROVED).build(),
                    TimeoffDBO.builder().id(3L).contractId(300L).externalId("003").status(TimeOffStatus.TAKEN).build()
            );
            val contractIdToCompanyIdMap = Map.of(
                    100L, companyId,
                    200L, companyId,
                    300L, companyId
            );

            when(currentUser.getContext()).thenReturn(getOpsUserDetails());
            when(timeoffRepository.findAllByExternalIdIn(externalIds)).thenReturn(timeOffs);
            when(contractServiceAdapter.getContractIdToCompanyIdMap(Set.of(100L, 200L, 300L))).thenReturn(contractIdToCompanyIdMap);

            bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(companyId, externalIds);

            verify(timeoffRepository).saveAllAndFlush(timeoffDBOArgumentCaptor.capture());
            val results = timeoffDBOArgumentCaptor.getValue();

            assertEquals(3, results.size());
            assertTrue(results.stream().allMatch(timeoff -> timeoff.status() == TimeOffStatus.DRAFT));
        }

        @Test
        void system_user_should_revoke_timeoffs_in_bulk() {
            val companyId = 100L;
            val externalIds = List.of("001", "002", "003");
            val timeOffs = List.of(
                    TimeoffDBO.builder().id(1L).contractId(100L).externalId("001").status(TimeOffStatus.APPROVAL_IN_PROGRESS).build(),
                    TimeoffDBO.builder().id(2L).contractId(200L).externalId("002").status(TimeOffStatus.APPROVED).build(),
                    TimeoffDBO.builder().id(3L).contractId(300L).externalId("003").status(TimeOffStatus.TAKEN).build()
            );
            val contractIdToCompanyIdMap = Map.of(
                    100L, companyId,
                    200L, companyId,
                    300L, companyId
            );

            when(currentUser.getContext()).thenReturn(null);
            when(timeoffRepository.findAllByExternalIdIn(externalIds)).thenReturn(timeOffs);
            when(contractServiceAdapter.getContractIdToCompanyIdMap(Set.of(100L, 200L, 300L))).thenReturn(contractIdToCompanyIdMap);

            bulkTimeOffService.bulkRevokeTimeOffsFromExternalIds(companyId, externalIds);

            verify(timeoffRepository).saveAllAndFlush(timeoffDBOArgumentCaptor.capture());
            val results = timeoffDBOArgumentCaptor.getValue();

            assertEquals(3, results.size());
            assertTrue(results.stream().allMatch(timeoff -> timeoff.status() == TimeOffStatus.DRAFT));
        }

    }

    private void mockBulkDataLoadingMethods(Long companyId, Long entityId, Set<Long> typeIds,
                                            Map<Long, ContractOuterClass.Contract> idToContractMap,
                                            List<TimeoffTypeDBO> companyTimeOffTypes,
                                            List<TimeoffSummaryDBO> timeOffSummaries,
                                            Map<String, List<TimeoffDBO>> timeOffsForContractIdAndTypeId,
                                            List<EntitlementChangeRecordEntity> allocationRecords,
                                            List<EntitlementChangeRecordEntity> allDeductionECRs,
                                            boolean carryForwardEnabled) {
        Set<Long> contractIds = idToContractMap.keySet();
        Set<Long> allocationIds = allocationRecords.stream().map(EntitlementChangeRecordEntity::getId).collect(Collectors.toSet());
        when(contractServiceAdapter.getContractsByIdsAnyStatus(contractIds, true)).thenReturn((new ArrayList<>(idToContractMap.values())));
        when(timeoffTypeService.fetchEntityTimeOffTypes(companyId, entityId, EntityType.COMPANY_ENTITY)).thenReturn(companyTimeOffTypes);

        if (carryForwardEnabled) {
            when(entitlementChangeRecordRepository.findAllByTypeInAndContractIdInAndCategoryIn(typeIds, contractIds, TimeoffValidator.Companion.getALLOCATION_CATEGORIES()))
                    .thenReturn(allocationRecords);
            when(entitlementChangeRecordRepository.findAllByCategoryInAndRefIdIn(TimeoffValidator.Companion.getDEDUCTION_CATEGORIES(),
                    allocationIds)).thenReturn(allDeductionECRs);
            val earliestStartDate = getEarliestSummaryStartDate(timeOffSummaries);
            when(timeoffSummaryService.getEarliestSummaryStartDate(any())).thenReturn(earliestStartDate);
            when(bulkTimeOffHelper.getContractIdTypeIdToTimeOffsMap(typeIds, contractIds, earliestStartDate))
                    .thenReturn(timeOffsForContractIdAndTypeId);
            carryForwardExpiryOn();
        } else {
            when(timeoffSummaryService.findLatestSummariesForContractIdInAndTypeIdIn(contractIds, typeIds)).thenReturn(timeOffSummaries);
            carryForwardExpiryOff();
        }
    }

    private LocalDate getEarliestSummaryStartDate(Collection<TimeoffSummaryDBO> timeoffSummaryDBOs) {
        // if no summaries found, return first day of the current year as the start date
        return timeoffSummaryDBOs.stream()
                .map(TimeoffSummaryDBO::periodStart)
                .filter(Objects::nonNull)
                .min(LocalDate::compareTo)
                .orElse(LocalDate.now().withDayOfYear(1));
    }

    private UserContext getCompanyUserUserDetails(Long companyId) {
        return new UserContext(
                10L,
                "username",
                "company",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        null,
                        companyId,
                        null,
                        100L,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

    private UserContext getOpsUserDetails() {
        return new UserContext(
                10L,
                "username",
                "operations",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        null,
                        null,
                        null,
                        100L,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

    private static GBFeatureResult createFeatureFlag(boolean isOn) {
        return new GBFeatureResult(null, isOn, !isOn, GBFeatureSource.defaultValue, null, null);
    }

    private void carryForwardExpiryOn() {
        when(featureFlagService.feature(anyString(), anyMap())).thenReturn(createFeatureFlag(true));
    }

    private void carryForwardExpiryOff() {
        when(featureFlagService.feature(anyString(), anyMap())).thenReturn(createFeatureFlag(false));
    }
}
