package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.types.*;

import java.util.List;

/**
 * Implementation of TimeoffMapper for testing purposes.
 * This class only implements the required abstract methods from the TimeoffMapper interface
 * and relies on the default implementations for the rest.
 */
public class TimeoffMapperImpl implements TimeoffMapper {

    @Override
    public List<TimeOff> map(List<TimeoffDBO> source) {
        return List.of();
    }
}
