package com.multiplier.timeoff.service.rules;

import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.repository.TimeOffDefinitionRuleRepository;
import com.multiplier.timeoff.repository.model.TimeOffDefinitionRuleDBO;
import com.multiplier.timeoff.types.ConditionKey;
import com.multiplier.timeoff.types.ConditionOperator;
import com.multiplier.timeoff.types.RuleType;
import lombok.val;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class TimeOffDefinitionRuleServiceTest {
    @Mock
    TimeOffDefinitionRuleRepository timeOffDefinitionRuleRepository;
    @Mock
    FeatureFlagService featureFlagService;
    @InjectMocks
    private TimeOffDefinitionRuleService timeOffDefinitionRuleService;

    @Nested
    class IsContractMatchForRulesTest {
        private final Long companyId = 234L;

        @Test
        void contract_should_not_match_when_policy_has_no_rules() {
            List<TimeOffDefinitionRuleDBO> rules = List.of();
            RuleResolverInput ruleResolverInput = RuleResolverInput.builder()
                    .companyId(companyId)
                    .build();
            assertFalse(timeOffDefinitionRuleService.isContractMatchForRules(ruleResolverInput, rules));
        }

        @Nested
        class EntityLevelPoliciesFlagOff {
            @BeforeEach
            void setup() {
                entityLevelPoliciesOff();
            }
            @Test
            void contract_should_match_when_there_is_rule_with_rule_type_is_all() {
                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.ALL).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).build()
                );
                RuleResolverInput ruleResolverInput = RuleResolverInput.builder().companyId(companyId).build();
                assertTrue(timeOffDefinitionRuleService.isContractMatchForRules(ruleResolverInput, rules));
            }

            @Test
            void contract_should_match_when_country_matches() {
                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY)
                                .conditionOperator(ConditionOperator.EQUALS)
                                .conditionValues(new String[] {"LKA", "IND"})
                                .build()
                );
                RuleResolverInput ruleResolverInput = RuleResolverInput.builder()
                        .country("LKA")
                        .companyId(companyId)
                        .build();
                assertTrue(timeOffDefinitionRuleService.isContractMatchForRules(ruleResolverInput, rules));
            }

            @Test
            void contract_should_match_when_all_conditions_matches() {
                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY)
                                .conditionOperator(ConditionOperator.EQUALS)
                                .conditionValues(new String[] {"LKA", "IND"})
                                .build(),
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY)
                                .conditionOperator(ConditionOperator.NOT_EQUALS)
                                .conditionValues(new String[] {"USA", "UAE"})
                                .build(),
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.GENDER)
                                .conditionOperator(ConditionOperator.EQUALS)
                                .conditionValues(new String[] {"MALE"})
                                .build(),
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY)
                                .conditionOperator(ConditionOperator.NOT_EQUALS)
                                .conditionValues(new String[] {"FEMALE"})
                                .build()
                );
                RuleResolverInput ruleResolverInput = RuleResolverInput.builder()
                        .country("LKA")
                        .gender("Male")
                        .companyId(companyId)
                        .build();
                assertTrue(timeOffDefinitionRuleService.isContractMatchForRules(ruleResolverInput, rules));
            }

            @Test
            void contract_should_not_match_when_country_in_not_equal_rule() {
                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY)
                                .conditionOperator(ConditionOperator.EQUALS)
                                .conditionValues(new String[] {"LKA", "IND"})
                                .build(),
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY)
                                .conditionOperator(ConditionOperator.NOT_EQUALS)
                                .conditionValues(new String[] {"USA", "UAE"})
                                .build(),
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.GENDER)
                                .conditionOperator(ConditionOperator.EQUALS)
                                .conditionValues(new String[] {"MALE"})
                                .build(),
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY)
                                .conditionOperator(ConditionOperator.NOT_EQUALS)
                                .conditionValues(new String[] {"FEMALE"})
                                .build()
                );
                RuleResolverInput ruleResolverInput = RuleResolverInput.builder()
                        .country("UAE")
                        .gender("Male")
                        .companyId(companyId)
                        .build();
                assertFalse(timeOffDefinitionRuleService.isContractMatchForRules(ruleResolverInput, rules));
            }
        }

        @Nested
        class EntityLevelPoliciesFlagOn {
            @BeforeEach
            void setup() {
                entityLevelPoliciesOn();
            }

            @Test
            void contract_should_match_when_country_and_entity_matches() {
                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY)
                                .conditionOperator(ConditionOperator.EQUALS)
                                .conditionValues(new String[] {"LKA", "IND"})
                                .build(),
                        TimeOffDefinitionRuleDBO.builder()
                                .ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.ENTITY)
                                .conditionOperator(ConditionOperator.EQUALS)
                                .conditionValues(new String[] {"4758"})
                                .build()
                );
                RuleResolverInput ruleResolverInput = RuleResolverInput.builder()
                        .country("LKA")
                        .companyId(companyId)
                        .entityId("4758")
                        .build();
                assertTrue(timeOffDefinitionRuleService.isContractMatchForRules(ruleResolverInput, rules));
            }
        }

    }

    @Nested
    class FindRulesByCompanyDefinitionIdIn {

        @Test
        void should_return_rules_for_given_company_definition_ids() {
            Set<Long> companyDefinitionIds = Set.of(1L,2L);
            List<TimeOffDefinitionRuleDBO> rules = List.of(
                    TimeOffDefinitionRuleDBO.builder().companyDefinitionId(1L).ruleType(RuleType.ALL).build()
            );

            when(timeOffDefinitionRuleRepository.findByCompanyDefinitionIdIn(companyDefinitionIds))
                    .thenReturn(rules);
            val result = timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(companyDefinitionIds);

            assertEquals(1, result.size());
        }
    }

    @Nested
    class SaveAndDeleteRulesTest {
        @Test
        void should_save_rules() {
            List<TimeOffDefinitionRuleDBO> rules = List.of(
                TimeOffDefinitionRuleDBO.builder()
                    .ruleType(RuleType.BY_CONDITION)
                    .conditionKey(ConditionKey.COUNTRY)
                    .conditionOperator(ConditionOperator.EQUALS)
                    .conditionValues(new String[] {"LKA"})
                    .build()
            );

            timeOffDefinitionRuleService.saveRules(rules);

            Mockito.verify(timeOffDefinitionRuleRepository).saveAll(rules);
        }

        @Test
        void should_delete_rules() {
            List<TimeOffDefinitionRuleDBO> rules = List.of(
                TimeOffDefinitionRuleDBO.builder()
                    .ruleType(RuleType.BY_CONDITION)
                    .conditionKey(ConditionKey.COUNTRY)
                    .conditionOperator(ConditionOperator.EQUALS)
                    .conditionValues(new String[] {"LKA"})
                    .build()
            );

            timeOffDefinitionRuleService.deleteRules(rules);

            Mockito.verify(timeOffDefinitionRuleRepository).deleteAll(rules);
        }
    }

    private void entityLevelPoliciesOn() {
        when(featureFlagService.isOn(eq(FeatureFlags.ENTITY_LEVEL_POLICIES), Mockito.anyMap())).thenReturn(true);
    }

    private void entityLevelPoliciesOff() {
        when(featureFlagService.isOn(eq(FeatureFlags.ENTITY_LEVEL_POLICIES), Mockito.anyMap())).thenReturn(false);
    }
}
