package com.multiplier.timeoff.service;

import com.multiplier.timeoff.processor.AllocationProcessor;
import com.multiplier.timeoff.processor.CarryForwardExpirationProcessor;
import com.multiplier.timeoff.processor.CarryForwardProcessor;
import com.multiplier.timeoff.types.TimeOffAllocationInput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.annotation.Scheduled;

import java.time.LocalDate;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TimeoffSchedulerTest {

    @Mock
    private TimeoffCron timeoffCron;

    @Mock
    private TimeoffService timeoffService;

    @Mock
    private AllocationProcessor allocationProcessor;

    @Mock
    private CarryForwardProcessor carryForwardProcessor;

    @Mock
    private CarryForwardExpirationProcessor carryForwardExpirationProcessor;

    @InjectMocks
    private TimeoffScheduler timeoffScheduler;

    @BeforeEach
    void setUp() {
        // Reset mocks before each test
        reset(timeoffCron, timeoffService, allocationProcessor, carryForwardProcessor, carryForwardExpirationProcessor);
    }

    @Test
    void triggerTimeoffUpdate_shouldCallTimeoffCronUpdateTimeoffStatus() {
        // When
        timeoffScheduler.triggerTimeoffUpdate();

        // Then
        verify(timeoffCron).updateTimeoffStatus();
    }

    @Test
    void triggerTimeoffUpdate_shouldHandleException() {
        // Given
        RuntimeException expectedException = new RuntimeException("Test exception");
        doThrow(expectedException).when(timeoffCron).updateTimeoffStatus();
        // When
        timeoffScheduler.triggerTimeoffUpdate();
        // Then
        verify(timeoffCron).updateTimeoffStatus();
    }

    @Test
    void triggerLegacyTimeoffReallocation_shouldCallTimeoffServiceReallocateTimeoff() {
        // When
        timeoffScheduler.triggerLegacyTimeoffReallocation();

        // Then
        verify(timeoffService).reallocateTimeoff(any(LocalDate.class));
    }

    @Test
    void triggerLegacyTimeoffReallocation_shouldHandleException() {
        // Given
        doThrow(new RuntimeException("Test exception")).when(timeoffService).reallocateTimeoff(any(LocalDate.class));

        // When
        timeoffScheduler.triggerLegacyTimeoffReallocation();

        // Then
        verify(timeoffService).reallocateTimeoff(any(LocalDate.class));
    }

    @Test
    void triggerTimeoffAllocation_shouldCallAllocationProcessor() {
        // When
        timeoffScheduler.triggerTimeoffAllocation();

        // Then
        verify(allocationProcessor).allocateTimeoffBalances(any(TimeOffAllocationInput.class));
    }

    @Test
    void triggerTimeoffAllocation_shouldHandleException() {
        // Given
        doThrow(new RuntimeException("Test exception")).when(allocationProcessor).allocateTimeoffBalances(any(TimeOffAllocationInput.class));

        // When
        timeoffScheduler.triggerTimeoffAllocation();

        // Then
        verify(allocationProcessor).allocateTimeoffBalances(any(TimeOffAllocationInput.class));
    }

    @Test
    void triggerTimeoffCarryForward_shouldCallCarryForwardProcessor() {
        // When
        timeoffScheduler.triggerTimeoffCarryForward();

        // Then
        verify(carryForwardProcessor).carryForwardTimeoffBalances(any(TimeOffAllocationInput.class));
    }

    @Test
    void triggerTimeoffCarryForward_shouldHandleException() {
        // Given
        doThrow(new RuntimeException("Test exception")).when(carryForwardProcessor).carryForwardTimeoffBalances(any(TimeOffAllocationInput.class));

        // When
        timeoffScheduler.triggerTimeoffCarryForward();

        // Then
        verify(carryForwardProcessor).carryForwardTimeoffBalances(any(TimeOffAllocationInput.class));
    }

    @Test
    void triggerCarryForwardExpiration_shouldCallCarryForwardExpirationProcessor() {
        // When
        timeoffScheduler.triggerCarryForwardExpiration();

        // Then
        verify(carryForwardExpirationProcessor).expireCarryForwardTimeoffBalances(any(TimeOffAllocationInput.class));
    }

    @Test
    void triggerCarryForwardExpiration_shouldHandleException() {
        // Given
        doThrow(new RuntimeException("Test exception")).when(carryForwardExpirationProcessor).expireCarryForwardTimeoffBalances(any(TimeOffAllocationInput.class));

        // When
        timeoffScheduler.triggerCarryForwardExpiration();

        // Then
        verify(carryForwardExpirationProcessor).expireCarryForwardTimeoffBalances(any(TimeOffAllocationInput.class));
    }

    @Test
    void testScheduledAnnotations() throws NoSuchMethodException {
        // Get the methods
        var triggerTimeoffUpdate = TimeoffScheduler.class.getDeclaredMethod("triggerTimeoffUpdate");
        var triggerLegacyTimeoffReallocation = TimeoffScheduler.class.getDeclaredMethod("triggerLegacyTimeoffReallocation");
        var triggerTimeoffAllocation = TimeoffScheduler.class.getDeclaredMethod("triggerTimeoffAllocation");
        var triggerTimeoffCarryForward = TimeoffScheduler.class.getDeclaredMethod("triggerTimeoffCarryForward");
        var triggerCarryForwardExpiration = TimeoffScheduler.class.getDeclaredMethod("triggerCarryForwardExpiration");

        // Verify @Scheduled annotations using proper assertions
        assertEquals("0 0 1 * * ?", triggerTimeoffUpdate.getAnnotation(Scheduled.class).cron());
        assertEquals("0 0 2 * * *", triggerLegacyTimeoffReallocation.getAnnotation(Scheduled.class).cron());
        assertEquals("0 0 2 * * *", triggerTimeoffAllocation.getAnnotation(Scheduled.class).cron());
        assertEquals("0 0 5 * * *", triggerTimeoffCarryForward.getAnnotation(Scheduled.class).cron());
        assertEquals("0 0 1 * * *", triggerCarryForwardExpiration.getAnnotation(Scheduled.class).cron());
    }

} 