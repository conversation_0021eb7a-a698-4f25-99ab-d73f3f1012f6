package com.multiplier.timeoff.service.mapper;

import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.types.*;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class TimeoffMapperTest {

    private final TimeoffMapper mapper = new TimeoffMapperImpl();

    @Test
    void mapToDBO_should_map_all_fields_correctly() {
        // Arrange
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 5);
        LocalDateTime now = LocalDate.of(2023, 1, 1).atStartOfDay();

        TimeOff timeOff = new TimeOff.Builder()
                .id(123L)
                .contract(new Contract.Builder().id(456L).build())
                .type(new TimeOffType.Builder()
                        .typeId(789L)
                        .key("annual")
                        .isPaidLeave(true)
                        .definition(new TimeOffTypeDefinition.Builder()
                                .type("annual")
                                .typeId(789L)
                                .label("Annual Leave")
                                .build())
                        .build())
                .status(TimeOffStatus.APPROVED)
                .noOfDays(5.0)
                .description("Vacation")
                .startDate(new TimeOffDate.Builder()
                        .dateOnly(startDate)
                        .session(TimeOffSession.MORNING)
                        .build())
                .endDate(new TimeOffDate.Builder()
                        .dateOnly(endDate)
                        .session(TimeOffSession.AFTERNOON)
                        .build())
                .createdBy(1001L)
                .createdOn(now)
                .updatedBy(1002L)
                .updatedOn(now)
                .build();

        // Act
        TimeoffDBO result = mapper.mapToDBO(timeOff);

        // Assert
        assertNotNull(result);
        assertEquals(123L, result.id());
        assertEquals(456L, result.contractId());
        assertEquals(TimeOffStatus.APPROVED, result.status());
        assertEquals(5.0, result.noOfDays());
        assertEquals("Vacation", result.description());

        // Check dates and sessions
        assertEquals(startDate, result.startDate());
        assertEquals(TimeOffSession.MORNING, result.startSession());
        assertEquals(endDate, result.endDate());
        assertEquals(TimeOffSession.AFTERNOON, result.endSession());

        // Check type information
        assertNotNull(result.type());
        assertEquals(789L, result.type().id());
        assertEquals("annual", result.type().key());
        assertTrue(result.type().isPaidLeave());
        assertEquals("Annual Leave", result.type().label());

        // Check audit fields
        assertEquals(1001L, result.createdBy());
        assertEquals(now, result.createdOn());
        assertEquals(1002L, result.updatedBy());
        assertEquals(now, result.updatedOn());
    }

    @Test
    void mapToDBO_should_handle_null_timeOff() {
        // Act
        TimeoffDBO result = mapper.mapToDBO(null);

        // Assert
        assertNull(result);
    }

    @Test
    void mapToDBO_should_handle_null_fields() {
        // Arrange
        TimeOff timeOff = new TimeOff.Builder()
                .id(123L)
                .contract(new Contract.Builder().id(456L).build())
                .status(TimeOffStatus.APPROVED)
                .build();

        // Act
        TimeoffDBO result = mapper.mapToDBO(timeOff);

        // Assert
        assertNotNull(result);
        assertEquals(123L, result.id());
        assertEquals(456L, result.contractId());
        assertEquals(TimeOffStatus.APPROVED, result.status());

        // Check null fields
        assertNull(result.noOfDays());
        assertNull(result.description());
        assertNull(result.startDate());
        assertNull(result.startSession());
        assertNull(result.endDate());
        assertNull(result.endSession());
        assertNull(result.type());
        assertNull(result.createdBy());
        assertNull(result.createdOn());
        assertNull(result.updatedBy());
        assertNull(result.updatedOn());
    }

    @Test
    void mapToDBO_should_handle_null_type_definition() {
        // Arrange
        TimeOff timeOff = new TimeOff.Builder()
                .id(123L)
                .contract(new Contract.Builder().id(456L).build())
                .type(new TimeOffType.Builder()
                        .typeId(789L)
                        .key("annual")
                        .isPaidLeave(true)
                        .build()) // No definition
                .status(TimeOffStatus.APPROVED)
                .build();

        // Act
        TimeoffDBO result = mapper.mapToDBO(timeOff);

        // Assert
        assertNotNull(result);
        assertNotNull(result.type());
        assertEquals(789L, result.type().id());
        assertEquals("annual", result.type().key());
        assertTrue(result.type().isPaidLeave());
        assertNull(result.type().label()); // Label should be null since definition was null
    }

    @Test
    void mapToDBO_should_handle_null_dates() {
        // Arrange
        TimeOff timeOff = new TimeOff.Builder()
                .id(123L)
                .contract(new Contract.Builder().id(456L).build())
                .type(new TimeOffType.Builder()
                        .typeId(789L)
                        .key("annual")
                        .build())
                .status(TimeOffStatus.APPROVED)
                .startDate(null) // Null start date
                .endDate(null)   // Null end date
                .build();

        // Act
        TimeoffDBO result = mapper.mapToDBO(timeOff);

        // Assert
        assertNotNull(result);
        assertNull(result.startDate());
        assertNull(result.startSession());
        assertNull(result.endDate());
        assertNull(result.endSession());
    }
}
