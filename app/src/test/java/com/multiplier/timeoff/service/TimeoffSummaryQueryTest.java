package com.multiplier.timeoff.service;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.company.schema.holiday.LegalEntityHoliday.Holiday;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.HolidayServiceAdapter;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.repository.TimeoffEntitlementDBORepository;
import com.multiplier.timeoff.repository.TimeoffRepository;
import com.multiplier.timeoff.repository.TimeoffSummaryRepository;
import com.multiplier.timeoff.repository.TimeoffTypeRepository;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.TimeoffTypeMapper;
import com.multiplier.timeoff.service.mapper.TimeoffTypeMapperImpl;
import com.multiplier.timeoff.types.TimeOffEncashmentInput;
import com.multiplier.timeoff.types.TimeOffSession;
import com.multiplier.timeoff.types.TimeOffStatus;
import com.multiplier.timeoff.types.TimeOffUnit;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mapstruct.Mapper;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.access.AccessDeniedException;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class TimeoffSummaryQueryTest {

    @Mock
    private TimeoffSummaryRepository timeoffSummaryRepository;
    @Mock
    private TimeoffEntitlementDBORepository timeoffEntitlementDBORepository;
    @Mock
    private TimeoffTypeRepository timeoffTypeRepository;
    @Mock
    private TimeoffRepository timeoffRepository;
    @Mock
    private HolidayServiceAdapter holidayServiceAdapter;
    @Mock
    private ContractServiceAdapter contractServiceAdapter;
    @Mock
    private AuthorizationService authorizationService;
    @Mock
    private CurrentUser currentUser;

    @Spy
    private final TimeoffTypeMapper timeoffTypeMapper = new TimeoffTypeMapperImpl();
    @InjectMocks
    private TimeoffSummaryQuery timeoffSummaryQuery;

    private Long contractId;
    private LocalDate lastWorkingDate;
    private DgsDataFetchingEnvironment dfe;
    private TimeOffEncashmentInput input;

    @BeforeEach
    void setup() {
        contractId = 100L;
        lastWorkingDate = LocalDate.of(2024,12,31);
        dfe = mock(DgsDataFetchingEnvironment.class);
        input = TimeOffEncashmentInput.newBuilder()
                .contractId(contractId)
                .lastWorkingDate(lastWorkingDate)
                .build();
        timeoffSummaryQuery.setContractServiceAdapter(contractServiceAdapter);
    }

    @Nested
    class GetEncashmentBalanceTest {

        private TimeoffSummaryDBO buildTimeoffSummaryDBO(
                Long contractId, Long typeId, LocalDate periodStart, LocalDate periodEnd,
                Double allocatedCount, Double carriedCount, Double takenCount) {
            return TimeoffSummaryDBO.builder()
                    .contractId(contractId)
                    .typeId(typeId)
                    .periodStart(periodStart)
                    .periodEnd(periodEnd)
                    .allocatedCount(allocatedCount)
                    .carriedCount(carriedCount)
                    .totalEntitledCount(allocatedCount + carriedCount)
                    .takenCount(takenCount)
                    .build();
        }

        private TimeoffEntitlementDBO buildTimeoffEntitlementDBO(
                Long contractId, Long typeId, Double value, TimeOffUnit unit) {
            return TimeoffEntitlementDBO.builder()
                    .contractId(contractId)
                    .typeId(typeId)
                    .value(value)
                    .unit(unit)
                    .build();
        }

        private TimeoffTypeDBO buildTimeoffTypeDBO(Long id, String key, String label) {
            return TimeoffTypeDBO.builder()
                    .id(id)
                    .key(key)
                    .label(label)
                    .build();
        }

        private TimeoffDBO buildTimeoffDBO(
                Long typeId, LocalDate startDate, LocalDate endDate,
                TimeOffSession startSession, TimeOffSession endSession,
                Double noOfDays, TimeOffStatus status) {
            return TimeoffDBO.builder()
                    .typeId(typeId)
                    .startDate(startDate)
                    .endDate(endDate)
                    .startSession(startSession)
                    .endSession(endSession)
                    .noOfDays(noOfDays)
                    .status(status)
                    .build();
        }

        private Holiday buildHoliday(int year, int month, int date, String name) {
            return Holiday.newBuilder()
                    .setYear(year)
                    .setMonth(month)
                    .setDate(date)
                    .setName(name)
                    .build();
        }

        @Test
        void should_send_empty_list_if_no_summaries_found() {
            // given
            List<TimeoffSummaryDBO> summaries = List.of();
            when(timeoffSummaryRepository.findSummariesByContractIdAndDate(contractId, lastWorkingDate))
                    .thenReturn(summaries);
            // when
            val result = timeoffSummaryQuery.getEncashmentBalance(contractId, lastWorkingDate);

            // then
            assert result.isEmpty();
        }

        @Test
        void should_throw_validation_exception_when_contract_is_null() {
            // given
            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(null);

            // when
            val ex = assertThrows(ValidationException.class, () ->
                    timeoffSummaryQuery.getEncashmentBalance(input, dfe)
            );

            // then
            assertThat(ex.getMessage()).isEqualTo("Cannot find contract for the given id ");
        }

        @Test
        void should_throw_validation_exception_when_contract_type_not_eligible() {
            // given
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setType(ContractOuterClass.ContractType.FREELANCER) // No timeoff for FREELANCER
                    .build();
            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(contract);

            // when
            val ex = assertThrows(ValidationException.class, () ->
                    timeoffSummaryQuery.getEncashmentBalance(input, dfe)
            );

            // then
            assertThat(ex.getMessage()).isEqualTo(String.format("Contract type is not eligible for timeoffs entitlements (id = %d, type = %s)", contract.getId(), contract.getType()));
        }

        @Test
        void should_return_empty_list_without_authorization_check_when_experience_is_operations() {
            // given
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .build();
            val userContext = mock(UserContext.class);

            when(contractServiceAdapter.getContractByIdAnyStatus(contractId)).thenReturn(contract);
            when(currentUser.getContext()).thenReturn(userContext);
            when(userContext.getExperience()).thenReturn("operations");
            when(timeoffSummaryRepository.findSummariesByContractIdAndDate(contractId, lastWorkingDate))
                    .thenReturn(Collections.emptyList());

            // when
            val result = timeoffSummaryQuery.getEncashmentBalance(input, dfe);

            // then
            verifyNoInteractions(authorizationService);
            assertThat(result).isEmpty();
        }

        @Test
        void should_return_empty_list_when_experience_is_company_and_access_allowed() {
            // given
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .build();
            val userContext = mock(UserContext.class);

            when(contractServiceAdapter.getContractByIdAnyStatus(contractId))
                    .thenReturn(contract);
            when(currentUser.getContext()).thenReturn(userContext);
            when(userContext.getExperience()).thenReturn("company");
            // Mock authorize to do nothing (simulates access allowed)
            doNothing().when(authorizationService).authorize(dfe, contract);
            when(timeoffSummaryRepository.findSummariesByContractIdAndDate(contractId, lastWorkingDate))
                    .thenReturn(Collections.emptyList());

            // when
            val result = timeoffSummaryQuery.getEncashmentBalance(input, dfe);

            // then
            verify(authorizationService).authorize(dfe, contract);
            assertThat(result).isEmpty();
        }

        @Test
        void should_throw_access_denied_exception_when_experience_is_company_and_access_denied() {
            // given
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .build();
            val userContext = mock(UserContext.class);

            when(contractServiceAdapter.getContractByIdAnyStatus(contractId))
                    .thenReturn(contract);
            when(currentUser.getContext()).thenReturn(userContext);
            when(userContext.getExperience()).thenReturn("company");
            // Mock authorize to throw AccessDeniedException
            doThrow(AccessDeniedException.class).when(authorizationService).authorize(dfe, contract);

            assertThrows(AccessDeniedException.class, () ->
                    timeoffSummaryQuery.getEncashmentBalance(input, dfe)
            );
        }

        @ParameterizedTest
        @MethodSource("provideTestCases1")
        void should_send_encashment_balance_when_contract_started_before_parameterized(TestCase testCase) {
            // given
            List<TimeoffSummaryDBO> summaries = List.of(
                    buildTimeoffSummaryDBO(
                            testCase.contractId(), 1L, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31),
                            24.0,4.0,7.0
                    ),
                    buildTimeoffSummaryDBO(
                            testCase.contractId(), 2L, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31),
                            10.0,0.0,3.0
                    )
            );

            List<TimeoffTypeDBO> types = List.of(
                    buildTimeoffTypeDBO(1L, "annual", "Annual Leave"),
                    buildTimeoffTypeDBO(2L, "sick", "Sick Leave")
            );

            val entitlements = List.of(
                    buildTimeoffEntitlementDBO(testCase.contractId(), 1L, 12.0, TimeOffUnit.DAYS),
                    buildTimeoffEntitlementDBO(testCase.contractId(), 2L, 10.0, TimeOffUnit.DAYS)
            );

            val timeoffs = List.of(
                    buildTimeoffDBO(
                            1L, LocalDate.of(2024, 5, 2), LocalDate.of(2024, 5, 5),
                            TimeOffSession.MORNING, TimeOffSession.AFTERNOON, 2.0, TimeOffStatus.TAKEN
                    ),
                    buildTimeoffDBO(
                            1L, LocalDate.of(2024, 8, 5), LocalDate.of(2024, 8, 8),
                            TimeOffSession.MORNING, TimeOffSession.AFTERNOON, 4.0, TimeOffStatus.APPROVED
                    ),
                    buildTimeoffDBO(
                            1L, LocalDate.of(2024, 9, 10), LocalDate.of(2024, 9, 10),
                            TimeOffSession.MORNING, TimeOffSession.AFTERNOON, 1.0, TimeOffStatus.TAKEN
                    ),
                    buildTimeoffDBO(
                            2L, LocalDate.of(2024, 2, 2), LocalDate.of(2024, 2, 2),
                            TimeOffSession.MORNING, TimeOffSession.AFTERNOON, 1.0, TimeOffStatus.TAKEN
                    ),
                    buildTimeoffDBO(
                            2L, LocalDate.of(2024, 6, 20), LocalDate.of(2024, 6, 21),
                            TimeOffSession.MORNING, TimeOffSession.AFTERNOON, 2.0, TimeOffStatus.APPROVED
                    )
            );

            when(timeoffSummaryRepository.findSummariesByContractIdAndDate(testCase.contractId(), testCase.lastWorkingDay()))
                    .thenReturn(summaries);
            when(timeoffEntitlementDBORepository.findAllByContractId(testCase.contractId()))
                    .thenReturn(entitlements);
            when(timeoffTypeRepository.findAllById(Set.of(1L, 2L)))
                    .thenReturn(types);
            when(timeoffRepository.findAllByContractIdTypeIdInStartDateInRangeStatusIn(
                    eq(testCase.contractId()), eq(List.of(1L, 2L)), any(LocalDate.class), any(LocalDate.class), eq(List.of(TimeOffStatus.APPROVED, TimeOffStatus.TAKEN))))
                    .thenReturn(timeoffs);
            when(holidayServiceAdapter.getHolidays(Set.of(testCase.contractId()), testCase.lastWorkingDay().getYear(), null))
                    .thenReturn(Collections.emptyList());

            // when
            val result = timeoffSummaryQuery.getEncashmentBalance(testCase.contractId(), testCase.lastWorkingDay());

            // then
            assert result.size() == testCase.expectedBalances().size();
            for (ExpectedBalance expected : testCase.expectedBalances()) {
                assert result.stream().anyMatch(balance ->
                        balance.getTotalProratedAllocatedCount().equals(expected.proratedAllocatedCount()) &&
                        balance.getTotalCarryForwardCount().equals(expected.carriedCount()) &&
                        balance.getTotalTakenCount().equals(expected.takenCount) &&
                        balance.getEncashableBalance().equals(expected.encashableBalance()) &&
                                balance.getType().getTypeId() == expected.typeId());
            }
        }

        @ParameterizedTest
        @MethodSource("provideTestCases2")
        void should_send_encashment_balance_when_contract_started_in_middle_of_leave_cycle_parameterized(TestCase testCase) {
            // given
            List<TimeoffSummaryDBO> summaries = List.of(
                    buildTimeoffSummaryDBO(
                            testCase.contractId(), 1L, LocalDate.of(2024, 8, 1), LocalDate.of(2025, 3, 31),
                            16.0,4.0,5.0
                    )
            );

            List<TimeoffTypeDBO> types = List.of(
                    buildTimeoffTypeDBO(1L, "annual", "Annual Leave")
            );

            List<TimeoffEntitlementDBO> entitlements = List.of(
                    buildTimeoffEntitlementDBO(testCase.contractId(), 1L, 24.0, TimeOffUnit.DAYS)
            );

            val timeoffs = List.of(
                    buildTimeoffDBO(
                            1L, LocalDate.of(2024, 5, 2), LocalDate.of(2024, 5, 5),
                            TimeOffSession.MORNING, TimeOffSession.AFTERNOON, 2.0, TimeOffStatus.TAKEN
                    ),
                    buildTimeoffDBO(
                            1L, LocalDate.of(2024, 11, 20), LocalDate.of(2024, 11, 26),
                            TimeOffSession.MORNING, TimeOffSession.AFTERNOON, 5.0, TimeOffStatus.TAKEN
                    )
            );

            when(timeoffSummaryRepository.findSummariesByContractIdAndDate(testCase.contractId(), testCase.lastWorkingDay()))
                    .thenReturn(summaries);
            when(timeoffEntitlementDBORepository.findAllByContractId(testCase.contractId()))
                    .thenReturn(entitlements);
            when(timeoffTypeRepository.findAllById(Set.of(1L)))
                    .thenReturn(types);
            when(timeoffRepository.findAllByContractIdTypeIdInStartDateInRangeStatusIn(
                    eq(testCase.contractId()), eq(List.of(1L)), any(LocalDate.class), any(LocalDate.class), eq(List.of(TimeOffStatus.APPROVED, TimeOffStatus.TAKEN))))
                    .thenReturn(timeoffs);
            when(holidayServiceAdapter.getHolidays(Set.of(testCase.contractId()), testCase.lastWorkingDay().getYear(), null))
                    .thenReturn(Collections.emptyList());

            // when
            val result = timeoffSummaryQuery.getEncashmentBalance(testCase.contractId(), testCase.lastWorkingDay());

            // then
            assert result.size() == testCase.expectedBalances().size();
            for (ExpectedBalance expected : testCase.expectedBalances()) {
                assert result.stream().anyMatch(balance ->
                        balance.getEncashableBalance().equals(expected.encashableBalance()) &&
                                balance.getType().getTypeId() == expected.typeId());
            }
        }

        @ParameterizedTest
        @MethodSource("provideTestCases3")
        void should_send_correct_encashment_balance_for_the_case_lwd_falls_inbetween_timeoff(TestCase testCase) {
            // given
            List<TimeoffSummaryDBO> summaries = List.of(
                    buildTimeoffSummaryDBO(
                            testCase.contractId(), 1L, LocalDate.of(2024, 1, 1), LocalDate.of(2024, 12, 31),
                            24.0, 4.0, 12.0
                    )
            );

            List<TimeoffTypeDBO> types = List.of(
                    buildTimeoffTypeDBO(1L, "annual", "Annual Leave")
            );

            List<TimeoffEntitlementDBO> entitlements = List.of(
                    buildTimeoffEntitlementDBO(testCase.contractId(), 1L, 24.0, TimeOffUnit.DAYS)
            );

            List<TimeoffDBO> timeoffs = List.of(
                    // Feb 20-22: 3 days, no holidays/weekends
                    buildTimeoffDBO(
                            1L, LocalDate.of(2024, 2, 20), LocalDate.of(2024, 2, 22),
                            TimeOffSession.MORNING, TimeOffSession.AFTERNOON, 3.0, TimeOffStatus.TAKEN
                    ),
                    // Aug 13-20: 8 days, but Aug 15 (holiday), Aug 17-18 (weekends) → 5 days
                    buildTimeoffDBO(
                            1L, LocalDate.of(2024, 8, 13), LocalDate.of(2024, 8, 20),
                            TimeOffSession.MORNING, TimeOffSession.AFTERNOON, 5.0, TimeOffStatus.APPROVED
                    ),
                    // Oct 29 - Nov 5: 8 days, but Oct 31, Nov 1 (holidays), Nov 2-3 (weekends), Oct 29 AFTERNOON → 3.5 days
                    buildTimeoffDBO(
                            1L, LocalDate.of(2024, 10, 29), LocalDate.of(2024, 11, 5),
                            TimeOffSession.AFTERNOON, TimeOffSession.AFTERNOON, 3.5, TimeOffStatus.APPROVED
                    )
            );

            // Mock holidays: Aug 15, Oct 31, Nov 1
            List<Holiday> holidays = List.of(
                    buildHoliday(2024, 8, 15, "Independence Day"),
                    buildHoliday(2024, 10, 31, "Public Holiday"),
                    buildHoliday(2024, 11, 1, "All Saints' Day")
            );

            when(timeoffSummaryRepository.findSummariesByContractIdAndDate(testCase.contractId(), testCase.lastWorkingDay()))
                    .thenReturn(summaries);
            when(timeoffEntitlementDBORepository.findAllByContractId(testCase.contractId()))
                    .thenReturn(entitlements);
            when(timeoffTypeRepository.findAllById(Set.of(1L)))
                    .thenReturn(types);
            when(timeoffRepository.findAllByContractIdTypeIdInStartDateInRangeStatusIn(
                    eq(testCase.contractId()), eq(List.of(1L)), any(LocalDate.class), any(LocalDate.class),
                    eq(List.of(TimeOffStatus.APPROVED, TimeOffStatus.TAKEN))))
                    .thenReturn(timeoffs);
            when(holidayServiceAdapter.getHolidays(Set.of(testCase.contractId()), testCase.lastWorkingDay().getYear(), null))
                    .thenReturn(holidays);

            // when
            val result = timeoffSummaryQuery.getEncashmentBalance(testCase.contractId(), testCase.lastWorkingDay());

            // then
            assert result.size() == testCase.expectedBalances().size();
            for (ExpectedBalance expected : testCase.expectedBalances()) {
                assert result.stream().anyMatch(balance ->
                        balance.getTotalProratedAllocatedCount().equals(expected.proratedAllocatedCount()) &&
                                balance.getTotalCarryForwardCount().equals(expected.carriedCount()) &&
                                balance.getTotalTakenCount().equals(expected.takenCount()) &&
                                balance.getEncashableBalance().equals(expected.encashableBalance()) &&
                                balance.getType().getTypeId() == expected.typeId());
            }
        }

        static Stream<TestCase> provideTestCases1() {
            return Stream.of(
                    new TestCase(
                            100L,
                            LocalDate.of(2024, 12, 31),
                            List.of(
                                    new ExpectedBalance(1L, 12.0, 4.0, 7.0, 9.0),
                                    new ExpectedBalance(2L, 10.0, 0.0, 3.0, 7.0)
                            )
                    )
                    , new TestCase(
                            100L,
                            LocalDate.of(2024, 1, 1),
                            List.of(
                                    new ExpectedBalance(1L, 0.0, 4.0, 0.0, 4.0),
                                    new ExpectedBalance(2L, 0.0, 0.0, 0.0, 0.0)

                            )
                    )
                    , new TestCase(
                            100L,
                            LocalDate.of(2024, 5, 17),
                            List.of(
                                    new ExpectedBalance(1L, 4.5, 4.0, 2.0,6.5),
                                    new ExpectedBalance(2L, 4.0, 0.0, 1.0,3.0)
                            )
                    )
                    , new TestCase(
                            100L,
                            LocalDate.of(2024, 10, 31),
                            List.of(
                                    new ExpectedBalance(1L, 10.0, 4.0, 7.0, 7.0),
                                    new ExpectedBalance(2L, 8.5, 0.0, 3.0, 5.5)
                            )
                    )
            );
        }

        static Stream<TestCase> provideTestCases2() {
            return Stream.of(
                    new TestCase(
                            100L,
                            LocalDate.of(2025, 2, 20),
                            List.of(
                                    new ExpectedBalance(1L, 13.5, 4.0, 5.0,12.5)
                            )
                    )
                    , new TestCase(
                            100L,
                            LocalDate.of(2024, 12, 31),
                            List.of(
                                    new ExpectedBalance(1L, 10.0, 4.0, 5.0, 9.0)
                            )
                    )
            );
        }

        static Stream<TestCase> provideTestCases3() {
            return Stream.of(
                    new TestCase(
                            100L,
                            LocalDate.of(2024, 8, 19),
                            List.of(
                                    new ExpectedBalance(1L, 15.5, 4.0, 7.0, 12.5)
                            )
                    )
                    , new TestCase(
                            100L,
                            LocalDate.of(2024, 11, 4),
                            List.of(
                                    new ExpectedBalance(1L, 20.5, 4.0, 10.5, 14.0)
                            )
                    )
            );
        }


        // Helper record for test cases
        private record TestCase(Long contractId, LocalDate lastWorkingDay, List<ExpectedBalance> expectedBalances) {
        }

        // Helper record for expected balance assertions
        private record ExpectedBalance(Long typeId, Double proratedAllocatedCount, Double carriedCount, Double takenCount, Double encashableBalance) {
        }
    }
}
