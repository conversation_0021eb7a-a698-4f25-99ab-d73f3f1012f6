package com.multiplier.timeoff.service;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.common.transport.user.UserScopes;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.member.schema.Member;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.MemberServiceAdapter;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.core.util.TimeOffUtil;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.featureflag.FeatureFlags;
import com.multiplier.timeoff.leavecompliance.service.CountryDefinitionService;
import com.multiplier.timeoff.repository.CompanyDefinitionRepository;
import com.multiplier.timeoff.repository.TimeoffEntitlementDBORepository;
import com.multiplier.timeoff.repository.builder.CompanyDefinitionSpecBuilder;
import com.multiplier.timeoff.repository.filters.CompanyDefinitionFilter;
import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.service.dto.CountryLocation;
import com.multiplier.timeoff.service.exception.DataCorruptionException;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.mapper.TimeOffDefinitionRuleMapper;
import com.multiplier.timeoff.service.mapper.TimeOffTypeDefinitionMapper;
import com.multiplier.timeoff.service.notifications.TimeoffNotificationHelper;
import com.multiplier.timeoff.service.rules.RuleResolverInput;
import com.multiplier.timeoff.service.rules.TimeOffDefinitionRuleService;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.val;
import org.apache.commons.collections4.ListUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class DefinitionServiceTest {

    @Mock
    CurrentUser currentUser;
    @Mock
    TimeoffTypeService timeoffTypeService;
    @Mock
    CountryDefinitionService countryDefinitionService;
    @Mock
    CompanyDefinitionRepository companyDefinitionRepository;
    @Mock
    TimeOffTypeDefinitionMapper timeOffTypeDefinitionMapper;
    @Mock
    TimeoffEntitlementDBORepository timeoffEntitlementDBORepository;
    @Mock
    TimeoffNotificationHelper timeoffNotificationHelper;
    @Mock
    ContractServiceAdapter contractServiceAdapter;
    @Mock
    TimeoffSummaryService timeoffSummaryService;
    @Mock
    TimeOffDefinitionRuleMapper timeOffDefinitionRuleMapper;
    @Mock
    TimeOffDefinitionRuleService timeOffDefinitionRuleService;
    @Mock
    MemberServiceAdapter memberServiceAdapter;
    @Mock
    FeatureFlagService featureFlagService;
    @Mock
    CompanyDefinitionSpecBuilder companyDefinitionSpecBuilder;
    @Mock
    CompanyServiceAdapter companyServiceAdapter;
    @Mock
    AuthorizationService authorizationService;

    @InjectMocks
    private DefinitionService definitionService;

    @Nested
    class CreateCompanyDefinitionTest {

        @Captor
        private ArgumentCaptor<CompanyDefinitionEntity> companyDefinitionEntityArgumentCaptor;

        @Test
        void should_throw_validation_exception_when_current_user_is_not_a_company_user() {
            TimeOffPolicyCreateInput timeOffPolicyCreateInput = TimeOffPolicyCreateInput.newBuilder()
                    .policyName("Annual leave policy for IND")
                    .build();
            val memberId = 101L;

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId));

            val ex = assertThrows(ValidationException.class, () ->
                    definitionService.createCompanyDefinition(timeOffPolicyCreateInput));

            assertTrue(ex.getMessage().contains("Cannot find company id of the current user"));
        }

        @Test
        void should_throw_validation_exception_when_given_type_not_found_to_create_definition() {
            val typeId = 500L;
            TimeOffPolicyCreateInput timeOffPolicyCreateInput = TimeOffPolicyCreateInput.newBuilder()
                    .policyName("Annual leave policy for IND")
                    .timeOffTypeId(typeId)
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenThrow(new ValidationException("Can not find time off type for id : " + typeId));

            val ex = assertThrows(ValidationException.class, () -> definitionService.createCompanyDefinition(timeOffPolicyCreateInput));
            assertEquals("Can not find time off type for id : " + typeId, ex.getMessage());
        }

        @Test
        void should_throw_validation_exception_when_given_type_to_create_definition_is_deleted() {
            val typeId = 500L;
            TimeOffPolicyCreateInput timeOffPolicyCreateInput = TimeOffPolicyCreateInput.newBuilder()
                    .policyName("Annual leave policy for IND")
                    .allocatedCount(TimeOffDurationInput.newBuilder().value(7.0).unit(TimeOffUnit.DAYS).build())
                    .timeOffTypeId(typeId)
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val timeOffType = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .status(TimeOffTypeStatus.DELETED)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffType);

            val ex = assertThrows(ValidationException.class, () -> definitionService.createCompanyDefinition(timeOffPolicyCreateInput));
            assertEquals("Can not create a definition from deleted type id " + typeId, ex.getMessage());
        }

        @Test
        void should_throw_validation_exception_when_given_type_to_create_definition_is_owned_by_another_company() {
            val typeId = 500L;
            TimeOffPolicyCreateInput timeOffPolicyCreateInput = TimeOffPolicyCreateInput.newBuilder()
                    .policyName("Annual leave policy for IND")
                    .timeOffTypeId(typeId)
                    .allocatedCount(TimeOffDurationInput.newBuilder().value(7.0).unit(TimeOffUnit.DAYS).build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val timeOffType = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(200L)
                    .status(TimeOffTypeStatus.ACTIVE)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffType);

            val ex = assertThrows(ValidationException.class, () -> definitionService.createCompanyDefinition(timeOffPolicyCreateInput));
            assertEquals("Can not create a definition for company id : 100 from type id : 500, since type owned by company id : 200", ex.getMessage());
        }

        @Test
        void should_throw_validation_exception_when_future_leave_config_is_invalid() {
            val typeId = 500L;
            val entityId = 600L;
            TimeOffPolicyCreateInput timeOffPolicyCreateInput = TimeOffPolicyCreateInput.newBuilder()
                    .policyName("Annual leave policy for IND")
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(TimeOffUnit.DAYS)
                            .value(14.0)
                            .build())
                    .carryForwardConfig(
                            CarryForwardConfigInput.newBuilder()
                                    .enabled(true)
                                    .maxLimit(
                                            TimeOffDurationInput.newBuilder()
                                                    .unit(TimeOffUnit.DAYS)
                                                    .value(5.0)
                                                    .build()
                                    )
                                    .expiryConfig(
                                            CarryForwardExpiryInput.newBuilder()
                                                    .enabled(true)
                                                    .expiry(
                                                            TimeOffDurationInput.newBuilder()
                                                                    .unit(TimeOffUnit.MONTHS)
                                                                    .value(3.0)
                                                                    .build()
                                                    )
                                                    .build()
                                    )
                                    .build()
                    )
                    .timeoffPolicyEntityInput(
                            TimeOffPolicyEntityInput.newBuilder()
                                    .type(EntityType.COMPANY_ENTITY)
                                    .id(entityId)
                                    .build())
                    .isUnlimitedLeavesAllowed(false)
                    .timeOffTypeId(typeId)
                    .futureLeaveConfig(FutureLeaveConfigInput.newBuilder()
                            .enabled(true)
                            .futureYearsAllowed(TimeOffUtil.MAX_FUTURE_LEAVES_ALLOWED + 1)
                            .lapsableLeaveConfig(
                                    LapsableLeaveConfigInput.newBuilder()
                                            .enabled(true)
                                            .expiry(TimeOffDurationInput.newBuilder()
                                                    .unit(TimeOffUnit.MONTHS)
                                                    .value(3.0)
                                                    .build())
                                            .build()
                            )
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val timeOffType = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(companyId)
                    .status(TimeOffTypeStatus.ACTIVE)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffType);


            val ex = assertThrows(ValidationException.class, () -> definitionService.createCompanyDefinition(timeOffPolicyCreateInput));
            assertEquals("Can not create a definition for company id : " + companyId + " since given max future leaves allowed years (6) are greater than 5", ex.getMessage());
        }

        @Test
        void should_create_timeoff_definition_successfully_when_carry_forward_expiry_is_true() {
            val typeId = 500L;
            val entityId = 600L;
            TimeOffPolicyCreateInput timeOffPolicyCreateInput = TimeOffPolicyCreateInput.newBuilder()
                    .policyName("Annual leave policy for IND")
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(TimeOffUnit.DAYS)
                            .value(14.0)
                            .build())
                    .carryForwardConfig(
                            CarryForwardConfigInput.newBuilder()
                                    .enabled(true)
                                    .maxLimit(
                                            TimeOffDurationInput.newBuilder()
                                                    .unit(TimeOffUnit.DAYS)
                                                    .value(5.0)
                                                    .build()
                                    )
                                    .expiryConfig(
                                            CarryForwardExpiryInput.newBuilder()
                                                    .enabled(true)
                                                    .expiry(
                                                            TimeOffDurationInput.newBuilder()
                                                                    .unit(TimeOffUnit.MONTHS)
                                                                    .value(3.0)
                                                                    .build()
                                                    )
                                                    .build()
                                    )
                                    .build()
                    )
                    .timeoffPolicyEntityInput(
                            TimeOffPolicyEntityInput.newBuilder()
                                    .type(EntityType.COMPANY_ENTITY)
                                    .id(entityId)
                                    .build())
                    .isUnlimitedLeavesAllowed(false)
                    .timeOffTypeId(typeId)
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val timeOffType = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(companyId)
                    .status(TimeOffTypeStatus.ACTIVE)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffType);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenReturn(
                    new CompanyDefinitionEntity(
                            301L,
                            302L,
                            new DefinitionEntity(304L, CountryCode.USA, null, false, null, null, null, new HashSet<>(), null, null, null),
                            companyId,
                            null,
                            null,
                            new ArrayList<>()
                    )
            );

            definitionService.createCompanyDefinition(timeOffPolicyCreateInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffNotificationHelper).sendTimeoffPolicyCreatedNotification(any());
            val savedCompanyDefinition = companyDefinitionEntityArgumentCaptor.getValue();

            // assert for company definition
            assertEquals(typeId, savedCompanyDefinition.getTypeId());
            assertEquals(companyId, savedCompanyDefinition.getCompanyId());

            // assert for definition
            assertNotNull(savedCompanyDefinition.getDefinition());
            assertEquals(timeOffPolicyCreateInput.getPolicyName(), savedCompanyDefinition.getDefinition().getName());
            assertNull(savedCompanyDefinition.getDefinition().getCountryCode());
            assertNull(savedCompanyDefinition.getDefinition().getStateCode());
            assertFalse(savedCompanyDefinition.getDefinition().isRequired());
            assertNull(savedCompanyDefinition.getDefinition().getDescription());
            assertNull(savedCompanyDefinition.getDefinition().getClause());
            assertEquals("ANNUAL", savedCompanyDefinition.getDefinition().getBasis());
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, savedCompanyDefinition.getDefinition().getStatus());


            // assert definition.validation
            assertEquals(1, savedCompanyDefinition.getDefinition().getValidations().size());
            assertEquals(List.of(ContractStatus.ONBOARDING.name(), ContractStatus.OFFBOARDING.name(), ContractStatus.ACTIVE.name()), savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().getAllowedContractStatuses());
            assertEquals(timeOffPolicyCreateInput.getAllocatedCount().getUnit(), savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertEquals(14.0, savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertNull(savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().getMaximumValue());
            assertNull(savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().getMinimumValue());
            assertEquals(timeOffPolicyCreateInput.getIsUnlimitedLeavesAllowed(), savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());

            // assert definition.configuration.allocationConfig
            assertNotNull(savedCompanyDefinition.getDefinition().getConfigurations().getAllocationConfig());
            assertEquals("ANNUALLY", savedCompanyDefinition.getDefinition().getConfigurations().getAllocationConfig().getBasis());
            assertTrue(savedCompanyDefinition.getDefinition().getConfigurations().getAllocationConfig().getProrated());

            // assert future leave config
            assertFalse(savedCompanyDefinition.getDefinition().getConfigurations().getFutureLeaveConfig().getEnabled());

            // assert definition.configuration.carryForwardConfig
            assertEquals(timeOffPolicyCreateInput.getCarryForwardConfig().getEnabled(), savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());

            // assert definition.configuration.carryForwardConfig.minForEligibility
            assertEquals(CarryForwardLimitValueType.FIXED, savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMinForEligibility().getType());
            assertEquals(0.0, savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMinForEligibility().getValue());
            assertEquals(TimeOffUnit.DAYS, savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMinForEligibility().getUnit());

            // assert definition.configuration.carryForwardConfig.max limit
            assertEquals(CarryForwardLimitValueType.FIXED, savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getType());
            assertEquals(timeOffPolicyCreateInput.getCarryForwardConfig().getMaxLimit().getValue(), savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getValue());
            assertEquals(timeOffPolicyCreateInput.getCarryForwardConfig().getMaxLimit().getUnit(), savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getUnit());

            //assert definition.configuration.carryForwardConfig.expiry
            assertEquals(timeOffPolicyCreateInput.getCarryForwardConfig().getExpiryConfig().getExpiry().getValue(), savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry().getValue());
            assertEquals(timeOffPolicyCreateInput.getCarryForwardConfig().getExpiryConfig().getExpiry().getUnit(), savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry().getUnit());

            // assert for entity
            assertEquals(EntityType.COMPANY_ENTITY, savedCompanyDefinition.getEntityType());
            assertEquals(entityId, savedCompanyDefinition.getEntityId());

            verify(timeOffDefinitionRuleService).saveRules(anyList());
        }

        @Test
        void should_create_timeoff_definition_successfully_when_future_leave_config_enabled() {
            val typeId = 500L;
            val entityId = 600L;
            TimeOffPolicyCreateInput timeOffPolicyCreateInput = TimeOffPolicyCreateInput.newBuilder()
                    .policyName("Annual leave policy for IND")
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(TimeOffUnit.DAYS)
                            .value(14.0)
                            .build())
                    .carryForwardConfig(
                            CarryForwardConfigInput.newBuilder()
                                    .enabled(true)
                                    .maxLimit(
                                            TimeOffDurationInput.newBuilder()
                                                    .unit(TimeOffUnit.DAYS)
                                                    .value(5.0)
                                                    .build()
                                    )
                                    .expiryConfig(
                                            CarryForwardExpiryInput.newBuilder()
                                                    .enabled(true)
                                                    .expiry(
                                                            TimeOffDurationInput.newBuilder()
                                                                    .unit(TimeOffUnit.MONTHS)
                                                                    .value(3.0)
                                                                    .build()
                                                    )
                                                    .build()
                                    )
                                    .build()
                    )
                    .timeoffPolicyEntityInput(
                            TimeOffPolicyEntityInput.newBuilder()
                                    .type(EntityType.COMPANY_ENTITY)
                                    .id(entityId)
                                    .build())
                    .isUnlimitedLeavesAllowed(false)
                    .timeOffTypeId(typeId)
                    .futureLeaveConfig(FutureLeaveConfigInput.newBuilder()
                            .enabled(true)
                            .lapsableLeaveConfig(
                                    LapsableLeaveConfigInput.newBuilder()
                                            .enabled(true)
                                            .expiry(TimeOffDurationInput.newBuilder()
                                                    .unit(TimeOffUnit.MONTHS)
                                                    .value(3.0)
                                                    .build())
                                            .build()
                            )
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val timeOffType = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(companyId)
                    .status(TimeOffTypeStatus.ACTIVE)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffType);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenReturn(
                    new CompanyDefinitionEntity(
                            301L,
                            302L,
                            new DefinitionEntity(304L, CountryCode.USA, null, false, null, null, null, new HashSet<>(), null, null, null),
                            companyId,
                            null,
                            null,
                            new ArrayList<>()
                    )
            );

            definitionService.createCompanyDefinition(timeOffPolicyCreateInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffNotificationHelper).sendTimeoffPolicyCreatedNotification(any());
            val savedCompanyDefinition = companyDefinitionEntityArgumentCaptor.getValue();

            // assert for company definition
            assertEquals(typeId, savedCompanyDefinition.getTypeId());
            assertEquals(companyId, savedCompanyDefinition.getCompanyId());

            // assert for definition
            assertNotNull(savedCompanyDefinition.getDefinition());
            assertEquals(timeOffPolicyCreateInput.getPolicyName(), savedCompanyDefinition.getDefinition().getName());
            assertNull(savedCompanyDefinition.getDefinition().getCountryCode());
            assertNull(savedCompanyDefinition.getDefinition().getStateCode());
            assertFalse(savedCompanyDefinition.getDefinition().isRequired());
            assertNull(savedCompanyDefinition.getDefinition().getDescription());
            assertNull(savedCompanyDefinition.getDefinition().getClause());
            assertEquals("ANNUAL", savedCompanyDefinition.getDefinition().getBasis());
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, savedCompanyDefinition.getDefinition().getStatus());


            // assert definition.validation
            assertEquals(1, savedCompanyDefinition.getDefinition().getValidations().size());
            assertEquals(List.of(ContractStatus.ONBOARDING.name(), ContractStatus.OFFBOARDING.name(), ContractStatus.ACTIVE.name()), savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().getAllowedContractStatuses());
            assertEquals(timeOffPolicyCreateInput.getAllocatedCount().getUnit(), savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertEquals(14.0, savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertNull(savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().getMaximumValue());
            assertNull(savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().getMinimumValue());
            assertEquals(timeOffPolicyCreateInput.getIsUnlimitedLeavesAllowed(), savedCompanyDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());

            // assert definition.configuration.allocationConfig
            assertNotNull(savedCompanyDefinition.getDefinition().getConfigurations().getAllocationConfig());
            assertEquals("ANNUALLY", savedCompanyDefinition.getDefinition().getConfigurations().getAllocationConfig().getBasis());
            assertTrue(savedCompanyDefinition.getDefinition().getConfigurations().getAllocationConfig().getProrated());

            // assert future leave config
            assertTrue(savedCompanyDefinition.getDefinition().getConfigurations().getFutureLeaveConfig().getEnabled());
            assertTrue(savedCompanyDefinition.getDefinition().getConfigurations().getFutureLeaveConfig().getLapsableLeaveConfig().getEnabled());
            assertEquals(TimeOffUnit.YEARS, savedCompanyDefinition.getDefinition().getConfigurations().getFutureLeaveConfig().getLapsableLeaveConfig().getExpiry().getUnit());
            assertEquals(1.0, savedCompanyDefinition.getDefinition().getConfigurations().getFutureLeaveConfig().getLapsableLeaveConfig().getExpiry().getValue());

            // assert definition.configuration.carryForwardConfig
            assertEquals(timeOffPolicyCreateInput.getCarryForwardConfig().getEnabled(), savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());

            // assert definition.configuration.carryForwardConfig.minForEligibility
            assertEquals(CarryForwardLimitValueType.FIXED, savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMinForEligibility().getType());
            assertEquals(0.0, savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMinForEligibility().getValue());
            assertEquals(TimeOffUnit.DAYS, savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMinForEligibility().getUnit());

            // assert definition.configuration.carryForwardConfig.max limit
            assertEquals(CarryForwardLimitValueType.FIXED, savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getType());
            assertEquals(timeOffPolicyCreateInput.getCarryForwardConfig().getMaxLimit().getValue(), savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getValue());
            assertEquals(timeOffPolicyCreateInput.getCarryForwardConfig().getMaxLimit().getUnit(), savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getUnit());

            //assert definition.configuration.carryForwardConfig.expiry
            assertEquals(timeOffPolicyCreateInput.getCarryForwardConfig().getExpiryConfig().getExpiry().getValue(), savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry().getValue());
            assertEquals(timeOffPolicyCreateInput.getCarryForwardConfig().getExpiryConfig().getExpiry().getUnit(), savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry().getUnit());

            // assert for entity
            assertEquals(EntityType.COMPANY_ENTITY, savedCompanyDefinition.getEntityType());
            assertEquals(entityId, savedCompanyDefinition.getEntityId());

            verify(timeOffDefinitionRuleService).saveRules(anyList());
        }

        @Test
        void should_create_timeoff_definition_successfully_with_carry_forward_expiry_is_false() {
            val typeId = 500L;
            TimeOffPolicyCreateInput timeOffPolicyCreateInput = TimeOffPolicyCreateInput.newBuilder()
                    .policyName("Annual leave policy for IND")
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(TimeOffUnit.DAYS)
                            .value(14.0)
                            .build())
                    .carryForwardConfig(
                            CarryForwardConfigInput.newBuilder()
                                    .enabled(true)
                                    .maxLimit(
                                            TimeOffDurationInput.newBuilder()
                                                    .unit(TimeOffUnit.DAYS)
                                                    .value(5.0)
                                                    .build()
                                    )
                                    .expiryConfig(
                                            CarryForwardExpiryInput.newBuilder()
                                                    .enabled(false)
                                                    .expiry(
                                                            TimeOffDurationInput.newBuilder()
                                                                    .unit(TimeOffUnit.MONTHS)
                                                                    .value(3.0)
                                                                    .build()
                                                    )
                                                    .build()
                                    )
                                    .build()
                    )
                    .isUnlimitedLeavesAllowed(true)
                    .timeOffTypeId(typeId)
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val timeOffType = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(companyId)
                    .status(TimeOffTypeStatus.ACTIVE)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffType);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenReturn(
                    new CompanyDefinitionEntity(
                            301L,
                            302L,
                            new DefinitionEntity(304L, CountryCode.USA, null, false, null, null, null, new HashSet<>(), null, null, null),
                            companyId,
                            null,
                            null,
                            new ArrayList<>()

                    )
            );

            definitionService.createCompanyDefinition(timeOffPolicyCreateInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffNotificationHelper).sendTimeoffPolicyCreatedNotification(any());
            val savedCompanyDefinition = companyDefinitionEntityArgumentCaptor.getValue();


            //assert definition.configuration.carryForwardConfig.expiry
            assertNull(savedCompanyDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry());

            // assert future leave config
            assertFalse(savedCompanyDefinition.getDefinition().getConfigurations().getFutureLeaveConfig().getEnabled());

            // assert for entity - Entity input is not set, hence entity type and entity id should be null
            assertNull(savedCompanyDefinition.getEntityType());
            assertNull(savedCompanyDefinition.getEntityId());

        }

        @Test
        void should_create_timeoff_definition_for_all_multiplier_entities() {
            val typeId = 500L;
            TimeOffPolicyCreateInput timeOffPolicyCreateInput = TimeOffPolicyCreateInput.newBuilder()
                    .policyName("Annual leave policy for IND")
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(TimeOffUnit.DAYS)
                            .value(14.0)
                            .build())
                    .carryForwardConfig(
                            CarryForwardConfigInput.newBuilder()
                                    .enabled(false)
                                    .build()
                    )
                    .isUnlimitedLeavesAllowed(false)
                    .timeOffTypeId(typeId)
                    .timeoffPolicyEntityInput(
                            TimeOffPolicyEntityInput.newBuilder()
                                    .id(-1L)
                                    .type(EntityType.EOR_PARTNER_ENTITY)
                                    .build()
                    )
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val timeOffType = TimeoffTypeDBO.builder()
                    .id(typeId)
                    .companyId(companyId)
                    .status(TimeOffTypeStatus.ACTIVE)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffType);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenReturn(
                    new CompanyDefinitionEntity(
                            301L,
                            302L,
                            new DefinitionEntity(304L, CountryCode.USA, null, false, null, null, null, new HashSet<>(), null, null, null),
                            companyId,
                            null,
                            null,
                            new ArrayList<>()
                    )
            );

            definitionService.createCompanyDefinition(timeOffPolicyCreateInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffNotificationHelper).sendTimeoffPolicyCreatedNotification(any());
            val savedCompanyDefinition = companyDefinitionEntityArgumentCaptor.getValue();

            // assert future leave config
            assertFalse(savedCompanyDefinition.getDefinition().getConfigurations().getFutureLeaveConfig().getEnabled());

            // assert for entity - Entity input is not set, hence entity type and entity id should be null
            assertEquals(EntityType.EOR_PARTNER_ENTITY, savedCompanyDefinition.getEntityType());
            assertEquals( -1L, savedCompanyDefinition.getEntityId());
        }
    }

    @Nested
    class FindCompanyDefinitionsTest {

        @Test
        void should_send_all_company_definitions_if_filter_is_null() {
            val companyUserId = 101L;
            val companyId = 100L;
            val definition1 = new DefinitionEntity();
            val definition2 = new DefinitionEntity();
            val allCompanyDefinitions = List.of(
                    new CompanyDefinitionEntity(100L, 200L, definition1, companyId, null, null, new ArrayList<>()),
                    new CompanyDefinitionEntity(101L, 201L, definition2, companyId, null, null, new ArrayList<>())
            );
            val typesList = List.of(
                    TimeoffTypeDBO.builder().id(200L).key("annualLeave").build(),
                    TimeoffTypeDBO.builder().id(201L).key("sickLeave").build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findAllNonDeletedByCompanyId(companyId)).thenReturn(allCompanyDefinitions);
            when(timeoffTypeService.findAllTypeDBOsByIds(allCompanyDefinitions.stream().map(TimeOffDefinitionEntity::getTypeId).collect(Collectors.toSet())))
                    .thenReturn(typesList);

            definitionService.findCompanyDefinitions(null);
            verify(companyDefinitionRepository).findAllNonDeletedByCompanyId(companyId);
        }

        @Test
        void should_send_company_definitions_by_id_when_id_is_passed() {
            CompanyTimeOffPolicyFilter filter = CompanyTimeOffPolicyFilter.newBuilder()
                    .entityIds(List.of(100L, 101L))
                    .timeOffTypeIds(List.of(100L, 101L))
                    .timeOffPolicyIds(List.of(200L, 201L))
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val definition1 = new DefinitionEntity();
            val definition2 = new DefinitionEntity();
            val companyDefinitions = List.of(
                    new CompanyDefinitionEntity(200L, 200L, definition1, companyId, null, null, new ArrayList<>()),
                    new CompanyDefinitionEntity(201L, 201L, definition2, companyId, null, null, new ArrayList<>())
            );
            val typesList = List.of(
                    TimeoffTypeDBO.builder().id(200L).key("annualLeave").build(),
                    TimeoffTypeDBO.builder().id(201L).key("sickLeave").build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findAllNonDeletedByDefinitionIdInAndCompanyId(filter.getTimeOffPolicyIds(), companyId)).thenReturn(companyDefinitions);
            when(timeoffTypeService.findAllTypeDBOsByIds(companyDefinitions.stream().map(TimeOffDefinitionEntity::getTypeId).collect(Collectors.toSet())))
                    .thenReturn(typesList);

            definitionService.findCompanyDefinitions(filter);
            verify(companyDefinitionRepository).findAllNonDeletedByDefinitionIdInAndCompanyId(filter.getTimeOffPolicyIds(), companyId);
            verify(companyDefinitionRepository, never()).findAllNonDeletedByCompanyId(companyId);
        }

        @Test
        void should_send_company_definitions_by_type_ids_and_entity_ids_when_id_is_not_passed() {
            CompanyTimeOffPolicyFilter filter = CompanyTimeOffPolicyFilter.newBuilder()
                    .entityIds(List.of(100L, 101L))
                    .timeOffTypeIds(List.of(100L, 101L))
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val definition1 = new DefinitionEntity();
            val definition2 = new DefinitionEntity();
            val companyDefinitions = List.of(
                    new CompanyDefinitionEntity(200L, 200L, definition1, companyId, null, null, new ArrayList<>()),
                    new CompanyDefinitionEntity(201L, 201L, definition2, companyId, null, null, new ArrayList<>())
            );
            val typesList = List.of(
                    TimeoffTypeDBO.builder().id(200L).key("annualLeave").build(),
                    TimeoffTypeDBO.builder().id(201L).key("sickLeave").build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findAllNonDeletedByTypeIdInAndCompanyId(filter.getTimeOffTypeIds(), companyId)).thenReturn(companyDefinitions);
            when(timeoffTypeService.findAllTypeDBOsByIds(companyDefinitions.stream().map(TimeOffDefinitionEntity::getTypeId).collect(Collectors.toSet())))
                    .thenReturn(typesList);

            definitionService.findCompanyDefinitions(filter);
            verify(companyDefinitionRepository).findAllNonDeletedByTypeIdInAndCompanyId(filter.getTimeOffTypeIds(), companyId);
            verify(companyDefinitionRepository, never()).findAllNonDeletedByCompanyId(companyId);
        }

        @Test
        void should_send_company_definitions_by_entity_ids_when_only_entity_ids_are_passed() {
            CompanyTimeOffPolicyFilter filter = CompanyTimeOffPolicyFilter.newBuilder()
                    .entityIds(List.of(100L, 101L))
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val definition1 = new DefinitionEntity();
            val definition2 = new DefinitionEntity();
            val companyDefinitions = List.of(
                    new CompanyDefinitionEntity(300L, 200L, definition1, companyId, null, 100L, new ArrayList<>()),
                    new CompanyDefinitionEntity(301L, 201L, definition2, companyId, null, 101L, new ArrayList<>())
            );
            val typesList = List.of(
                    TimeoffTypeDBO.builder().id(200L).key("annualLeave").build(),
                    TimeoffTypeDBO.builder().id(201L).key("sickLeave").build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findAllNonDeletedByCompanyIdAndEntityIdIn(companyId, filter.getEntityIds())).thenReturn(companyDefinitions);
            when(timeoffTypeService.findAllTypeDBOsByIds(companyDefinitions.stream().map(TimeOffDefinitionEntity::getTypeId).collect(Collectors.toSet())))
                    .thenReturn(typesList);

            definitionService.findCompanyDefinitions(filter);
            verify(companyDefinitionRepository).findAllNonDeletedByCompanyIdAndEntityIdIn(companyId, filter.getEntityIds());
            verify(companyDefinitionRepository, never()).findAllNonDeletedByCompanyId(companyId);
        }
    }

    @Nested
    class DeleteDefinitionTest {

        @Captor
        private ArgumentCaptor<CompanyDefinitionEntity> companyDefinitionEntityArgumentCaptor;

        @Test
        void should_throw_validation_exception_when_definition_belongs_to_another_company() {
            val definitionId = 200L;
            val companyUserId = 101L;
            val companyId = 100L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findByDefinitionId(definitionId))
                    .thenReturn(Optional.of(new CompanyDefinitionEntity(1L, 1L, new DefinitionEntity(), 1000L, EntityType.COMPANY_ENTITY, 1L, new ArrayList<>())));
            val ex = assertThrows(ValidationException.class, () -> definitionService.deleteDefinition(definitionId));
            assertEquals("company user (user_id : 10, company_id : 100) try to delete company definition id : 1 owned by another company (id : 1000)", ex.getMessage());
        }

        @Test
        void should_return_null_when_definition_not_exist_to_delete() {
            val definitionId = 200L;
            val companyUserId = 101L;
            val companyId = 100L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findByDefinitionId(definitionId)).thenReturn(Optional.empty());
            val result = definitionService.deleteDefinition(definitionId);
            assertNull(result);
        }

        @Test
        void should_throw_error_when_definition_has_active_assignments() {
            val policyId = 200L;
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    new DefinitionEntity(),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            companyDefinition.getDefinition().setId(policyId);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findByDefinitionId(policyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(policyId)))
                    .thenReturn(List.of(TimeoffEntitlementDBO.builder().contractId(1L).value(2.0).build()));
            val ex = assertThrows(ValidationException.class, () -> definitionService.deleteDefinition(policyId));
            assertEquals("Can not delete company definition id : 100 since it has active assignments.", ex.getMessage());
        }

        @Test
        void should_throw_error_when_member_try_to_delete_definition() {
            val policyId = 200L;
            val memberId = 101L;

            when(currentUser.getContext()).thenReturn(getMemberUserDetails(memberId));
            val ex = assertThrows(ValidationException.class, () -> definitionService.deleteDefinition(policyId));
            assertEquals("Can not delete definition id : 200 for experience : member", ex.getMessage());
        }

        @Test
        void should_delete_definition_successfully_for_company_user() {
            val policyId = 200L;
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    new DefinitionEntity(),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            companyDefinition.getDefinition().setId(policyId);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findByDefinitionId(policyId)).thenReturn(Optional.of(companyDefinition));
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenReturn(
                    new CompanyDefinitionEntity(
                            301L,
                            302L,
                            new DefinitionEntity(304L, CountryCode.USA, null, false, null, null, null, new HashSet<>(), null, null, null),
                            companyId,
                            null,
                            null,
                            new ArrayList<>()
                    )
            );
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(policyId)))
                    .thenReturn(List.of());

            definitionService.deleteDefinition(policyId);
            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffNotificationHelper).sendTimeoffPolicyDeletedNotification(any(), any());
            val result = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(TimeOffTypeDefinitionStatus.DELETED, result.getDefinition().getStatus());
        }

        @Test
        void should_delete_definition_successfully_for_ops_user() {
            val policyId = 200L;
            val userId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    new DefinitionEntity(),
                    1425L,
                    null,
                    null,
                    new ArrayList<>()
            );
            companyDefinition.getDefinition().setId(policyId);

            when(currentUser.getContext()).thenReturn(getOpsUserDetails(userId));
            when(companyDefinitionRepository.findByDefinitionId(policyId)).thenReturn(Optional.of(companyDefinition));
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenReturn(
                    new CompanyDefinitionEntity(
                            301L,
                            302L,
                            new DefinitionEntity(304L, CountryCode.USA, null, false, null, null, null, new HashSet<>(), null, null, null),
                            companyId,
                            null,
                            null,
                            new ArrayList<>()
                    )
            );
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(policyId)))
                    .thenReturn(List.of());

            definitionService.deleteDefinition(policyId);
            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            val result = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(TimeOffTypeDefinitionStatus.DELETED, result.getDefinition().getStatus());
        }

    }

    @Nested
    class UpdateDefinitionTest {
        @Captor
        private ArgumentCaptor<CompanyDefinitionEntity> companyDefinitionEntityArgumentCaptor;
        @Captor
        private ArgumentCaptor<List<TimeoffEntitlementDBO>> entitlementListArgumentCaptor;

        @Test
        void should_throw_error_when_try_to_update_definition_assigned_to_other_companies() {
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("New policy")
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(21.0)
                            .unit(TimeOffUnit.DAYS)
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(1L);

            val ex = assertThrows(ValidationException.class, () -> definitionService.updateDefinition(updateDefinitionInput));
            assertEquals("Can not update definition id : 100 for company id : 100 since it also belongs to other companies", ex.getMessage());
        }

        @Test
        void should_throw_error_when_definition_not_found_in_company_definition_to_update() {
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("New policy")
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(21.0)
                            .unit(TimeOffUnit.DAYS)
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 105L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    null,
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));

            val ex = assertThrows(ValidationException.class, () -> definitionService.updateDefinition(updateDefinitionInput));
            assertEquals("Can not find definition with id : 100, company id : 105 to update", ex.getMessage());
        }

        @Test
        void should_throw_error_when_validation_not_found_in_the_definition_to_update() {
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("New policy")
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(21.0)
                            .unit(TimeOffUnit.DAYS)
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    new DefinitionEntity(),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));

            val ex = assertThrows(DataCorruptionException.class, () -> definitionService.updateDefinition(updateDefinitionInput));
            assertEquals("validation entity not found for definition id :100", ex.getMessage());
        }

        @Test
        void should_throw_error_when_configuration_not_found_in_the_definition_to_update() {
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("New policy")
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(21.0)
                            .unit(TimeOffUnit.DAYS)
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));

            val ex = assertThrows(DataCorruptionException.class, () -> definitionService.updateDefinition(updateDefinitionInput));
            assertEquals("configuration entity not found for definition id :100", ex.getMessage());
        }

        @Test
        void should_throw_error_when_policy_name_is_empty_in_input() {
            // Arrange
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("") // Empty name
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(21.0)
                            .unit(TimeOffUnit.DAYS)
                            .build())
                    .build();

            val companyUserId = 101L;
            val companyId = 100L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));

            // Act & Assert
            val ex = assertThrows(ValidationException.class, () -> definitionService.updateDefinition(updateDefinitionInput));

            assertEquals("Policy name is empty for policy ID: 100", ex.getMessage());
        }

        @Test
        void should_throw_error_when_allocated_count_is_invalid1() {
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Updated")
                    .isUnlimitedLeavesAllowed(false)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(21.25)
                            .unit(TimeOffUnit.DAYS)
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, null);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));

            val ex = assertThrows(ValidationException.class, () -> definitionService.updateDefinition(updateDefinitionInput));
            assertEquals("Allocated days should be multiple of 0.5, but given : 21.25", ex.getMessage());
        }

        @Test
        void should_throw_error_when_allocated_count_is_invalid2() {
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Updated")
                    .isUnlimitedLeavesAllowed(false)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(1.2)
                            .unit(TimeOffUnit.WEEKS)
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, null);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));

            val ex = assertThrows(ValidationException.class, () -> definitionService.updateDefinition(updateDefinitionInput));
            assertEquals("Allocated count should be a integer, but given : 1.2 WEEKS", ex.getMessage());
        }

        @Test
        void should_throw_error_when_carry_forward_max_is_over_than_allocated_count() {
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Updated")
                    .isUnlimitedLeavesAllowed(false)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(10.0)
                            .unit(TimeOffUnit.DAYS)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(true)
                            .maxLimit(TimeOffDurationInput.newBuilder()
                                    .unit(TimeOffUnit.DAYS)
                                    .value(15.0)
                                    .build())
                            .expiryConfig(CarryForwardExpiryInput.newBuilder()
                                    .enabled(true)
                                    .expiry(TimeOffDurationInput.newBuilder()
                                            .value(6.0)
                                            .unit(TimeOffUnit.MONTHS)
                                            .build())
                                    .build())
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, null);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));

            val ex = assertThrows(ValidationException.class, () -> definitionService.updateDefinition(updateDefinitionInput));
            assertEquals("Invalid carry forward config input for definition id : 100, company id : 100, carry forward max leaves (15.0 days) greater than allocated leaves (10.0 days)", ex.getMessage());
        }

        @Test
        void should_update_unlimitedLeavesAllowed_false_and_carryForwardEnabled_false_to_unlimitedLeavesAllowed_false_and_carryForwardEnabled_true() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = false, carryForwardEnabled = false
             *   - going to update =>  name,  allocatedCount and carryForwardEnabled to true
             * */
            val newEntitledCount = 14.0;
            val newEntitledUnit = TimeOffUnit.WEEKS;
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves updated")
                    .isUnlimitedLeavesAllowed(false)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(newEntitledCount)
                            .unit(newEntitledUnit)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(true)
                            .maxLimit(TimeOffDurationInput.newBuilder()
                                    .unit(TimeOffUnit.WEEKS)
                                    .value(1.0)
                                    .build())
                            .expiryConfig(CarryForwardExpiryInput.newBuilder()
                                    .enabled(true)
                                    .expiry(TimeOffDurationInput.newBuilder()
                                            .value(6.0)
                                            .unit(TimeOffUnit.MONTHS)
                                            .build())
                                    .build())
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(newEntitledCount, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(false, null, null, null);
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(14.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(14.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(3L).typeId(200L).contractId(300L).value(150.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(300L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L, 300L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(false, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(newEntitledCount, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(TimeOffUnit.WEEKS, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertTrue(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertEquals(1.0, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getValue());
            assertEquals(TimeOffUnit.WEEKS, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getUnit());
            assertEquals(6.0, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry().getValue());
            assertEquals(TimeOffUnit.MONTHS, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry().getUnit());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(14.0) &&
                            ent.unit() == TimeOffUnit.WEEKS));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(14.0) &&
                            ent.unit() == TimeOffUnit.WEEKS));
        }

        @Test
        void should_update_unlimitedLeavesAllowed_false_and_carryForwardEnabled_false_to_unlimitedLeavesAllowed_false_and_carryForwardEnabled_false() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = false, carryForwardEnabled = false
             *   - going to update =>  name,  allocatedCount fields only, carryForward remains same
             * */
            val newEntitledCount = 21.0;
            val newEntitledUnit = TimeOffUnit.DAYS;
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves updated")
                    .isUnlimitedLeavesAllowed(false)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(newEntitledCount)
                            .unit(newEntitledUnit)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(false)
                            .maxLimit(TimeOffDurationInput.newBuilder()
                                    .build())
                            .expiryConfig(CarryForwardExpiryInput.newBuilder()
                                    .enabled(false)
                                    .expiry(TimeOffDurationInput.newBuilder()
                                            .build())
                                    .build())
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(false, null, null, null);
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(14.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(14.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(3L).typeId(200L).contractId(300L).value(150.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(300L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L, 300L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class)))
                    .thenAnswer(invocation -> invocation.<CompanyDefinitionEntity>getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(false, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(newEntitledCount, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(TimeOffUnit.DAYS, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertFalse(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMinForEligibility());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(newEntitledCount) &&
                            ent.unit() == newEntitledUnit));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(newEntitledCount) &&
                            ent.unit() == newEntitledUnit));
        }

        @Test
        void should_update_unlimitedLeavesAllowed_false_and_carryForwardEnabled_false_to_unlimitedLeavesAllowed_true_and_carryForwardEnabled_false() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = false, carryForwardEnabled = false
             *   - going to update =>  name,  allocatedCount fields and unlimitedLeavesAllowed to true and carryForward remain false
             * */
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves")
                    .isUnlimitedLeavesAllowed(true)
                    .allocatedCount(TimeOffDurationInput.newBuilder().build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(false)
                            .maxLimit(TimeOffDurationInput.newBuilder().build())
                            .expiryConfig(CarryForwardExpiryInput.newBuilder()
                                    .enabled(false)
                                    .expiry(TimeOffDurationInput.newBuilder().build())
                                    .build())
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(false, null, null, null);
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(15.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(15.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(true, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(365.0, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(TimeOffUnit.DAYS, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertFalse(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMinForEligibility());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));

        }

        @Test
        void should_update_unlimitedLeavesAllowed_false_and_carryForwardEnabled_false_to_unlimitedLeavesAllowed_true_and_carryForwardEnabled_true() {
            /* use case : (Not a valid business case -> testing for bad inputs)
             *   - created definition => unlimitedLeavesAllowed = false, carryForwardEnabled = false
             *   - going to update =>  name,  allocatedCount fields and unlimitedLeavesAllowed to true and carryForward to true
             * */
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves")
                    .isUnlimitedLeavesAllowed(true)
                    .allocatedCount(TimeOffDurationInput.newBuilder().build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(true)
                            .maxLimit(TimeOffDurationInput.newBuilder()
                                    .unit(TimeOffUnit.DAYS)
                                    .value(10.0)
                                    .build())
                            .expiryConfig(CarryForwardExpiryInput.newBuilder()
                                    .enabled(false)
                                    .expiry(TimeOffDurationInput.newBuilder().build())
                                    .build())
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(false, null, null, null);
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(15.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(15.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(true, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(365.0, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(TimeOffUnit.DAYS, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertFalse(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMinForEligibility());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
        }

        @Test
        void should_throw_error_unlimitedLeavesAllowed_false_and_carryForwardEnabled_true_to_unlimitedLeavesAllowed_false_and_carryForwardEnabled_false() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = false, carryForwardEnabled = true
             *   - going to update =>  name,  allocatedCount fields and carryForward to false, unlimitedLeavesAllowed remains false
             * */
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves")
                    .isUnlimitedLeavesAllowed(false)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .value(17.0)
                            .unit(TimeOffUnit.DAYS)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(false)
                            .maxLimit(TimeOffDurationInput.newBuilder()
                                    .unit(TimeOffUnit.DAYS)
                                    .value(10.0)
                                    .build())
                            .expiryConfig(CarryForwardExpiryInput.newBuilder()
                                    .enabled(false)
                                    .expiry(TimeOffDurationInput.newBuilder().build())
                                    .build())
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(true, null, null, null);
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));

            val ex = assertThrows(ValidationException.class, () -> definitionService.updateDefinition(updateDefinitionInput));
            assertEquals("Disabling carry forward leaves not permitted for already carry forward enabled company definition id:100", ex.getMessage());
        }

        @Test
        void should_update_unlimitedLeavesAllowed_false_and_carryForwardEnabled_true_to_unlimitedLeavesAllowed_false_and_carryForwardEnabled_true() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = false, carryForwardEnabled = true
             *   - going to update =>  name,  allocatedCount fields only and unlimitedLeavesAllowed remains false and carryForward remains true
             * */
            val newEntitledValue = 25.0;
            val newEntitledUnit = TimeOffUnit.DAYS;
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves updated")
                    .isUnlimitedLeavesAllowed(false)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(newEntitledUnit)
                            .value(newEntitledValue)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(true)
                            .maxLimit(TimeOffDurationInput.newBuilder()
                                    .unit(TimeOffUnit.DAYS)
                                    .value(10.0)
                                    .build())
                            .expiryConfig(CarryForwardExpiryInput.newBuilder()
                                    .enabled(true)
                                    .expiry(TimeOffDurationInput.newBuilder()
                                            .unit(TimeOffUnit.MONTHS)
                                            .value(6.0)
                                            .build())
                                    .build())
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(true,
                    null,
                    new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 10.0, TimeOffUnit.DAYS),
                    new CarryForwardExpiryEntity(6.0, TimeOffUnit.MONTHS));
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(15.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(15.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class)))
                    .thenAnswer(invocation -> invocation.getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(false, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(newEntitledValue, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(newEntitledUnit, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertTrue(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertEquals(10.0, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getValue());
            assertEquals(TimeOffUnit.DAYS, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getUnit());
            assertEquals(6.0, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry().getValue());
            assertEquals(TimeOffUnit.MONTHS, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry().getUnit());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(newEntitledValue) &&
                            ent.unit() == newEntitledUnit));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(newEntitledValue) &&
                            ent.unit() == newEntitledUnit));
        }

        @Test
        void should_update_unlimitedLeavesAllowed_false_and_carryForwardEnabled_true_to_unlimitedLeavesAllowed_true_and_carryForwardEnabled_false() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = false, carryForwardEnabled = true
             *   - going to update =>  name,  allocatedCount fields and unlimitedLeavesAllowed to true and carryForward to false
             * */
            val newEntitledValue = 25.0;
            val newEntitledUnit = TimeOffUnit.DAYS;
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves updated")
                    .isUnlimitedLeavesAllowed(true)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(newEntitledUnit)
                            .value(newEntitledValue)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(false)
                            .maxLimit(null)
                            .expiryConfig(null)
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(true,
                    null,
                    new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 10.0, TimeOffUnit.DAYS),
                    new CarryForwardExpiryEntity(6.0, TimeOffUnit.MONTHS));
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(15.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(15.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(true, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(365.0, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(TimeOffUnit.DAYS, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertFalse(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
        }

        @Test
        void should_update_unlimitedLeavesAllowed_false_and_carryForwardEnabled_true_to_unlimitedLeavesAllowed_true_and_carryForwardEnabled_true() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = false, carryForwardEnabled = true
             *   - going to update =>  name,  allocatedCount fields and unlimitedLeavesAllowed to true and carryForward to true
             *              -> should keep carry forward in false
             * */
            val newEntitledValue = 25.0;
            val newEntitledUnit = TimeOffUnit.DAYS;
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves updated")
                    .isUnlimitedLeavesAllowed(true)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(newEntitledUnit)
                            .value(newEntitledValue)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(true)
                            .maxLimit(TimeOffDurationInput.newBuilder()
                                    .unit(TimeOffUnit.DAYS)
                                    .value(10.0)
                                    .build())
                            .expiryConfig(CarryForwardExpiryInput.newBuilder()
                                    .enabled(true)
                                    .expiry(TimeOffDurationInput.newBuilder()
                                            .unit(TimeOffUnit.MONTHS)
                                            .value(6.0)
                                            .build())
                                    .build())
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(true,
                    null,
                    new CarryForwardLimitEntity(CarryForwardLimitValueType.FIXED, 10.0, TimeOffUnit.DAYS),
                    new CarryForwardExpiryEntity(6.0, TimeOffUnit.MONTHS));
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(15.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(15.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(true, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(365.0, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(TimeOffUnit.DAYS, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertFalse(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
        }

        @Test
        void should_update_unlimitedLeavesAllowed_true_and_carryForwardEnabled_false_to_unlimitedLeavesAllowed_true_and_carryForwardEnabled_false() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = true, carryForwardEnabled = false
             *   - going to update =>  name, allocatedCount fields only, carryForward remain false unlimitedLeavesAllowed remains true
             * */
            val newEntitledValue = 25.0;
            val newEntitledUnit = TimeOffUnit.DAYS;
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves updated")
                    .isUnlimitedLeavesAllowed(true)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(newEntitledUnit)
                            .value(newEntitledValue)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(false)
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(false,
                    null,
                    null,
                    null);
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(15.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(15.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(true, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(365.0, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(TimeOffUnit.DAYS, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertFalse(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
        }

        @Test
        void should_update_unlimitedLeavesAllowed_true_and_carryForwardEnabled_false_to_unlimitedLeavesAllowed_true_and_carryForwardEnabled_true() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = true, carryForwardEnabled = false
             *   - going to update =>  name, allocatedCount fields only, unlimitedLeavesAllowed remains true, carryForward to true
             * */
            val newEntitledValue = 25.0;
            val newEntitledUnit = TimeOffUnit.DAYS;
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves updated")
                    .isUnlimitedLeavesAllowed(true)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(newEntitledUnit)
                            .value(newEntitledValue)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(true)
                            .maxLimit(TimeOffDurationInput.newBuilder()
                                    .unit(TimeOffUnit.DAYS)
                                    .value(10.0)
                                    .build())
                            .expiryConfig(CarryForwardExpiryInput.newBuilder()
                                    .enabled(true)
                                    .expiry(TimeOffDurationInput.newBuilder()
                                            .unit(TimeOffUnit.MONTHS)
                                            .value(6.0)
                                            .build())
                                    .build())
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(false,
                    null,
                    null,
                    null);
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(15.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(15.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(true, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(365.0, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(TimeOffUnit.DAYS, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertFalse(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(365.0) &&
                            ent.unit() == TimeOffUnit.DAYS));
        }

        @Test
        void should_update_unlimitedLeavesAllowed_true_and_carryForwardEnabled_false_to_unlimitedLeavesAllowed_false_and_carryForwardEnabled_true() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = true, carryForwardEnabled = false
             *   - going to update =>  name, allocatedCount fields and unlimitedLeavesAllowed to false, carryForward to true
             * */
            val newEntitledValue = 25.0;
            val newEntitledUnit = TimeOffUnit.DAYS;
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves updated")
                    .isUnlimitedLeavesAllowed(false)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(newEntitledUnit)
                            .value(newEntitledValue)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(true)
                            .maxLimit(TimeOffDurationInput.newBuilder()
                                    .unit(TimeOffUnit.DAYS)
                                    .value(10.0)
                                    .build())
                            .expiryConfig(CarryForwardExpiryInput.newBuilder()
                                    .enabled(true)
                                    .expiry(TimeOffDurationInput.newBuilder()
                                            .unit(TimeOffUnit.MONTHS)
                                            .value(6.0)
                                            .build())
                                    .build())
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(false,
                    null,
                    null,
                    null);
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(15.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(15.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(false, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(newEntitledValue, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(newEntitledUnit, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertTrue(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertEquals(10.0, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getValue());
            assertEquals(TimeOffUnit.DAYS, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit().getUnit());
            assertEquals(6.0, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry().getValue());
            assertEquals(TimeOffUnit.MONTHS, updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry().getUnit());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(newEntitledValue) &&
                            ent.unit() == newEntitledUnit));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(newEntitledValue) &&
                            ent.unit() == newEntitledUnit));
        }

        @Test
        void should_update_unlimitedLeavesAllowed_true_and_carryForwardEnabled_false_to_unlimitedLeavesAllowed_false_and_carryForwardEnabled_false() {
            /* use case :
             *   - created definition => unlimitedLeavesAllowed = true, carryForwardEnabled = false
             *   - going to update =>  name, allocatedCount fields and unlimitedLeavesAllowed to false, carryForward remains false
             * */
            val newEntitledValue = 25.0;
            val newEntitledUnit = TimeOffUnit.DAYS;
            val updateDefinitionInput = TimeOffPolicyUpdateInput.newBuilder()
                    .id(100L)
                    .policyName("Sri Lanka unlimited annual leaves updated")
                    .isUnlimitedLeavesAllowed(false)
                    .allocatedCount(TimeOffDurationInput.newBuilder()
                            .unit(newEntitledUnit)
                            .value(newEntitledValue)
                            .build())
                    .carryForwardConfig(CarryForwardConfigInput.newBuilder()
                            .enabled(false)
                            .build())
                    .build();
            val companyUserId = 101L;
            val companyId = 100L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    buildDefinitionEntity(14.0, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            val carryForwardConfigEntity = new CarryForwardConfigEntity(false,
                    null,
                    null,
                    null);
            val configurationEntity = new TimeoffDefinitionConfigEntity(null, carryForwardConfigEntity);
            companyDefinition.getDefinition().setConfigurations(configurationEntity);

            val existingEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().id(1L).typeId(200L).contractId(100L).value(15.0).unit(TimeOffUnit.DAYS).build(),
                    TimeoffEntitlementDBO.builder().id(2L).typeId(200L).contractId(200L).value(15.0).unit(TimeOffUnit.DAYS).build()
            );
            val assignedContracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(100L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(200L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.countNonDeletedByDefinitionIdAndCompanyIdNot(updateDefinitionInput.getId(), companyId))
                    .thenReturn(0L);
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(updateDefinitionInput.getId(),
                    companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(updateDefinitionInput.getId())))
                    .thenReturn(existingEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(Set.of(100L, 200L)))
                    .thenReturn(assignedContracts);
            when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class))).thenAnswer(invocation -> invocation.getArgument(0));
            when(timeoffTypeService.findTimeOffTypeDBOById(companyDefinition.getTypeId())).thenReturn(
                    TimeoffTypeDBO.builder().companyId(companyId).id(companyDefinition.getTypeId()).build());

            definitionService.updateDefinition(updateDefinitionInput);

            verify(companyDefinitionRepository).save(companyDefinitionEntityArgumentCaptor.capture());
            verify(timeoffTypeService).findTimeOffTypeDBOById(companyDefinition.getTypeId());
            verify(timeoffNotificationHelper).sendTimeoffPolicyUpdatedNotification(any(), any());
            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(anyList(), anyMap(), any());

            val updatedDefinition = companyDefinitionEntityArgumentCaptor.getValue();
            assertEquals(updateDefinitionInput.getPolicyName(), updatedDefinition.getDefinition().getName());
            assertEquals(false, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().isUnlimitedLeavesAllowed());
            assertEquals(newEntitledValue, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getDefaultValue());
            assertEquals(newEntitledUnit, updatedDefinition.getDefinition().getValidations().stream().findFirst().get().getUnit());
            assertFalse(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getEnabled());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getMaxLimit());
            assertNull(updatedDefinition.getDefinition().getConfigurations().getCarryForwardConfig().getExpiry());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            assertEquals(2, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 100L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(newEntitledValue) &&
                            ent.unit() == newEntitledUnit));
            assertTrue(updatedEntitlements.stream()
                    .anyMatch(ent -> ent.contractId() == 200L &&
                            ent.type().id() == 200L &&
                            ent.value().equals(newEntitledValue) &&
                            ent.unit() == newEntitledUnit));
        }

    }

    @Nested
    class ValidateAssignmentsTest {

        @Test
        void should_throw_error_when_company_definition_not_found_to_validate() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 500L;
            val typeId = 300L;
            TimeOffPolicyValidateUsersInput input = TimeOffPolicyValidateUsersInput.newBuilder()
                    .timeOffTypeId(typeId)
                    .timeOffPolicyId(policyId)
                    .contractIds(List.of(1L, 2L))
                    .build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId))
                    .thenReturn(Optional.empty());
            when(authorizationService.filterAccessibleContractIds(dfe, input.getContractIds())).thenReturn(input.getContractIds());

            val ex = assertThrows(ValidationException.class, () -> definitionService.validateDefinitionAssignment(input, dfe));
            assertEquals("Can not find definition id: 500 for company id : 100", ex.getMessage());
        }

        @Test
        void should_throw_error_when_request_has_inaccessible_contract_ids() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 500L;
            val typeId = 300L;
            TimeOffPolicyValidateUsersInput input = TimeOffPolicyValidateUsersInput.newBuilder()
                    .timeOffTypeId(typeId)
                    .timeOffPolicyId(policyId)
                    .contractIds(List.of(1L, 2L)) // 2L is inaccessible
                    .build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(authorizationService.filterAccessibleContractIds(dfe, input.getContractIds())).thenReturn(List.of(1L));

            val ex = assertThrows(ValidationException.class, () -> definitionService.validateDefinitionAssignment(input, dfe));
            assertEquals("User tried to validate policy assignment to un-authorized contracts. policy id = 500 access denied ids : [2]", ex.getMessage());
        }

        @Test
        void should_validate_contract_ids_when_no_existing_rules_found() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 500L;
            val typeId = 300L;
            val entitledCount = 21;
            val entitlements = List.of(
                    TimeoffEntitlementDBO.builder().contractId(1L).typeId(typeId).value(14.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(2L).typeId(typeId).value(24.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(3L).typeId(typeId).value(12.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(4L).typeId(typeId).value(24.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(5L).typeId(typeId).value(14.0).build()
            );

            /*
             * contract id - 1L - invalid assignment - company id not equal to definition company id
             * contract id - 2L - invalid assignment - EOR employee, policy entitled count < current entitled count
             * contract id - 3L - valid assignment - EOR employee, policy entitled count > current entitled count
             * contract id - 4L - valid assignment - HRIS employee, policy entitled count < current entitled count
             * contract id - 5L - valid assignment - HRIS employee, policy entitled count > current entitled count
             * contract id - 6L - valid assignment - no existing entitlements
             */
            val contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(1000L).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(3L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(4L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(5L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(6L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build()
            );

            TimeOffPolicyValidateUsersInput input = TimeOffPolicyValidateUsersInput.newBuilder()
                    .timeOffTypeId(typeId)
                    .timeOffPolicyId(policyId)
                    .contractIds(List.of(1L, 2L, 3L, 4L, 5L, 6L))
                    .build();
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    typeId,
                    buildDefinitionEntity(entitledCount, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            companyDefinition.getDefinition().setId(policyId);
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId))
                    .thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByContractIdInAndTypeId(input.getContractIds(), typeId))
                    .thenReturn(entitlements);
            when(timeoffEntitlementDBORepository.findAllByDefinitionId(policyId))
                    .thenReturn(List.of());
            when(contractServiceAdapter.getContractsByIdsAnyStatus(input.getContractIds()))
                    .thenReturn(contracts);
            when(authorizationService.filterAccessibleContractIds(dfe, input.getContractIds())).thenReturn(input.getContractIds());

            val result = definitionService.validateDefinitionAssignment(input, dfe);

            assertEquals(2, result.getInvalidContractsToAssign().size());
            assertTrue(result.getInvalidContractsToAssign().stream().anyMatch(c -> c.getId().equals(1L)));
            assertTrue(result.getInvalidContractsToAssign().stream().anyMatch(c -> c.getId().equals(2L)));

            assertEquals(4, result.getValidContractsToAssign().size());
            assertTrue(result.getValidContractsToAssign().stream().anyMatch(c -> c.getId().equals(3L)));
            assertTrue(result.getValidContractsToAssign().stream().anyMatch(c -> c.getId().equals(4L)));
            assertTrue(result.getValidContractsToAssign().stream().anyMatch(c -> c.getId().equals(5L)));
            assertTrue(result.getValidContractsToAssign().stream().anyMatch(c -> c.getId().equals(6L)));

            assertTrue(result.getInvalidContractsToUnassigned().isEmpty());

        }

        @Test
        void should_validate_contract_ids_when_there_are_existing_rules() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 500L;
            val typeId = 300L;
            val entitledCount = 21;
            val entitlements = List.of(
                    TimeoffEntitlementDBO.builder().contractId(1L).typeId(typeId).value(14.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(2L).typeId(typeId).value(24.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(3L).typeId(typeId).value(12.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(4L).typeId(typeId).value(24.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(5L).typeId(typeId).value(14.0).build()
            );
            val alreadyAssignedEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().contractId(11L).typeId(typeId).value(14.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(12L).typeId(typeId).value(14.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(13L).typeId(typeId).value(14.0).build()
            );

            /*
             * contract id - 1L - invalid assignment - company id not equal to definition company id
             * contract id - 2L - invalid assignment - EOR employee, policy entitled count < current entitled count
             * contract id - 3L - valid assignment - EOR employee, policy entitled count > current entitled count
             * contract id - 4L - valid assignment - HRIS employee, policy entitled count < current entitled count
             * contract id - 5L - valid assignment - HRIS employee, policy entitled count > current entitled count
             * contract id - 6L - valid assignment - no existing entitlements
             *
             * contract id - 11L - HRIS contract -> valid contract to un-assign
             * contract id - 12L - EOR contract -> invalid contract to un-assign
             * contract id - 13L - EOR contract -> invalid contract to un-assign
             */
            val contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(1000L).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(3L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(4L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(5L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(6L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(11L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(12L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(13L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build()
            );

            TimeOffPolicyValidateUsersInput input = TimeOffPolicyValidateUsersInput.newBuilder()
                    .timeOffTypeId(typeId)
                    .timeOffPolicyId(policyId)
                    .contractIds(List.of(1L, 2L, 3L, 4L, 5L, 6L))
                    .build();
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    typeId,
                    buildDefinitionEntity(entitledCount, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            companyDefinition.getDefinition().setId(policyId);
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId))
                    .thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByContractIdInAndTypeId(input.getContractIds(), typeId))
                    .thenReturn(entitlements);
            when(timeoffEntitlementDBORepository.findAllByDefinitionId(policyId))
                    .thenReturn(alreadyAssignedEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(ListUtils.union(input.getContractIds(), List.of(11L, 12L, 13L))))
                    .thenReturn(contracts);
            when(authorizationService.filterAccessibleContractIds(dfe, input.getContractIds())).thenReturn(input.getContractIds());

            val result = definitionService.validateDefinitionAssignment(input, dfe);

            assertEquals(2, result.getInvalidContractsToAssign().size());
            assertTrue(result.getInvalidContractsToAssign().stream().anyMatch(c -> c.getId().equals(1L)));
            assertTrue(result.getInvalidContractsToAssign().stream().anyMatch(c -> c.getId().equals(2L)));

            assertEquals(4, result.getValidContractsToAssign().size());
            assertTrue(result.getValidContractsToAssign().stream().anyMatch(c -> c.getId().equals(3L)));
            assertTrue(result.getValidContractsToAssign().stream().anyMatch(c -> c.getId().equals(4L)));
            assertTrue(result.getValidContractsToAssign().stream().anyMatch(c -> c.getId().equals(5L)));
            assertTrue(result.getValidContractsToAssign().stream().anyMatch(c -> c.getId().equals(6L)));

            assertEquals(2, result.getInvalidContractsToUnassigned().size());
            assertTrue(result.getInvalidContractsToUnassigned().stream().anyMatch(c -> c.getId().equals(12L)));
            assertTrue(result.getInvalidContractsToUnassigned().stream().anyMatch(c -> c.getId().equals(13L)));
        }

        @Test
        void should_return_invalid_list_for_unassignment_when_contract_ids_to_validate_are_empty() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 134L;
            val typeId = 1L;
            val entitledCount = 21;
            TimeOffPolicyValidateUsersInput input = TimeOffPolicyValidateUsersInput.newBuilder()
                    .contractIds(List.of())
                    .timeOffPolicyId(policyId)
                    .timeOffTypeId(2L)
                    .build();

            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    typeId,
                    buildDefinitionEntity(entitledCount, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            companyDefinition.getDefinition().setId(policyId);
            val alreadyAssignedEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().contractId(1L).typeId(typeId).value(14.0).definitionId(policyId).build(), // EOR
                    TimeoffEntitlementDBO.builder().contractId(2L).typeId(typeId).value(24.0).definitionId(policyId).build() // HR MEMBER
            );
            val contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(1000L).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build());
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId))
                    .thenReturn(Optional.of(companyDefinition));
            when(timeoffEntitlementDBORepository.findAllByDefinitionId(policyId))
                    .thenReturn(alreadyAssignedEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(List.of(1L, 2L)))
                    .thenReturn(contracts);
            when(authorizationService.filterAccessibleContractIds(dfe, input.getContractIds())).thenReturn(input.getContractIds());

            val result = definitionService.validateDefinitionAssignment(input, dfe);
            assertTrue(result.getValidContractsToAssign().isEmpty());
            assertTrue(result.getInvalidContractsToAssign().isEmpty());
            assertEquals(1, result.getInvalidContractsToUnassigned().size());
            assertEquals(1L, result.getInvalidContractsToUnassigned().get(0).getId());
        }

    }

    @Nested
    class AssignOrUnAssignUsersTest {

        @Captor
        private ArgumentCaptor<List<TimeoffEntitlementDBO>> entitlementListArgumentCaptor;

        @Captor
        private ArgumentCaptor<List<TimeOffDefinitionRuleDBO>> definitionRulelistArgumentCaptor;

        @Test
        void should_throw_error_if_policy_rule_is_null() {
            val companyUserId = 101L;
            val companyId = 100L;
            TimeOffPolicyAssignUsersInput input = TimeOffPolicyAssignUsersInput.newBuilder()
                    .build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            val ex = assertThrows(IllegalArgumentException.class, () -> definitionService.assignOrUnassignUsers(dfe, input));
            assertEquals("Can not assign definition for users with empty rule for company id : 100", ex.getMessage());
        }

        @Test
        void should_assign_for_contract_ids_and_save_rule_all_when_no_existing_rules_found() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 200L;
            val typeId = 300L;
            val entitledCount = 14;
            val entitlements = List.of(
                    TimeoffEntitlementDBO.builder().contractId(1L).typeId(typeId).value(7.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(2L).typeId(typeId).value(10.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(3L).typeId(typeId).value(21.0).build(), // should not update this
                    TimeoffEntitlementDBO.builder().contractId(4L).typeId(typeId).value(10.0).build(), // should not update this
                    TimeoffEntitlementDBO.builder().contractId(5L).typeId(typeId).value(10.0).build()
            );
            TimeOffPolicyAssignUsersInput input = TimeOffPolicyAssignUsersInput.newBuilder()
                    .timeOffPolicyId(policyId)
                    .timeOffPolicyRule(RuleInput.newBuilder().type(RuleType.ALL).build())
                    .contractIds(List.of(1L, 2L, 3L, 4L, 5L, 6L))
                    .build();
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    typeId,
                    buildDefinitionEntity(entitledCount, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            companyDefinition.getDefinition().setId(policyId);
            val contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(3L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(4L).setCompanyId(4000L).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(5L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(6L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );
            val timeOffTypeDBO = TimeoffTypeDBO.builder().id(typeId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId))
                    .thenReturn(Optional.of(companyDefinition));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffTypeDBO);
            when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinition.getId()))).thenReturn(List.of());
            when(timeoffEntitlementDBORepository.findAllByContractIdInAndTypeId(input.getContractIds(), typeId)).thenReturn(entitlements);
            when(timeoffEntitlementDBORepository.findAllByDefinitionId(policyId)).thenReturn(List.of());
            when(contractServiceAdapter.getContractsByIdsAnyStatus(input.getContractIds())).thenReturn(contracts);
            when(timeOffTypeDefinitionMapper.map(companyDefinition, timeOffTypeDBO)).thenReturn(TimeOffTypeDefinition.newBuilder().build());
            when(authorizationService.filterAccessibleContractIds(dfe, input.getContractIds())).thenReturn(input.getContractIds());

            definitionService.assignOrUnassignUsers(dfe, input);

            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeOffDefinitionRuleService).saveRules(definitionRulelistArgumentCaptor.capture());
            verify(timeOffDefinitionRuleService, never()).deleteRules(any());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(any(), any(), any());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            val savedRules = definitionRulelistArgumentCaptor.getValue();

            assertEquals(4, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(1L) && e.value() == 14.0));
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(2L) && e.value() == 14.0));
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(5L) && e.value() == 14.0));
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(6L) && e.value() == 14.0));

            assertEquals(1, savedRules.size());
            assertEquals(RuleType.ALL, savedRules.get(0).ruleType());

        }

        @Test
        void should_assign_for_contract_ids_and_save_condition_rules_when_no_existing_rules_found() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 200L;
            val typeId = 300L;
            val entitledCount = 14;
            val entitlements = List.of(
                    TimeoffEntitlementDBO.builder().contractId(1L).typeId(typeId).value(7.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(2L).typeId(typeId).value(10.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(3L).typeId(typeId).value(21.0).build(), // should not update this
                    TimeoffEntitlementDBO.builder().contractId(4L).typeId(typeId).value(10.0).build(), // should not update this
                    TimeoffEntitlementDBO.builder().contractId(5L).typeId(typeId).value(10.0).build()
            );
            TimeOffPolicyAssignUsersInput input = TimeOffPolicyAssignUsersInput.newBuilder()
                    .timeOffPolicyId(policyId)
                    .timeOffPolicyRule(RuleInput.newBuilder().type(RuleType.BY_CONDITION)
                            .conditions(List.of(
                                    ConditionInput.newBuilder().key(ConditionKey.DEPARTMENT).operator(ConditionOperator.EQUALS).values(List.of("1", "2")).build(),
                                    ConditionInput.newBuilder().key(ConditionKey.ENTITY).operator(ConditionOperator.EQUALS).values(List.of("3", "4")).build()
                            ))
                            .build())
                    .contractIds(List.of(1L, 2L, 3L, 4L, 5L, 6L))
                    .build();
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    typeId,
                    buildDefinitionEntity(entitledCount, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            companyDefinition.getDefinition().setId(policyId);
            val contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(3L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(4L).setCompanyId(4000L).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(5L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(6L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );

            val timeOffTypeDBO = TimeoffTypeDBO.builder().id(typeId).build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffTypeDBO);
            when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinition.getId()))).thenReturn(List.of());
            when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinition.getId()))).thenReturn(List.of());
            when(timeoffEntitlementDBORepository.findAllByContractIdInAndTypeId(input.getContractIds(), typeId)).thenReturn(entitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(input.getContractIds())).thenReturn(contracts);
            when(timeOffTypeDefinitionMapper.map(companyDefinition, timeOffTypeDBO)).thenReturn(TimeOffTypeDefinition.newBuilder().build());
            when(authorizationService.filterAccessibleContractIds(dfe, input.getContractIds())).thenReturn(input.getContractIds());

            definitionService.assignOrUnassignUsers(dfe, input);

            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeOffDefinitionRuleService).saveRules(definitionRulelistArgumentCaptor.capture());
            verify(timeOffDefinitionRuleService, never()).deleteRules(any());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(any(), any(), any());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            val savedRules = definitionRulelistArgumentCaptor.getValue();

            assertEquals(4, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(1L) && e.value() == 14.0));
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(2L) && e.value() == 14.0));
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(5L) && e.value() == 14.0));
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(6L) && e.value() == 14.0));

            assertEquals(2, savedRules.size());
            assertTrue(savedRules.stream().anyMatch(r -> r.ruleType() == RuleType.BY_CONDITION && r.conditionKey() == ConditionKey.DEPARTMENT));
            assertTrue(savedRules.stream().anyMatch(r -> r.ruleType() == RuleType.BY_CONDITION && r.conditionKey() == ConditionKey.ENTITY));

        }

        @Test
        void should_assign_for_contract_ids_and_save_condition_rules_when_there_are_existing_rules() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 200L;
            val typeId = 300L;
            val entitledCount = 14;

            val entitlements = List.of(
                    TimeoffEntitlementDBO.builder().contractId(1L).typeId(typeId).value(7.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(2L).typeId(typeId).value(10.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(3L).typeId(typeId).value(21.0).build(), // should not update this
                    TimeoffEntitlementDBO.builder().contractId(4L).typeId(typeId).value(10.0).build(), // should not update this
                    TimeoffEntitlementDBO.builder().contractId(5L).typeId(typeId).value(10.0).build()
            );
            val alreadyAssignedEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().contractId(10L).typeId(typeId).value(10.0).build(), // should not unassign this
                    TimeoffEntitlementDBO.builder().contractId(11L).typeId(typeId).value(10.0).build(),
                    TimeoffEntitlementDBO.builder().contractId(12L).typeId(typeId).value(10.0).build()
            );
            TimeOffPolicyAssignUsersInput input = TimeOffPolicyAssignUsersInput.newBuilder()
                    .timeOffPolicyId(policyId)
                    .timeOffPolicyRule(RuleInput.newBuilder().type(RuleType.BY_CONDITION)
                            .conditions(List.of(
                                    ConditionInput.newBuilder().key(ConditionKey.DEPARTMENT).operator(ConditionOperator.EQUALS).values(List.of("1", "2")).build(),
                                    ConditionInput.newBuilder().key(ConditionKey.ENTITY).operator(ConditionOperator.EQUALS).values(List.of("3", "4")).build()
                            ))
                            .build())
                    .contractIds(List.of(1L, 2L, 3L, 4L, 5L, 6L))
                    .build();
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    typeId,
                    buildDefinitionEntity(entitledCount, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            companyDefinition.getDefinition().setId(policyId);
            val contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(3L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(4L).setCompanyId(4000L).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(5L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(6L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(10L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(11L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build(),
                    ContractOuterClass.Contract.newBuilder().setId(12L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build()
            );
            val timeOffTypeDBO = TimeoffTypeDBO.builder().id(typeId).build();
            List<TimeOffDefinitionRuleDBO> rules = List.of(
                    TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.DEPARTMENT)
                            .conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"item1", "item2", "item3"}).build()
            );
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);


            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId)).thenReturn(Optional.of(companyDefinition));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffTypeDBO);
            when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinition.getId()))).thenReturn(rules);
            when(timeoffEntitlementDBORepository.findAllByContractIdInAndTypeId(input.getContractIds(), typeId)).thenReturn(entitlements);
            when(timeoffEntitlementDBORepository.findAllByDefinitionId(policyId)).thenReturn(alreadyAssignedEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(ListUtils.union(input.getContractIds(), List.of(10L, 11L, 12L)))).thenReturn(contracts);
            when(timeOffTypeDefinitionMapper.map(companyDefinition, timeOffTypeDBO)).thenReturn(TimeOffTypeDefinition.newBuilder().build());
            when(authorizationService.filterAccessibleContractIds(dfe, input.getContractIds())).thenReturn(input.getContractIds());

            definitionService.assignOrUnassignUsers(dfe, input);

            verify(timeoffEntitlementDBORepository).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeOffDefinitionRuleService).saveRules(definitionRulelistArgumentCaptor.capture());
            verify(timeOffDefinitionRuleService).deleteRules(any());
            verify(timeoffSummaryService).updateSummaryOnDefinitionAssignment(any(), any(), any());

            val updatedEntitlements = entitlementListArgumentCaptor.getValue();
            val savedRules = definitionRulelistArgumentCaptor.getValue();

            assertEquals(4, updatedEntitlements.size());
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(1L) && e.value() == 14.0));
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(2L) && e.value() == 14.0));
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(5L) && e.value() == 14.0));
            assertTrue(updatedEntitlements.stream().anyMatch(e -> e.contractId().equals(6L) && e.value() == 14.0));

            assertEquals(2, savedRules.size());
            assertTrue(savedRules.stream().anyMatch(r -> r.ruleType() == RuleType.BY_CONDITION && r.conditionKey() == ConditionKey.DEPARTMENT));
            assertTrue(savedRules.stream().anyMatch(r -> r.ruleType() == RuleType.BY_CONDITION && r.conditionKey() == ConditionKey.ENTITY));
        }

        @Test
        void should_throw_error_if_unauthorized_contract_ids_are_found() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 200L;

            TimeOffPolicyAssignUsersInput input = TimeOffPolicyAssignUsersInput.newBuilder()
                    .timeOffPolicyId(policyId)
                    .timeOffPolicyRule(RuleInput.newBuilder().type(RuleType.BY_CONDITION)
                            .conditions(List.of(
                                    ConditionInput.newBuilder().key(ConditionKey.DEPARTMENT).operator(ConditionOperator.EQUALS).values(List.of("1", "2")).build(),
                                    ConditionInput.newBuilder().key(ConditionKey.ENTITY).operator(ConditionOperator.EQUALS).values(List.of("3", "4")).build()
                            ))
                            .build())
                    .contractIds(List.of(1L, 2L, 3L, 4L, 5L, 6L))
                    .build();
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(authorizationService.filterAccessibleContractIds(dfe, input.getContractIds())).thenReturn(List.of(1L, 2L));

            val ex = assertThrows(ValidationException.class, () -> definitionService.assignOrUnassignUsers(dfe, input));
            assertEquals("User tried to assign policies to un-authorized contracts. policy id = 200 access denied ids : [3, 4, 5, 6]", ex.getMessage());

            verify(timeoffEntitlementDBORepository, never()).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeOffDefinitionRuleService, never()).saveRules(definitionRulelistArgumentCaptor.capture());
            verify(timeOffDefinitionRuleService, never()).deleteRules(any());
            verify(timeoffSummaryService, never()).updateSummaryOnDefinitionAssignment(any(), any(), any());

        }

        @Test
        void should_unassign_already_assigned_contracts_if_contract_ids_are_empty() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 200L;
            val typeId = 300L;
            TimeOffPolicyAssignUsersInput input = TimeOffPolicyAssignUsersInput.newBuilder()
                    .timeOffPolicyId(policyId)
                    .timeOffPolicyRule(RuleInput.newBuilder().type(RuleType.ALL).build())
                    .contractIds(List.of())
                    .build();
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    typeId,
                    buildDefinitionEntity(14, false),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            companyDefinition.getDefinition().setId(policyId);
            val timeOffTypeDBO = TimeoffTypeDBO.builder().id(typeId).build();
            List<TimeOffDefinitionRuleDBO> rules = List.of(
                    TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.DEPARTMENT)
                            .conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"item1", "item2", "item3"}).build()
            );
            val alreadyAssignedEntitlements = List.of(
                    TimeoffEntitlementDBO.builder().contractId(1L).typeId(typeId).value(14.0).definitionId(policyId).build(), // EOR
                    TimeoffEntitlementDBO.builder().contractId(2L).typeId(typeId).value(24.0).definitionId(policyId).build() // HR MEMBER
            );
            val contracts = List.of(
                    ContractOuterClass.Contract.newBuilder().setId(1L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.EMPLOYEE).build(),
                    ContractOuterClass.Contract.newBuilder().setId(2L).setCompanyId(companyId).setType(ContractOuterClass.ContractType.HR_MEMBER).build());
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId))
                    .thenReturn(Optional.of(companyDefinition));
            when(timeoffTypeService.findTimeOffTypeDBOById(typeId)).thenReturn(timeOffTypeDBO);
            when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinition.getId()))).thenReturn(rules);
            when(timeoffEntitlementDBORepository.findAllByDefinitionId(policyId)).thenReturn(alreadyAssignedEntitlements);
            when(contractServiceAdapter.getContractsByIdsAnyStatus(List.of(1L, 2L))).thenReturn(contracts);
            when(timeOffTypeDefinitionMapper.map(companyDefinition, timeOffTypeDBO)).thenReturn(TimeOffTypeDefinition.newBuilder().build());
            when(authorizationService.filterAccessibleContractIds(dfe, input.getContractIds())).thenReturn(input.getContractIds());


            definitionService.assignOrUnassignUsers(dfe, input);

            verify(timeoffEntitlementDBORepository, never()).saveAllAndFlush(entitlementListArgumentCaptor.capture());
            verify(timeOffDefinitionRuleService).saveRules(definitionRulelistArgumentCaptor.capture());
            verify(timeOffDefinitionRuleService).deleteRules(any());
            verify(timeoffSummaryService, never()).updateSummaryOnDefinitionAssignment(any(), any(), any());
            verify(timeoffEntitlementDBORepository, never()).deleteAllByContractIdAndTypeId(1L, typeId);
            verify(timeoffEntitlementDBORepository).deleteAllByContractIdAndTypeId(2L, typeId);
            verify(timeoffSummaryService, never()).deleteSummaryByContractIdAndTypeId(1L, typeId);
            verify(timeoffSummaryService).deleteSummaryByContractIdAndTypeId(2L, typeId);
        }
    }

    @Nested
    class FindDefinitionAssignmentDetailsTest {

        @Test
        void should_throw_error_if_definition_not_found() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 345L;

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId))
                    .thenReturn(Optional.empty());

            val ex = assertThrows(ValidationException.class, () -> definitionService.findDefinitionAssignmentDetails(policyId));
            assertEquals("Can not find definition id: 345 for company id : 100", ex.getMessage());
        }

        @Test
        void should_return_empty_list_when_no_rules_found_for_definition() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 345L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    new DefinitionEntity(),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId))
                    .thenReturn(Optional.of(companyDefinition));
            when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinition.getId())))
                    .thenReturn(List.of());
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(policyId)))
                    .thenReturn(List.of());
            when(timeOffDefinitionRuleMapper.map(List.of())).thenReturn(null);

            val result = definitionService.findDefinitionAssignmentDetails(policyId);
            assertTrue(result.getAssignedContracts().isEmpty());
        }

        @Test
        void should_return_assigned_contracts_and_rules_successfully() {
            val companyUserId = 101L;
            val companyId = 100L;
            val policyId = 345L;
            val companyDefinition = new CompanyDefinitionEntity(
                    100L,
                    200L,
                    new DefinitionEntity(),
                    companyId,
                    null,
                    null,
                    new ArrayList<>()
            );
            List<TimeOffDefinitionRuleDBO> rules = List.of(
                    TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.DEPARTMENT)
                            .conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"item1", "item2", "item3"}).build()
            );
            val existingEntitlementsForDefinition = List.of(
                    TimeoffEntitlementDBO.builder().contractId(1L).value(2.0).definitionId(policyId).build(),
                    TimeoffEntitlementDBO.builder().contractId(2L).value(2.0).definitionId(policyId).build(),
                    TimeoffEntitlementDBO.builder().contractId(3L).value(2.0).definitionId(policyId).build()
            );

            when(currentUser.getContext()).thenReturn(getCompanyUserUserDetails(companyUserId, companyId));
            when(companyDefinitionRepository.findNonDeletedByDefinitionIdAndCompanyId(policyId, companyId))
                    .thenReturn(Optional.of(companyDefinition));
            when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinition.getId())))
                    .thenReturn(rules);
            when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(Set.of(policyId)))
                    .thenReturn(existingEntitlementsForDefinition);
            when(timeOffDefinitionRuleMapper.map(rules)).thenReturn(Rule.newBuilder().build());

            val result = definitionService.findDefinitionAssignmentDetails(policyId);
            assertEquals(3, result.getAssignedContracts().size());
            assertTrue(result.getAssignedContracts().stream().anyMatch(c -> c.getId().equals(1L)));
            assertTrue(result.getAssignedContracts().stream().anyMatch(c -> c.getId().equals(2L)));
            assertTrue(result.getAssignedContracts().stream().anyMatch(c -> c.getId().equals(3L)));
        }

    }

    @Nested
    class FindAllDefinitionsForContract {

        @Test
        void should_return_empty_list_of_definitions_for_freelancer() {
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setType(ContractOuterClass.ContractType.FREELANCER)
                    .build();
            val foundDefinitions = definitionService.findAllDefinitionsForContract(contract);
            assertTrue(foundDefinitions.isEmpty());
        }

        @Test
        void should_return_empty_list_of_definitions_for_contractor() {
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setType(ContractOuterClass.ContractType.CONTRACTOR)
                    .build();
            val foundDefinitions = definitionService.findAllDefinitionsForContract(contract);
            assertTrue(foundDefinitions.isEmpty());
        }

        @Nested
        class EntityLevelPoliciesFlagOff {
            @BeforeEach
            public void setUp() {
                entityLevelPoliciesOff();
            }

            /**
             * - company definitions
             *     - type 1 - companyDefinition1, companyDefinition2
             *     - type 2 - companyDefinition3
             *     - type 3 - companyDefinition4
             *     - type 4 - companyDefinition5
             *  - country definitions
             *      - type 2 - countryDefinition1
             *      - type 10 - countryDefinition2
             *    Expected entitlements
             *       - type 1 - companyDefinition2 (companyDefinition1 not eligible since companyDefinition2 updated more recently)
             *       - type 2 - countryDefinition1 (entitled value in companyDefinition3 is smaller than countryDefinition1)
             *    Rejected entitlements
             *       - countryDefinition2, companyDefinition4 - not allowed for ONBOARDING contracts
             *       - companyDefinition5 - rule not match for contract
             *
             */
            @Test
            void should_return_definitions_for_EOR_contract() {

                long contractId = 1001L;
                long memberId = 100L;
                Long companyId = 100L;

                DefinitionEntity definitionEntity1 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation1 = new TimeoffDefinitionValidationEntity(
                        14.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity1.setValidations(Set.of(validation1));
                definitionEntity1.setRequired(true);

                DefinitionEntity definitionEntity2 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation2 = new TimeoffDefinitionValidationEntity(
                        7.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity2.setValidations(Set.of(validation2));
                definitionEntity2.setRequired(true);

                DefinitionEntity definitionEntity3 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation3 = new TimeoffDefinitionValidationEntity(
                        14.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity3.setValidations(Set.of(validation3));
                definitionEntity3.setRequired(true);

                DefinitionEntity definitionEntity4 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation4 = new TimeoffDefinitionValidationEntity(
                        21.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity4.setValidations(Set.of(validation4));
                definitionEntity4.setRequired(true);

                DefinitionEntity definitionEntity5 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation5 = new TimeoffDefinitionValidationEntity(
                        21.0, null, null, TimeOffUnit.DAYS, List.of("ACTIVE"), false
                );
                definitionEntity5.setValidations(Set.of(validation5));
                definitionEntity5.setRequired(true);

                val companyDefinitionEntity1 = new CompanyDefinitionEntity(1L, 1L, definitionEntity1, companyId, null, null, new ArrayList<>());
                companyDefinitionEntity1.setUpdatedOn(LocalDateTime.of(2024, 2, 1, 10, 0, 0));

                val companyDefinitionEntity2 = new CompanyDefinitionEntity(2L, 1L, definitionEntity2, companyId, null, null, new ArrayList<>());
                companyDefinitionEntity2.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val companyDefinitionEntity3 = new CompanyDefinitionEntity(3L, 2L, definitionEntity3, companyId, null, null, new ArrayList<>());
                companyDefinitionEntity3.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val companyDefinitionEntity4 = new CompanyDefinitionEntity(4L, 3L, definitionEntity5, companyId, null, null, new ArrayList<>());
                companyDefinitionEntity4.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val companyDefinitionEntity5 = new CompanyDefinitionEntity(5L, 4L, definitionEntity3, companyId, null, null, new ArrayList<>());
                companyDefinitionEntity5.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val countryDefinition1 = new CountryDefinitionEntity(1L, 2L, definitionEntity4);
                countryDefinition1.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));
                val countryDefinition2 = new CountryDefinitionEntity(2L, 10L, definitionEntity5);
                countryDefinition2.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val companyDefinitions = List.of(companyDefinitionEntity1, companyDefinitionEntity2, companyDefinitionEntity3,
                        companyDefinitionEntity4, companyDefinitionEntity5);

                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(1L).ruleType(RuleType.ALL).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(2L).ruleType(RuleType.ALL).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(3L).ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY).conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"IND"}).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(3L).ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.GENDER).conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"MALE"}).build()
                );

                val contract = ContractOuterClass.Contract.newBuilder()
                        .setId(contractId)
                        .setCompanyId(companyId)
                        .setMemberId(memberId)
                        .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                        .setCountry("IND")
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .build();
                val members = List.of(
                        Member.newBuilder().setId(memberId).setGender(com.multiplier.member.schema.Gender.MALE).build()
                );
                when(definitionService.findAllCompanyDefinitions(companyId)).thenReturn(companyDefinitions);
                when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(anySet())).thenReturn(rules);
                when(countryDefinitionService.getAllCountryDefinitionsByCountry(CountryCode.IND, null)).thenReturn(List.of(countryDefinition1, countryDefinition2));
                when(memberServiceAdapter.getMembers(Set.of(memberId))).thenReturn(members);
                when(timeOffDefinitionRuleService.isContractMatchForRules(any(), anyList()))
                        .thenReturn(true)
                        .thenReturn(true)
                        .thenReturn(true)
                        .thenReturn(true)
                        .thenReturn(false);

                val foundDefinitions = definitionService.findAllDefinitionsForContract(contract);

                assertEquals(4, foundDefinitions.size());
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 1 && td.getId() == 2 && td instanceof CompanyDefinitionEntity));
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 2 && td.getId() == 1 && td instanceof CountryDefinitionEntity));
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 10 && td.getId() == 2 && td instanceof CountryDefinitionEntity));
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 3 && td.getId() == 4 && td instanceof CompanyDefinitionEntity));
            }

            @Test
            void should_return_definitions_for_hr_member() {
                /*
                 * - company definitions
                 *     - type 1 - annualLeaveDefinition1, annualLeaveDefinition2
                 *     - type 2 - sickLeaveDefinition
                 *     - type 3 - maternityLeaveDefinition
                 *     - type 4 - paternityLeaveDefinition
                 *  - country definitions - No country definitions for HR MEMBER
                 *    Expected entitlements
                 *       - type 1 - annualLeaveDefinition2 (annualLeaveDefinition1 not eligible since annualLeaveDefinition2 updated more recently)
                 *       - type 2 - sickLeaveDefinition
                 *    Rejected entitlements
                 *       - maternityLeaveDefinition - rule not match for contract
                 *       - paternityLeaveDefinition - not allowed for ONBOARDING contracts
                 *
                 */
                long contractId = 1001L;
                long memberId = 100L;
                Long companyId = 100L;

                DefinitionEntity definitionEntity1 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation1 = new TimeoffDefinitionValidationEntity(
                        14.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity1.setValidations(Set.of(validation1));
                definitionEntity1.setRequired(true);

                DefinitionEntity definitionEntity2 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation2 = new TimeoffDefinitionValidationEntity(
                        7.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity2.setValidations(Set.of(validation2));
                definitionEntity2.setRequired(true);

                DefinitionEntity definitionEntity3 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation3 = new TimeoffDefinitionValidationEntity(
                        14.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity3.setValidations(Set.of(validation3));
                definitionEntity3.setRequired(true);

                DefinitionEntity definitionEntity5 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation5 = new TimeoffDefinitionValidationEntity(
                        21.0, null, null, TimeOffUnit.DAYS, List.of("ACTIVE"), false
                );
                definitionEntity5.setValidations(Set.of(validation5));
                definitionEntity5.setRequired(true);

                val annualLeaveDefinition1 = new CompanyDefinitionEntity(1L, 1L, definitionEntity1, companyId, null, null, new ArrayList<>());
                annualLeaveDefinition1.setUpdatedOn(LocalDateTime.of(2024, 2, 1, 10, 0, 0));

                val annualLeaveDefinition2 = new CompanyDefinitionEntity(2L, 1L, definitionEntity2, companyId, null, null, new ArrayList<>());
                annualLeaveDefinition2.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val sickLeaveDefinition = new CompanyDefinitionEntity(3L, 2L, definitionEntity3, companyId, null, null, new ArrayList<>());
                sickLeaveDefinition.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val maternityLeaveDefinition = new CompanyDefinitionEntity(4L, 3L, definitionEntity5, companyId, null, null, new ArrayList<>());
                maternityLeaveDefinition.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val paternityLeaveDefinition = new CompanyDefinitionEntity(5L, 4L, definitionEntity3, companyId, null, null, new ArrayList<>());
                paternityLeaveDefinition.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                List<CompanyDefinitionEntity> companyDefinitions = List.of(annualLeaveDefinition1, annualLeaveDefinition2, sickLeaveDefinition,
                        maternityLeaveDefinition, paternityLeaveDefinition);
                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(1L).ruleType(RuleType.ALL).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(2L).ruleType(RuleType.ALL).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(3L).ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY).conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"IND"}).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(3L).ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.GENDER).conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"MALE"}).build()
                );

                val contract = ContractOuterClass.Contract.newBuilder()
                        .setId(contractId)
                        .setCompanyId(companyId)
                        .setMemberId(memberId)
                        .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                        .setCountry("IND")
                        .setType(ContractOuterClass.ContractType.HR_MEMBER)
                        .build();
                val members = List.of(
                        Member.newBuilder().setId(memberId).setGender(com.multiplier.member.schema.Gender.MALE).build()
                );
                when(definitionService.findAllCompanyDefinitions(companyId)).thenReturn(companyDefinitions);
                when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(anySet())).thenReturn(rules);
                when(memberServiceAdapter.getMembers(Set.of(memberId))).thenReturn(members);
                when(timeOffDefinitionRuleService.isContractMatchForRules(any(), anyList()))
                        .thenReturn(true)
                        .thenReturn(true)
                        .thenReturn(true)
                        .thenReturn(false);

                val foundDefinitions = definitionService.findAllDefinitionsForContract(contract);

                assertEquals(2, foundDefinitions.size());
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 1 && td.getId() == 2 && td instanceof CompanyDefinitionEntity));
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 2 && td.getId() == 3 && td instanceof CompanyDefinitionEntity));
            }
        }

        @Nested
        class EntityLevelPoliciesFlagOn {
            private final Long companyId = 100L;

            @Captor
            private ArgumentCaptor<List<CompanyDefinitionFilter>> companyDefinitionFiltersArgumentCaptor;

            @BeforeEach
            public void setUp() {
                entityLevelPoliciesOn();
            }

            @Test
            void should_return_definitions_for_EOR_contract() {
                /*
                 * - company definitions
                 *     - type 1 - companyDefinition1, companyDefinition2
                 *     - type 2 - companyDefinition3
                 *     - type 3 - companyDefinition4
                 *     - type 4 - companyDefinition5
                 *  - country definitions
                 *      - type 2 - countryDefinition1
                 *      - type 10 - countryDefinition2
                 *
                 *    Expected entitlements
                 *       - type 1 - companyDefinition2 (companyDefinition1 not eligible since companyDefinition2 updated more recently)
                 *       - type 2 - countryDefinition1 (entitled value in companyDefinition3 is smaller than countryDefinition1)
                 *    Rejected entitlements
                 *       - countryDefinition2, companyDefinition4 - not allowed for ONBOARDING contracts
                 *       - companyDefinition5 - rule not match for contract
                 *
                 */
                long contractId = 1001L;
                long memberId = 100L;
                val entityId = -1L;

                DefinitionEntity definitionEntity1 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation1 = new TimeoffDefinitionValidationEntity(
                        14.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity1.setValidations(Set.of(validation1));
                definitionEntity1.setRequired(true);

                DefinitionEntity definitionEntity2 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation2 = new TimeoffDefinitionValidationEntity(
                        7.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity2.setValidations(Set.of(validation2));
                definitionEntity2.setRequired(true);

                DefinitionEntity definitionEntity3 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation3 = new TimeoffDefinitionValidationEntity(
                        14.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity3.setValidations(Set.of(validation3));
                definitionEntity3.setRequired(true);

                DefinitionEntity definitionEntity4 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation4 = new TimeoffDefinitionValidationEntity(
                        21.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity4.setValidations(Set.of(validation4));
                definitionEntity4.setRequired(true);

                DefinitionEntity definitionEntity5 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation5 = new TimeoffDefinitionValidationEntity(
                        21.0, null, null, TimeOffUnit.DAYS, List.of("ACTIVE"), false
                );
                definitionEntity5.setValidations(Set.of(validation5));
                definitionEntity5.setRequired(true);

                val companyDefinitionEntity1 = new CompanyDefinitionEntity(1L, 1L, definitionEntity1, companyId, EntityType.EOR_PARTNER_ENTITY, entityId, new ArrayList<>());
                companyDefinitionEntity1.setUpdatedOn(LocalDateTime.of(2024, 2, 1, 10, 0, 0));

                val companyDefinitionEntity2 = new CompanyDefinitionEntity(2L, 1L, definitionEntity2, companyId, EntityType.EOR_PARTNER_ENTITY, entityId, new ArrayList<>());
                companyDefinitionEntity2.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val companyDefinitionEntity3 = new CompanyDefinitionEntity(3L, 2L, definitionEntity3, companyId, EntityType.EOR_PARTNER_ENTITY, entityId, new ArrayList<>());
                companyDefinitionEntity3.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val companyDefinitionEntity4 = new CompanyDefinitionEntity(4L, 3L, definitionEntity5, companyId,  EntityType.EOR_PARTNER_ENTITY, entityId, new ArrayList<>());
                companyDefinitionEntity4.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val companyDefinitionEntity5 = new CompanyDefinitionEntity(5L, 4L, definitionEntity3, companyId,  EntityType.EOR_PARTNER_ENTITY, entityId, new ArrayList<>());
                companyDefinitionEntity5.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val countryDefinition1 = new CountryDefinitionEntity(1L, 2L, definitionEntity4);
                countryDefinition1.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));
                val countryDefinition2 = new CountryDefinitionEntity(2L, 10L, definitionEntity5);
                countryDefinition2.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val companyDefinitions = List.of(companyDefinitionEntity1, companyDefinitionEntity2, companyDefinitionEntity3,
                        companyDefinitionEntity4, companyDefinitionEntity5);

                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(1L).ruleType(RuleType.ALL).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(2L).ruleType(RuleType.ALL).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(3L).ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY).conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"IND"}).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(3L).ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.GENDER).conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"MALE"}).build()
                );
                val contract = ContractOuterClass.Contract.newBuilder()
                        .setId(contractId)
                        .setCompanyId(companyId)
                        .setMemberId(memberId)
                        .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                        .setCountry("IND")
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .build();
                val members = List.of(
                        Member.newBuilder().setId(memberId).setGender(com.multiplier.member.schema.Gender.MALE).build()
                );
                Specification<CompanyDefinitionEntity> specBuilder = mock(Specification.class);

                when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(anySet())).thenReturn(rules);
                when(countryDefinitionService.getAllCountryDefinitionsByCountry(CountryCode.IND, null)).thenReturn(List.of(countryDefinition1, countryDefinition2));
                when(memberServiceAdapter.getMembers(Set.of(memberId))).thenReturn(members);
                when(timeOffDefinitionRuleService.isContractMatchForRules(any(), anyList()))
                        .thenReturn(true)
                        .thenReturn(true)
                        .thenReturn(true)
                        .thenReturn(true)
                        .thenReturn(false);
                when(companyDefinitionSpecBuilder.build(companyDefinitionFiltersArgumentCaptor.capture())).thenReturn(specBuilder);
                when(companyDefinitionRepository.findAll(specBuilder)).thenReturn(companyDefinitions);

                val foundDefinitions = definitionService.findAllDefinitionsForContract(contract);

                assertEquals(4, foundDefinitions.size());
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 1 && td.getId() == 2 && td instanceof CompanyDefinitionEntity));
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 2 && td.getId() == 1 && td instanceof CountryDefinitionEntity));
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 10 && td.getId() == 2 && td instanceof CountryDefinitionEntity));
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 3 && td.getId() == 4 && td instanceof CompanyDefinitionEntity));

                val filters = companyDefinitionFiltersArgumentCaptor.getValue();
                assertEquals(1, filters.size());
                assertEquals(EntityType.EOR_PARTNER_ENTITY, filters.get(0).getEntityType());
                assertEquals(entityId, filters.get(0).getEntityId());
                assertEquals(companyId, filters.get(0).getCompanyId());
            }

            @Test
            void should_return_definitions_for_hr_member() {
                /*
                 * - company definitions
                 *     - type 1 - annualLeaveDefinition1, annualLeaveDefinition2
                 *     - type 2 - sickLeaveDefinition
                 *     - type 3 - maternityLeaveDefinition
                 *     - type 4 - paternityLeaveDefinition
                 *  - country definitions - No country definitions for HR MEMBER
                 *    Expected entitlements
                 *       - type 1 - annualLeaveDefinition2 (annualLeaveDefinition1 not eligible since annualLeaveDefinition2 updated more recently)
                 *       - type 2 - sickLeaveDefinition
                 *    Rejected entitlements
                 *       - maternityLeaveDefinition - rule not match for contract
                 *       - paternityLeaveDefinition - not allowed for ONBOARDING contracts
                 *
                 */
                long contractId = 1001L;
                long memberId = 100L;
                val entityId = 654L;

                DefinitionEntity definitionEntity1 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation1 = new TimeoffDefinitionValidationEntity(
                        14.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity1.setValidations(Set.of(validation1));
                definitionEntity1.setRequired(true);

                DefinitionEntity definitionEntity2 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation2 = new TimeoffDefinitionValidationEntity(
                        7.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity2.setValidations(Set.of(validation2));
                definitionEntity2.setRequired(true);

                DefinitionEntity definitionEntity3 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation3 = new TimeoffDefinitionValidationEntity(
                        14.0, null, null, TimeOffUnit.DAYS, List.of("ONBOARDING"), false
                );
                definitionEntity3.setValidations(Set.of(validation3));
                definitionEntity3.setRequired(true);

                DefinitionEntity definitionEntity5 = new DefinitionEntity();
                TimeoffDefinitionValidationEntity validation5 = new TimeoffDefinitionValidationEntity(
                        21.0, null, null, TimeOffUnit.DAYS, List.of("ACTIVE"), false
                );
                definitionEntity5.setValidations(Set.of(validation5));
                definitionEntity5.setRequired(true);

                val annualLeaveDefinition1 = new CompanyDefinitionEntity(1L, 1L, definitionEntity1, companyId, EntityType.COMPANY_ENTITY, entityId, new ArrayList<>());
                annualLeaveDefinition1.setUpdatedOn(LocalDateTime.of(2024, 2, 1, 10, 0, 0));

                val annualLeaveDefinition2 = new CompanyDefinitionEntity(2L, 1L, definitionEntity2, companyId, EntityType.COMPANY_ENTITY, entityId, new ArrayList<>());
                annualLeaveDefinition2.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val sickLeaveDefinition = new CompanyDefinitionEntity(3L, 2L, definitionEntity3, companyId, EntityType.COMPANY_ENTITY, entityId, new ArrayList<>());
                sickLeaveDefinition.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val maternityLeaveDefinition = new CompanyDefinitionEntity(4L, 3L, definitionEntity5, companyId, EntityType.COMPANY_ENTITY, entityId, new ArrayList<>());
                maternityLeaveDefinition.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                val paternityLeaveDefinition = new CompanyDefinitionEntity(5L, 4L, definitionEntity3, companyId, EntityType.COMPANY_ENTITY, entityId, new ArrayList<>());
                paternityLeaveDefinition.setUpdatedOn(LocalDateTime.of(2024, 2, 5, 10, 0, 0));

                List<CompanyDefinitionEntity> companyDefinitions = List.of(annualLeaveDefinition1, annualLeaveDefinition2, sickLeaveDefinition,
                        maternityLeaveDefinition, paternityLeaveDefinition);
                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(1L).ruleType(RuleType.ALL).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(2L).ruleType(RuleType.ALL).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(3L).ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.COUNTRY).conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"IND"}).build(),
                        TimeOffDefinitionRuleDBO.builder().companyDefinitionId(3L).ruleType(RuleType.BY_CONDITION)
                                .conditionKey(ConditionKey.GENDER).conditionOperator(ConditionOperator.EQUALS).conditionValues(new String[]{"MALE"}).build()
                );

                val contract = ContractOuterClass.Contract.newBuilder()
                        .setId(contractId)
                        .setCompanyId(companyId)
                        .setMemberId(memberId)
                        .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                        .setCountry("IND")
                        .setType(ContractOuterClass.ContractType.HR_MEMBER)
                        .build();
                val members = List.of(
                        Member.newBuilder().setId(memberId).setGender(com.multiplier.member.schema.Gender.MALE).build()
                );
                Specification<CompanyDefinitionEntity> specBuilder = mock(Specification.class);

                when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(anySet())).thenReturn(rules);
                when(memberServiceAdapter.getMembers(Set.of(memberId))).thenReturn(members);
                when(timeOffDefinitionRuleService.isContractMatchForRules(any(), anyList()))
                        .thenReturn(true)
                        .thenReturn(true)
                        .thenReturn(true)
                        .thenReturn(false);
                when(contractServiceAdapter.getEntityIdByContractId(contractId)).thenReturn(entityId);
                when(companyDefinitionSpecBuilder.build(companyDefinitionFiltersArgumentCaptor.capture())).thenReturn(specBuilder);
                when(companyDefinitionRepository.findAll(specBuilder)).thenReturn(companyDefinitions);


                val foundDefinitions = definitionService.findAllDefinitionsForContract(contract);

                assertEquals(2, foundDefinitions.size());
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 1 && td.getId() == 2 && td instanceof CompanyDefinitionEntity));
                assertTrue(foundDefinitions.stream().anyMatch(td -> td.getTypeId() == 2 && td.getId() == 3 && td instanceof CompanyDefinitionEntity));

                val filters = companyDefinitionFiltersArgumentCaptor.getValue();
                assertEquals(1, filters.size());
                assertEquals(EntityType.COMPANY_ENTITY, filters.get(0).getEntityType());
                assertEquals(entityId, filters.get(0).getEntityId());
                assertEquals(companyId, filters.get(0).getCompanyId());
            }
        }

    }

    @Nested
    class BackfillRulesForExistingCompanyDefinitionsTest {

        @Captor
        private ArgumentCaptor<List<TimeOffDefinitionRuleDBO>> ruleCaptor;

        @Test
        void should_do_nothing_if_company_id_is_null_and_for_all_is_false() {

            definitionService.backfillRulesForExistingCompanyDefinitions(null, false);
            verifyNoInteractions(companyDefinitionRepository);
        }

        @Test
        void should_update_for_given_company_id() {
            val companyId = 100L;

            val definition1 = new DefinitionEntity();
            definition1.setId(1L);
            definition1.setCountryCode(CountryCode.LKA);

            val definition2 = new DefinitionEntity();
            definition2.setId(2L);
            definition2.setCountryCode(CountryCode.IND);

            List<CompanyDefinitionEntity> companyDefinitions = List.of(
                    new CompanyDefinitionEntity(1L, 1L, definition1, companyId, null, null, new ArrayList<>()),
                    new CompanyDefinitionEntity(2L, 2L, definition2, companyId, null, null, new ArrayList<>()),
                    new CompanyDefinitionEntity(3L, 2L, new DefinitionEntity(), companyId, null, null, new ArrayList<>())
            );

            List<TimeOffDefinitionRuleDBO> rules = List.of(
                    TimeOffDefinitionRuleDBO.builder().companyDefinitionId(3L).ruleType(RuleType.ALL).build()
            );
            List<TimeoffTypeDBO> timeoffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).label("Annual Leave").build(),
                    TimeoffTypeDBO.builder().id(2L).label("Sick Leave").build()
            );

            when(companyDefinitionRepository.findAllNonDeletedByCompanyId(companyId)).thenReturn(companyDefinitions);
            when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(1L, 2L, 3L))).thenReturn(rules);
            when(timeoffTypeService.findAllTypeDBOsByIds(Set.of(1L, 2L))).thenReturn(timeoffTypes);

            definitionService.backfillRulesForExistingCompanyDefinitions(companyId, false);

            verify(timeOffDefinitionRuleService).saveRules(ruleCaptor.capture());

            val savedRules = ruleCaptor.getValue();

            assertEquals(2, savedRules.size());
            assertTrue(savedRules.stream().anyMatch(r -> r.companyDefinition().getId() == 1L
                    && r.ruleType() == RuleType.BY_CONDITION &&
                    r.conditionKey() == ConditionKey.COUNTRY &&
                    r.conditionValues().length == 1 && r.conditionValues()[0].equals(CountryCode.LKA.name())));
            assertTrue(savedRules.stream().anyMatch(r -> r.companyDefinition().getId() == 2L
                    && r.ruleType() == RuleType.BY_CONDITION &&
                    r.conditionKey() == ConditionKey.COUNTRY &&
                    r.conditionValues().length == 1 && r.conditionValues()[0].equals(CountryCode.IND.name())));
            assertEquals("LKA Annual Leave Policy", companyDefinitions.get(0).getDefinition().getName());
            assertEquals("IND Sick Leave Policy", companyDefinitions.get(1).getDefinition().getName());
        }

        @Test
        void should_update_for_all() {
            val definition1 = new DefinitionEntity();
            definition1.setId(1L);
            definition1.setCountryCode(CountryCode.LKA);

            val definition2 = new DefinitionEntity();
            definition2.setId(2L);
            definition2.setCountryCode(CountryCode.IND);

            List<CompanyDefinitionEntity> companyDefinitions = List.of(
                    new CompanyDefinitionEntity(1L, 1L, definition1, 1000L, null, null, new ArrayList<>()),
                    new CompanyDefinitionEntity(2L, 2L, definition2, 2000L, null, null, new ArrayList<>()),
                    new CompanyDefinitionEntity(3L, 2L, new DefinitionEntity(), 3000L, null, null, new ArrayList<>())
            );

            List<TimeOffDefinitionRuleDBO> rules = List.of(
                    TimeOffDefinitionRuleDBO.builder().companyDefinitionId(3L).ruleType(RuleType.ALL).build()
            );
            List<TimeoffTypeDBO> timeoffTypes = List.of(
                    TimeoffTypeDBO.builder().id(1L).label("Annual Leave").build(),
                    TimeoffTypeDBO.builder().id(2L).label("Sick Leave").build()
            );

            when(companyDefinitionRepository.findAll()).thenReturn(companyDefinitions);
            when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(1L, 2L, 3L))).thenReturn(rules);
            when(timeoffTypeService.findAllTypeDBOsByIds(Set.of(1L, 2L))).thenReturn(timeoffTypes);

            definitionService.backfillRulesForExistingCompanyDefinitions(null, true);

            verify(timeOffDefinitionRuleService).saveRules(ruleCaptor.capture());

            val savedRules = ruleCaptor.getValue();

            assertEquals(2, savedRules.size());
            assertTrue(savedRules.stream().anyMatch(r -> r.companyDefinition().getId() == 1L
                    && r.companyId() == 1000L
                    && r.ruleType() == RuleType.BY_CONDITION &&
                    r.conditionKey() == ConditionKey.COUNTRY &&
                    r.conditionValues().length == 1 && r.conditionValues()[0].equals(CountryCode.LKA.name())));
            assertTrue(savedRules.stream().anyMatch(r -> r.companyDefinition().getId() == 2L
                    && r.companyId() == 2000L
                    && r.ruleType() == RuleType.BY_CONDITION &&
                    r.conditionKey() == ConditionKey.COUNTRY &&
                    r.conditionValues().length == 1 && r.conditionValues()[0].equals(CountryCode.IND.name())));
            assertEquals("LKA Annual Leave Policy", companyDefinitions.get(0).getDefinition().getName());
            assertEquals("IND Sick Leave Policy", companyDefinitions.get(1).getDefinition().getName());
        }


    }

    @Nested
    class findAvailableDefinitionsByContract {

        @Test
        void should_return_empty_list_of_definitions_for_empty_contract_list() {
            val foundDefinitions = definitionService. findAvailableDefinitionsByContract(100L, List.of());
            assertTrue(foundDefinitions.isEmpty());
        }

        /**
         * Test case for the scenario where only the default definitions are available.
         * And the country definitions (e.g. VNM) and the company definitions are NOT available.
         */
        @Test
        void should_return_default_definitions_when_country_and_company_definition_are_not_available() {
            val annualTypeId = 1L;
            // Note that this `defaultAnnualDefinition` is used since country definitions are not available.
            val defaultAnnualDefinition = buildCountryDefinition(2L, annualTypeId, null, 15.0);
            val countryLocation = new CountryLocation(CountryCode.VNM, null);
            when(countryDefinitionService.getCountryLocationToCountryDefinitionsMap(List.of(countryLocation)))
                    .thenReturn(Map.of(countryLocation, List.of(defaultAnnualDefinition)));

            val companyId = 2L;
            val contractId = 1L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setCountry("VNM")
                    .build();

            val result = definitionService.findAvailableDefinitionsByContract(companyId, Set.of(contract));
            assertEquals(1, result.keySet().size());

            val typeIdToTimeOffDefinition = assertNonNull(result.get(contractId));
            assertEquals(1, typeIdToTimeOffDefinition.keySet().size());

            // Verify that there is only a country definition available, default definition is ignored.
            val annualTimeOffDefinition = assertNonNull(typeIdToTimeOffDefinition.get(annualTypeId));
            val annualDefinition = assertNonNull(annualTimeOffDefinition.getDefinition());
            assertTrue(annualDefinition.isRequired());
            assertNull(annualDefinition.getStateCode());
            assertNull(annualDefinition.getCountryCode());
            assertEquals(1L, annualDefinition.getId());
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, annualDefinition.getStatus());
            assertEquals("Any Clause", annualDefinition.getClause());
            assertEquals("Annual leave", annualDefinition.getDescription());
            assertEquals("ANNUAL", annualDefinition.getBasis());

            val validations = assertNonNull(annualDefinition.getValidations());
            assertEquals(1, validations.size());
            val validation = validations.iterator().next();
            assertFalse(assertNonNull(validation.isUnlimitedLeavesAllowed()));
            assertNull(validation.getMaximumValue());
            assertEquals(15.0, validation.getDefaultValue());
            assertEquals(15.0, validation.getMinimumValue());
            assertEquals(TimeOffUnit.DAYS, validation.getUnit());
            assertEquals(List.of("ONBOARDING", "ACTIVE"), validation.getAllowedContractStatuses());
        }

        /**
         * Test case for the scenario where only the country definitions (e.g. VNM) are available.
         * The default definitions are ignored and the company definitions are NOT available.
         */
        @Test
        void should_return_country_definitions_when_only_country_definitions_available() {
            val annualTypeId = 1L;
            val annualCountryDefinition = buildCountryDefinition(1L, annualTypeId, CountryCode.VNM, 15.0);
            // Note that this `defaultSickDefinition` is ignored if there is a country definition available.
            val countryLocation = new CountryLocation(CountryCode.VNM, null);
            when(countryDefinitionService.getCountryLocationToCountryDefinitionsMap(List.of(countryLocation)))
                    .thenReturn(Map.of(countryLocation, List.of(annualCountryDefinition)));

            val companyId = 2L;
            val contractId = 1L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setCountry("VNM")
                    .build();

            val result = definitionService.findAvailableDefinitionsByContract(companyId, Set.of(contract));
            assertEquals(1, result.keySet().size());

            val typeIdToTimeOffDefinition = assertNonNull(result.get(contractId));
            assertEquals(1, typeIdToTimeOffDefinition.keySet().size());

            // Verify that there is only a country definition available, default definition is ignored.
            val annualTimeOffDefinition = assertNonNull(typeIdToTimeOffDefinition.get(annualTypeId));
            val annualDefinition = assertNonNull(annualTimeOffDefinition.getDefinition());
            assertTrue(annualDefinition.isRequired());
            assertNull(annualDefinition.getStateCode());
            assertEquals(1L, annualDefinition.getId());
            assertEquals(CountryCode.VNM, annualDefinition.getCountryCode());
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, annualDefinition.getStatus());
            assertEquals("Any Clause", annualDefinition.getClause());
            assertEquals("Annual leave", annualDefinition.getDescription());
            assertEquals("ANNUAL", annualDefinition.getBasis());

            val validations = assertNonNull(annualDefinition.getValidations());
            assertEquals(1, validations.size());
            val validation = validations.iterator().next();
            assertFalse(assertNonNull(validation.isUnlimitedLeavesAllowed()));
            assertNull(validation.getMaximumValue());
            assertEquals(15.0, validation.getDefaultValue());
            assertEquals(15.0, validation.getMinimumValue());
            assertEquals(TimeOffUnit.DAYS, validation.getUnit());
            assertEquals(List.of("ONBOARDING", "ACTIVE"), validation.getAllowedContractStatuses());
        }

        /**
         * Test case for the scenario where only the country definitions are available.
         * And the country w/ state definitions (e.g. VNM + HCM) and the company definitions are NOT available.
         */
        @Test
        void should_return_country_definitions_when_country_with_state_and_company_definitions_are_not_available() {
            val annualTypeId = 1L;
            // Note that this `defaultAnnualDefinition` is used since country definitions are not available.
            val defaultAnnualDefinition = buildCountryDefinition(2L, annualTypeId, CountryCode.VNM, 15.0);
            val countryLocation = new CountryLocation(CountryCode.VNM, "HCM");
            when(countryDefinitionService.getCountryLocationToCountryDefinitionsMap(List.of(countryLocation)))
                    .thenReturn(Map.of(countryLocation, List.of(defaultAnnualDefinition)));

            val companyId = 2L;
            val contractId = 1L;
            val contract = ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setCompanyId(companyId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setCountry("VNM")
                    .setCountryStateCode("HCM")
                    .build();

            val result = definitionService.findAvailableDefinitionsByContract(companyId, Set.of(contract));
            assertEquals(1, result.keySet().size());

            val typeIdToTimeOffDefinition = assertNonNull(result.get(contractId));
            assertEquals(1, typeIdToTimeOffDefinition.keySet().size());

            // Verify that there is only a country definition available, default definition is ignored.
            val annualTimeOffDefinition = assertNonNull(typeIdToTimeOffDefinition.get(annualTypeId));
            val annualDefinition = assertNonNull(annualTimeOffDefinition.getDefinition());
            assertTrue(annualDefinition.isRequired());
            assertNull(annualDefinition.getStateCode());
            assertEquals(CountryCode.VNM, annualDefinition.getCountryCode());
            assertEquals(1L, annualDefinition.getId());
            assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, annualDefinition.getStatus());
            assertEquals("Any Clause", annualDefinition.getClause());
            assertEquals("Annual leave", annualDefinition.getDescription());
            assertEquals("ANNUAL", annualDefinition.getBasis());

            val validations = assertNonNull(annualDefinition.getValidations());
            assertEquals(1, validations.size());
            val validation = validations.iterator().next();
            assertFalse(assertNonNull(validation.isUnlimitedLeavesAllowed()));
            assertNull(validation.getMaximumValue());
            assertEquals(15.0, validation.getDefaultValue());
            assertEquals(15.0, validation.getMinimumValue());
            assertEquals(TimeOffUnit.DAYS, validation.getUnit());
            assertEquals(List.of("ONBOARDING", "ACTIVE"), validation.getAllowedContractStatuses());
        }

        @Nested
        class EntityLevelPoliciesFlagOff {

            @BeforeEach
            public void setUp() {
                entityLevelPoliciesOff();
            }

            /**
             *  - company - company Annual Leave definition, company sick leave definition
             *  - LKA - has Annual & Sick Leave definitions
             *  - IND MI - has Maternity Leave definition
             *  - IND DD - no state specific defs
             *  - PHL - no country defs
             *  - default - Personal Leave definition
             *
             *  - contract 1 - [EOR, LKA]
             *          -> eligible for company annual leave definition since it has higher allocated count than LKA annual leave definition
             *          -> eligible for LKA sick leave definition since it has higher allocated count than company sick leave definition
             *  - contract 2 - [HR, IND]
             *          -> eligible for company Annual leave definition,
             *          -> eligible for company Sick leave definition,
             *          -> default unpaid leave definition
             * - contract 3 - [EOR, IND-MI]
             *          -> eligible for company Annual leave definition,
             *          -> eligible for company Sick leave definition,
             *          -> eligible for country state Maternity leave definition
             * - contract 4 - [EOR, IND-DD]
             *          -> eligible for company Annual leave definition,
             *          -> eligible for default personal leave definition
             * - contract 5 - [EOR, PHL]
             *          -> eligible for company Annual leave definition,
             *          -> eligible for default personal leave definition
             * Expected map
             *    {1L -> {1L : companyAnnualLeaveDefinition, 2L : LKASickLeaveDefinition},
             *     2L -> {1L : companyAnnualLeaveDefinition, 2L, companySickLeaveDefinition, 14L : defaultUnpaidDefinition},
             *     3L -> {1L : companyAnnualLeaveDefinition, 2L, companySickLeaveDefinition, 3L : IndMaternityDefinition},
             *     4L -> {1L : companyAnnualLeaveDefinition, 2L, companySickLeaveDefinition, 8L : defaultPersonalLeaveDefinition},
             *     5L -> {1L : companyAnnualLeaveDefinition, 2L, companySickLeaveDefinition, 8L : defaultPersonalLeaveDefinition},
             *    }
             **/
            @Test
            void should_return_definitions_by_contracts_when_requested() {
                val companyId = 100L;
                val contract1 = ContractOuterClass.Contract.newBuilder()
                        .setId(1L)
                        .setCompanyId(companyId)
                        .setMemberId(1L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setCountry("LKA")
                        .build();
                val contract2 = ContractOuterClass.Contract.newBuilder()
                        .setId(2L)
                        .setCompanyId(companyId)
                        .setMemberId(2L)
                        .setType(ContractOuterClass.ContractType.HR_MEMBER)
                        .setCountry("IND")
                        .build();
                val contract3 = ContractOuterClass.Contract.newBuilder()
                        .setId(3L)
                        .setCompanyId(companyId)
                        .setMemberId(3L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setCountry("IND")
                        .setCountryStateCode("MI")
                        .build();
                val contract4 = ContractOuterClass.Contract.newBuilder()
                        .setId(4L)
                        .setCompanyId(companyId)
                        .setMemberId(4L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setCountry("IND")
                        .setCountryStateCode("DD")
                        .build();
                val contract5 = ContractOuterClass.Contract.newBuilder()
                        .setId(5L)
                        .setCompanyId(companyId)
                        .setMemberId(5L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setCountry("PHL")
                        .build();


                val member1 = Member.newBuilder().setId(1L).build();
                val member2 = Member.newBuilder().setId(2L).build();
                val member3 = Member.newBuilder().setId(3L).build();
                val member4 = Member.newBuilder().setId(4L).build();
                val member5 = Member.newBuilder().setId(5L).build();

                val contracts = List.of(contract1, contract2, contract3, contract4, contract5);
                val members = List.of(member1, member2, member3, member4, member5);

                // company definitions
                val company1AnnualLeaveDefinition = new CompanyDefinitionEntity(1L, 1L, buildDefinitionEntity(17.0, false), companyId, null, null, new ArrayList<>());
                val company1SickLeaveDefinition = new CompanyDefinitionEntity(2L, 2L, buildDefinitionEntity(5.0, false), companyId, null, null, new ArrayList<>());

                List<CompanyDefinitionEntity> companyDefinitions = List.of(company1AnnualLeaveDefinition, company1SickLeaveDefinition);

                // LKA definitions
                val lkaAnnualLeaveDefinition = new CountryDefinitionEntity(1L, 1L, buildDefinitionEntity(14, false, CountryCode.LKA, ""));
                val lkaSickLeaveDefinition = new CountryDefinitionEntity(2L, 2L, buildDefinitionEntity(7, false, CountryCode.LKA, null));

                // IND definitions
                val indMIMaternityLeaveDefinition = new CountryDefinitionEntity(3L, 3L, buildDefinitionEntity(85, false, CountryCode.IND, "MI"));

                // default definitions
                val defaultDefinition = new CountryDefinitionEntity(6L, 8L, buildDefinitionEntity(3.0, false));

                val countryLocations = List.of(
                        new CountryLocation(CountryCode.LKA, null),
                        new CountryLocation(CountryCode.IND, "MI"),
                        new CountryLocation(CountryCode.IND, "DD"),
                        new CountryLocation(CountryCode.PHL, null)
                );

                Map<CountryLocation, List<CountryDefinitionEntity>> countryDefinitions = Map.of(
                        countryLocations.get(0), List.of(lkaAnnualLeaveDefinition, lkaSickLeaveDefinition),
                        countryLocations.get(1), List.of(indMIMaternityLeaveDefinition),
                        countryLocations.get(2), List.of(defaultDefinition),
                        countryLocations.get(3), List.of(defaultDefinition)
                );

                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.ALL).companyDefinitionId(1L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.ALL).companyDefinitionId(2L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.ALL).companyDefinitionId(3L).build()
                );

                when(companyDefinitionRepository.findAllNonDeletedByCompanyId(companyId)).thenReturn(companyDefinitions);

                when(countryDefinitionService.getCountryLocationToCountryDefinitionsMap(countryLocations)).thenReturn(countryDefinitions);
                when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(1L, 2L))).thenReturn(rules);
                when(memberServiceAdapter.getMembers(Set.of(1L, 2L, 3L, 4L, 5L))).thenReturn(members);
                when(timeOffDefinitionRuleService.isContractMatchForRules(any(), anyList())).thenReturn(true);

                val resultMap = definitionService.findAvailableDefinitionsByContract(companyId, contracts);

                assertEquals(5, resultMap.size());
                val defsForContract1 = resultMap.get(1L);
                val defsForContract2 = resultMap.get(2L);
                val defsForContract3 = resultMap.get(3L);
                val defsForContract4 = resultMap.get(4L);
                val defsForContract5 = resultMap.get(5L);


                assertEquals(2, defsForContract1.size());
                assertEquals(company1AnnualLeaveDefinition, defsForContract1.get(1L));
                assertEquals(lkaSickLeaveDefinition, defsForContract1.get(2L));

                assertEquals(3, defsForContract2.size());
                assertEquals(company1AnnualLeaveDefinition, defsForContract2.get(1L));
                assertEquals(company1SickLeaveDefinition, defsForContract2.get(2L));
                assertEquals(HrMemberTimeOffDefinitionKt.getUnpaidLeaveDefinition(), defsForContract2.get(14L));

                assertEquals(3, defsForContract3.size());
                assertEquals(company1AnnualLeaveDefinition, defsForContract3.get(1L));
                assertEquals(company1SickLeaveDefinition, defsForContract3.get(2L));
                assertEquals(indMIMaternityLeaveDefinition, defsForContract3.get(3L));

                assertEquals(3, defsForContract4.size());
                assertEquals(company1AnnualLeaveDefinition, defsForContract4.get(1L));
                assertEquals(company1SickLeaveDefinition, defsForContract4.get(2L));
                assertEquals(defaultDefinition, defsForContract4.get(8L));

                assertEquals(3, defsForContract5.size());
                assertEquals(company1AnnualLeaveDefinition, defsForContract5.get(1L));
                assertEquals(company1SickLeaveDefinition, defsForContract5.get(2L));
                assertEquals(defaultDefinition, defsForContract5.get(8L));
            }

            /**
             * Test case for the scenario where only the company definitions are available.
             * And the country definitions are NOT available.
             */
            @Test
            void should_return_company_definitions_when_only_company_definitions_available() {
                val companyId = 2L;
                val companyDefinitionId = 1L;
                val typeId = 1L;
                val memberId = 3L;
                val contractId = 1L;

                val companyDefinitions = List.of(
                        buildCompanyDefinition(companyDefinitionId, companyId, typeId, null, 15.0)
                );
                val rules = List.of(buildTimeOffDefinitionRule(companyDefinitionId));
                val members = List.of(buildMember(memberId));
                val contract = ContractOuterClass.Contract.newBuilder()
                        .setId(contractId)
                        .setCompanyId(companyId)
                        .setMemberId(memberId)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .build();

                when(companyDefinitionRepository.findAllNonDeletedByCompanyId(companyId)).thenReturn(companyDefinitions);
                when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinitionId))).thenReturn(rules);
                when(memberServiceAdapter.getMembers(Set.of(memberId))).thenReturn(members);
                when(timeOffDefinitionRuleService.isContractMatchForRules(any(RuleResolverInput.class), anyList())).thenReturn(true);

                val result = definitionService.findAvailableDefinitionsByContract(companyId, Set.of(contract));

                assertEquals(1, result.keySet().size());

                val typeIdToTimeOffDefinition = assertNonNull(result.get(contractId));
                assertEquals(1, typeIdToTimeOffDefinition.keySet().size());

                val annualTimeOffDefinition = assertNonNull(typeIdToTimeOffDefinition.get(typeId));
                val annualDefinition = assertNonNull(annualTimeOffDefinition.getDefinition());
                assertTrue(annualDefinition.isRequired());
                assertNull(annualDefinition.getStateCode());
                assertEquals(1L, annualDefinition.getId());
                assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, annualDefinition.getStatus());
                assertEquals("Any Clause", annualDefinition.getClause());
                assertEquals("Annual leave", annualDefinition.getDescription());
                assertEquals("ANNUAL", annualDefinition.getBasis());

                val validations = assertNonNull(annualDefinition.getValidations());
                assertEquals(1, validations.size());
                val validation = validations.iterator().next();
                assertFalse(assertNonNull(validation.isUnlimitedLeavesAllowed()));
                assertNull(validation.getMaximumValue());
                assertEquals(15.0, validation.getDefaultValue());
                assertEquals(15.0, validation.getMinimumValue());
                assertEquals(TimeOffUnit.DAYS, validation.getUnit());
                assertEquals(List.of("ONBOARDING", "ACTIVE"), validation.getAllowedContractStatuses());
            }


            /**
             * Test case for the scenario where country definitions and company definitions are merged.
             * As a result, the company definitions will be used since the company complies the country definitions.
             */
            @Test
            void should_return_company_definitions_when_company_complies_country_definitions() {
                val annualTypeId = 1L;
                // Note that the default entitled value `12.0` will be replaced by the company definition entitled value `15.0`.
                val countryAnnualDefinition = buildCountryDefinition(2L, annualTypeId, CountryCode.VNM, 12.0);
                val countryLocation = new CountryLocation(CountryCode.VNM, null);


                val companyId = 2L;
                val companyDefinitionId = 1L;
                val memberId = 3L;

                // Note that the default entitled value `15.0` will replace the country definition entitled value `12.0` above.
                val companyAnnualDefinition = buildCompanyDefinition(companyDefinitionId, companyId, annualTypeId, null, 15.0);

                val contractId = 1L;
                val contract = ContractOuterClass.Contract.newBuilder()
                        .setId(contractId)
                        .setCompanyId(companyId)
                        .setMemberId(memberId)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setCountry("VNM")
                        .build();

                when(countryDefinitionService.getCountryLocationToCountryDefinitionsMap(List.of(countryLocation))).thenReturn(Map.of(countryLocation, List.of(countryAnnualDefinition)));
                when(companyDefinitionRepository.findAllNonDeletedByCompanyId(companyId)).thenReturn(List.of(companyAnnualDefinition));
                when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinitionId))).thenReturn(List.of(buildTimeOffDefinitionRule(companyDefinitionId)));
                when(memberServiceAdapter.getMembers(Set.of(memberId))).thenReturn(List.of(buildMember(memberId)));

                when(timeOffDefinitionRuleService.isContractMatchForRules(any(RuleResolverInput.class), anyList())).thenReturn(true);

                val result = definitionService.findAvailableDefinitionsByContract(companyId, Set.of(contract));

                assertEquals(1, result.keySet().size());

                val typeIdToTimeOffDefinition = assertNonNull(result.get(contractId));
                assertEquals(1, typeIdToTimeOffDefinition.keySet().size());

                // Verify that there is only a country definition available, default definition is ignored.
                val annualTimeOffDefinition = assertNonNull(typeIdToTimeOffDefinition.get(annualTypeId));
                val annualDefinition = assertNonNull(annualTimeOffDefinition.getDefinition());
                assertTrue(annualDefinition.isRequired());
                assertNull(annualDefinition.getStateCode());
                assertEquals(1L, annualDefinition.getId());
                assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, annualDefinition.getStatus());
                assertEquals("Any Clause", annualDefinition.getClause());
                assertEquals("Annual leave", annualDefinition.getDescription());
                assertEquals("ANNUAL", annualDefinition.getBasis());

                val validations = assertNonNull(annualDefinition.getValidations());
                assertEquals(1, validations.size());
                val validation = validations.iterator().next();
                assertFalse(assertNonNull(validation.isUnlimitedLeavesAllowed()));
                assertNull(validation.getMaximumValue());
                assertEquals(15.0, validation.getDefaultValue());
                assertEquals(15.0, validation.getMinimumValue());
                assertEquals(TimeOffUnit.DAYS, validation.getUnit());
                assertEquals(List.of("ONBOARDING", "ACTIVE"), validation.getAllowedContractStatuses());
            }

            /**
             * Test case for the scenario where country definitions and company definitions are merged.
             * As a result, the country definitions will be used since the company violates the country definitions.
             */
            @Test
            void should_return_country_definitions_when_company_violates_country_definitions() {
                val annualTypeId = 1L;
                // Note that the default entitled value `15.0` will replace the company definition entitled value `12.0`.
                val countryAnnualDefinition = buildCountryDefinition(2L, annualTypeId, CountryCode.VNM, 15.0);
                val countryLocation = new CountryLocation(CountryCode.VNM, null);
                val companyId = 2L;
                val companyDefinitionId = 1L;
                // Note that the default entitled value `12.0` will be replaced by the country definition entitled value `15.0` above.
                val companyAnnualDefinition = buildCompanyDefinition(companyDefinitionId, companyId, annualTypeId, null, 12.0);
                val memberId = 3L;
                val contractId = 1L;
                val contract = ContractOuterClass.Contract.newBuilder()
                        .setId(contractId)
                        .setCompanyId(companyId)
                        .setMemberId(memberId)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setCountry("VNM")
                        .build();


                when(countryDefinitionService.getCountryLocationToCountryDefinitionsMap(List.of(countryLocation))).thenReturn(Map.of(countryLocation, List.of(countryAnnualDefinition)));
                when(companyDefinitionRepository.findAllNonDeletedByCompanyId(companyId)).thenReturn(List.of(companyAnnualDefinition));
                when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(companyDefinitionId))).thenReturn(List.of(buildTimeOffDefinitionRule(companyDefinitionId)));
                when(memberServiceAdapter.getMembers(Set.of(memberId))).thenReturn(List.of(buildMember(memberId)));
                when(timeOffDefinitionRuleService.isContractMatchForRules(any(RuleResolverInput.class), anyList())).thenReturn(true);

                val result = definitionService.findAvailableDefinitionsByContract(companyId, Set.of(contract));

                assertEquals(1, result.keySet().size());
                val typeIdToTimeOffDefinition = assertNonNull(result.get(contractId));
                assertEquals(1, typeIdToTimeOffDefinition.keySet().size());

                // Verify that there is only a country definition available, default definition is ignored.
                val annualTimeOffDefinition = assertNonNull(typeIdToTimeOffDefinition.get(annualTypeId));
                val annualDefinition = assertNonNull(annualTimeOffDefinition.getDefinition());
                assertTrue(annualDefinition.isRequired());
                assertNull(annualDefinition.getStateCode());
                assertEquals(1L, annualDefinition.getId());
                assertEquals(TimeOffTypeDefinitionStatus.ACTIVE, annualDefinition.getStatus());
                assertEquals("Any Clause", annualDefinition.getClause());
                assertEquals("Annual leave", annualDefinition.getDescription());
                assertEquals("ANNUAL", annualDefinition.getBasis());

                val validations = assertNonNull(annualDefinition.getValidations());
                assertEquals(1, validations.size());
                val validation = validations.iterator().next();
                assertFalse(assertNonNull(validation.isUnlimitedLeavesAllowed()));
                assertNull(validation.getMaximumValue());
                assertEquals(15.0, validation.getDefaultValue());
                assertEquals(15.0, validation.getMinimumValue());
                assertEquals(TimeOffUnit.DAYS, validation.getUnit());
                assertEquals(List.of("ONBOARDING", "ACTIVE"), validation.getAllowedContractStatuses());
            }
        }

        @Nested
        class EntityLevelPoliciesFlagOn {

            @Captor
            private ArgumentCaptor<List<CompanyDefinitionFilter>> companyDefinitionFiltersArgumentCaptor;

            @BeforeEach
            public void setUp() {
                entityLevelPoliciesOn();
            }

            /*
             *  ========> ENTITY DEFINITIONS
             *  - LKA EOR entity
             *      - lkrEOREntityAnnualDefinition
             *      - lkrEOREntitySickDefinition
             *  - IND EOR entity
             *      - indEOREntityChildCareDefinition,
             *      - indEOREntityFestivalDefinition
             *  - PHL EOR entity
             *        - phlEOREntitySpecialDefinition
             *        - phlEOREntityFamilyDefinition,
             *  - Ind Company entity
             *        - indCompanyEntityAnnualDefinition,
             *        - indCompanyEntityPaternityDefinition
             *
             * =========> COUNTRY DEFINITIONS
             *  Sri Lanka definitions
             *  - lkaAnnualLeaveDefinition
             *  - lkaSickLeaveDefinition
             *
             * India definitions
             *  Mumbai (MI) State
             *      - indMIMaternityLeaveDefinition
             *
             * PHL - no country defs
             *
             * Global default definitions
             *   - defaultPersonalDefiniton
             *
             * ------------------- Expected results Map -------------------
             *  - contract 1 - [EOR, LKA, lkrEOREntityId]
             *     -> lkrEOREntityAnnualDefinition : entity definition get precedence over country definition since allocation count is higher
             *     -> lkaSickLeaveDefinition : country definition get precedence over entity definition since allocation count is higher
             *  - contract 2 - [HR, IND, indEORCompanyEntityId]
             *      -> indCompanyEntityAnnualDefinition,
             *      -> indCompanyEntityPaternityDefinition
             *      -> defaultUnpaidLeaveDefinition
             * - contract 3 - [EOR, IND-MI, indEORCompanyEntityId]
             *          -> indEOREntityChildCareDefinition,
             *          -> indEOREntityFestivalDefinition,
             *          -> indMIMaternityLeaveDefinition
             * - contract 4 - [EOR, IND-DD, indEORCompanyEntityId]
             *          -> indEOREntityChildCareDefinition,
             *          -> indEOREntityFestivalDefinition,
             *          -> defaultPersonalDefinition
             *
             * - contract 5 - [EOR, PHL, phlEOREntityId]
             *          -> phlEOREntitySpecialDefinition,
             *          -> phlEOREntityFamilyDefinition
             *          -> defaultPersonalDefinition
             * Expected map
             *    {1L -> {1L : lkrEOREntityAnnualDefinition, 2L : lkaSickLeaveDefinition},
             *     2L -> {1L : indCompanyEntityAnnualDefinition, 7L: indCompanyEntityPaternityDefinition, 14L : defaultUnpaidDefinition},
             *     3L -> {3L : indEOREntityChildCareDefinition, 4L: indEOREntityFestivalDefinition, 9L : IndMaternityDefinition},
             *     4L -> {3L : indEOREntityChildCareDefinition, 4L: indEOREntityFestivalDefinition, 13L : defaultPersonalLeaveDefinition},
             *     5L -> {5L : phlEOREntitySpecialDefinition, 6L: phlEOREntityFamilyDefinition, 13L : defaultPersonalDefinition},
             *    }
             */
            @Test
            void should_return_definitions_by_contracts_when_requested() {
                val companyId = 100L;
                val contract1 = ContractOuterClass.Contract.newBuilder()
                        .setId(1L)
                        .setCompanyId(companyId)
                        .setMemberId(1L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setCountry("LKA")
                        .build();
                val contract2 = ContractOuterClass.Contract.newBuilder()
                        .setId(2L)
                        .setCompanyId(companyId)
                        .setMemberId(2L)
                        .setType(ContractOuterClass.ContractType.HR_MEMBER)
                        .setCountry("IND")
                        .build();
                val contract3 = ContractOuterClass.Contract.newBuilder()
                        .setId(3L)
                        .setCompanyId(companyId)
                        .setMemberId(3L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setCountry("IND")
                        .setCountryStateCode("MI")
                        .build();
                val contract4 = ContractOuterClass.Contract.newBuilder()
                        .setId(4L)
                        .setCompanyId(companyId)
                        .setMemberId(4L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setCountry("IND")
                        .setCountryStateCode("DD")
                        .build();
                val contract5 = ContractOuterClass.Contract.newBuilder()
                        .setId(5L)
                        .setCompanyId(companyId)
                        .setMemberId(5L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setCountry("PHL")
                        .build();


                val member1 = Member.newBuilder().setId(1L).build();
                val member2 = Member.newBuilder().setId(2L).build();
                val member3 = Member.newBuilder().setId(3L).build();
                val member4 = Member.newBuilder().setId(4L).build();
                val member5 = Member.newBuilder().setId(5L).build();

                val companyEntityIndId = 979L;

                val contracts = List.of(contract1, contract2, contract3, contract4, contract5);
                val members = List.of(member1, member2, member3, member4, member5);

                // lkr eor entity definitions
                val lkrEOREntityAnnualDefinition= new CompanyDefinitionEntity(1L, 1L, buildDefinitionEntity(17.0, false), companyId, EntityType.EOR_PARTNER_ENTITY, -1L, new ArrayList<>());
                val lkrEOREntitySickDefinition = new CompanyDefinitionEntity(2L, 2L, buildDefinitionEntity(5.0, false), companyId, EntityType.EOR_PARTNER_ENTITY, -1L, new ArrayList<>());

                // ind eor entity definitions
                val indEOREntityChildCareDefinition= new CompanyDefinitionEntity(3L, 3L, buildDefinitionEntity(5.0, false), companyId, EntityType.EOR_PARTNER_ENTITY, -1L, new ArrayList<>());
                val indEOREntityFestivalDefinition = new CompanyDefinitionEntity(4L, 4L, buildDefinitionEntity(8.0, false), companyId, EntityType.EOR_PARTNER_ENTITY, -1L, new ArrayList<>());

                // phl eor entity definitions
                val phlEOREntitySpecialDefinition= new CompanyDefinitionEntity(5L, 5L, buildDefinitionEntity(12.0, false), companyId, EntityType.EOR_PARTNER_ENTITY, -1L, new ArrayList<>());
                val phlEOREntityFamilyDefinition = new CompanyDefinitionEntity(6L, 6L, buildDefinitionEntity(14.0, false), companyId, EntityType.EOR_PARTNER_ENTITY, -1L, new ArrayList<>());

                // ind company entity definitions
                val indCompanyEntityAnnualDefinition= new CompanyDefinitionEntity(7L, 1L, buildDefinitionEntity(12.0, false), companyId, EntityType.COMPANY_ENTITY, companyEntityIndId, new ArrayList<>());
                val indCompanyEntityPaternityDefinition = new CompanyDefinitionEntity(8L, 7L, buildDefinitionEntity(14.0, false), companyId, EntityType.COMPANY_ENTITY, companyEntityIndId, new ArrayList<>());

                List<CompanyDefinitionEntity> entityDefinitions = List.of(
                        lkrEOREntityAnnualDefinition, lkrEOREntitySickDefinition,
                        indEOREntityChildCareDefinition, indEOREntityFestivalDefinition,
                        phlEOREntitySpecialDefinition, phlEOREntityFamilyDefinition,
                        indCompanyEntityAnnualDefinition, indCompanyEntityPaternityDefinition
                        );

                // LKA definitions
                val lkaAnnualLeaveDefinition = new CountryDefinitionEntity(1L, 1L, buildDefinitionEntity(14, false, CountryCode.LKA, ""));
                val lkaSickLeaveDefinition = new CountryDefinitionEntity(2L, 2L, buildDefinitionEntity(7, false, CountryCode.LKA, null));

                // IND definitions
                val indMIMaternityLeaveDefinition = new CountryDefinitionEntity(3L, 9L, buildDefinitionEntity(85, false, CountryCode.IND, "MI"));

                // default definitions
                val defaultDefinition = new CountryDefinitionEntity(6L, 13L, buildDefinitionEntity(3.0, false));

                val countryLocations = List.of(
                        new CountryLocation(CountryCode.LKA, null),
                        new CountryLocation(CountryCode.IND, "MI"),
                        new CountryLocation(CountryCode.IND, "DD"),
                        new CountryLocation(CountryCode.PHL, null)
                );

                Map<CountryLocation, List<CountryDefinitionEntity>> countryDefinitions = Map.of(
                        countryLocations.get(0), List.of(lkaAnnualLeaveDefinition, lkaSickLeaveDefinition),
                        countryLocations.get(1), List.of(indMIMaternityLeaveDefinition),
                        countryLocations.get(2), List.of(defaultDefinition),
                        countryLocations.get(3), List.of(defaultDefinition)
                );

                List<TimeOffDefinitionRuleDBO> rules = List.of(
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.ENTITY).conditionValues(new String[]{"-1"}).companyDefinitionId(1L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.COUNTRY).conditionValues(new String[]{"LKA"}).companyDefinitionId(1L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.ENTITY).conditionValues(new String[]{"-1"}).companyDefinitionId(2L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.COUNTRY).conditionValues(new String[]{"LKA"}).companyDefinitionId(2L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.ENTITY).conditionValues(new String[]{"-1"}).companyDefinitionId(3L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.COUNTRY).conditionValues(new String[]{"IND"}).companyDefinitionId(3L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.ENTITY).conditionValues(new String[]{"-1"}).companyDefinitionId(4L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.COUNTRY).conditionValues(new String[]{"IND"}).companyDefinitionId(4L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.ENTITY).conditionValues(new String[]{"-1"}).companyDefinitionId(5L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.COUNTRY).conditionValues(new String[]{"PHL"}).companyDefinitionId(5L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.ENTITY).conditionValues(new String[]{"-1"}).companyDefinitionId(6L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.COUNTRY).conditionValues(new String[]{"PHL"}).companyDefinitionId(6L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.ENTITY).conditionValues(new String[]{"979"}).companyDefinitionId(7L).build(),
                        TimeOffDefinitionRuleDBO.builder().ruleType(RuleType.BY_CONDITION).conditionKey(ConditionKey.ENTITY).conditionValues(new String[]{"979"}).companyDefinitionId(8L).build()
                );
                Specification<CompanyDefinitionEntity> specBuilder = mock(Specification.class);

                when(countryDefinitionService.getCountryLocationToCountryDefinitionsMap(countryLocations)).thenReturn(countryDefinitions);
                when(timeOffDefinitionRuleService.findRulesByCompanyDefinitionIdIn(Set.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L))).thenReturn(rules);
                when(memberServiceAdapter.getMembers(Set.of(1L, 2L, 3L, 4L, 5L))).thenReturn(members);
                when(timeOffDefinitionRuleService.isContractMatchForRules(any(), anyList()))
                        .thenReturn(true).thenReturn(true).thenReturn(false).thenReturn(false).thenReturn(false).thenReturn(false)
                        .thenReturn(true).thenReturn(true)
                        .thenReturn(false).thenReturn(false).thenReturn(true).thenReturn(true).thenReturn(false).thenReturn(false)
                        .thenReturn(false).thenReturn(false).thenReturn(true).thenReturn(true).thenReturn(false).thenReturn(false)
                        .thenReturn(false).thenReturn(false).thenReturn(false).thenReturn(false).thenReturn(true).thenReturn(true)
                ;
                when(contractServiceAdapter.getEntityIdByContractId(contract2.getId())).thenReturn(companyEntityIndId);
                when(companyDefinitionSpecBuilder.build(companyDefinitionFiltersArgumentCaptor.capture())).thenReturn(specBuilder);
                when(companyDefinitionRepository.findAll(specBuilder)).thenReturn(entityDefinitions);

                val resultMap = definitionService.findAvailableDefinitionsByContract(companyId, contracts);

                assertEquals(5, resultMap.size());
                val defsForContract1 = resultMap.get(1L);
                val defsForContract2 = resultMap.get(2L);
                val defsForContract3 = resultMap.get(3L);
                val defsForContract4 = resultMap.get(4L);
                val defsForContract5 = resultMap.get(5L);


                assertEquals(2, defsForContract1.size());
                assertEquals(lkrEOREntityAnnualDefinition, defsForContract1.get(1L));
                assertEquals(lkaSickLeaveDefinition, defsForContract1.get(2L));

                assertEquals(3, defsForContract2.size());
                assertEquals(indCompanyEntityAnnualDefinition, defsForContract2.get(1L));
                assertEquals(indCompanyEntityPaternityDefinition, defsForContract2.get(7L));
                assertEquals(HrMemberTimeOffDefinitionKt.getUnpaidLeaveDefinition(), defsForContract2.get(14L));

                assertEquals(3, defsForContract3.size());
                assertEquals(indEOREntityChildCareDefinition, defsForContract3.get(3L));
                assertEquals(indEOREntityFestivalDefinition, defsForContract3.get(4L));
                assertEquals(indMIMaternityLeaveDefinition, defsForContract3.get(9L));

                assertEquals(3, defsForContract4.size());
                assertEquals(indEOREntityChildCareDefinition, defsForContract4.get(3L));
                assertEquals(indEOREntityFestivalDefinition, defsForContract4.get(4L));
                assertEquals(defaultDefinition, defsForContract4.get(13L));

                assertEquals(3, defsForContract5.size());
                assertEquals(phlEOREntitySpecialDefinition, defsForContract5.get(5L));
                assertEquals(phlEOREntityFamilyDefinition, defsForContract5.get(6L));
                assertEquals(defaultDefinition, defsForContract5.get(13L));

                val filters = companyDefinitionFiltersArgumentCaptor.getValue();

                assertEquals(EntityType.COMPANY_ENTITY, filters.get(0).getEntityType());
                assertEquals(companyEntityIndId, filters.get(0).getEntityId());
                assertEquals(companyId, filters.get(0).getCompanyId());
            }

        }

        @Nested
        class GetDefinitionIdToAssignedEmployeeCountMapTest {
            @Test
            void should_return_empty_map_when_no_entitlements_found() {
                // Given
                Set<Long> definitionIds = Set.of(1L, 2L);
                when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(definitionIds))
                        .thenReturn(List.of());

                // When
                Map<Long, Integer> result = definitionService.getDefinitionIdToAssignedEmployeeCountMap(definitionIds);

                // Then
                assertThat(result).isEmpty();
            }

            @Test
            void should_return_correct_employee_count_per_definition() {
                // Given
                Set<Long> definitionIds = Set.of(1L, 2L);
                List<TimeoffEntitlementDBO> entitlements = List.of(
                        TimeoffEntitlementDBO.builder()
                                .definitionId(1L)
                                .value(10.0)
                                .build(),
                        TimeoffEntitlementDBO.builder()
                                .definitionId(1L)
                                .value(5.0)
                                .build(),
                        TimeoffEntitlementDBO.builder()
                                .definitionId(2L)
                                .value(0.0)
                                .build()
                );

                when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(definitionIds))
                        .thenReturn(entitlements);

                // When
                Map<Long, Integer> result = definitionService.getDefinitionIdToAssignedEmployeeCountMap(definitionIds);

                // Then
                assertThat(result).hasSize(1)
                        .containsEntry(1L, 2);

            }
        }

        @Nested
        class FindAllDefinitionsByIdsTest {
            @Test
            void should_return_empty_map_when_no_definitions_found() {
                // Given
                Long companyId = 1L;
                Set<Long> definitionIds = Set.of(1L, 2L);

                when(companyDefinitionRepository.findAllNonDeletedByDefinitionIdInAndCompanyId(definitionIds, companyId))
                        .thenReturn(List.of());
                when(countryDefinitionService.getAllCountryDefinitionsByDefinitionIds(definitionIds))
                        .thenReturn(List.of());

                // When
                Map<Long, TimeOffDefinitionEntity> result = definitionService.findAllDefinitionsByIds(companyId, definitionIds);

                // Then
                assertThat(result).isEmpty();
            }

            @Test
            void should_return_both_company_and_country_definitions() {
                // Given
                Long companyId = 1L;
                Set<Long> definitionIds = Set.of(1L, 2L);

                CompanyDefinitionEntity companyDefinition = new CompanyDefinitionEntity();
                companyDefinition.setId(1L);
                companyDefinition.setDefinition(buildDefinition(1L, null, 10.0));

                CountryDefinitionEntity countryDefinition = new CountryDefinitionEntity();
                countryDefinition.setId(2L);
                countryDefinition.setDefinition(buildDefinition(2L, null, 15.0));

                when(companyDefinitionRepository.findAllNonDeletedByDefinitionIdInAndCompanyId(definitionIds, companyId))
                        .thenReturn(List.of(companyDefinition));
                when(countryDefinitionService.getAllCountryDefinitionsByDefinitionIds(Set.of(2L)))
                        .thenReturn(List.of(countryDefinition));

                // When
                Map<Long, TimeOffDefinitionEntity> result = definitionService.findAllDefinitionsByIds(companyId, definitionIds);

                // Then
                assertThat(result).hasSize(2)
                        .containsEntry(1L, companyDefinition)
                        .containsEntry(2L, countryDefinition);

            }
        }

        @Nested
        class MigrateCompanyLevelDefinitionsToEntityLevelTest {
            @Captor
            private ArgumentCaptor<CompanyDefinitionEntity> companyDefinitionEntityArgumentCaptor;

            @Captor
            private ArgumentCaptor<List<TimeoffEntitlementDBO>> entitlementListArgumentCaptor;

            @Test
            void should_do_nothing_when_no_company_ids_and_for_all_is_false() {
                // Given
                List<Long> companyIds = Collections.emptyList();
                boolean forAll = false;

                // When
                definitionService.migrateCompanyLevelDefinitionsToEntityLevel(companyIds, forAll);

                // Then
                verify(companyDefinitionRepository, never()).findAllByEntityIdIsNull();
                verify(companyDefinitionRepository, never()).findAllByEntityIdIsNullAndCompanyIdIn(any());
            }

            @Test
            void should_migrate_definitions_for_specific_company_ids() {
                // Given
                Long companyId1 = 100L;
                Long companyId2 = 200L;
                List<Long> companyIds = List.of(companyId1, companyId2);
                boolean forAll = false;

                // Setup company definitions
                CompanyDefinitionEntity companyDefinition1 = buildCompanyDefinition(1L, companyId1, 1L, null, 15.0);
                CompanyDefinitionEntity companyDefinition2 = buildCompanyDefinition(2L, companyId2, 2L, null, 20.0);
                List<CompanyDefinitionEntity> companyDefinitions = List.of(companyDefinition1, companyDefinition2);

                // Setup entitlements
                TimeoffEntitlementDBO entitlement1 = TimeoffEntitlementDBO.builder()
                        .contractId(10L)
                        .definitionId(companyDefinition1.getDefinition().getId())
                        .build();
                TimeoffEntitlementDBO entitlement2 = TimeoffEntitlementDBO.builder()
                        .contractId(20L)
                        .definitionId(companyDefinition2.getDefinition().getId())
                        .build();

                // Setup contracts
                ContractOuterClass.Contract contract1 = ContractOuterClass.Contract.newBuilder()
                        .setId(10L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .build();
                ContractOuterClass.Contract contract2 = ContractOuterClass.Contract.newBuilder()
                        .setId(20L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .build();

                // Mock repository calls
                when(companyDefinitionRepository.findAllByEntityIdIsNullAndCompanyIdIn(companyIds))
                        .thenReturn(companyDefinitions);
                when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(anySet()))
                        .thenReturn(List.of(entitlement1, entitlement2));
                when(contractServiceAdapter.getContractsByIdsAnyStatus(anySet(), eq(true)))
                        .thenReturn(List.of(contract1, contract2));
                when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class)))
                        .thenAnswer(invocation -> invocation.getArgument(0));

                // When
                definitionService.migrateCompanyLevelDefinitionsToEntityLevel(companyIds, forAll);

                // Then
                verify(companyDefinitionRepository).findAllByEntityIdIsNullAndCompanyIdIn(companyIds);
                verify(timeoffEntitlementDBORepository).findAllByDefinitionIdIn(anySet());
                verify(contractServiceAdapter).getContractsByIdsAnyStatus(anySet(), eq(true));
                verify(companyDefinitionRepository, times(2)).save(any(CompanyDefinitionEntity.class));
            }

            @Test
            void should_migrate_definitions_for_all_companies_when_for_all_is_true() {
                // Given
                List<Long> companyIds = Collections.emptyList();
                boolean forAll = true;

                // Setup company definitions
                CompanyDefinitionEntity companyDefinition1 = buildCompanyDefinition(1L, 100L, 1L, null, 15.0);
                CompanyDefinitionEntity companyDefinition2 = buildCompanyDefinition(2L, 200L, 2L, null, 20.0);
                List<CompanyDefinitionEntity> companyDefinitions = List.of(companyDefinition1, companyDefinition2);

                // Setup entitlements
                TimeoffEntitlementDBO entitlement1 = TimeoffEntitlementDBO.builder()
                        .contractId(10L)
                        .definitionId(companyDefinition1.getDefinition().getId())
                        .build();
                TimeoffEntitlementDBO entitlement2 = TimeoffEntitlementDBO.builder()
                        .contractId(20L)
                        .definitionId(companyDefinition2.getDefinition().getId())
                        .build();

                // Setup contracts
                ContractOuterClass.Contract contract1 = ContractOuterClass.Contract.newBuilder()
                        .setId(10L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .build();
                ContractOuterClass.Contract contract2 = ContractOuterClass.Contract.newBuilder()
                        .setId(20L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .build();

                // Mock repository calls
                when(companyDefinitionRepository.findAllByEntityIdIsNull())
                        .thenReturn(companyDefinitions);
                when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(anySet()))
                        .thenReturn(List.of(entitlement1, entitlement2));
                when(contractServiceAdapter.getContractsByIdsAnyStatus(anySet(), eq(true)))
                        .thenReturn(List.of(contract1, contract2));
                when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class)))
                        .thenAnswer(invocation -> invocation.getArgument(0));

                // When
                definitionService.migrateCompanyLevelDefinitionsToEntityLevel(companyIds, forAll);

                // Then
                verify(companyDefinitionRepository).findAllByEntityIdIsNull();
                verify(timeoffEntitlementDBORepository).findAllByDefinitionIdIn(anySet());
                verify(contractServiceAdapter).getContractsByIdsAnyStatus(anySet(), eq(true));
                verify(companyDefinitionRepository, times(2)).save(any(CompanyDefinitionEntity.class));
            }

            @Test
            void should_handle_migration_failure_for_some_definitions() {
                // Given
                Long companyId = 100L;
                List<Long> companyIds = List.of(companyId);
                boolean forAll = false;

                // Setup company definitions
                CompanyDefinitionEntity companyDefinition1 = buildCompanyDefinition(1L, companyId, 1L, null, 15.0);
                CompanyDefinitionEntity companyDefinition2 = buildCompanyDefinition(2L, companyId, 2L, null, 20.0);
                List<CompanyDefinitionEntity> companyDefinitions = List.of(companyDefinition1, companyDefinition2);

                // Setup entitlements
                TimeoffEntitlementDBO entitlement1 = TimeoffEntitlementDBO.builder()
                        .contractId(10L)
                        .definitionId(companyDefinition1.getDefinition().getId())
                        .build();
                TimeoffEntitlementDBO entitlement2 = TimeoffEntitlementDBO.builder()
                        .contractId(20L)
                        .definitionId(companyDefinition2.getDefinition().getId())
                        .build();

                // Setup contracts
                ContractOuterClass.Contract contract1 = ContractOuterClass.Contract.newBuilder()
                        .setId(10L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .build();
                ContractOuterClass.Contract contract2 = ContractOuterClass.Contract.newBuilder()
                        .setId(20L)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .build();

                // Mock repository calls
                when(companyDefinitionRepository.findAllByEntityIdIsNullAndCompanyIdIn(companyIds))
                        .thenReturn(companyDefinitions);
                when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(anySet()))
                        .thenReturn(List.of(entitlement1, entitlement2));
                when(contractServiceAdapter.getContractsByIdsAnyStatus(anySet(), eq(true)))
                        .thenReturn(List.of(contract1, contract2));
                when(companyDefinitionRepository.save(any(CompanyDefinitionEntity.class)))
                        .thenThrow(new RuntimeException("Migration failed"))
                        .thenAnswer(invocation -> invocation.getArgument(0));

                // When
                definitionService.migrateCompanyLevelDefinitionsToEntityLevel(companyIds, forAll);

                // Then
                verify(companyDefinitionRepository).findAllByEntityIdIsNullAndCompanyIdIn(companyIds);
                verify(timeoffEntitlementDBORepository).findAllByDefinitionIdIn(anySet());
                verify(contractServiceAdapter).getContractsByIdsAnyStatus(anySet(), eq(true));
                verify(companyDefinitionRepository, times(2)).save(any(CompanyDefinitionEntity.class));
            }

            @Test
            void should_handle_definitions_with_no_assignments() {
                // Given
                Long companyId = 100L;
                List<Long> companyIds = List.of(companyId);
                boolean forAll = false;

                // Setup company definitions
                CompanyDefinitionEntity companyDefinition = buildCompanyDefinition(1L, companyId, 1L, null, 15.0);
                List<CompanyDefinitionEntity> companyDefinitions = List.of(companyDefinition);

                // Mock repository calls
                when(companyDefinitionRepository.findAllByEntityIdIsNullAndCompanyIdIn(companyIds))
                        .thenReturn(companyDefinitions);
                when(timeoffEntitlementDBORepository.findAllByDefinitionIdIn(anySet()))
                        .thenReturn(Collections.emptyList());

                // Allow getContractsByIdsAnyStatus with empty set
                when(contractServiceAdapter.getContractsByIdsAnyStatus(Collections.emptySet(), true))
                        .thenReturn(Collections.emptyList());

                // When
                definitionService.migrateCompanyLevelDefinitionsToEntityLevel(companyIds, forAll);

                // Then
                verify(companyDefinitionRepository).findAllByEntityIdIsNullAndCompanyIdIn(companyIds);
                verify(timeoffEntitlementDBORepository).findAllByDefinitionIdIn(anySet());
                verify(contractServiceAdapter).getContractsByIdsAnyStatus(Collections.emptySet(), true);
                verify(companyDefinitionRepository, never()).save(any(CompanyDefinitionEntity.class));
            }
        }


        private CountryDefinitionEntity buildCountryDefinition(
                Long id, Long typeId, CountryCode country, double defaultEntitledValue) {
            val definition = new CountryDefinitionEntity();
            definition.setId(id);
            definition.setTypeId(typeId);
            definition.setDefinition(buildDefinition(1L, country, defaultEntitledValue));
            return definition;
        }

        private CompanyDefinitionEntity buildCompanyDefinition(
                Long id, Long companyId, Long typeId, CountryCode country, double defaultEntitledValue) {
            val companyDefinition = new CompanyDefinitionEntity();
            companyDefinition.setId(id);
            companyDefinition.setCompanyId(companyId);
            companyDefinition.setTypeId(typeId);
            companyDefinition.setDefinition(buildDefinition(id, country, defaultEntitledValue));
            return companyDefinition;
        }

        private DefinitionEntity buildDefinition(Long id, CountryCode country, double defaultEntitledValue) {
            val definition = new DefinitionEntity();
            definition.setId(id);
            definition.setRequired(true);
            definition.setCountryCode(country);
            definition.setStatus(TimeOffTypeDefinitionStatus.ACTIVE);
            definition.setClause("Any Clause");
            definition.setDescription("Annual leave");
            definition.setBasis("ANNUAL");
            definition.setValidations(Set.of(buildValidation(defaultEntitledValue)));
            return definition;
        }

        private TimeoffDefinitionValidationEntity buildValidation(double defaultValue) {
            return new TimeoffDefinitionValidationEntity(
                    defaultValue, // default value
                    15.0, // minimum value
                    null, // maximum value
                    TimeOffUnit.DAYS,
                    List.of("ONBOARDING", "ACTIVE"), // allowed contract statuses
                    false // is unlimited leaves allowed
            );
        }

        private TimeOffDefinitionRuleDBO buildTimeOffDefinitionRule(Long companyDefinitionId) {
            return new TimeOffDefinitionRuleDBO().companyDefinitionId(companyDefinitionId);
        }

        private Member buildMember(Long memberId) {
            return Member.newBuilder().setId(memberId).build();
        }
    }

    @Nested
    class GetDefinitionIdToAssignedEntityMapTest {

        @Test
        void should_send_empty_map_when_id_set_is_empty() {
            val result = definitionService.getDefinitionIdToAssignedEntityMap(Collections.emptySet());
            assertTrue(result.isEmpty());
        }

        @Test
        void should_send_entity_map_when_requested() {
            val companyId = 100L;

            val companyEntityId = 987L;
            val companyEntityName = "NN - COLOMBO";

            val definitionId1 = 1L;
            val definitionId2 = 2L;
            val definition1 = buildDefinitionEntity(14.0, false);
            definition1.setId(definitionId1);
            val definition2 = buildDefinitionEntity(21.0, false);
            definition2.setId(definitionId2);

            List<CompanyDefinitionEntity> companyDefs = List.of(
                    new CompanyDefinitionEntity(1L, 1L, definition1, companyId, EntityType.COMPANY_ENTITY, companyEntityId, new ArrayList<>()),
                    new CompanyDefinitionEntity(2L, 1L, definition2, companyId, EntityType.EOR_PARTNER_ENTITY, -1L, new ArrayList<>())
            );
            List<CompanyOuterClass.LegalEntity> legalEntities = List.of(
                    CompanyOuterClass.LegalEntity.newBuilder().setId(companyEntityId).setLegalName(companyEntityName).build()
            );
            val definitionIds = Set.of(definitionId1, definitionId2);


            when(companyDefinitionRepository.findByDefinitionIdIn(Set.of(definitionId1, definitionId2))).thenReturn(companyDefs);
            when(companyServiceAdapter.getLegalEntitiesByIds(Set.of(companyEntityId))).thenReturn(legalEntities);

            val result = definitionService.getDefinitionIdToAssignedEntityMap(definitionIds);

            assertEquals(2, result.size());

            val entityInfo1 = result.get(definitionId1);
            val entityInfo2 = result.get(definitionId2);

            assertNonNull(entityInfo1);
            assertEquals(EntityType.COMPANY_ENTITY, entityInfo1.getType());
            assertEquals(companyEntityId, entityInfo1.getId());
            assertEquals(companyEntityName, entityInfo1.getName());

            assertNonNull(entityInfo2);
            assertEquals(EntityType.EOR_PARTNER_ENTITY, entityInfo2.getType());
            assertEquals(-1L, entityInfo2.getId());
            assertEquals(TimeOffUtil.EOR_VIRTUAL_ENTITY_NAME, entityInfo2.getName());
        }
    }

    @Nested
    class GetDefinitionsByFilterTest {
        @Captor
        private ArgumentCaptor<List<CompanyDefinitionFilter>> companyDefinitionFiltersArgumentCaptor;

        @Test
        void should_return_empty_list_when_no_definitions_found() {
            val result = definitionService.getDefinitions(null);
            assertTrue(result.isEmpty());
        }

        @Test
        void should_throw_exception_for_company_user() {
            val filter = TimeOffPolicyFilter.newBuilder()
                    .entityFilters(List.of(EntityFilter.newBuilder()
                                    .id(1L)
                                    .type(EntityType.COMPANY_ENTITY)
                                    .build()))
                    .build();
            val companyUserContext = getCompanyUserUserDetails(1L, 1L);
            when(currentUser.getContext()).thenReturn(companyUserContext);

            val ex = assertThrows(ValidationException.class, () -> definitionService.getDefinitions(filter));
            assertEquals("Current user is not allowed to access definitions, user id : " + companyUserContext.getId() +
                    ", experience : " + companyUserContext.getExperience(), ex.getMessage());
        }

        @Test
        void should_return_definitions_for_ops_user() {
            val filter = TimeOffPolicyFilter.newBuilder()
                    .entityFilters(List.of(EntityFilter.newBuilder()
                            .id(1L)
                            .type(EntityType.COMPANY_ENTITY)
                            .build()))
                    .build();
            Specification<CompanyDefinitionEntity> specBuilder = mock(Specification.class);
            val opsUserContext = getOpsUserDetails(1L);
            val companyDefinitions = List.of(
                    new CompanyDefinitionEntity(1L, 1L, buildDefinitionEntity(14.0, false), 1L, EntityType.COMPANY_ENTITY, 1L, new ArrayList<>())
            );

            List<TimeoffTypeDBO> typesList = List.of(
                    TimeoffTypeDBO.builder().id(1L).key("annualLeave").build());

            when(timeoffTypeService.findAllTypeDBOsByIds(Set.of(1L))).thenReturn(typesList);

            when(currentUser.getContext()).thenReturn(opsUserContext);
            when(companyDefinitionSpecBuilder.build(companyDefinitionFiltersArgumentCaptor.capture())).thenReturn(specBuilder);
            when(companyDefinitionRepository.findAll(specBuilder)).thenReturn(companyDefinitions);

            definitionService.getDefinitions(filter);

            val specBuilderFilters = companyDefinitionFiltersArgumentCaptor.getValue();
            assertEquals(1, specBuilderFilters.size());
            assertEquals(EntityType.COMPANY_ENTITY, specBuilderFilters.get(0).getEntityType());
            assertEquals(1L, specBuilderFilters.get(0).getEntityId());
            assertNull(specBuilderFilters.get(0).getCompanyId());
        }
    }

    private DefinitionEntity buildDefinitionEntity(double entitledCount, boolean isUnlimitedLeaves) {
        return new DefinitionEntity(
                100L, null, null, false, null, null, null,
                Set.of(
                        new TimeoffDefinitionValidationEntity(
                                isUnlimitedLeaves ? 365.0 : entitledCount, // default value
                                null, // minimum value
                                null, // maximum value
                                TimeOffUnit.DAYS,
                                List.of(ContractStatus.ONBOARDING.name(), ContractStatus.OFFBOARDING.name(), ContractStatus.ACTIVE.name()),
                                isUnlimitedLeaves // is unlimited leaves allowed
                        ))
                , null
                , TimeOffTypeDefinitionStatus.ACTIVE
                , "Policy"

        );
    }

    private DefinitionEntity buildDefinitionEntity(double entitledCount, boolean isUnlimitedLeaves, CountryCode countryCode, String state) {
        val definition = buildDefinitionEntity(entitledCount, isUnlimitedLeaves);
        definition.setCountryCode(countryCode);
        definition.setStateCode(state);
        return definition;
    }


    private UserContext getMemberUserDetails(long memberId) {
        return new UserContext(
                10L,
                "username",
                "member",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        memberId,
                        null,
                        null,
                        null,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

    private UserContext getOpsUserDetails(long opsUserId) {
        return new UserContext(
                10L,
                "username",
                "operations",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        opsUserId,
                        null,
                        null,
                        null,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

    private UserContext getCompanyUserUserDetails(Long companyUserId, Long companyId) {
        return new UserContext(
                10L,
                "username",
                "company",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        null,
                        companyId,
                        null,
                        companyUserId,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

    private void entityLevelPoliciesOn() {
        when(featureFlagService.isOn(eq(FeatureFlags.ENTITY_LEVEL_POLICIES), anyMap())).thenReturn(true);
    }

    private void entityLevelPoliciesOff() {
        when(featureFlagService.isOn(eq(FeatureFlags.ENTITY_LEVEL_POLICIES), anyMap())).thenReturn(false);
    }


    private <T> @NotNull T assertNonNull(@Nullable T value) {
        assertNotNull(value);
        return value;
    }
}
