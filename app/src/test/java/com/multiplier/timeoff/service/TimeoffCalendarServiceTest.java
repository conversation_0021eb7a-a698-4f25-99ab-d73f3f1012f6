package com.multiplier.timeoff.service;

import com.multiplier.company.schema.holiday.LegalEntityHoliday;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.HolidayServiceAdapter;
import com.multiplier.timeoff.adapters.WorkshiftServiceAdapter;
import com.multiplier.timeoff.core.common.dto.WorkshiftDTO;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.core.util.WorkshiftUtil;
import com.multiplier.timeoff.repository.dto.TimeoffEntryWithDescription;
import com.multiplier.timeoff.service.exception.ValidationException;
import com.multiplier.timeoff.service.timeoffEntry.TimeoffEntryServiceV2;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static com.multiplier.timeoff.types.TimeOffSession.FULL_DAY;
import static java.time.DayOfWeek.FRIDAY;
import static java.time.DayOfWeek.TUESDAY;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TimeoffCalendarServiceTest {

    @InjectMocks
    private TimeoffCalendarService timeoffCalendarService;

    @Mock
    private AuthorizationService authorizationService;
    
    @Mock
    private ContractServiceAdapter contractServiceAdapter;
    
    @Mock
    private TimeoffSummaryService timeoffSummaryService;
    
    @Mock
    private TimeoffEntryServiceV2 timeoffEntryServiceV2;
    
    @Mock
    private HolidayServiceAdapter holidayServiceAdapter;
    @Mock
    private WorkshiftServiceAdapter workshiftServiceAdapter;
    
    @Mock
    private DgsDataFetchingEnvironment dfe;



    @Nested
    class GetTimeOffCalendarTest {
        private ContractOuterClass.Contract contract;
        private TimeOffCalendarFilter filter;

        private static final Long CONTRACT_ID = 1L;
        private static final LocalDate START_DATE = LocalDate.of(2024, 1, 1);
        private static final LocalDate END_DATE = LocalDate.of(2024, 12, 31);

        @BeforeEach
        void setUp() {
            // Setup contract
            contract = ContractOuterClass.Contract.newBuilder()
                    .setId(CONTRACT_ID)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .build();

            // Setup filter
            filter = TimeOffCalendarFilter.newBuilder()
                    .contractId(CONTRACT_ID)
                    .startDate(START_DATE)
                    .endDate(END_DATE)
                    .build();
        }

        @Test
        void getTimeOffCalendar_ShouldThrowExceptionForNullFilter() {
            // When/Then
            assertThrows(IllegalArgumentException.class, () ->
                    timeoffCalendarService.getTimeOffCalendar(dfe, null)
            );
        }

        @Test
        void getTimeOffCalendar_ShouldThrowExceptionForIneligibleContractType() {
            // Given
            ContractOuterClass.Contract ineligibleContract = ContractOuterClass.Contract.newBuilder()
                    .setId(CONTRACT_ID)
                    .setType(ContractOuterClass.ContractType.CONTRACTOR)
                    .build();

            when(contractServiceAdapter.findContractByContractId(CONTRACT_ID)).thenReturn(ineligibleContract);

            // When/Then
            assertThrows(ValidationException.class, () ->
                    timeoffCalendarService.getTimeOffCalendar(dfe, filter)
            );
        }

        @Test
        void getTimeOffCalendar_ShouldThrowExceptionWhenNoSummariesFound() {
            // Given
            TimeOffCalendarFilter filterWithoutDates = TimeOffCalendarFilter.newBuilder()
                    .contractId(CONTRACT_ID)
                    .build();
            when(contractServiceAdapter.findContractByContractId(CONTRACT_ID)).thenReturn(contract);
            when(timeoffSummaryService.getEarliestActiveSummaryStartDate(CONTRACT_ID)).thenReturn(null);

            // When
           val ex = assertThrows(ValidationException.class, () ->timeoffCalendarService.getTimeOffCalendar(dfe, filterWithoutDates));

            // Then
            assertEquals("No timeoff summaries found for contract id : " + CONTRACT_ID + ", statuses : (ACTIVE, UPCOMING)", ex.getMessage());

        }

        @Test
        void getTimeOffCalendar_ShouldReturnCalendarWithEvents() {
            // Given
            when(contractServiceAdapter.findContractByContractId(CONTRACT_ID)).thenReturn(contract);

            // Setup holidays
            List<LegalEntityHoliday.Holiday> holidays = createTestHolidays();
            when(holidayServiceAdapter.getHolidays(Set.of(CONTRACT_ID), 2024,null))
                    .thenReturn(holidays);
            WorkshiftDTO workshift = WorkshiftDTO.builder()
                    .startDate(FRIDAY)
                    .endDate(TUESDAY)
                    .build();
            when(workshiftServiceAdapter.getWorkshiftByContractId(CONTRACT_ID)).thenReturn(workshift);

            // Setup timeoffs
            List<TimeoffEntryWithDescription> timeoffEntries = List.of(
                    TimeoffEntryWithDescription.builder()
                            .id(1L)
                            .date(LocalDate.of(2024, 2, 1))
                            .session(FULL_DAY)
                            .description("Vacation")
                            .type(TimeOffEntryType.TIMEOFF)
                            .build()
            );
            when(timeoffEntryServiceV2.getNonDeletedNonDraftTimeoffEntries(START_DATE, END_DATE, CONTRACT_ID))
                    .thenReturn(timeoffEntries);

            // When
            TimeOffCalendar result = timeoffCalendarService.getTimeOffCalendar(dfe, filter);

            // Then
            assertNotNull(result);
            assertEquals(START_DATE, result.getStartDate());
            assertEquals(END_DATE, result.getEndDate());

            val restDays = WorkshiftUtil.getRestDaysListForRange(START_DATE, END_DATE, workshift);

            // Verify events
            List<TimeOffCalendarEvent> events = result.getEvents();
            assertEquals(3 + restDays.size(), events.size());

            // Verify holiday events
            List<TimeOffCalendarEvent> holidayEvents = events.stream()
                    .filter(e -> e.getType() == TimeOffCalendarEventType.HOLIDAY)
                    .toList();
            assertEquals(2, holidayEvents.size());

            // Verify timeoff events
            List<TimeOffCalendarEvent> timeoffEvents = events.stream()
                    .filter(e -> e.getType() == TimeOffCalendarEventType.TIMEOFF)
                    .toList();
            assertEquals(1, timeoffEvents.size());
            assertEquals(LocalDate.of(2024, 2, 1), timeoffEvents.get(0).getDate());
            assertEquals(FULL_DAY, timeoffEvents.get(0).getSession());
            assertEquals("Vacation", timeoffEvents.get(0).getDescription());

            // verify restDays
            List<LocalDate> restDayEvents = events.stream()
                    .filter(e -> e.getType() == TimeOffCalendarEventType.RESTDAY)
                    .map(TimeOffCalendarEvent::getDate)
                    .toList();
            assertEquals(restDays, restDayEvents);
        }

        @Test
        void getTimeOffCalendar_ShouldUseDefaultDatesWhenNotProvided() {
            // Given
            TimeOffCalendarFilter filterWithoutDates = TimeOffCalendarFilter.newBuilder()
                    .contractId(CONTRACT_ID)
                    .build();
            LocalDate startDate = LocalDate.of(2024, 1, 1);
            LocalDate endDate = LocalDate.of(2025, 12, 31);
            when(contractServiceAdapter.findContractByContractId(CONTRACT_ID)).thenReturn(contract);
            when(timeoffSummaryService.getEarliestActiveSummaryStartDate(CONTRACT_ID)).thenReturn(startDate);
            when(timeoffSummaryService.getFurthestSummaryEndDate(CONTRACT_ID)).thenReturn(endDate);

            WorkshiftDTO workshift = WorkshiftDTO.builder()
                    .startDate(FRIDAY)
                    .endDate(TUESDAY)
                    .build();
            // Set up empty holidays and timeoffs for simplicity
            when(holidayServiceAdapter.getHolidays(any(), any(Integer.class), any())).thenReturn(Collections.emptyList());
            when(timeoffEntryServiceV2.getNonDeletedNonDraftTimeoffEntries(startDate, endDate, CONTRACT_ID)).thenReturn(Collections.emptyList());
            when(workshiftServiceAdapter.getWorkshiftByContractId(CONTRACT_ID)).thenReturn(workshift);

            // When
            TimeOffCalendar result = timeoffCalendarService.getTimeOffCalendar(dfe, filterWithoutDates);

            val restDays = WorkshiftUtil.getRestDaysListForRange(startDate, endDate, workshift);

            // Then
            assertNotNull(result);
            assertEquals(startDate, result.getStartDate());
            assertEquals(endDate, result.getEndDate());

            // verify restDays
            List<LocalDate> restDayEvents = result.getEvents().stream()
                    .filter(e -> e.getType() == TimeOffCalendarEventType.RESTDAY)
                    .map(TimeOffCalendarEvent::getDate)
                    .toList();
            assertEquals(restDays, restDayEvents);
        }

        // Helper methods to create test data

        private List<LegalEntityHoliday.Holiday> createTestHolidays() {
            LegalEntityHoliday.Holiday newYear = LegalEntityHoliday.Holiday.newBuilder()
                    .setYear(2024)
                    .setMonth(1)
                    .setDate(1)
                    .setName("New Year's Day")
                    .addContractIds(CONTRACT_ID)
                    .build();

            LegalEntityHoliday.Holiday christmas = LegalEntityHoliday.Holiday.newBuilder()
                    .setYear(2024)
                    .setMonth(12)
                    .setDate(25)
                    .setName("Christmas Day")
                    .addContractIds(CONTRACT_ID)
                    .build();

            return Arrays.asList(newYear, christmas);
        }
    }
    
}
