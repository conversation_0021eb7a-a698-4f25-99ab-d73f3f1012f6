package com.multiplier.timeoff.service;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.common.transport.user.UserScopes;
import com.multiplier.company.schema.holiday.LegalEntityHoliday;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.HolidayServiceAdapter;
import com.multiplier.timeoff.adapters.WorkshiftServiceAdapter;
import com.multiplier.timeoff.core.common.dto.WorkshiftDTO;
import com.multiplier.timeoff.core.security.AuthorizationService;
import com.multiplier.timeoff.processor.TimeoffValidator;
import com.multiplier.timeoff.repository.TimeoffEntryRepository;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.repository.model.TimeoffEntryDBO;
import com.multiplier.timeoff.service.mapper.TimeOffInputMapper;
import com.multiplier.timeoff.service.mapper.TimeoffEntryMapper;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;
import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class TimeoffBreakdownServiceTest {

    @Mock
    HolidayServiceAdapter holidayServiceAdapter;

    @Mock
    AuthorizationService authorizationService;

    @Mock
    ContractServiceAdapter contractServiceAdapter;

    @Mock
    WorkshiftServiceAdapter workshiftServiceAdapter;

    @Mock
    CurrentUser currentUser;

    @Mock
    TimeoffValidator timeoffValidator;

    @Mock
    TimeoffEntryRepository timeoffEntryRepository;

    @Mock
    TimeoffEntryMapper timeoffEntryMapper;

    @Mock
    TimeOffInputMapper timeOffInputMapper;

    @InjectMocks
    TimeoffBreakdownService timeoffBreakdownService;
  
    private void stubHolidayServiceAdaptor() {
        val holidays = List.of(
                LegalEntityHoliday.Holiday.newBuilder().setYear(2025).setMonth(10).setDate(6).build(),
                LegalEntityHoliday.Holiday.newBuilder().setYear(2025).setMonth(10).setDate(15).build()
        );
        when(holidayServiceAdapter.getHolidays(anySet(), anyInt(), ArgumentMatchers.<Integer>any())).thenReturn(holidays);
    }

    private WorkshiftDTO getWorkshift() {
        return WorkshiftDTO.builder().startDate(DayOfWeek.MONDAY).endDate(DayOfWeek.FRIDAY).build();
    }

    private WorkshiftDTO getWorkshift(DayOfWeek startDay, DayOfWeek endDay) {
        return WorkshiftDTO.builder().startDate(startDay).endDate(endDay)
                .build();
    }

    @Nested
    class generateTimeoffEntriesTest {

        private static final Long CONTRACT_ID = 1L;

        static Stream<Arguments> normalWorkWeekLeavesTestCases() {
            return Stream.of(
                Arguments.of("2025-10-13", "MORNING", "2025-10-13", "MORNING", 0.5),
                Arguments.of("2025-10-13", "MORNING", "2025-10-13", "AFTERNOON", 1.0),
                Arguments.of("2025-10-13", "AFTERNOON", "2025-10-13", "MORNING", 0.5),
                Arguments.of("2025-10-13", "MORNING", "2025-10-14", "MORNING", 1.5),
                Arguments.of("2025-10-13", "MORNING", "2025-10-14", "AFTERNOON", 2.0),
                Arguments.of("2025-10-13", "MORNING", "2025-10-17", "AFTERNOON", 4.0),
                Arguments.of("2025-10-13", "MORNING", "2025-10-21", "AFTERNOON", 6.0)
            );
        }

        @Test
        void should_make_rest_days_zero() {
            val startDateInput = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-04"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val endDateInput = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-05"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                .startDate(startDateInput.getDateOnly())
                .startSession(startDateInput.getSession())
                .endDate(endDateInput.getDateOnly())
                .endSession(endDateInput.getSession())
                .contractId(CONTRACT_ID)
                .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);
            val noOfDays = breakdown.getRight();
            assertEquals(0.0, noOfDays);
        }

        @Test
        void should_make_rest_days_zero_for_wrapped_workshift() {
            val startDateInput = TimeOffDateInput.newBuilder()
                        .dateOnly(LocalDate.parse("2025-10-08"))
                        .session(TimeOffSession.MORNING)
                        .build();
            val endDateInput = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-09"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDateInput.getDateOnly())
                    .startSession(startDateInput.getSession())
                    .endDate(endDateInput.getDateOnly())
                    .endSession(endDateInput.getSession())
                    .contractId(CONTRACT_ID)
                    .build();
            val workshift = getWorkshift(DayOfWeek.FRIDAY, DayOfWeek.TUESDAY);

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val noOfDays = breakdown.getRight();
            assertEquals(0.0, noOfDays);
        }

        @Test
        void should_make_holidays_days_zero() {
            val startDateInput = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-06"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val endDateInput = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-07"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDateInput.getDateOnly())
                    .startSession(startDateInput.getSession())
                    .endDate(endDateInput.getDateOnly())
                    .endSession(endDateInput.getSession())
                    .contractId(CONTRACT_ID)
                    .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val noOfDays = breakdown.getRight();
            assertEquals(1.0, noOfDays);
        }

        @Test
        void should_make_holidays_and_rest_days_zero() {
            val startDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-05"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val endDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-07"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDate.getDateOnly())
                    .startSession(startDate.getSession())
                    .endDate(endDate.getDateOnly())
                    .endSession(endDate.getSession())
                    .contractId(CONTRACT_ID)
                    .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val noOfDays = breakdown.getRight();
            assertEquals(1.0, noOfDays);
        }

        @ParameterizedTest
        @MethodSource("normalWorkWeekLeavesTestCases")
        void should_give_proper_no_of_days_values(
            String parameterStartDate,
            TimeOffSession parameterStartSession,
            String parameterEndDate,
            TimeOffSession parameterEndSession,
            double expectedNoOfDays
        ) {
            val startDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse(parameterStartDate))
                    .session(parameterStartSession)
                    .build();
            val endDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse(parameterEndDate))
                    .session(parameterEndSession)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDate.getDateOnly())
                    .startSession(startDate.getSession())
                    .endDate(endDate.getDateOnly())
                    .endSession(endDate.getSession())
                    .contractId(CONTRACT_ID)
                    .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val noOfDays = breakdown.getRight();
            assertEquals(expectedNoOfDays, noOfDays);
        }

        @Test
        void should_add_correct_time_off_session_values_half_day_morning() {
            val startDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-01"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val endDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-01"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val workshift = getWorkshift();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDate.getDateOnly())
                    .startSession(startDate.getSession())
                    .endDate(endDate.getDateOnly())
                    .endSession(endDate.getSession())
                    .contractId(CONTRACT_ID)
                    .build();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val timeoffEntries = breakdown.getLeft();
            val actualEntry = timeoffEntries.get(0);
            assertEquals(LocalDate.parse("2025-10-01"), actualEntry.date());
            assertEquals(0.5, actualEntry.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry.type());
            assertEquals(TimeOffSession.MORNING, actualEntry.session());
        }

        @Test
        void should_add_correct_time_off_session_values_half_day_afternoon() {
            val startDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-01"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val endDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-01"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDate.getDateOnly())
                    .startSession(startDate.getSession())
                    .endDate(endDate.getDateOnly())
                    .endSession(endDate.getSession())
                    .contractId(CONTRACT_ID)
                    .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val timeoffEntries = breakdown.getLeft();
            val actualEntry = timeoffEntries.get(0);
            assertEquals(LocalDate.parse("2025-10-01"), actualEntry.date());
            assertEquals(0.5, actualEntry.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry.type());
            assertEquals(TimeOffSession.AFTERNOON, actualEntry.session());
        }

        @Test
        void should_add_correct_time_off_session_values_full_day() {
            val startDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-01"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val endDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-01"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDate.getDateOnly())
                    .startSession(startDate.getSession())
                    .endDate(endDate.getDateOnly())
                    .endSession(endDate.getSession())
                    .contractId(CONTRACT_ID)
                    .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val timeoffEntries = breakdown.getLeft();
            val actualEntry = timeoffEntries.get(0);
            assertEquals(LocalDate.parse("2025-10-01"), actualEntry.date());
            assertEquals(1.0, actualEntry.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry.type());
            assertEquals(TimeOffSession.FULL_DAY, actualEntry.session());
        }

        @Test
        void should_add_correct_time_off_session_values_withing_two_days() {
            val startDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-01"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val endDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-02"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDate.getDateOnly())
                    .startSession(startDate.getSession())
                    .endDate(endDate.getDateOnly())
                    .endSession(endDate.getSession())
                    .contractId(CONTRACT_ID)
                    .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val timeoffEntries = breakdown.getLeft();
            val actualEntry = timeoffEntries.get(0);
            assertEquals(LocalDate.parse("2025-10-01"), actualEntry.date());
            assertEquals(0.5, actualEntry.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry.type());
            assertEquals(TimeOffSession.AFTERNOON, actualEntry.session());

            val actualEntry2 = timeoffEntries.get(1);
            assertEquals(LocalDate.parse("2025-10-02"), actualEntry2.date());
            assertEquals(0.5, actualEntry2.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry2.type());
            assertEquals(TimeOffSession.MORNING, actualEntry2.session());
        }

        @Test
        void should_add_correct_time_off_session_values_one_and_half_day() {
            val startDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-01"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val endDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-02"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDate.getDateOnly())
                    .startSession(startDate.getSession())
                    .endDate(endDate.getDateOnly())
                    .endSession(endDate.getSession())
                    .contractId(CONTRACT_ID)
                    .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val timeoffEntries = breakdown.getLeft();
            val actualEntry = timeoffEntries.get(0);
            assertEquals(LocalDate.parse("2025-10-01"), actualEntry.date());
            assertEquals(1.0, actualEntry.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry.type());
            assertEquals(TimeOffSession.FULL_DAY, actualEntry.session());

            val actualEntry2 = timeoffEntries.get(1);
            assertEquals(LocalDate.parse("2025-10-02"), actualEntry2.date());
            assertEquals(0.5, actualEntry2.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry2.type());
            assertEquals(TimeOffSession.MORNING, actualEntry2.session());
        }

        @Test
        void should_add_correct_time_off_session_values_one_and_half_day_start_session_is_afternoon() {
            val startDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-01"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val endDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-02"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val workshift = getWorkshift();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDate.getDateOnly())
                    .startSession(startDate.getSession())
                    .endDate(endDate.getDateOnly())
                    .endSession(endDate.getSession())
                    .contractId(CONTRACT_ID)
                    .build();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val timeoffEntries = breakdown.getLeft();
            val actualEntry = timeoffEntries.get(0);
            assertEquals(LocalDate.parse("2025-10-01"), actualEntry.date());
            assertEquals(0.5, actualEntry.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry.type());
            assertEquals(TimeOffSession.AFTERNOON, actualEntry.session());

            val actualEntry2 = timeoffEntries.get(1);
            assertEquals(LocalDate.parse("2025-10-02"), actualEntry2.date());
            assertEquals(1.0, actualEntry2.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry2.type());
            assertEquals(TimeOffSession.FULL_DAY, actualEntry2.session());
        }

        @Test
        void should_add_correct_time_off_session_values_two_full_day() {
            val startDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-01"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val endDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-02"))
                    .session(TimeOffSession.AFTERNOON)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDate.getDateOnly())
                    .startSession(startDate.getSession())
                    .endDate(endDate.getDateOnly())
                    .endSession(endDate.getSession())
                    .contractId(CONTRACT_ID)
                    .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val timeoffEntries = breakdown.getLeft();
            val actualEntry = timeoffEntries.get(0);
            assertEquals(LocalDate.parse("2025-10-01"), actualEntry.date());
            assertEquals(1.0, actualEntry.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry.type());
            assertEquals(TimeOffSession.FULL_DAY, actualEntry.session());

            val actualEntry2 = timeoffEntries.get(1);
            assertEquals(LocalDate.parse("2025-10-02"), actualEntry2.date());
            assertEquals(1.0, actualEntry2.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry2.type());
            assertEquals(TimeOffSession.FULL_DAY, actualEntry2.session());
        }

        @Test
        void should_add_correct_time_off_session_values_on_holidays_rest_days_and_timeoff_days() {
            val startDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-14"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val endDate = TimeOffDateInput.newBuilder()
                    .dateOnly(LocalDate.parse("2025-10-20"))
                    .session(TimeOffSession.MORNING)
                    .build();
            val timeoffDBO = TimeoffDBO.builder()
                    .startDate(startDate.getDateOnly())
                    .startSession(startDate.getSession())
                    .endDate(endDate.getDateOnly())
                    .endSession(endDate.getSession())
                    .contractId(CONTRACT_ID)
                    .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.generateTimeOffEntries(timeoffDBO, workshift);

            val timeoffEntries = breakdown.getLeft();
            val actualEntry = timeoffEntries.get(0);
            assertEquals(LocalDate.parse("2025-10-14"), actualEntry.date());
            assertEquals(1.0, actualEntry.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry.type());
            assertEquals(TimeOffSession.FULL_DAY, actualEntry.session());

            val actualEntry2 = timeoffEntries.get(1);
            assertEquals(LocalDate.parse("2025-10-15"), actualEntry2.date());
            assertEquals(0.0, actualEntry2.value());
            assertEquals(TimeOffEntryType.HOLIDAY, actualEntry2.type());
            assertEquals(TimeOffSession.FULL_DAY, actualEntry2.session());

            val actualEntry3 = timeoffEntries.get(4);
            assertEquals(LocalDate.parse("2025-10-18"), actualEntry3.date());
            assertEquals(0.0, actualEntry3.value());
            assertEquals(TimeOffEntryType.RESTDAY, actualEntry3.type());
            assertEquals(TimeOffSession.FULL_DAY, actualEntry3.session());

            val actualEntry4 = timeoffEntries.get(5);
            assertEquals(LocalDate.parse("2025-10-19"), actualEntry4.date());
            assertEquals(0.0, actualEntry4.value());
            assertEquals(TimeOffEntryType.RESTDAY, actualEntry4.type());
            assertEquals(TimeOffSession.FULL_DAY, actualEntry4.session());

            val actualEntry5 = timeoffEntries.get(6);
            assertEquals(LocalDate.parse("2025-10-20"), actualEntry5.date());
            assertEquals(0.5, actualEntry5.value());
            assertEquals(TimeOffEntryType.TIMEOFF, actualEntry5.type());
            assertEquals(TimeOffSession.MORNING, actualEntry5.session());
        }

        private void stubHolidayServiceAdaptor() {
            val holidays = List.of(
                LegalEntityHoliday.Holiday.newBuilder().setYear(2025).setMonth(10).setDate(6).build(),
                LegalEntityHoliday.Holiday.newBuilder().setYear(2025).setMonth(10).setDate(15).build()
            );
            when(holidayServiceAdapter.getHolidays(anySet(), anyInt(), ArgumentMatchers.<Integer>any())).thenReturn(holidays);
        }

    }

    @Nested
    class getTimeoffEntriesForDateRange {

        private static final Long CONTRACT_ID = 1L;

        static Stream<Arguments> normalWorkWeekLeavesTestCases() {
            return Stream.of(
                    Arguments.of("2025-10-13", "MORNING", "2025-10-13", "MORNING", 0.5),
                    Arguments.of("2025-10-13", "MORNING", "2025-10-13", "AFTERNOON", 1.0),
                    Arguments.of("2025-10-13", "AFTERNOON", "2025-10-13", "MORNING", 0.5),
                    Arguments.of("2025-10-13", "MORNING", "2025-10-14", "MORNING", 1.5),
                    Arguments.of("2025-10-13", "MORNING", "2025-10-14", "AFTERNOON", 2.0),
                    Arguments.of("2025-10-13", "MORNING", "2025-10-17", "AFTERNOON", 4.0),
                    Arguments.of("2025-10-13", "MORNING", "2025-10-21", "AFTERNOON", 6.0)
            );
        }


        @ParameterizedTest
        @MethodSource("normalWorkWeekLeavesTestCases")
        void should_give_proper_no_of_days_values(
                String parameterStartDate,
                TimeOffSession parameterStartSession,
                String parameterEndDate,
                TimeOffSession parameterEndSession,
                double expectedNoOfDays
        ) {
            val startDate = TimeOffDate.newBuilder()
                    .dateOnly(LocalDate.parse(parameterStartDate))
                    .session(parameterStartSession)
                    .build();
            val endDate = TimeOffDate.newBuilder()
                    .dateOnly(LocalDate.parse(parameterEndDate))
                    .session(parameterEndSession)
                    .build();
            val workshift = getWorkshift();

            stubHolidayServiceAdaptor();

            val breakdown = timeoffBreakdownService.calculateTimeoffEntriesAndApplyingDays(CONTRACT_ID, startDate, endDate, workshift);

            val noOfDays = breakdown.getRight();
            assertEquals(expectedNoOfDays, noOfDays);
        }
    }

    @Nested
    class getTimeoffBreakdown {

        private static final Long CONTRACT_ID = 1L;
        private static final Long MEMBER_ID = 1L;

        @Test
        void should_return_timeoff_entries_and_no_of_days() {
            final var timeoffBreakdownInput = getTimeOffBreakdownInput("2025-10-01", TimeOffSession.MORNING,
                    "2025-10-04", TimeOffSession.AFTERNOON);

            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            stubAuthorization(dfe);
            stubValidations();
            stubWorkshiftAdaptor();
            stubHolidayServiceAdaptor();
            stubMapper(timeoffBreakdownInput);

            val expectedTimeoffEntries = List.of(
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-01"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.TIMEOFF)
                            .value(1.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-02"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.TIMEOFF)
                            .value(1.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-03"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.TIMEOFF)
                            .value(1.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-04"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.RESTDAY)
                            .value(0.0)
                            .build()
            );

            val expectedTimeoffBreakdown = List.of(TimeOffBreakdown.newBuilder()
                    .timeOffEntries(expectedTimeoffEntries)
                    .noOfDays(3.0)
                    .startDate(LocalDate.parse("2025-10-01"))
                    .endDate(LocalDate.parse("2025-10-04"))
                    .build());



            val breakdown = timeoffBreakdownService.getTimeoffBreakdown(dfe, timeoffBreakdownInput);

            assertEquals(expectedTimeoffBreakdown, breakdown);

        }

        @Test
        void should_return_timeoff_entries_and_no_of_days_with_rest_days() {
            final var timeoffBreakdownInput = getTimeOffBreakdownInput("2025-10-01", TimeOffSession.MORNING,
                    "2025-10-05", TimeOffSession.AFTERNOON);

            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            stubAuthorization(dfe);
            stubValidations();
            stubWorkshiftAdaptor();
            stubHolidayServiceAdaptor();

            val expectedTimeoffEntries = List.of(
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-01"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.TIMEOFF)
                            .value(1.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-02"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.TIMEOFF)
                            .value(1.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-03"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.TIMEOFF)
                            .value(1.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-04"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.RESTDAY)
                            .value(0.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-05"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.RESTDAY)
                            .value(0.0)
                            .build()
            );

            val expectedTimeoffBreakdown = List.of(TimeOffBreakdown.newBuilder()
                    .timeOffEntries(expectedTimeoffEntries)
                    .noOfDays(3.0)
                    .startDate(LocalDate.parse("2025-10-01"))
                    .endDate(LocalDate.parse("2025-10-05"))
                    .build());

            stubMapper(timeoffBreakdownInput);


            val breakdown = timeoffBreakdownService.getTimeoffBreakdown(dfe, timeoffBreakdownInput);

            assertEquals(expectedTimeoffBreakdown, breakdown);

        }

        @Test
        void should_return_timeoff_entries_and_no_of_days_with_rest_days_and_holidays() {
            final var timeoffBreakdownInput = getTimeOffBreakdownInput("2025-10-01", TimeOffSession.MORNING,
                    "2025-10-07", TimeOffSession.AFTERNOON);

            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            stubAuthorization(dfe);
            stubValidations();
            stubWorkshiftAdaptor();
            stubHolidayServiceAdaptor();
            stubMapper(timeoffBreakdownInput);

            val expectedTimeoffEntries = List.of(
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-01"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.TIMEOFF)
                            .value(1.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-02"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.TIMEOFF)
                            .value(1.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-03"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.TIMEOFF)
                            .value(1.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-04"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.RESTDAY)
                            .value(0.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-05"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.RESTDAY)
                            .value(0.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-06"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.HOLIDAY)
                            .value(0.0)
                            .build(),
                    TimeOffEntry.newBuilder()
                            .date(LocalDate.parse("2025-10-07"))
                            .session(TimeOffSession.FULL_DAY)
                            .type(TimeOffEntryType.TIMEOFF)
                            .value(1.0)
                            .build()
            );

            val expectedTimeoffBreakdown = List.of(TimeOffBreakdown.newBuilder()
                    .timeOffEntries(expectedTimeoffEntries)
                    .noOfDays(4.0)
                    .startDate(LocalDate.parse("2025-10-01"))
                    .endDate(LocalDate.parse("2025-10-07"))
                    .build());



            val breakdown = timeoffBreakdownService.getTimeoffBreakdown(dfe, timeoffBreakdownInput);

            assertEquals(expectedTimeoffBreakdown, breakdown);

        }


        private TimeOffBreakdownInput getTimeOffBreakdownInput(
                String startDateValue,
                TimeOffSession startSession,
                String endDateValue,
                TimeOffSession endSession
        ) {
            val startDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(startDateValue)).session(startSession).build();
            val endDate =
                    TimeOffDateInput.newBuilder().dateOnly(LocalDate.parse(endDateValue)).session(endSession).build();
            return TimeOffBreakdownInput.newBuilder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .contractId(CONTRACT_ID)
                    .build();
        }

        private void stubMapper(TimeOffBreakdownInput input) {
            when(timeOffInputMapper.map(input.getStartDate())).thenReturn(TimeOffDate.newBuilder()
                    .dateOnly(input.getStartDate().getDateOnly())
                    .session(input.getStartDate().getSession())
                    .build());
            when(timeOffInputMapper.map(input.getEndDate())).thenReturn(TimeOffDate.newBuilder()
                    .dateOnly(input.getEndDate().getDateOnly())
                    .session(input.getEndDate().getSession())
                    .build());
        }

        private void stubAuthorization(DgsDataFetchingEnvironment dfe) {
            val contract = ContractOuterClass.ContractBasicInfo.newBuilder()
                    .setContractId(getTimeoffBreakdown.CONTRACT_ID)
                    .setStarted(true)
                    .build();

            when(currentUser.getContext()).thenReturn(getMemberUserDetails());
            when(contractServiceAdapter.getBasicContractById(getTimeoffBreakdown.CONTRACT_ID)).thenReturn(contract);
            doNothing().when(authorizationService).authorize(dfe, contract);
        }

        private void stubValidations() {
            doReturn(true).when(timeoffValidator).throwIfContractIsNotValid(any());
            doReturn(true).when(timeoffValidator).throwIfTimeoffBreakdownInputIsNotValid(any(TimeOffBreakdownInput.class),
                    any(), any());
        }

        private void stubWorkshiftAdaptor() {
            WorkshiftDTO workshift = WorkshiftDTO.builder()
                    .startDate(DayOfWeek.MONDAY)
                    .endDate(DayOfWeek.FRIDAY).build();
            doReturn(workshift).when(workshiftServiceAdapter).getWorkshiftByContract(any());
        }

        private UserContext getMemberUserDetails() {
            return new UserContext(
                    1L,
                    "username",
                    "member",
                    Collections.emptySet(),
                    "signupChannel",
                    "firstName",
                    "lastName",
                    "auth",
                    new UserScopes(
                            getTimeoffBreakdown.MEMBER_ID,
                            null,
                            null,
                            null,
                            null,
                            null,
                            false
                    ),
                    "user",
                    List.of()
            );
        }

    }

    @Nested
    class getTimeOffEntriesGroupedByTimeOffId {

        @BeforeEach
        void setupMapper() {
            lenient().when(timeoffEntryMapper.map(any(TimeoffEntryDBO.class))).thenAnswer(invocation -> {
                TimeoffEntryDBO dbo = invocation.getArgument(0);
                return TimeOffEntry.newBuilder()
                        .date(dbo.date())
                        .value(dbo.value())
                        .session(dbo.session())
                        .type(dbo.type())
                        .build();
            });
        }

        @Test
        void shouldReturnGroupedTimeOffEntries() {
            // Given
            Set<Long> timeoffIds = Set.of(1L, 2L);

            List<TimeoffEntryDBO> timeoffEntryDBOs = List.of(
                    createTimeoffEntryDBO(101L, 1L, LocalDate.of(2024, 1, 15), 1.0, TimeOffSession.FULL_DAY, TimeOffEntryType.TIMEOFF),
                    createTimeoffEntryDBO(102L, 1L, LocalDate.of(2024, 1, 16), 0.0, TimeOffSession.FULL_DAY, TimeOffEntryType.RESTDAY),
                    createTimeoffEntryDBO(103L, 2L, LocalDate.of(2024, 1, 20), 0.5, TimeOffSession.MORNING, TimeOffEntryType.TIMEOFF)
            );

            when(timeoffEntryRepository.findAllByTimeoffIdIn(timeoffIds)).thenReturn(timeoffEntryDBOs);

            // When
            Map<Long, List<TimeOffEntry>> result = timeoffBreakdownService
                    .getTimeOffEntriesGroupedByTimeOffId(timeoffIds);

            // Then
            assertNotNull(result);
            assertEquals(2, result.size());

            // Verify timeoff ID 1 has 2 entries
            assertTrue(result.containsKey(1L));
            assertEquals(2, result.get(1L).size());

            // Verify timeoff ID 2 has 1 entry
            assertTrue(result.containsKey(2L));
            assertEquals(1, result.get(2L).size());

            // Verify the conversion is correct
            TimeOffEntry firstEntry = result.get(1L).get(0);
            assertEquals(LocalDate.of(2024, 1, 15), firstEntry.getDate());
            assertEquals(1.0, firstEntry.getValue());
            assertEquals(TimeOffSession.FULL_DAY, firstEntry.getSession());
            assertEquals(TimeOffEntryType.TIMEOFF, firstEntry.getType());

            verify(timeoffEntryRepository).findAllByTimeoffIdIn(timeoffIds);
        }

        @Test
        void shouldReturnEmptyMapWhenNoEntriesFound() {
            // Given
            Set<Long> timeoffIds = Set.of(1L, 2L);

            when(timeoffEntryRepository.findAllByTimeoffIdIn(timeoffIds)).thenReturn(List.of());

            // When
            Map<Long, List<TimeOffEntry>> result = timeoffBreakdownService
                    .getTimeOffEntriesGroupedByTimeOffId(timeoffIds);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(timeoffEntryRepository).findAllByTimeoffIdIn(timeoffIds);
        }

        @Test
        void shouldHandleEmptyTimeoffIdsSet() {
            // Given
            Set<Long> timeoffIds = Set.of();

            when(timeoffEntryRepository.findAllByTimeoffIdIn(timeoffIds)).thenReturn(List.of());

            // When
            Map<Long, List<TimeOffEntry>> result = timeoffBreakdownService
                    .getTimeOffEntriesGroupedByTimeOffId(timeoffIds);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
            verify(timeoffEntryRepository).findAllByTimeoffIdIn(timeoffIds);
        }

        @Test
        void shouldGroupMultipleEntriesCorrectly() {
            // Given
            Set<Long> timeoffIds = Set.of(1L);

            List<TimeoffEntryDBO> timeoffEntryDBOs = List.of(
                    createTimeoffEntryDBO(101L, 1L, LocalDate.of(2024, 1, 15), 0.5, TimeOffSession.AFTERNOON, TimeOffEntryType.TIMEOFF),
                    createTimeoffEntryDBO(102L, 1L, LocalDate.of(2024, 1, 16), 1.0, TimeOffSession.FULL_DAY, TimeOffEntryType.TIMEOFF),
                    createTimeoffEntryDBO(103L, 1L, LocalDate.of(2024, 1, 17), 0.0, TimeOffSession.FULL_DAY, TimeOffEntryType.HOLIDAY)
            );

            when(timeoffEntryRepository.findAllByTimeoffIdIn(timeoffIds)).thenReturn(timeoffEntryDBOs);

            // When
            Map<Long, List<TimeOffEntry>> result = timeoffBreakdownService
                    .getTimeOffEntriesGroupedByTimeOffId(timeoffIds);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertTrue(result.containsKey(1L));
            assertEquals(3, result.get(1L).size());

            List<TimeOffEntry> entries = result.get(1L);

            // Verify all entries are properly converted
            assertEquals(LocalDate.of(2024, 1, 15), entries.get(0).getDate());
            assertEquals(0.5, entries.get(0).getValue());
            assertEquals(TimeOffSession.AFTERNOON, entries.get(0).getSession());
            assertEquals(TimeOffEntryType.TIMEOFF, entries.get(0).getType());

            assertEquals(LocalDate.of(2024, 1, 16), entries.get(1).getDate());
            assertEquals(1.0, entries.get(1).getValue());
            assertEquals(TimeOffSession.FULL_DAY, entries.get(1).getSession());
            assertEquals(TimeOffEntryType.TIMEOFF, entries.get(1).getType());

            assertEquals(LocalDate.of(2024, 1, 17), entries.get(2).getDate());
            assertEquals(0.0, entries.get(2).getValue());
            assertEquals(TimeOffSession.FULL_DAY, entries.get(2).getSession());
            assertEquals(TimeOffEntryType.HOLIDAY, entries.get(2).getType());

            verify(timeoffEntryRepository).findAllByTimeoffIdIn(timeoffIds);
        }

        private TimeoffEntryDBO createTimeoffEntryDBO(Long id, Long timeoffId, LocalDate date,
                                                      Double value, TimeOffSession session, TimeOffEntryType type) {
            return TimeoffEntryDBO.builder()
                    .id(id)
                    .timeoffId(timeoffId)
                    .date(date)
                    .value(value)
                    .session(session)
                    .type(type)
                    .build();
        }
    }

}
