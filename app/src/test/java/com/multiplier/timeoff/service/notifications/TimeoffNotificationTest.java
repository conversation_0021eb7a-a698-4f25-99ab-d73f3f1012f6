package com.multiplier.timeoff.service.notifications;

import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.core.schema.common.Common;
import com.multiplier.member.schema.Member;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.MemberServiceAdapter;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.multiplier.timeoff.service.exception.NotificationException;
import com.multiplier.timeoff.types.TimeOffSession;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Map;
import java.util.Set;
import java.time.LocalDate;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class TimeoffNotificationTest {

    @Mock
    private ContractServiceAdapter contractServiceAdapter;
    @Mock
    private CompanyServiceAdapter companyServiceAdapter;
    @Mock
    private MemberServiceAdapter memberServiceAdapter;
    @Mock
    private TimeoffNotificationHelper timeoffNotificationHelper;

    @InjectMocks
    private TimeoffNotification underTest;

    @Nested
    class GetMemberEmail {

        private final ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder().setId(1).build();

        @Test
        void should_return_email_get_by_contract_id() {
            // given
            doReturn(Map.of(contract.getId(),"workEmail")).when(contractServiceAdapter).getContractMemberEmailsByContractIds(any());

            // then
            assertEquals("workEmail", underTest.getMemberEmailByContractId(contract.getId()));
        }

        @Test
        void should_throw_exception_given_no_email_found_by_contract_id() {
            // given
            Map<String, String> contractEmails = Map.of(); // Empty map
            doReturn(contractEmails).when(contractServiceAdapter).getContractMemberEmailsByContractIds(any());
            // when
            Long contractId = contract.getId();
            // then
            assertThrows(NotificationException.class, () -> underTest.getMemberEmailByContractId(contractId));
        }
    }

    @ParameterizedTest
    @CsvSource({
            "line1,line2,street,city,state,province,zip,postal,line1 line2 street city state province zip postal",
            "line1,line2,street,city,state,province,' ',postal,line1 line2 street city state province postal",
            "line1,line2,street,'  ',state,province,zip,postal,line1 line2 street state province zip postal",
            "line1,'   ',street,city,state,province,zip,postal,line1 street city state province zip postal"
    })
    void testGetCompanyAddress(final String line1, final String line2, final String street, final String city,
            final String state, final String province, final String zipCode, final String postalCode, final String resultAddress) {
        // given
        CompanyOuterClass.Company company = CompanyOuterClass.Company.newBuilder()
                .setPrimaryEntity(CompanyOuterClass.LegalEntity.newBuilder()
                        .setAddress(CompanyOuterClass.Address.newBuilder()
                                .setLine1(line1)
                                .setLine2(line2)
                                .setStreet(street)
                                .setCity(city)
                                .setState(state)
                                .setProvince(province)
                                .setZipcode(zipCode)
                                .setPostalCode(postalCode)
                                .build())
                        .build())
                .build();

        // then
        assertEquals(resultAddress, underTest.getCompanyAddress(company));
    }

    @Nested
    class SendTimeoffCreatedEmailToApprover {
        private final TimeoffDBO timeoff = TimeoffDBO.builder()
                .id(1L)
                .contractId(1L)
                .startDate(LocalDate.now())
                .endDate(LocalDate.now().plusDays(1))
                .startSession(TimeOffSession.MORNING)
                .endSession(TimeOffSession.AFTERNOON)
                .description("Test timeoff")
                .build();

        private final CompanyOuterClass.CompanyUser approver = CompanyOuterClass.CompanyUser.newBuilder()
                .setId(1L)
                .addEmails(Common.EmailAddress.newBuilder()
                        .setEmail("<EMAIL>")
                        .setType("primary")
                        .build())
                .build();

        private final ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                .setId(1L)
                .setMemberId(1L)
                .build();

        private final Member member = Member.newBuilder()
                .setId(1L)
                .setFirstName("John")
                .setLastName("Doe")
                .build();

        @Test
        void should_send_timeoff_created_email_to_approver() {
            // given
            doReturn(contract).when(contractServiceAdapter).getContractByIdAnyStatus(timeoff.contractId());
            doReturn(member).when(memberServiceAdapter).getMember(contract.getMemberId());

            // when
            underTest.sendTimeoffCreatedEmailToApprover(timeoff, approver);

            // then
            verify(timeoffNotificationHelper).sendTimeoffCreatedEmailToCompanyUsers(
                    "<EMAIL>",
                    "John",
                    "Doe",
                    timeoff
            );
        }
    }

    @Test
    void should_send_timeoff_approved_email_to_member() {
        // given
        Long contractId = 1L;
        Long companyId = 101L;
        TimeoffDBO timeoff = mock(TimeoffDBO.class);
        when(timeoff.contractId()).thenReturn(contractId);

        ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                .setId(contractId)
                .setCompanyId(companyId)
                .build();

        CompanyOuterClass.Company company = CompanyOuterClass.Company.newBuilder()
                .setDisplayName("Multiplier Inc")
                .setPrimaryEntity(CompanyOuterClass.LegalEntity.newBuilder()
                        .setAddress(CompanyOuterClass.Address.newBuilder()
                                .setLine1("Line 1")
                                .setCity("City")
                                .build())
                        .build())
                .build();

        doReturn(contract).when(contractServiceAdapter).getContractByIdAnyStatus(contractId);
        doReturn(company).when(companyServiceAdapter).getCompany(companyId);
        doReturn(Map.of(contractId, "<EMAIL>")).when(contractServiceAdapter).getContractMemberEmailsByContractIds(Set.of(contractId));

        // when
        underTest.sendTimeoffApprovedEmailToMember(timeoff);

        // then
        verify(timeoffNotificationHelper).sendTimeoffApproveEmailToMember(
                "<EMAIL>", "Multiplier Inc", "Line 1 City", timeoff
        );
    }

    @Test
    void should_send_timeoff_rejected_email_to_member() {
        // given
        Long contractId = 1L;
        Long companyId = 101L;
        TimeoffDBO timeoff = mock(TimeoffDBO.class);
        when(timeoff.contractId()).thenReturn(contractId);

        ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                .setId(contractId)
                .setCompanyId(companyId)
                .build();

        CompanyOuterClass.Company company = CompanyOuterClass.Company.newBuilder()
                .setDisplayName("Multiplier Inc")
                .setPrimaryEntity(CompanyOuterClass.LegalEntity.newBuilder()
                        .setAddress(CompanyOuterClass.Address.newBuilder()
                                .setLine1("Line 1")
                                .setCity("City")
                                .build())
                        .build())
                .build();

        doReturn(contract).when(contractServiceAdapter).getContractByIdAnyStatus(contractId);
        doReturn(company).when(companyServiceAdapter).getCompany(companyId);
        doReturn(Map.of(contractId, "<EMAIL>")).when(contractServiceAdapter).getContractMemberEmailsByContractIds(Set.of(contractId));

        // when
        underTest.sendTimeoffRejectedEmailToMember(timeoff);

        // then
        verify(timeoffNotificationHelper).sendTimeoffRejectedEmailToMember(
                "<EMAIL>", "Multiplier Inc", "Line 1 City", timeoff
        );
    }


}