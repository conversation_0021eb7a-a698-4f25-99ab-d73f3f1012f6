package com.multiplier.timeoff.service;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.member.schema.Member;
import com.multiplier.timeoff.adapters.*;
import com.multiplier.timeoff.report.domain.ReportCategory;
import com.multiplier.timeoff.report.generator.ReportGeneratorService;
import com.multiplier.timeoff.repository.TimeoffTypeRepository;
import com.multiplier.timeoff.repository.model.TimeoffTypeDBO;
import com.multiplier.timeoff.types.*;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class TimeOffReportGenerationHelperTest {

    @Mock
    private ReportGeneratorService reportGeneratorService;

    @Mock
    private MemberServiceAdapter memberServiceAdapter;

    @Mock
    private CompanyServiceAdapter companyServiceAdapter;

    @Mock
    private ContractServiceAdapter contractServiceAdapter;

    @Mock
    private TimeoffTypeRepository timeoffTypeRepository;

    @Mock
    private ApprovalServiceAdapter approvalServiceAdapter;

    @InjectMocks
    private TimeOffReportGenerationHelper timeOffReportGenerationHelper;

    @Test
    void should_generate_report_with_valid_time_off_data() {
        // Given
        Long timeOffId = 1L;
        Long contractId = 100L;
        Long memberId = 200L;
        Long typeId = 300L;
        Long approvedByUserId = 400L;

        TimeOff timeOff = createMockTimeOff(timeOffId, contractId, typeId);
        List<TimeOff> timeOffs = Collections.singletonList(timeOff);

        ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                .setId(contractId)
                .setMemberId(memberId)
                .build();

        Member member = Member.newBuilder()
                .setId(memberId)
                .setFullLegalName("John Doe")
                .build();

        TimeoffTypeDBO timeoffType = TimeoffTypeDBO.builder()
                .id(typeId)
                .description("Annual Leave")
                .build();

        when(contractServiceAdapter.getContractsByIdsAnyStatus(any()))
                .thenReturn(Collections.singletonList(contract));
        when(memberServiceAdapter.getMembers(any()))
                .thenReturn(Collections.singletonList(member));
        when(timeoffTypeRepository.findById(typeId))
                .thenReturn(Optional.of(timeoffType));
        when(approvalServiceAdapter.getItemApprovedBy(timeOffId))
                .thenReturn(com.multiplier.core.schema.platform.Approval.IdResponse.newBuilder()
                        .setId(approvedByUserId)
                        .build());
        when(reportGeneratorService.generateReport(any(), any()))
                .thenReturn(DocumentReadable.newBuilder().build());

        // When
        DocumentReadable result = timeOffReportGenerationHelper.generateReport(timeOffs);

        // Then
        verify(contractServiceAdapter).getContractsByIdsAnyStatus(Collections.singleton(contractId));
        verify(memberServiceAdapter).getMembers(Collections.singletonList(memberId));
        verify(timeoffTypeRepository).findById(typeId);
        verify(approvalServiceAdapter).getItemApprovedBy(timeOffId);
        verify(companyServiceAdapter).getCompanyUser(any());
        verify(reportGeneratorService).generateReport(eq(ReportCategory.TIMEOFF_REPORT), any());
        assertNotNull(result);
    }

    @Test
    void should_handle_null_approved_by_user() {
        // Given
        Long timeOffId = 1L;
        Long contractId = 100L;
        Long memberId = 200L;
        Long typeId = 300L;

        TimeOff timeOff = createMockTimeOff(timeOffId, contractId, typeId);
        List<TimeOff> timeOffs = Collections.singletonList(timeOff);

        ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                .setId(contractId)
                .setMemberId(memberId)
                .build();

        Member member = Member.newBuilder()
                .setId(memberId)
                .setFullLegalName("John Doe")
                .build();

        TimeoffTypeDBO timeoffType = TimeoffTypeDBO.builder()
                .id(typeId)
                .description("Annual Leave")
                .build();

        when(contractServiceAdapter.getContractsByIdsAnyStatus(any()))
                .thenReturn(Collections.singletonList(contract));
        when(memberServiceAdapter.getMembers(any()))
                .thenReturn(Collections.singletonList(member));
        when(timeoffTypeRepository.findById(typeId))
                .thenReturn(Optional.of(timeoffType));
        when(approvalServiceAdapter.getItemApprovedBy(timeOffId))
                .thenReturn(com.multiplier.core.schema.platform.Approval.IdResponse.newBuilder()
                        .setId(-1L)
                        .build());
        when(reportGeneratorService.generateReport(any(), any()))
                .thenReturn(DocumentReadable.newBuilder().build());

        // When
        DocumentReadable result = timeOffReportGenerationHelper.generateReport(timeOffs);

        // Then
        verify(contractServiceAdapter).getContractsByIdsAnyStatus(Collections.singleton(contractId));
        verify(memberServiceAdapter).getMembers(Collections.singletonList(memberId));
        verify(timeoffTypeRepository).findById(typeId);
        verify(approvalServiceAdapter).getItemApprovedBy(timeOffId);
        verify(companyServiceAdapter, never()).getCompanyUser(any());
        verify(reportGeneratorService).generateReport(eq(ReportCategory.TIMEOFF_REPORT), any());
        assertNotNull(result);
    }

    @Test
    void should_handle_missing_time_off_type() {
        // Given
        Long timeOffId = 1L;
        Long contractId = 100L;
        Long memberId = 200L;
        Long typeId = 300L;

        TimeOff timeOff = createMockTimeOff(timeOffId, contractId, typeId);
        List<TimeOff> timeOffs = Collections.singletonList(timeOff);

        ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                .setId(contractId)
                .setMemberId(memberId)
                .build();

        Member member = Member.newBuilder()
                .setId(memberId)
                .setFullLegalName("John Doe")
                .build();

        when(contractServiceAdapter.getContractsByIdsAnyStatus(any()))
                .thenReturn(Collections.singletonList(contract));
        when(memberServiceAdapter.getMembers(any()))
                .thenReturn(Collections.singletonList(member));
        when(timeoffTypeRepository.findById(typeId))
                .thenReturn(Optional.empty());
        when(approvalServiceAdapter.getItemApprovedBy(timeOffId))
                .thenReturn(com.multiplier.core.schema.platform.Approval.IdResponse.newBuilder()
                        .setId(-1L)
                        .build());
        when(reportGeneratorService.generateReport(any(), any()))
                .thenReturn(DocumentReadable.newBuilder().build());

        // When
        DocumentReadable result = timeOffReportGenerationHelper.generateReport(timeOffs);

        // Then
        verify(contractServiceAdapter).getContractsByIdsAnyStatus(Collections.singleton(contractId));
        verify(memberServiceAdapter).getMembers(Collections.singletonList(memberId));
        verify(timeoffTypeRepository).findById(typeId);
        verify(approvalServiceAdapter).getItemApprovedBy(timeOffId);
        verify(companyServiceAdapter, never()).getCompanyUser(any());
        verify(reportGeneratorService).generateReport(eq(ReportCategory.TIMEOFF_REPORT), any());
        assertNotNull(result);
    }

    private TimeOff createMockTimeOff(Long id, Long contractId, Long typeId) {
        Contract contract = new Contract();
        contract.setId(contractId);

        TimeOffType timeOffType = new TimeOffType();
        timeOffType.setTypeId(typeId);

        TimeOffDate startDate = new TimeOffDate();
        startDate.setDateOnly(LocalDate.of(2024, 1, 1));
        startDate.setSession(TimeOffSession.MORNING);

        TimeOffDate endDate = new TimeOffDate();
        endDate.setDateOnly(LocalDate.of(2024, 1, 2));
        endDate.setSession(TimeOffSession.AFTERNOON);

        TimeOff timeOff = new TimeOff();
        timeOff.setId(id);
        timeOff.setContract(contract);
        timeOff.setType(timeOffType);
        timeOff.setStartDate(startDate);
        timeOff.setEndDate(endDate);
        timeOff.setStatus(TimeOffStatus.APPROVED);
        timeOff.setNoOfDays(2.0);
        return timeOff;
    }

} 