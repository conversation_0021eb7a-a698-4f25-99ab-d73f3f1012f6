package com.multiplier.timeoff.service.builder;


import com.multiplier.timeoff.types.DateRange;
import com.multiplier.timeoff.types.TimeOffFilter;
import com.multiplier.timeoff.types.TimeOffStatus;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;


class TimeoffFiltersTest {
    @Test
    void buildFromGraphFilter() {
        TimeOffFilter graphFilter = TimeOffFilter.newBuilder().ids(List.of(1L)).contractIds(List.of(1L)).timeOffStatus(List.of(TimeOffStatus.APPROVED)).build();

        var result = TimeoffFilters.build(graphFilter);

        assertThat(result.ids()).containsExactly(1L);
        assertThat(result.contractIds()).containsExactly(1L);
        assertThat(result.statuses()).containsExactly(TimeOffStatus.APPROVED);
    }

    @Test
    void returnsEmptyFilterWhenGraphFilterIsNull() {
        var result = TimeoffFilters.build(null);

        assertThat(result).isNotNull();
        assertThat(result.ids()).isNull();
        assertThat(result.contractIds()).isNull();
        assertThat(result.statuses()).isNull();
        assertThat(result.startDateFrom()).isNull();
        assertThat(result.startDateUpto()).isNull();
    }

    @Test
    void keepsFieldNullWhenItIsNullInGraphFilter() {
        TimeOffFilter graphFilter = TimeOffFilter.newBuilder().build();
        var result = TimeoffFilters.build(graphFilter);

        assertThat(result).isNotNull();
        assertThat(result.ids()).isNull();
        assertThat(result.contractIds()).isNull();
        assertThat(result.statuses()).isNull();
        assertThat(result.startDateFrom()).isNull();
        assertThat(result.startDateUpto()).isNull();
    }

    @Test
    void keepsFromDateFieldNullWhenItIsNullInGraphFilter() {
        TimeOffFilter graphFilter = TimeOffFilter.newBuilder()
                .startDateRange(DateRange.newBuilder().build())
                .build();
        var result = TimeoffFilters.build(graphFilter);

        assertThat(result.startDateFrom()).isNull();
        assertThat(result.startDateUpto()).isNull();
    }

    @Test
    void populatesFromDateFieldsFromGraphFilter() {
        final var now = LocalDateTime.now();
        TimeOffFilter graphFilter = TimeOffFilter.newBuilder()
                .startDateRange(DateRange.newBuilder()
                        .startDate(now)
                        .endDate(now)
                        .build())
                .build();
        var result = TimeoffFilters.build(graphFilter);

        assertThat(result.startDateFrom()).isEqualTo(now.toLocalDate());
        assertThat(result.startDateUpto()).isEqualTo(now.toLocalDate());
    }
}
