package com.multiplier.timeoff.service;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.member.schema.Member;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.adapters.MemberServiceAdapter;
import com.multiplier.timeoff.report.domain.ReportCategory;
import com.multiplier.timeoff.report.generator.ReportGeneratorService;
import com.multiplier.timeoff.repository.TimeoffRepository;
import com.multiplier.timeoff.service.builder.TimoffSpecificationBuilder;
import com.multiplier.timeoff.types.*;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class TimeOffBalanceReportGenerationHelperTest {//NOSONAR : public modifier there to make code more readable

    @Mock
    ReportGeneratorService reportGeneratorService;

    @Mock
    MemberServiceAdapter memberServiceAdapter;

    @Mock
    ContractServiceAdapter contractServiceAdapter;

    @Mock
    TimeoffRepository timeoffRepository;

    @Mock
    TimoffSpecificationBuilder timeoffSpecBuilder;

    @InjectMocks
    TimeOffBalanceReportGenerationHelper timeOffBalanceReportGenerationHelper;


    @Test
    void when_member_time_off_balance_report_request_success_test() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<TimeOffType> timeOffTypeList = new ArrayList<>();

        /* key = 'annual|type = 'annual'|typeId = 1|label = 'annual'|desc = 'Annual Leave'|contarct id = 100|periodStart = '2022-01-01'|periodEnd = '2022-12-31|entitledCount = 14|takenCount = 2|pendingCount = 3|remaining = 9 */
        timeOffTypeList.add(getMockTimeOffType("annual", "annual", 1L, "annual", "Annual Leave", 100L, LocalDateTime.parse("2022-01-01 00:00:00", formatter), LocalDateTime.parse("2022-12-31 23:59:59", formatter), 14.0, 2.0, 3.0, 9.0));
        /* key = 'sick'|type = 'sick'|typeId = 2|label = 'sick'|desc = 'Sick Leave'|contarct id = 100|periodStart = '2022-01-01'|periodEnd = '2022-12-31|entitledCount = 7|takenCount = 1|pendingCount = 1|remaining = 5 */
        timeOffTypeList.add(getMockTimeOffType("sick", "sick", 2L, "sick", "Sick Leave", 100L, LocalDateTime.parse("2022-01-01 00:00:00", formatter), LocalDateTime.parse("2022-12-31 23:59:59", formatter), 7.0, 1.0, 1.0, 5.0));
        /* key = 'sick'|type = 'sick'|typeId = 2|label = 'sick'|desc = 'Sick Leave'|contarct id = 100|periodStart = '2022-01-01'|periodEnd = '2022-12-31|entitledCount = 7|takenCount = 1|pendingCount = 1|remaining = 5 */
        timeOffTypeList.add(getMockTimeOffType("sick", "sick", 2L, "sick", "Sick Leave", 101L, LocalDateTime.parse("2022-01-01 00:00:00", formatter), LocalDateTime.parse("2022-12-31 23:59:59", formatter), 7.0, 0.0, 1.0, 6.0));

        Member testMember1 = Member.newBuilder().setId(1L).setFullLegalName("Hakeem Mohammed").build();
        Member testMember2 = Member.newBuilder().setId(2L).setFullLegalName("Jason Roy").build();
        when(contractServiceAdapter.getContractByIdAnyStatus(100L))
            .thenReturn(ContractOuterClass.Contract.newBuilder()
                .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                .setCountry(CountryCode.ABW.name())
                .setMemberId(testMember1.getId())
                .build());

        when(contractServiceAdapter.getContractByIdAnyStatus(101L))
            .thenReturn(ContractOuterClass.Contract.newBuilder()
                .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                .setCountry(CountryCode.SLE.name())
                .setMemberId(testMember2.getId())
                .build());

        when(memberServiceAdapter.getMember(testMember1.getId()))
            .thenReturn(testMember1);
        when(memberServiceAdapter.getMember(testMember2.getId()))
            .thenReturn(testMember2);
        timeOffBalanceReportGenerationHelper.generateReport(timeOffTypeList);

        verify(reportGeneratorService).generateReport(Mockito.eq(ReportCategory.TIMEOFF_BALANCE_REPORT), Mockito.anyList());

    }

    @Test
    void member_time_off_balance_report_request_success_when_data_contain_nulls_test() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<TimeOffType> timeOffTypeList = new ArrayList<>();

        /* key = 'annual|type = 'annual'|typeId = 1|label = 'annual'|desc = 'Annual Leave'|contarct id = 100|periodStart = '2022-01-01'|periodEnd = '2022-12-31|entitledCount = null|takenCount = null|pendingCount = null|remaining = null */
        timeOffTypeList.add(getMockTimeOffType("annual", "annual", 1L, "annual", "Annual Leave", 100L, LocalDateTime.parse("2022-01-01 00:00:00", formatter), LocalDateTime.parse("2022-12-31 23:59:59", formatter), null, null, null, null));

        Member testMember = Member.newBuilder().setId(1L).setFullLegalName("Hakeem Mohammed").build();
        when(contractServiceAdapter.getContractByIdAnyStatus(100L))
                .thenReturn(ContractOuterClass.Contract.newBuilder()
                        .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                        .setCountry("ABW")
                        .setMemberId(testMember.getId())
                        .build());

        when(memberServiceAdapter.getMember(testMember.getId()))
                .thenReturn(testMember);

        DocumentReadable documentReadable = timeOffBalanceReportGenerationHelper.generateReport(timeOffTypeList);

        verify(reportGeneratorService).generateReport(Mockito.eq(ReportCategory.TIMEOFF_BALANCE_REPORT), Mockito.anyList());
    }

    private TimeOffType getMockTimeOffType(String key, String type, Long typeId, String label, String desc, Long contractId, LocalDateTime periodStart, LocalDateTime periodEnd, Double entitledCount, Double takenCount, Double pendingCount, Double remaining) {
        return new TimeOffType.Builder().key(key)
            .definition(
                new TimeOffTypeDefinition.Builder()
                    .type(type)
                    .typeId(typeId)
                    .label(label)
                    .description(desc)
                    .build())
            .contract(
                Contract.newBuilder()
                    .id(contractId)
                    .build()
            )
            .periodStart(periodStart)
            .periodEnd(periodEnd)
            .entitled(entitledCount)
            .taken(takenCount)
            .pending(pendingCount)
            .remaining(remaining)
            .build();
    }

}
