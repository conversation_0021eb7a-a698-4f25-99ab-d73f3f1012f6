package com.multiplier.timeoff.service.bulk;


import com.google.type.Date;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.growthbook.sdk.model.GBFeatureResult;
import com.multiplier.growthbook.sdk.model.GBFeatureSource;
import com.multiplier.timeoff.core.common.dto.BulkTimeOffDataHolder;
import com.multiplier.timeoff.core.common.dto.BulkTimeOffRequest;
import com.multiplier.timeoff.featureflag.FeatureFlagService;
import com.multiplier.timeoff.processor.TimeoffValidator;
import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.types.TimeOffStatus;
import lombok.val;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class BulkTimeOffValidationTest {
    @Mock
    private FeatureFlagService featureFlagService;
    @Mock
    private TimeoffValidator timeoffValidator;
    @InjectMocks
    private BulkTimeOffValidator bulkTimeOffValidator;

    @Test
    void should_send_employee_id_related_error_found_error_when_contract_not_found() {
        BulkTimeOffRequest.Input input = BulkTimeOffRequest.Input.builder().externalTimeOffId("001").contractId(100L).build();
        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .idToContractMap(Map.of())
                .build();
        val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
        assertNotNull(errors);
        assertEquals(1, errors.size());
        assertEquals("Employee ID is invalid. Either the employee ID is not present or the employee is not activated", errors.get(0));
    }

    @Test
    void should_send_employee_id_related_error_when_contract_not_started() {
        BulkTimeOffRequest.Input input = BulkTimeOffRequest.Input.builder().externalTimeOffId("001").contractId(1L).build();
        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .idToContractMap(
                        Map.of(1L, ContractOuterClass.Contract.newBuilder()
                                            .setId(1L)
                                            .setStarted(false)
                                            .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                                            .build()))
                .build();
        val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
        assertNotNull(errors);
        assertEquals(1, errors.size());
        assertEquals("Employee ID is invalid. Either the employee ID is not present or the employee is not activated", errors.get(0));
    }

    @Test
    void should_send_employee_id_related_error_when_contract_belongs_to_another_company() {
        BulkTimeOffRequest.Input input = BulkTimeOffRequest.Input.builder().externalTimeOffId("001").contractId(1L).build();
        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(200L)
                .idToContractMap(
                        Map.of(1L, ContractOuterClass.Contract.newBuilder()
                                .setId(1L)
                                .setCompanyId(100L)
                                .setStarted(true)
                                .build()))
                .build();
        val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
        assertNotNull(errors);
        assertEquals(1, errors.size());
        assertEquals("Employee ID is invalid. Either the employee ID is not present or the employee is not activated", errors.get(0));
    }

    @Test
    void should_send_employee_id_related_error_when_contract_belongs_to_another_entity() {
        BulkTimeOffRequest.Input input = BulkTimeOffRequest.Input.builder().externalTimeOffId("001").contractId(1L).build();
        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(100L)
                .entityId(300L)
                .idToContractMap(
                        Map.of(1L, ContractOuterClass.Contract.newBuilder()
                                .setId(1L)
                                .setCompanyId(100L)
                                .setLegalEntityId(200L)
                                .setStarted(true)
                                .build()))
                .build();
        val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
        assertNotNull(errors);
        assertEquals(1, errors.size());
        assertEquals("Employee ID is invalid. Either the employee ID is not present or the employee is not activated", errors.get(0));
    }

    @Test
    void should_send_data_related_errors_when_all_data_are_invalid_pt1() {
        Long companyId = 100L;
        List<String> invalidStartDates = List.of("2023/11/12", "12-11-2023"); // Invalid formats
        List<String> invalidEndDates = List.of("2023/12/01", "01-12-2023");

        for (int i = 0; i < invalidStartDates.size(); i++) {
            BulkTimeOffRequest.Input input = BulkTimeOffRequest.Input.builder()
                    .externalTimeOffId("00" + (i + 1))
                    .contractId(1L)
                    .type("") // invalid empty type
                    .startDate(invalidStartDates.get(i))
                    .endDate(invalidEndDates.get(i))
                    .noOfDays("x") // invalid no of days
                    .build();

            Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                    "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                    "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                    "maternity", TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build()
            );

            BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                    .companyId(companyId)
                    .idToContractMap(
                            Map.of(1L, ContractOuterClass.Contract.newBuilder()
                                    .setId(1L)
                                    .setCompanyId(companyId)
                                    .setStarted(true)
                                    .build()))
                    .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                    .build();
            val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
            assertNotNull(errors);
            assertEquals(4, errors.size());
            assertEquals("Time off type is not available in the system. It can be ONLY any of the values, mentioned above in the description", errors.get(0));
            assertEquals("No of days should be greater than zero and can be ONLY in multiples of 0.5", errors.get(1));
            assertEquals("Start date Should be in format of YYYY-MM-DD.", errors.get(2));
            assertEquals("End date Should be in format of YYYY-MM-DD.", errors.get(3));
        }
    }

    @Test
    void should_send_data_related_errors_when_all_data_are_invalid_pt2() {
        Long companyId = 100L;

        List<BulkTimeOffRequest.Input> invalidInputs = List.of(
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("001")
                        .contractId(1L)
                        .type("child care") // non-existing type
                        .startDate("") // empty date
                        .endDate("") // empty date
                        .noOfDays("1.001") // invalid no of days
                        .build(),
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("002")
                        .contractId(1L)
                        .type("child care") // non-existing type
                        .startDate("") // empty date
                        .endDate("") // empty date
                        .noOfDays("1.001") // invalid no of days
                        .build()
        );

        Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                "maternity", TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build()
        );

        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(companyId)
                .idToContractMap(
                        Map.of(1L, ContractOuterClass.Contract.newBuilder()
                                .setId(1L)
                                .setCompanyId(companyId)
                                .setStarted(true)
                                .build()))
                .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                .build();

        List<List<String>> expectedErrors = List.of(
                List.of(
                        "Time off type is not available in the system. It can be ONLY any of the values, mentioned above in the description",
                        "No of days should be greater than zero and can be ONLY in multiples of 0.5",
                        "Start date is empty",
                        "End date is empty"
                ),
                List.of(
                        "Time off type is not available in the system. It can be ONLY any of the values, mentioned above in the description",
                        "No of days should be greater than zero and can be ONLY in multiples of 0.5",
                        "Start date is empty",
                        "End date is empty"
                )
        );

        for (int i = 0; i < invalidInputs.size(); i++) {
            val errors = bulkTimeOffValidator.validate(invalidInputs.get(i), bulkTimeOffDataHolder);
            assertNotNull(errors);
            assertEquals(4, errors.size());
            assertEquals(expectedErrors.get(i), errors);
        }
    }

    @Test
    void should_send_error_when_start_date_is_after_end_date() {
        Long companyId = 100L;
        List<String> startDates = List.of("15/12/2023", "2023-12-15");
        List<String> endDates = List.of("12/12/2023", "2023-12-12");

        for (int i = 0; i < startDates.size(); i++) {
            BulkTimeOffRequest.Input input = BulkTimeOffRequest.Input.builder()
                    .externalTimeOffId("00" + (i + 1))
                    .contractId(1L)
                    .type("annual")
                    .startDate(startDates.get(i))
                    .endDate(endDates.get(i))
                    .noOfDays("1")
                    .build();

            Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                    "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                    "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                    "maternity", TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build()
            );

            BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                    .companyId(companyId)
                    .idToContractMap(
                            Map.of(1L, ContractOuterClass.Contract.newBuilder()
                                    .setId(1L)
                                    .setCompanyId(companyId)
                                    .setStarted(true)
                                    .setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build())
                                    .build()))
                    .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                    .build();

            val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
            assertNotNull(errors);
            assertEquals(1, errors.size());
            assertEquals("End date should be after the start date", errors.get(0));
        }
    }

    @Test
    void should_send_error_when_start_date_is_before_contract_start_date() {
        Long companyId = 100L;
        List<String> startDates = List.of("15/10/2023", "2023-10-15");
        List<String> endDates = List.of("18/10/2023", "2023-10-18");

        for (int i = 0; i < startDates.size(); i++) {
            BulkTimeOffRequest.Input input = BulkTimeOffRequest.Input.builder()
                    .externalTimeOffId("00" + (i + 1))
                    .contractId(1L)
                    .type("annual")
                    .startDate(startDates.get(i))
                    .endDate(endDates.get(i))
                    .noOfDays("1")
                    .build();

            Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                    "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                    "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                    "maternity", TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build()
            );

            BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                    .companyId(companyId)
                    .idToContractMap(
                            Map.of(1L, ContractOuterClass.Contract.newBuilder()
                                    .setId(1L)
                                    .setCompanyId(companyId)
                                    .setStarted(true)
                                    .setStartOn(Date.newBuilder().setYear(2023).setMonth(11).setDay(11).build()) // Contract starts on 11-Nov-2023
                                    .build()))
                    .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                    .build();

            val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
            assertNotNull(errors);
            assertEquals(2, errors.size());
            assertEquals("Can not add timeoffs for dates preceding the contract start date. Start date should be after the contract start date", errors.get(0));
            assertEquals("Can not add timeoffs for dates preceding the contract start date. End date should be after the contract start date", errors.get(1));
        }
    }

    @Test
    void should_send_error_when_no_of_days_are_negative() {
        Long companyId = 100L;

        List<BulkTimeOffRequest.Input> invalidInputs = List.of(
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("001")
                        .contractId(1L)
                        .type("annual")
                        .startDate("12/12/2023") // dd/MM/yyyy
                        .endDate("15/12/2023") // dd/MM/yyyy
                        .noOfDays("-2.5")
                        .build(),
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("002")
                        .contractId(1L)
                        .type("annual")
                        .startDate("2023-12-12") // yyyy-MM-dd
                        .endDate("2023-12-15") // yyyy-MM-dd
                        .noOfDays("-2.5")
                        .build()
        );

        Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                "maternity", TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build()
        );

        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(companyId)
                .idToContractMap(
                        Map.of(1L, ContractOuterClass.Contract.newBuilder()
                                .setId(1L)
                                .setCompanyId(companyId)
                                .setStarted(true)
                                .setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build())
                                .build()))
                .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                .build();

        for (BulkTimeOffRequest.Input input : invalidInputs) {
            val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
            assertNotNull(errors);
            assertEquals(1, errors.size());
            assertEquals("No of days should be greater than zero and can be ONLY in multiples of 0.5", errors.get(0));
        }
    }

    @Test
    void should_send_error_when_no_of_days_are_not_tally_with_start_date_end_date() {
        Long companyId = 100L;

        List<BulkTimeOffRequest.Input> invalidInputs = List.of(
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("001")
                        .contractId(1L)
                        .type("annual")
                        .startDate("12/12/2023") // dd/MM/yyyy
                        .endDate("15/12/2023") // dd/MM/yyyy
                        .noOfDays("6")
                        .build(),
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("002")
                        .contractId(1L)
                        .type("annual")
                        .startDate("2023-12-12") // yyyy-MM-dd
                        .endDate("2023-12-15") // yyyy-MM-dd
                        .noOfDays("6")
                        .build()
        );

        Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                "maternity", TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build()
        );

        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(companyId)
                .idToContractMap(
                        Map.of(1L, ContractOuterClass.Contract.newBuilder()
                                .setId(1L)
                                .setCompanyId(companyId)
                                .setStarted(true)
                                .setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build())
                                .build()))
                .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                .build();

        for (BulkTimeOffRequest.Input input : invalidInputs) {
            val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
            assertNotNull(errors);
            assertEquals(1, errors.size());
            assertEquals("No of days are higher than the given period for start/end dates", errors.get(0));
        }
    }

    @Test
    void should_not_send_error_for_unpaid_leaves() {
        Long companyId = 100L;

        List<BulkTimeOffRequest.Input> validInputs = List.of(
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("001")
                        .contractId(1L)
                        .type("Unpaid Leave")
                        .startDate("12/12/2023")
                        .endDate("15/12/2023")
                        .noOfDays("2")
                        .build(),
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("002")
                        .contractId(1L)
                        .type("Unpaid Leave")
                        .startDate("2023-12-12")
                        .endDate("2023-12-15")
                        .noOfDays("2")
                        .build()
        );

        Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                "annual leave", TimeoffTypeDBO.builder().id(1L).key("annual").label("Annual Leave").companyId(companyId).build(),
                "sick leave", TimeoffTypeDBO.builder().id(2L).key("sick").label("Sick Leave").companyId(companyId).build(),
                "maternity leave", TimeoffTypeDBO.builder().id(3L).key("maternity").label("Maternity Leave").companyId(companyId).build(),
                "unpaid leave", TimeoffTypeDBO.builder().id(4L).key("unpaid").label("Unpaid Leave").companyId(companyId).build()
        );

        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(companyId)
                .idToContractMap(
                        Map.of(1L, ContractOuterClass.Contract.newBuilder()
                                .setId(1L)
                                .setCompanyId(companyId)
                                .setStarted(true)
                                .setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build())
                                .build()))
                .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                .build();

        for (BulkTimeOffRequest.Input input : validInputs) {
            val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
            assertNotNull(errors);
            assertTrue(errors.isEmpty(), "Expected no validation errors but got: " + errors);
        }
    }

    @Test
    void should_send_error_when_leave_balance_not_sufficient_with_carry_forward_expiry_enabled() {
        Long companyId = 100L;

        List<BulkTimeOffRequest.Input> inputDates = List.of(
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("001")
                        .contractId(1L)
                        .type("annual")
                        .startDate("12/12/2023")
                        .endDate("15/12/2023")
                        .noOfDays("2")
                        .build(),
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("002")
                        .contractId(1L)
                        .type("annual")
                        .startDate("2023-12-12")
                        .endDate("2023-12-15")
                        .noOfDays("2")
                        .build()
        );

        Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                1L, ContractOuterClass.Contract.newBuilder().setId(1L).setStarted(true).setCompanyId(companyId).setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build()).build(),
                2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(companyId).setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build()).build()
        );
        Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                "maternity", TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build(),
                "unpaid", TimeoffTypeDBO.builder().id(4L).key("unpaid").companyId(companyId).build()
        );

        Map<String, TimeoffSummaryDBO> timeOffSummaries = Map.of(
                "1_1", TimeoffSummaryDBO.builder().contractId(1L).typeId(1L).allocatedCount(7.0).takenCount(5.0).pendingCount(1.0).build(),
                "1_2", TimeoffSummaryDBO.builder().contractId(1L).typeId(2L).allocatedCount(7.0).takenCount(2.0).pendingCount(1.0).build()
        );
        Map<String, List<TimeoffDBO>> contractIdTypeIdToTimeOffMap = Map.of(
                "1_1", List.of(
                        TimeoffDBO.builder().id(1L).typeId(1L).contractId(1L).noOfDays(4.0).startDate(LocalDate.of(2023, 1, 24)).endDate(LocalDate.of(2023, 1, 28)).status(TimeOffStatus.TAKEN).build(),
                        TimeoffDBO.builder().id(2L).typeId(1L).contractId(1L).noOfDays(10.0).startDate(LocalDate.of(2023, 7, 1)).endDate(LocalDate.of(2023, 7, 11)).status(TimeOffStatus.TAKEN).build(),
                        TimeoffDBO.builder().id(3L).typeId(1L).contractId(1L).noOfDays(4.0).startDate(LocalDate.of(2023, 11, 21)).endDate(LocalDate.of(2023, 11, 25)).status(TimeOffStatus.APPROVAL_IN_PROGRESS).build()),
                "1_2", List.of(
                        TimeoffDBO.builder().id(4L).typeId(2L).contractId(1L).status(TimeOffStatus.APPROVAL_IN_PROGRESS).build(),
                        TimeoffDBO.builder().id(5L).typeId(2L).contractId(1L).status(TimeOffStatus.APPROVED).build(),
                        TimeoffDBO.builder().id(6L).typeId(2L).contractId(1L).status(TimeOffStatus.TAKEN).build()),
                "2_2", List.of(
                        TimeoffDBO.builder().id(7L).typeId(2L).contractId(2L).noOfDays(4.0).startDate(LocalDate.of(2023, 7, 21)).endDate(LocalDate.of(2023, 7, 25)).status(TimeOffStatus.TAKEN).build())
        );
        Map<String, List<EntitlementChangeRecordEntity>> allocationRecords = Map.of(
                "1_1", List.of(
                        new EntitlementChangeRecordEntity(1L, 1L, 1L, EntitlementChangeCategory.ALLOCATION, 14, LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31), null),
                        new EntitlementChangeRecordEntity(2L, 1L, 1L, EntitlementChangeCategory.CARRY_FORWARD, 7, LocalDate.of(2023, 1, 1), LocalDate.of(2023, 5, 31), null)),
                "1_2", List.of(new EntitlementChangeRecordEntity(3L, 2L, 1L, EntitlementChangeCategory.ALLOCATION, 14, LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31), null)),
                "2_1", List.of(new EntitlementChangeRecordEntity(4L, 1L, 2L, EntitlementChangeCategory.ALLOCATION, 14, LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31), null)),
                "2_2", List.of(
                        new EntitlementChangeRecordEntity(5L, 2L, 2L, EntitlementChangeCategory.ALLOCATION, 14, LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31), null),
                        new EntitlementChangeRecordEntity(6L, 2L, 2L, EntitlementChangeCategory.CARRY_FORWARD, 10, LocalDate.of(2023, 1, 1), LocalDate.of(2023, 6, 1), null))
        );

        List<EntitlementChangeRecordEntity> allDeductionECRs = List.of(
                new EntitlementChangeRecordEntity(7L, 1L, 1L, EntitlementChangeCategory.EXPIRATION, 7, null, null, 2L),
                new EntitlementChangeRecordEntity(8L, 2L, 2L, EntitlementChangeCategory.EXPIRATION, 10, null, null, 6L)
        );

        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(companyId)
                .idToContractMap(idToContractMap)
                .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                .contractIdTypeIdToSummaryMap(timeOffSummaries)
                .contractIdTypeIdToTimeOffsMap(contractIdTypeIdToTimeOffMap)
                .contractIdTypeIdToAllocationECRsMap(allocationRecords)
                .deductionECRs(allDeductionECRs)
                .build();

        // Mocking balance check to return false for both date formats
        when(timeoffValidator.isBalanceSufficient(
                LocalDate.of(2023,12,12),
                LocalDate.of(2023,12,15),
                2.0,
                allocationRecords.get("1_1"),
                List.of(allDeductionECRs.get(0)),
                contractIdTypeIdToTimeOffMap.get("1_1")
        )).thenReturn(false);

        carryForwardExpiryOn();  // Assuming this enables carry-forward expiry.

        for (BulkTimeOffRequest.Input input : inputDates) {
            val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
            assertNotNull(errors);
            assertEquals(1, errors.size());
            assertEquals("Count is greater than the available leave balance", errors.get(0));
        }
    }

    @Test
    void should_send_error_when_leave_balance_not_sufficient_with_carry_forward_expiry_disabled() {
        Long companyId = 100L;

        List<BulkTimeOffRequest.Input> inputDates = List.of(
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("001")
                        .contractId(1L)
                        .type("annual")
                        .startDate("12/12/2023")
                        .endDate("15/12/2023")
                        .noOfDays("2")
                        .build(),
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("002")
                        .contractId(1L)
                        .type("annual")
                        .startDate("2023-12-12")
                        .endDate("2023-12-15")
                        .noOfDays("2")
                        .build()
        );

        Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                1L, ContractOuterClass.Contract.newBuilder().setId(1L).setStarted(true).setCompanyId(companyId).setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build()).build(),
                2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(companyId).setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build()).build()
        );
        Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                "maternity", TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build(),
                "unpaid", TimeoffTypeDBO.builder().id(4L).key("unpaid").companyId(companyId).build()
        );
        Map<String, TimeoffSummaryDBO> timeOffSummaries = Map.of(
                "1_1", TimeoffSummaryDBO.builder().contractId(1L).typeId(1L).allocatedCount(7.0).takenCount(5.0).pendingCount(1.0).build(),
                "1_2", TimeoffSummaryDBO.builder().contractId(1L).typeId(2L).allocatedCount(7.0).takenCount(2.0).pendingCount(1.0).build()
        );
        Map<String, TimeoffDBO> externalIdToExistingTimeOffMap = Map.of("001", TimeoffDBO.builder().id(4L).typeId(1L).contractId(1L).noOfDays(1.0).startDate(LocalDate.of(2023, 12, 21)).endDate(LocalDate.of(2023, 12, 26)).status(TimeOffStatus.APPROVED).build());

        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(companyId)
                .idToContractMap(idToContractMap)
                .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                .externalIdToExistingTimeOffMap(externalIdToExistingTimeOffMap)
                .contractIdTypeIdToSummaryMap(timeOffSummaries)
                .build();

        carryForwardExpiryOff();  // Assuming this is a method that disables carry-forward expiry.

        for (BulkTimeOffRequest.Input input : inputDates) {
            val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
            assertNotNull(errors);
            assertEquals(1, errors.size());
            assertEquals("Count is greater than the available leave balance", errors.get(0));
        }
    }

    @Test
    void should_not_send_error_when_leave_balance_is_sufficient_with_carry_forward_expiry_disabled() {
        Long companyId = 100L;
        BulkTimeOffRequest.Input input = BulkTimeOffRequest.Input.builder()
                .externalTimeOffId("001")
                .contractId(1L)
                .type("annual")
                .startDate("12/12/2023")
                .endDate("15/12/2023")
                .noOfDays("2")
                .build();


        Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                1L, ContractOuterClass.Contract.newBuilder().setId(1L).setStarted(true).setCompanyId(companyId).setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build()).build(),
                2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(companyId).setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build()).build()
        );
        Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                "maternity",TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build(),
                "unpaid",TimeoffTypeDBO.builder().id(4L).key("unpaid").companyId(companyId).build()
        );
        Map<String, TimeoffSummaryDBO> timeOffSummaries = Map.of(
                "1_1", TimeoffSummaryDBO.builder().contractId(1L).typeId(1L).totalEntitledCount(7.0).takenCount(5.0).pendingCount(1.0).build(),
                "1_2", TimeoffSummaryDBO.builder().contractId(1L).typeId(2L).totalEntitledCount(7.0).takenCount(2.0).pendingCount(1.0).build()
        );
        Map<String, TimeoffDBO> externalIdToExistingTimeOffMap = Map.of("001", TimeoffDBO.builder().id(4L).typeId(1L).contractId(1L).noOfDays(4.0).startDate(LocalDate.of(2023, 12, 21)).endDate(LocalDate.of(2023, 12, 26)).status(TimeOffStatus.APPROVED).build() );


        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(companyId)
                .idToContractMap(idToContractMap)
                .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                .externalIdToExistingTimeOffMap(externalIdToExistingTimeOffMap)
                .contractIdTypeIdToSummaryMap(timeOffSummaries)
                .build();

        carryForwardExpiryOff();
        val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
        assertNotNull(errors);
        assertTrue(errors.isEmpty());
    }

    @Test
    void should_not_send_error_when_leave_balance_is_sufficient_with_carry_forward_expiry_disabled_new_format() {
        Long companyId = 100L;
        BulkTimeOffRequest.Input input = BulkTimeOffRequest.Input.builder()
                .externalTimeOffId("001")
                .contractId(1L)
                .type("annual")
                .startDate("2023-12-12")
                .endDate("2023-12-15")
                .noOfDays("2")
                .build();

        Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                1L, ContractOuterClass.Contract.newBuilder().setId(1L).setStarted(true).setCompanyId(companyId).setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build()).build(),
                2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(companyId).setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build()).build()
        );
        Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                "maternity", TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build(),
                "unpaid", TimeoffTypeDBO.builder().id(4L).key("unpaid").companyId(companyId).build()
        );
        Map<String, TimeoffSummaryDBO> timeOffSummaries = Map.of(
                "1_1", TimeoffSummaryDBO.builder().contractId(1L).typeId(1L).totalEntitledCount(7.0).takenCount(5.0).pendingCount(1.0).build(),
                "1_2", TimeoffSummaryDBO.builder().contractId(1L).typeId(2L).totalEntitledCount(7.0).takenCount(2.0).pendingCount(1.0).build()
        );
        Map<String, TimeoffDBO> externalIdToExistingTimeOffMap = Map.of(
                "001", TimeoffDBO.builder().id(4L).typeId(1L).contractId(1L).noOfDays(4.0).startDate(LocalDate.of(2023, 12, 21)).endDate(LocalDate.of(2023, 12, 26)).status(TimeOffStatus.APPROVED).build()
        );

        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(companyId)
                .idToContractMap(idToContractMap)
                .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                .externalIdToExistingTimeOffMap(externalIdToExistingTimeOffMap)
                .contractIdTypeIdToSummaryMap(timeOffSummaries)
                .build();

        carryForwardExpiryOff();  // Disabling carry forward expiry
        val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);

        assertNotNull(errors);
        assertTrue(errors.isEmpty());
    }

    @Test
    void should_send_error_when_summary_not_found_with_carry_forward_expiry_disabled() {
        Long companyId = 100L;

        List<BulkTimeOffRequest.Input> inputDates = List.of(
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("001")
                        .contractId(1L)
                        .type("annual")
                        .startDate("12/12/2023")
                        .endDate("15/12/2023")
                        .noOfDays("2")
                        .build(),
                BulkTimeOffRequest.Input.builder()
                        .externalTimeOffId("002")
                        .contractId(1L)
                        .type("annual")
                        .startDate("2023-12-12")
                        .endDate("2023-12-15")
                        .noOfDays("2")
                        .build()
        );

        Map<Long, ContractOuterClass.Contract> idToContractMap = Map.of(
                1L, ContractOuterClass.Contract.newBuilder().setId(1L).setStarted(true).setCompanyId(companyId).setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build()).build(),
                2L, ContractOuterClass.Contract.newBuilder().setId(2L).setStarted(true).setCompanyId(companyId).setStartOn(Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build()).build()
        );
        Map<String, TimeoffTypeDBO> companyTimeOffTypes = Map.of(
                "annual", TimeoffTypeDBO.builder().id(1L).key("annual").companyId(companyId).build(),
                "sick", TimeoffTypeDBO.builder().id(2L).key("sick").companyId(companyId).build(),
                "maternity", TimeoffTypeDBO.builder().id(3L).key("maternity").companyId(companyId).build(),
                "unpaid", TimeoffTypeDBO.builder().id(4L).key("unpaid").companyId(companyId).build()
        );

        // In this case, there's no summary for the "annual" leave type with contractId 1
        Map<String, TimeoffSummaryDBO> missingTimeOffSummaries = Map.of();

        BulkTimeOffDataHolder bulkTimeOffDataHolder = BulkTimeOffDataHolder.builder()
                .companyId(companyId)
                .idToContractMap(idToContractMap)
                .labelToCompanyTimeOffTypeMap(companyTimeOffTypes)
                .contractIdTypeIdToSummaryMap(missingTimeOffSummaries)  // Simulating no summary found
                .build();

        carryForwardExpiryOff();  // Assuming this disables carry-forward expiry

        for (BulkTimeOffRequest.Input input : inputDates) {
            val errors = bulkTimeOffValidator.validate(input, bulkTimeOffDataHolder);
            assertNotNull(errors);
            assertEquals(1, errors.size());
            assertEquals("Count is greater than the available leave balance", errors.get(0));  // Expecting the error
        }
    }

    private static GBFeatureResult createFeatureFlag(boolean isOn) {
        return new GBFeatureResult(null, isOn, !isOn, GBFeatureSource.defaultValue, null, null);
    }

    private void carryForwardExpiryOff() {
        when(featureFlagService.feature(anyString(), anyMap())).thenReturn(createFeatureFlag(false));
    }

    private void carryForwardExpiryOn() {
        when(featureFlagService.feature(anyString(), anyMap())).thenReturn(createFeatureFlag(true));
    }
}
