package com.multiplier.timeoff.validation;

import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.repository.model.*;
import com.multiplier.timeoff.types.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.time.LocalDate;
import java.time.Month;

import static com.multiplier.timeoff.ProtobufExtensionsKt.toDate;


class TimeoffDefinitionAllocatorTest {

    private final TimeoffDefinitionAllocator timeoffDefinitionAllocator = new TimeoffDefinitionAllocator();

    @Test
    void shouldAllocateTheTimeOffNonProratedStartingThisYearBeforeFinancialYearEnd() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder().build();

        AllocationConfigEntity  allocationConfigEntity = new AllocationConfigEntity("WEEKLY", false);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.WEEKS)
                .value(2.00)
                .build();

        LocalDate startOn = LocalDate.of(2022, 5, 10);
        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(definitionEntity, entitlementDBO, ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(startOn)).build(), summaryDBO, true, Month.JUNE);

        LocalDate today = LocalDate.of(2022, 6, 29);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(14.0, request.timeoffSummaryDBO().allocatedCount());
    }

    @Test
    void shouldAllocateTheTimeOffProratedStartingNextYearBeforeFinancialYearEnd() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder().build();

        AllocationConfigEntity  allocationConfigEntity = new AllocationConfigEntity("WEEKLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.WEEKS)
                .value(2.00)
                .build();

        LocalDate startOn = LocalDate.of(2022, 5, 10);
        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(definitionEntity, entitlementDBO, ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(startOn)).build(), summaryDBO, true, Month.JUNE);

        LocalDate today = LocalDate.of(2022, 6, 30);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(2, request.timeoffSummaryDBO().allocatedCount());
    }

    @Test
    void shouldAllocateTheTimeOffProratedStartingThisYearAfterFinancialYearEnd() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder().build();

        AllocationConfigEntity  allocationConfigEntity = new AllocationConfigEntity("ANNUALLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);
        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.WEEKS)
                .value(2.00)
                .build();

        LocalDate startOn = LocalDate.of(2021, 8, 1);

        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(definitionEntity,
                entitlementDBO,
                ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(startOn)).build(),
                summaryDBO,
                true,
                Month.JUNE);

        LocalDate today = LocalDate.of(2022, 6, 29);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(13, request.timeoffSummaryDBO().allocatedCount());
        Assertions.assertEquals(LocalDate.of(2022, 6, 30), request.timeoffSummaryDBO().periodEnd());
    }

    @Test
    void shouldAllocateTheTimeOffProratedStartingThisYearInSameFinancialMonth() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder().build();

        AllocationConfigEntity  allocationConfigEntity = new AllocationConfigEntity("ANNUALLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.DAYS)
                .value(30.00)
                .build();

        LocalDate startOn = LocalDate.of(2022, 6, 1);
        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(definitionEntity, entitlementDBO, ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(startOn)).build(), summaryDBO, true, Month.JUNE);

        LocalDate today = LocalDate.of(2022, 5, 25);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(3.0, request.timeoffSummaryDBO().allocatedCount());
        Assertions.assertEquals(LocalDate.of(2022, 6, 30), request.timeoffSummaryDBO().periodEnd());
    }

    @Test
    void shouldAllocateTheTimeOffProratedWithFarPastStartOn() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder().build();

        AllocationConfigEntity  allocationConfigEntity = new AllocationConfigEntity("ANNUALLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.DAYS)
                .value(30.00)
                .build();

        LocalDate startOn = LocalDate.of(2021, 6, 1);
        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(definitionEntity, entitlementDBO, ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(startOn)).build(), summaryDBO, true, Month.JUNE);

        LocalDate today = LocalDate.of(2022, 4, 25);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(30.0, request.timeoffSummaryDBO().allocatedCount());
        Assertions.assertEquals(LocalDate.of(2021, 7, 1), request.timeoffSummaryDBO().periodStart());
        Assertions.assertEquals(LocalDate.of(2022, 6, 30), request.timeoffSummaryDBO().periodEnd());
    }

    @Test
    void shouldNotReallocateForOnceBasis() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .id(123L)
                .allocatedCount(2.00)
                .build();

        AllocationConfigEntity  allocationConfigEntity = new AllocationConfigEntity("ONCE", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.WEEKS)
                .value(2.00)
                .build();
        LocalDate startOn = LocalDate.of(2022, 7, 1);
        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(definitionEntity, entitlementDBO, ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(startOn)).build(), summaryDBO, false, Month.JUNE);

        LocalDate today = LocalDate.of(2022, 6, 29);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(2.0, request.timeoffSummaryDBO().allocatedCount());
    }

    @Test
    void shouldNotReallocateIfThePeriodDidNotEnd() {
        LocalDate today = LocalDate.of(2022, 6, 29);
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .id(123L)
                .allocatedCount(2.00)
                .periodEnd(today.plusDays(2))
                .build();

        AllocationConfigEntity  allocationConfigEntity = new AllocationConfigEntity("ANNUALLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.WEEKS)
                .value(2.00)
                .build();
        LocalDate startOn = LocalDate.of(2022, 7, 1);
        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(definitionEntity, entitlementDBO, ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(startOn)).build(), summaryDBO, false, Month.JUNE);

        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(2.0, request.timeoffSummaryDBO().allocatedCount());
    }

    @Test
    void shouldReallocateTheLeavesInCorrectUnitsForRightPeriod() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .id(123L)
                .allocatedCount(2.00)
                .periodEnd(LocalDate.of(2021, 11, 9))
                .build();

        AllocationConfigEntity  allocationConfigEntity = new AllocationConfigEntity("WEEKLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.WEEKS)
                .value(2.00)
                .build();
        LocalDate startOn = LocalDate.of(2021, 7, 1);
        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(definitionEntity, entitlementDBO, ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(startOn)).build(), summaryDBO, false, Month.JUNE);

        LocalDate today = LocalDate.of(2022, 6, 29);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(14.0, request.timeoffSummaryDBO().allocatedCount());
        Assertions.assertEquals(LocalDate.of(2021, 11, 16), request.timeoffSummaryDBO().periodEnd());
    }

    @Test
    void shouldReallocateTheLeavesCorrectlyForSamePeriodEnd() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .id(123L)
                .allocatedCount(2.00)
                .periodEnd(LocalDate.of(2021, 6, 30))
                .build();
        AllocationConfigEntity  allocationConfigEntity = new AllocationConfigEntity("ANNUALLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.WEEKS)
                .value(2.00)
                .build();
        LocalDate startOn = LocalDate.of(2021, 7, 1);
        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(definitionEntity, entitlementDBO, ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(startOn)).build(), summaryDBO, false, Month.JUNE);

        LocalDate today = LocalDate.of(2022, 6, 29);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(14.0, request.timeoffSummaryDBO().allocatedCount());
        Assertions.assertEquals(LocalDate.of(2022, 6, 30), request.timeoffSummaryDBO().periodEnd());
    }

    @Test
    void shouldFixTimeoffAllocationForNonProratedCase() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .periodStart(LocalDate.of(2022, 7, 1))
                .periodEnd(LocalDate.of(2023, 6, 30))
                .build();

        AllocationConfigEntity allocationConfigEntity = new AllocationConfigEntity("ANNUALLY", false);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.DAYS)
                .value(30.00)
                .build();

        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(
                definitionEntity,
                entitlementDBO,
                ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(LocalDate.of(2022, 7, 1))).build(),
                summaryDBO,
                true,
                Month.JUNE
        );

        timeoffDefinitionAllocator.fixTimeoffAllocation(request);

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(30.0, request.timeoffSummaryDBO().allocatedCount());
    }

    @Test
    void shouldFixTimeoffAllocationForProratedCase() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .periodStart(LocalDate.of(2022, 7, 1))
                .periodEnd(LocalDate.of(2023, 6, 30))
                .build();

        AllocationConfigEntity allocationConfigEntity = new AllocationConfigEntity("ANNUALLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.DAYS)
                .value(30.00)
                .build();

        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(
                definitionEntity,
                entitlementDBO,
                ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(LocalDate.of(2022, 7, 1))).build(),
                summaryDBO,
                true,
                Month.JUNE
        );

        timeoffDefinitionAllocator.fixTimeoffAllocation(request);

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(30.0, request.timeoffSummaryDBO().allocatedCount());
    }

    @Test
    void shouldFixTimeoffAllocationForProratedCaseWithWeeks() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .periodStart(LocalDate.of(2022, 7, 1))
                .periodEnd(LocalDate.of(2023, 6, 30))
                .build();

        AllocationConfigEntity allocationConfigEntity = new AllocationConfigEntity("ANNUALLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.WEEKS)
                .value(4.00)
                .build();

        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(
                definitionEntity,
                entitlementDBO,
                ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(LocalDate.of(2022, 7, 1))).build(),
                summaryDBO,
                true,
                Month.JUNE
        );

        timeoffDefinitionAllocator.fixTimeoffAllocation(request);

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(28.0, request.timeoffSummaryDBO().allocatedCount());
    }

    @Test
    void shouldFixTimeoffAllocationForProratedCaseWithYears() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .periodStart(LocalDate.of(2022, 7, 1))
                .periodEnd(LocalDate.of(2023, 6, 30))
                .build();

        AllocationConfigEntity allocationConfigEntity = new AllocationConfigEntity("ANNUALLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.YEARS)
                .value(1.00)
                .build();

        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(
                definitionEntity,
                entitlementDBO,
                ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(LocalDate.of(2022, 7, 1))).build(),
                summaryDBO,
                true,
                Month.JUNE
        );

        timeoffDefinitionAllocator.fixTimeoffAllocation(request);

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(365.0, request.timeoffSummaryDBO().allocatedCount());
    }

    @Test
    void shouldReallocateForDailyBasisBeforeFinancialYearEnd() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .id(123L)
                .allocatedCount(2.00)
                .periodEnd(LocalDate.of(2022, 6, 29))
                .build();

        AllocationConfigEntity allocationConfigEntity = new AllocationConfigEntity("DAILY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.DAYS)
                .value(2.00)
                .build();

        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(
                definitionEntity,
                entitlementDBO,
                ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(LocalDate.of(2022, 6, 1))).build(),
                summaryDBO,
                false,
                Month.JUNE
        );

        LocalDate today = LocalDate.of(2022, 6, 30);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(2.0, request.timeoffSummaryDBO().allocatedCount());
        Assertions.assertEquals(LocalDate.of(2022, 6, 30), request.timeoffSummaryDBO().periodEnd());
    }

    @Test
    void shouldReallocateForMonthlyBasisBeforeFinancialYearEnd() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .id(123L)
                .allocatedCount(2.00)
                .periodEnd(LocalDate.of(2022, 5, 31))
                .build();

        AllocationConfigEntity allocationConfigEntity = new AllocationConfigEntity("MONTHLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.DAYS)
                .value(2.00)
                .build();

        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(
                definitionEntity,
                entitlementDBO,
                ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(LocalDate.of(2022, 5, 1))).build(),
                summaryDBO,
                false,
                Month.JUNE
        );

        LocalDate today = LocalDate.of(2022, 6, 30);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(2.0, request.timeoffSummaryDBO().allocatedCount());
        Assertions.assertEquals(LocalDate.of(2022, 6, 30), request.timeoffSummaryDBO().periodEnd());
    }

    @Test
    void shouldReallocateForQuarterlyBasisBeforeFinancialYearEnd() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .id(123L)
                .allocatedCount(2.00)
                .periodEnd(LocalDate.of(2022, 3, 31))
                .build();

        AllocationConfigEntity allocationConfigEntity = new AllocationConfigEntity("QUARTERLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.DAYS)
                .value(2.00)
                .build();

        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(
                definitionEntity,
                entitlementDBO,
                ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(LocalDate.of(2022, 3, 1))).build(),
                summaryDBO,
                false,
                Month.JUNE
        );

        LocalDate today = LocalDate.of(2022, 6, 30);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(2.0, request.timeoffSummaryDBO().allocatedCount());
        Assertions.assertEquals(LocalDate.of(2022, 6, 30), request.timeoffSummaryDBO().periodEnd());
    }

    @Test
    void shouldReallocateForMonthlyBasisWithLastDayOfMonth() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .id(123L)
                .allocatedCount(2.00)
                .periodEnd(LocalDate.of(2022, 1, 31))
                .build();

        AllocationConfigEntity allocationConfigEntity = new AllocationConfigEntity("MONTHLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.DAYS)
                .value(2.00)
                .build();

        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(
                definitionEntity,
                entitlementDBO,
                ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(LocalDate.of(2022, 1, 1))).build(),
                summaryDBO,
                false,
                Month.JUNE
        );

        LocalDate today = LocalDate.of(2022, 6, 30);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(2.0, request.timeoffSummaryDBO().allocatedCount());
        Assertions.assertEquals(LocalDate.of(2022, 2, 28), request.timeoffSummaryDBO().periodEnd());
    }

    @Test
    void shouldReallocateForQuarterlyBasisWithLastDayOfMonth() {
        TimeoffSummaryDBO summaryDBO = TimeoffSummaryDBO.builder()
                .id(123L)
                .allocatedCount(2.00)
                .periodEnd(LocalDate.of(2022, 1, 31))
                .build();

        AllocationConfigEntity allocationConfigEntity = new AllocationConfigEntity("QUARTERLY", true);
        TimeoffDefinitionConfigEntity timeoffDefinitionConfigEntity = new TimeoffDefinitionConfigEntity(allocationConfigEntity, null);
        DefinitionEntity definitionEntity = new DefinitionEntity();
        definitionEntity.setConfigurations(timeoffDefinitionConfigEntity);

        TimeoffEntitlementDBO entitlementDBO = TimeoffEntitlementDBO.builder()
                .unit(TimeOffUnit.DAYS)
                .value(2.00)
                .build();

        TimeoffDefinitionConfigRequest request = TimeoffDefinitionConfigRequest.of(
                definitionEntity,
                entitlementDBO,
                ContractOuterClass.Contract.newBuilder().setCompanyId(123L).setStartOn(toDate(LocalDate.of(2022, 1, 1))).build(),
                summaryDBO,
                false,
                Month.JUNE
        );

        LocalDate today = LocalDate.of(2022, 6, 30);
        try (MockedStatic<LocalDate> mockLocalDate = Mockito.mockStatic(LocalDate.class, Mockito.CALLS_REAL_METHODS)) {
            mockLocalDate.when(LocalDate::now).thenReturn(today);
            timeoffDefinitionAllocator.allocate(request);
        }

        Assertions.assertNotNull(request.timeoffSummaryDBO());
        Assertions.assertEquals(2.0, request.timeoffSummaryDBO().allocatedCount());
        Assertions.assertEquals(LocalDate.of(2022, 4, 30), request.timeoffSummaryDBO().periodEnd());
    }
}
