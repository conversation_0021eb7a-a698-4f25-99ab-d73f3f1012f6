package com.multiplier.timeoff.core.common.util;

import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class EmailUtilTest {

    @Nested
    class maskEmail {

        @Test
        void when_email_is_null_then_returns_null() {
            var maskedEmail = EmailUtil.maskEmail(null);

            assertNull(maskedEmail);
        }

        @Test
        void when_email_is_valid_return_masked_email() {
            var email = "<EMAIL>";
            var maskedEmail = EmailUtil.maskEmail(email);
            var expectedMaskedEmail = "hollan*******e@stuffn******m";

            assertEquals(expectedMaskedEmail, maskedEmail);
        }

        @Test
        void when_email_is_invalid_return_original_email() {
            var email = "holland.pierce";
            var maskedEmail = EmailUtil.maskEmail(email);

            assertEquals(email, maskedEmail);
        }

        @Test
        void when_email_is_empty_return_empty() {
            var email = "@";
            var maskedEmail = EmailUtil.maskEmail(email);

            assertEquals(email, maskedEmail);
        }

        @Test
        void when_email_is_single_char_return_star() {
            var email = "a@b";
            var maskedEmail = EmailUtil.maskEmail(email);
            var expectedMaskedEmail = "*@*";

            assertEquals(expectedMaskedEmail, maskedEmail);
        }
    }
}
