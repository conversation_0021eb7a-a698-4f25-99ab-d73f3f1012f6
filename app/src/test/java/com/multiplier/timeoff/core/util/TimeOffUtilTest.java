package com.multiplier.timeoff.core.util;

import com.multiplier.company.schema.holiday.LegalEntityHoliday;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.country.schema.holiday.HolidayOuterClass;
import com.multiplier.timeoff.repository.model.DefinitionEntity;
import com.multiplier.timeoff.repository.model.TimeoffEntitlementDBO;
import com.multiplier.timeoff.types.EntityType;
import com.multiplier.timeoff.types.TimeOffSession;
import com.multiplier.timeoff.types.TimeOffUnit;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.*;

import static com.multiplier.timeoff.core.util.TimeOffUtil.CONTRACT_QUERY_CHUNK_SIZE;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
@ExtendWith(MockitoExtension.class)
class TimeOffUtilTest {

    @Nested
    class countWeekDays {
        @Test
        void when_same_week_then_returns_correct_result() {
            var monday = LocalDate.of(2022, 7, 11);
            var tuesday = LocalDate.of(2022, 7, 12);
            var friday = LocalDate.of(2022, 7, 15);

            assertEquals(1, TimeOffUtil.countWeekDays(monday, tuesday));
            assertEquals(4, TimeOffUtil.countWeekDays(monday, friday));
        }

        @Test
        void when_different_week_then_returns_correct_result() {
            var monday = LocalDate.of(2022, 7, 11);
            var friday = LocalDate.of(2022, 7, 15);
            var nextMonday = LocalDate.of(2022, 7, 18);

            assertEquals(1, TimeOffUtil.countWeekDays(friday, nextMonday));
            assertEquals(5, TimeOffUtil.countWeekDays(monday, nextMonday));
        }

        @Test
        void when_same_day_then_returns_zero() {
            var monday = LocalDate.of(2022, 7, 11);

            assertEquals(0, TimeOffUtil.countWeekDays(monday, monday));
        }
    }

    @Nested
    class splitLists {

        @Test
        void should_split_to_one_chunk_if_size_is_less_than_chunk_size() {
            var list = List.of(1L, 2L, 3L);
            var result = TimeOffUtil.splitList(list);
            assertEquals(1, result.size());
            assertEquals(3, result.get(0).size());
        }

        @Test
        void should_split_to_one_chunk_if_size_is_same_as_chunk_size() {
            var list = new ArrayList<Long>();
            for (long i = 1L; i <= CONTRACT_QUERY_CHUNK_SIZE; i++) {
                list.add(i);
            }
            var result = TimeOffUtil.splitList(list);
            assertEquals(1, result.size());
            assertEquals(CONTRACT_QUERY_CHUNK_SIZE, result.get(0).size());
        }

        @Test
        void should_split_to_multiple_chunk_if_size_is_greater_than_chunk_size() {
            var list = new ArrayList<Long>();
            for (long i = 1L; i <= CONTRACT_QUERY_CHUNK_SIZE *2 + 10000; i++) {
                list.add(i);
            }
            var result = TimeOffUtil.splitList(list);
            assertEquals(3, result.size());
            assertEquals(CONTRACT_QUERY_CHUNK_SIZE, result.get(0).size());
            assertEquals(CONTRACT_QUERY_CHUNK_SIZE, result.get(1).size());
            assertEquals(10000, result.get(2).size());
        }
    }

    @Nested
    class round {
        @Test
        void should_round_to_nearest_half() {
            assertEquals(1.0, TimeOffUtil.round(1.0));
            assertEquals(1.5, TimeOffUtil.round(1.3));
            assertEquals(1.5, TimeOffUtil.round(1.7));
            assertEquals(2.0, TimeOffUtil.round(1.8));
        }
    }

    @Nested
    class isLastDayOfMonth {
        @Test
        void should_return_true_for_last_day_of_month() {
            assertTrue(TimeOffUtil.isLastDayOfMonth(LocalDate.of(2024, 1, 31)));
            assertTrue(TimeOffUtil.isLastDayOfMonth(LocalDate.of(2024, 2, 29)));
            assertTrue(TimeOffUtil.isLastDayOfMonth(LocalDate.of(2024, 3, 31)));
        }

        @Test
        void should_return_false_for_non_last_day_of_month() {
            assertFalse(TimeOffUtil.isLastDayOfMonth(LocalDate.of(2024, 1, 30)));
            assertFalse(TimeOffUtil.isLastDayOfMonth(LocalDate.of(2024, 2, 28)));
            assertFalse(TimeOffUtil.isLastDayOfMonth(LocalDate.of(2024, 3, 30)));
        }
    }

    @Nested
    class getDays {
        @Test
        void should_convert_different_units_to_days() {
            assertEquals(5.0, TimeOffUtil.getDays(5.0, TimeOffUnit.DAYS));
            assertEquals(155.0, TimeOffUtil.getDays(5.0, TimeOffUnit.MONTHS));
            assertEquals(35.0, TimeOffUtil.getDays(5.0, TimeOffUnit.WEEKS));
            assertEquals(1825.0, TimeOffUtil.getDays(5.0, TimeOffUnit.YEARS));
        }
    }

    @Nested
    class toContractIdTypeIDKey {
        @Test
        void should_generate_correct_key() {
            assertEquals("123_456", TimeOffUtil.toContractIdTypeIDKey(123L, 456L));
        }
    }

    @Nested
    class getDefinitionForEntitlement {
        @Mock
        private TimeoffEntitlementDBO entitlementDBO;
        @Mock
        private DefinitionEntity definitionEntity;

        @Test
        void should_return_definition_when_present() {
            when(entitlementDBO.definition()).thenReturn(definitionEntity);
            assertEquals(definitionEntity, TimeOffUtil.getDefinitionForEntitlement(entitlementDBO));
        }

    }

    @Nested
    class isEntityHolidayAPublicHoliday {
        @Test
        void should_return_false_when_holidays_empty() {
            var date = LocalDate.of(2024, 1, 1);
            assertFalse(TimeOffUtil.isEntityHolidayAPublicHoliday(date, Collections.emptyList()));
        }

        @Test
        void should_return_true_when_date_matches_holiday() {
            var date = LocalDate.of(2024, 1, 1);
            var holidays = List.of(
                    LegalEntityHoliday.Holiday.newBuilder()
                            .setDate(1)
                            .setMonth(1)
                            .build()
            );
            assertTrue(TimeOffUtil.isEntityHolidayAPublicHoliday(date, holidays));
        }

        @Test
        void should_return_false_when_date_does_not_match_holiday() {
            var date = LocalDate.of(2024, 1, 2);
            var holidays = List.of(
                    LegalEntityHoliday.Holiday.newBuilder()
                            .setDate(1)
                            .setMonth(1)
                            .build()
            );
            assertFalse(TimeOffUtil.isEntityHolidayAPublicHoliday(date, holidays));
        }
    }

    @Nested
    class isCountryHolidayAPublicHoliday {
        @Test
        void should_return_false_when_holidays_empty() {
            var date = LocalDate.of(2024, 1, 1);
            assertFalse(TimeOffUtil.isCountryHolidayAPublicHoliday(date, Collections.emptyList()));
        }

        @Test
        void should_return_true_when_date_matches_holiday() {
            var date = LocalDate.of(2024, 1, 1);
            var holidays = List.of(
                    HolidayOuterClass.Holiday.newBuilder()
                            .setDate(1)
                            .setMonth(1)
                            .build()
            );
            assertTrue(TimeOffUtil.isCountryHolidayAPublicHoliday(date, holidays));
        }

        @Test
        void should_return_false_when_date_does_not_match_holiday() {
            var date = LocalDate.of(2024, 1, 2);
            var holidays = List.of(
                    HolidayOuterClass.Holiday.newBuilder()
                            .setDate(1)
                            .setMonth(1)
                            .build()
            );
            assertFalse(TimeOffUtil.isCountryHolidayAPublicHoliday(date, holidays));
        }
    }

    @Nested
    class flatMap {
        @Test
        void should_flatten_collection_of_lists() {
            var input = List.of(
                    List.of(1, 2, 3),
                    List.of(4, 5),
                    List.of(6)
            );
            var expected = List.of(1, 2, 3, 4, 5, 6);
            assertEquals(expected, TimeOffUtil.flatMap(input));
        }
    }

    @Nested
    class toSet {
        @Test
        void should_convert_collection_to_set() {
            var input = List.of(1, 2, 2, 3, 3, 3);
            var expected = Set.of(1, 2, 3);
            assertEquals(expected, TimeOffUtil.toSet(input));
        }
    }

    @Nested
    class mergeLists {
        @Test
        void should_merge_lists_without_duplicates() {
            var list1 = List.of(1, 2, 3);
            var list2 = List.of(2, 3, 4);
            var list3 = List.of(3, 4, 5);
            var expected = List.of(1, 2, 3, 4, 5);
            assertEquals(expected, TimeOffUtil.mergeLists(list1, list2, list3));
        }
    }

    @Nested
    class generateExternalId {
        @Test
        void should_generate_correct_external_id() {
            assertEquals("123-456", TimeOffUtil.generateExternalId(123L, "456"));
        }
    }

    @Nested
    class companyDefinitionsByEntityKey {
        @Test
        void should_generate_correct_key() {
            assertEquals("123_456_COMPANY_ENTITY", TimeOffUtil.companyDefinitionsByEntityKey(123L, 456L, EntityType.COMPANY_ENTITY));
        }
    }

    @Nested
    class getEntityTypeForContract {
        @Test
        void should_return_correct_entity_type() {
            assertEquals(EntityType.EOR_PARTNER_ENTITY, TimeOffUtil.getEntityTypeForContract(ContractOuterClass.ContractType.EMPLOYEE));
            assertEquals(EntityType.COMPANY_ENTITY, TimeOffUtil.getEntityTypeForContract(ContractOuterClass.ContractType.HR_MEMBER));
        }
    }

    @Nested
    class zeroIfNull {
        @Test
        void should_return_zero_for_null() {
            assertEquals(0.0, TimeOffUtil.zeroIfNull(null));
        }

        @Test
        void should_return_value_for_non_null() {
            assertEquals(5.0, TimeOffUtil.zeroIfNull(5.0));
        }
    }

    @Nested
    class getLabelToTimeOffTypeMapKey {
        @Test
        void should_convert_label_to_lowercase_and_trim() {
            assertEquals("annual leave", TimeOffUtil.getLabelToTimeOffTypeMapKey(" Annual Leave "));
        }
    }

    @Nested
    class falseIfNull {
        @Test
        void should_return_false_for_null() {
            assertFalse(TimeOffUtil.falseIfNull(null));
        }

        @Test
        void should_return_value_for_non_null() {
            assertTrue(TimeOffUtil.falseIfNull(true));
            assertFalse(TimeOffUtil.falseIfNull(false));
        }
    }

    @Nested
    class convertHolidaysToLocalDates {
        @Test
        void should_convert_holidays_to_local_dates() {
            var holidays = List.of(
                    LegalEntityHoliday.Holiday.newBuilder()
                            .setYear(2024)
                            .setMonth(1)
                            .setDate(1)
                            .build(),
                    LegalEntityHoliday.Holiday.newBuilder()
                            .setYear(2024)
                            .setMonth(12)
                            .setDate(25)
                            .build()
            );

            var expected = List.of(
                    LocalDate.of(2024, 1, 1),
                    LocalDate.of(2024, 12, 25)
            );

            assertEquals(expected, TimeOffUtil.convertHolidaysToLocalDates(holidays));
        }

        @Test
        void should_return_empty_list_for_empty_holidays() {
            assertEquals(Collections.emptyList(), TimeOffUtil.convertHolidaysToLocalDates(Collections.emptyList()));
        }
    }

    @Nested
    class calculateNoOfDays {
        @Test
        void should_return_zero_for_invalid_date_range() {
            var startDate = LocalDate.of(2024, 1, 2);
            var endDate = LocalDate.of(2024, 1, 1);
            var holidayDates = Collections.<LocalDate>emptyList();

            assertEquals(0.0, TimeOffUtil.calculateNoOfTimeoffDays(startDate, endDate, TimeOffSession.MORNING, TimeOffSession.AFTERNOON, holidayDates));
        }

        @Test
        void should_calculate_single_day_full_day() {
            var date = LocalDate.of(2024, 1, 2);
            var holidayDates = Collections.<LocalDate>emptyList();

            assertEquals(1.0, TimeOffUtil.calculateNoOfTimeoffDays(date, date, TimeOffSession.MORNING, TimeOffSession.AFTERNOON, holidayDates));
        }

        @Test
        void should_calculate_single_day_half_day() {
            var date = LocalDate.of(2024, 1, 2);
            var holidayDates = Collections.<LocalDate>emptyList();

            assertEquals(0.5, TimeOffUtil.calculateNoOfTimeoffDays(date, date, TimeOffSession.MORNING, TimeOffSession.MORNING, holidayDates));
            assertEquals(0.5, TimeOffUtil.calculateNoOfTimeoffDays(date, date, TimeOffSession.AFTERNOON, TimeOffSession.AFTERNOON, holidayDates));
        }

        @Test
        void should_calculate_multiple_days_without_holidays() {
            var startDate = LocalDate.of(2024, 1, 1);
            var endDate = LocalDate.of(2024, 1, 5);
            var holidayDates = Collections.<LocalDate>emptyList();

            assertEquals(5.0, TimeOffUtil.calculateNoOfTimeoffDays(startDate, endDate, TimeOffSession.MORNING, TimeOffSession.AFTERNOON, holidayDates));
        }

        @Test
        void should_exclude_holidays() {
            var startDate = LocalDate.of(2024, 1, 1);
            var endDate = LocalDate.of(2024, 1, 5);
            var holidays = List.of(LocalDate.of(2024, 1, 2));

            assertEquals(4.0, TimeOffUtil.calculateNoOfTimeoffDays(startDate, endDate, TimeOffSession.MORNING, TimeOffSession.AFTERNOON, holidays));
        }

        @Test
        void should_handle_half_days_at_start_and_end() {
            var startDate = LocalDate.of(2024, 1, 1);
            var endDate = LocalDate.of(2024, 1, 3);
            var holidays = Collections.<LocalDate>emptyList();

            assertEquals(2.0, TimeOffUtil.calculateNoOfTimeoffDays(startDate, endDate, TimeOffSession.AFTERNOON, TimeOffSession.MORNING, holidays));
        }

        @Test
        void should_exclude_weekends_and_holidays() {
            var startDate = LocalDate.of(2024, 1, 1);
            var endDate = LocalDate.of(2024, 1, 8);
            var holidays = List.of(LocalDate.of(2024, 1, 2));

            assertEquals(5.0, TimeOffUtil.calculateNoOfTimeoffDays(startDate, endDate, TimeOffSession.MORNING, TimeOffSession.AFTERNOON, holidays));
        }

        @Test
        void should_return_zero_for_negative_result() {
            var startDate = LocalDate.of(2024, 1, 1);
            var endDate = LocalDate.of(2024, 1, 1);
            var holidays = List.of(LocalDate.of(2024, 1, 1));

            assertEquals(0.0, TimeOffUtil.calculateNoOfTimeoffDays(startDate, endDate, TimeOffSession.MORNING, TimeOffSession.MORNING, holidays));
        }
    }

    @Nested
    class isWeekDay {
        @Test
        void should_return_true_for_weekdays() {
            assertTrue(TimeOffUtil.isWeekDay(LocalDate.of(2024, 1, 1))); // Monday
            assertTrue(TimeOffUtil.isWeekDay(LocalDate.of(2024, 1, 2))); // Tuesday
            assertTrue(TimeOffUtil.isWeekDay(LocalDate.of(2024, 1, 3))); // Wednesday
            assertTrue(TimeOffUtil.isWeekDay(LocalDate.of(2024, 1, 4))); // Thursday
            assertTrue(TimeOffUtil.isWeekDay(LocalDate.of(2024, 1, 5))); // Friday
        }

        @Test
        void should_return_false_for_weekends() {
            assertFalse(TimeOffUtil.isWeekDay(LocalDate.of(2024, 1, 6))); // Saturday
            assertFalse(TimeOffUtil.isWeekDay(LocalDate.of(2024, 1, 7))); // Sunday
        }
    }
}