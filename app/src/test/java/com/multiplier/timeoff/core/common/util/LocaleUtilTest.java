package com.multiplier.timeoff.core.common.util;

import com.multiplier.timeoff.types.CountryCode;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class LocaleUtilTest {

    @Test
    void testCreateDefaultWithCountryIsoAlpha3CodeAndLanguage() {
        var locale = LocaleUtil.createDefault("USA", "en");

        assertNotNull(locale);
        assertEquals("en", locale.getLanguage());
        assertEquals("US", locale.getCountry());
        // Add more test cases for other scenarios
    }

    @Test
    void testCreateDefaultWithCountryCodeAndLanguage() {
        var locale = LocaleUtil.createDefault(CountryCode.USA, "en");

        assertNotNull(locale);
        assertEquals("en", locale.getLanguage());
        assertEquals("US", locale.getCountry());
        // Add more test cases for other scenarios
    }
}
