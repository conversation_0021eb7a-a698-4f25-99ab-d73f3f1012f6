package com.multiplier.timeoff.core.util;

import com.multiplier.timeoff.repository.model.CarryForwardConfigEntity;
import com.multiplier.timeoff.repository.model.CarryForwardLimitEntity;
import com.multiplier.timeoff.repository.model.CarryForwardLimitValueType;
import com.multiplier.timeoff.repository.model.TimeoffSummaryDBO;
import com.multiplier.timeoff.types.TimeOffUnit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class TimeoffCarryForwardUtilTest {

    @Nested
    class GetMaximumCarryForwardCountTests {

        @Test
        void should_return_zero_when_config_is_null() {
            // Given - null config

            // When
            Double result = TimeoffCarryForwardUtil.getMaximumCarryForwardCount(null);

            // Then
            assertEquals(0.0, result);
        }

        @Test
        void should_return_max_value_when_max_limit_is_null() {
            // Given
            CarryForwardConfigEntity config = new CarryForwardConfigEntity(
                    true,
                    null,
                    null,
                    null
            );

            // When
            Double result = TimeoffCarryForwardUtil.getMaximumCarryForwardCount(config);

            // Then
            assertEquals(Double.MAX_VALUE, result);
        }

        @Test
        void should_return_max_value_when_max_limit_value_is_null() {
            // Given
            CarryForwardLimitEntity maxLimit = new CarryForwardLimitEntity(
                    CarryForwardLimitValueType.FIXED,
                    null,
                    TimeOffUnit.DAYS
            );

            CarryForwardConfigEntity config = new CarryForwardConfigEntity(
                    true,
                    null,
                    maxLimit,
                    null
            );

            // When
            Double result = TimeoffCarryForwardUtil.getMaximumCarryForwardCount(config);

            // Then
            assertEquals(Double.MAX_VALUE, result);
        }

        @Test
        void should_return_configured_value_when_max_limit_value_is_set() {
            // Given
            double expectedValue = 10.0;
            CarryForwardLimitEntity maxLimit = new CarryForwardLimitEntity(
                    CarryForwardLimitValueType.FIXED,
                    expectedValue,
                    TimeOffUnit.DAYS
            );

            CarryForwardConfigEntity config = new CarryForwardConfigEntity(
                    true,
                    null,
                    maxLimit,
                    null
            );

            // When
            Double result = TimeoffCarryForwardUtil.getMaximumCarryForwardCount(config);

            // Then
            assertEquals(expectedValue, result);
        }

        @Test
        void should_return_zero_when_carry_forward_is_disabled() {
            // Given
            CarryForwardConfigEntity config = new CarryForwardConfigEntity(
                    false,
                    null,
                    null,
                    null
            );

            // When
            Double result = TimeoffCarryForwardUtil.getMaximumCarryForwardCount(config);

            // Then
            assertEquals(0.0, result);
        }

        @Test
        void should_return_zero_when_carry_forward_is_disabled_even_with_max_limit_set() {
            // Given
            CarryForwardLimitEntity maxLimit = new CarryForwardLimitEntity(
                    CarryForwardLimitValueType.FIXED,
                    15.0,
                    TimeOffUnit.DAYS
            );

            CarryForwardConfigEntity config = new CarryForwardConfigEntity(
                    false,
                    null,
                    maxLimit,
                    null
            );

            // When
            Double result = TimeoffCarryForwardUtil.getMaximumCarryForwardCount(config);

            // Then
            assertEquals(0.0, result);
        }

        @Test
        void should_handle_percentage_type_limit_correctly() {
            // Given
            double expectedValue = 25.5;
            CarryForwardLimitEntity maxLimit = new CarryForwardLimitEntity(
                    CarryForwardLimitValueType.PERCENTAGE,
                    expectedValue,
                    TimeOffUnit.DAYS
            );

            CarryForwardConfigEntity config = new CarryForwardConfigEntity(
                    true,
                    null,
                    maxLimit,
                    null
            );

            // When
            Double result = TimeoffCarryForwardUtil.getMaximumCarryForwardCount(config);

            // Then
            assertEquals(expectedValue, result);
        }

        @Test
        void should_handle_zero_value_limit() {
            // Given
            CarryForwardLimitEntity maxLimit = new CarryForwardLimitEntity(
                    CarryForwardLimitValueType.FIXED,
                    0.0,
                    TimeOffUnit.DAYS
            );

            CarryForwardConfigEntity config = new CarryForwardConfigEntity(
                    true,
                    null,
                    maxLimit,
                    null
            );

            // When
            Double result = TimeoffCarryForwardUtil.getMaximumCarryForwardCount(config);

            // Then
            assertEquals(0.0, result);
        }
    }

    @Nested
    class CalculateNextCycleCarryForwardBalanceTests {

        private TimeoffSummaryDBO summaryDBO;
        private CarryForwardConfigEntity carryForwardConfig;

        @BeforeEach
        void setUp() {
            // Create a default summary with positive balance
            summaryDBO = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .allocatedCount(20.0)
                    .carryForwardCount(5.0)
                    .carryForwardExpiredCount(0.0)
                    .usedFromAllocatedCount(8.0)
                    .usedFromCarryForwardCount(2.0)
                    .usedFromNextCycleCarryForwardCount(0.0)
                    .usedFromLapsableCount(0.0)
                    .build();

            // Create a default enabled carry forward config
            CarryForwardLimitEntity maxLimit = new CarryForwardLimitEntity(
                    CarryForwardLimitValueType.FIXED,
                    10.0,
                    TimeOffUnit.DAYS
            );

            carryForwardConfig = new CarryForwardConfigEntity(
                    true,
                    null,
                    maxLimit,
                    null
            );
        }

        @Test
        void should_return_zero_when_config_is_null() {
            // Given - null config

            // When
            Double result = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(null, summaryDBO);

            // Then
            assertEquals(0.0, result);
        }

        @Test
        void should_return_zero_when_carry_forward_is_disabled() {
            // Given
            CarryForwardConfigEntity disabledConfig = new CarryForwardConfigEntity(
                    false,
                    null,
                    null,
                    null
            );

            // When
            Double result = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(disabledConfig, summaryDBO);

            // Then
            assertEquals(0.0, result);
        }

        @Test
        void should_return_zero_when_summary_has_zero_balance() {
            // Given
            TimeoffSummaryDBO zeroBalanceSummary = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .allocatedCount(10.0)
                    .carryForwardCount(0.0)
                    .carryForwardExpiredCount(0.0)
                    .usedFromAllocatedCount(10.0)
                    .usedFromCarryForwardCount(0.0)
                    .usedFromNextCycleCarryForwardCount(0.0)
                    .usedFromLapsableCount(0.0)
                    .build();

            // When
            Double result = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(carryForwardConfig, zeroBalanceSummary);

            // Then
            assertEquals(0.0, result);
        }

        @Test
        void should_return_zero_when_summary_has_negative_balance() {
            // Given
            TimeoffSummaryDBO negativeBalanceSummary = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .allocatedCount(10.0)
                    .carryForwardCount(0.0)
                    .carryForwardExpiredCount(0.0)
                    .usedFromAllocatedCount(15.0)
                    .usedFromCarryForwardCount(0.0)
                    .usedFromNextCycleCarryForwardCount(0.0)
                    .usedFromLapsableCount(0.0)
                    .build();

            // When
            Double result = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(carryForwardConfig, negativeBalanceSummary);

            // Then
            assertEquals(0.0, result);
        }

        @Test
        void should_calculate_correct_balance_when_current_balance_is_less_than_max_limit() {
            // Given - current summary balance = 15.0 (20 + 5 - 8 - 2), max limit = 10.0, used from next cycle = 0.0
            // Expected: min(15.0, 10.0) - 0.0 = 10.0

            // When
            Double result = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(carryForwardConfig, summaryDBO);

            // Then
            assertEquals(10.0, result);
        }

        @Test
        void should_calculate_correct_balance_when_current_balance_is_greater_than_max_limit() {
            // Given
            CarryForwardLimitEntity smallMaxLimit = new CarryForwardLimitEntity(
                    CarryForwardLimitValueType.FIXED,
                    5.0,
                    TimeOffUnit.DAYS
            );

            CarryForwardConfigEntity configWithSmallLimit = new CarryForwardConfigEntity(
                    true,
                    null,
                    smallMaxLimit,
                    null
            );

            // Current summary balance = 15.0, max limit = 5.0, used from next cycle = 0.0
            // Expected: min(15.0, 5.0) - 0.0 = 5.0

            // When
            Double result = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(configWithSmallLimit, summaryDBO);

            // Then
            assertEquals(5.0, result);
        }

        @Test
        void should_subtract_used_from_next_cycle_carry_forward_amount() {
            // Given
            TimeoffSummaryDBO summaryWithUsedNextCycle = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .allocatedCount(20.0)
                    .carryForwardCount(5.0)
                    .carryForwardExpiredCount(0.0)
                    .usedFromAllocatedCount(8.0)
                    .usedFromCarryForwardCount(2.0)
                    .usedFromNextCycleCarryForwardCount(3.0)
                    .usedFromLapsableCount(0.0)
                    .build();

            // Current summary balance = 15.0, max limit = 10.0, used from next cycle = 3.0
            // Expected: min(15.0, 10.0) - 3.0 = 7.0

            // When
            Double result = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(carryForwardConfig, summaryWithUsedNextCycle);

            // Then
            assertEquals(7.0, result);
        }

        @Test
        void should_handle_unlimited_carry_forward_when_max_limit_is_null() {
            // Given
            CarryForwardConfigEntity unlimitedConfig = new CarryForwardConfigEntity(
                    true,
                    null,
                    null,
                    null
            );

            // Current summary balance = 15.0, max limit = Double.MAX_VALUE, used from next cycle = 0.0
            // Expected: min(15.0, Double.MAX_VALUE) - 0.0 = 15.0

            // When
            Double result = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(unlimitedConfig, summaryDBO);

            // Then
            assertEquals(15.0, result);
        }

        @Test
        void should_handle_case_when_result_would_be_negative() {
            // Given
            TimeoffSummaryDBO summaryWithHighUsage = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .allocatedCount(20.0)
                    .carryForwardCount(5.0)
                    .carryForwardExpiredCount(0.0)
                    .usedFromAllocatedCount(8.0)
                    .usedFromCarryForwardCount(2.0)
                    .usedFromNextCycleCarryForwardCount(12.0)
                    .usedFromLapsableCount(0.0)
                    .build();

            // Current summary balance = 15.0, max limit = 10.0, used from next cycle = 12.0
            // Expected: min(15.0, 10.0) - 12.0 = -2.0

            // When
            Double result = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(carryForwardConfig, summaryWithHighUsage);

            // Then
            assertEquals(-2.0, result);
        }

        @Test
        void should_handle_null_values_in_summary_gracefully() {
            // Given
            TimeoffSummaryDBO summaryWithNulls = TimeoffSummaryDBO.builder()
                    .id(1L)
                    .allocatedCount(null)
                    .carryForwardCount(null)
                    .carryForwardExpiredCount(null)
                    .usedFromAllocatedCount(null)
                    .usedFromCarryForwardCount(null)
                    .usedFromNextCycleCarryForwardCount(null)
                    .usedFromLapsableCount(null)
                    .build();

            // When & Then - Should not throw exception
            assertDoesNotThrow(() -> {
                Double result = TimeoffCarryForwardUtil.calculateNextCycleCarryForwardBalance(carryForwardConfig, summaryWithNulls);
                // The result depends on how TimeoffSummaryDBO handles null values in its calculation methods
                assertNotNull(result);
            });
        }
    }
}