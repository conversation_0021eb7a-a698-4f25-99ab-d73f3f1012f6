package com.multiplier.timeoff.core.util;

import com.multiplier.timeoff.core.common.dto.WorkshiftDTO;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Stream;
import lombok.val;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
public class WorkshiftUtilTest {

    @Nested
    class IsRestDay {

        static Stream<Arguments> normalWorkWeekTestCases() {
            LocalDate monday = LocalDate.of(2024, 1, 1); // Monday
            return Stream.of(Arguments.of(monday, false), // Monday - working day
                Arguments.of(monday.plusDays(1), false), // Tuesday - working day
                Arguments.of(monday.plusDays(2), false), // Wednesday - working day
                Arguments.of(monday.plusDays(3), false), // Thursday - working day
                Arguments.of(monday.plusDays(4), false), // Friday - working day
                Arguments.of(monday.plusDays(5), true), // Saturday - rest day
                Arguments.of(monday.plusDays(6), true)  // Sunday - rest day
            );
        }

        static Stream<Arguments> wrappedWorkWeekTestCases() {
            LocalDate monday = LocalDate.of(2024, 1, 1); // Monday
            return Stream.of(Arguments.of(monday, false), // Monday - working day
                Arguments.of(monday.plusDays(1), false), // Tuesday - working day
                Arguments.of(monday.plusDays(2), true), // Wednesday - rest day
                Arguments.of(monday.plusDays(3), true), // Thursday - rest day
                Arguments.of(monday.plusDays(4), false), // Friday - working day
                Arguments.of(monday.plusDays(5), false), // Saturday - working day
                Arguments.of(monday.plusDays(6), false)  // Sunday - working day
            );
        }

        @ParameterizedTest
        @MethodSource("normalWorkWeekTestCases")
        @DisplayName("Should correctly identify rest days for normal work week (Monday to Friday)")
        void should_correctly_identify_rest_days_for_normal_work_week(LocalDate date, boolean expectedIsRestDay) {
            // Given
            val workshift = WorkshiftDTO.builder().startDate(DayOfWeek.MONDAY).endDate(DayOfWeek.FRIDAY).build();

            // When
            boolean result = WorkshiftUtil.isRestDay(date, workshift);

            // Then
            assertEquals(expectedIsRestDay, result);
        }

        @ParameterizedTest
        @MethodSource("wrappedWorkWeekTestCases")
        @DisplayName("Should correctly identify rest days for wrapped work week (Friday to Tuesday)")
        void should_correctly_identify_rest_days_for_wrapped_work_week(LocalDate date, boolean expectedIsRestDay) {
            // Given
            val workshift = WorkshiftDTO.builder().startDate(DayOfWeek.FRIDAY).endDate(DayOfWeek.TUESDAY).build();

            // When
            boolean result = WorkshiftUtil.isRestDay(date, workshift);

            // Then
            assertEquals(expectedIsRestDay, result);
        }

    }

    @Nested
    class GetRestDaysListForRange {

        @Test
        @DisplayName("Should return weekend rest days for normal work week over one week range")
        void should_return_weekend_rest_days_for_normal_work_week() {
            // Given
            val workshift = WorkshiftDTO.builder().startDate(DayOfWeek.MONDAY).endDate(DayOfWeek.FRIDAY).build();

            LocalDate startDate = LocalDate.of(2024, 1, 1); // Monday
            LocalDate endDate = LocalDate.of(2024, 1, 7); // Sunday

            // When
            List<LocalDate> restDays = WorkshiftUtil.getRestDaysListForRange(startDate, endDate, workshift);

            // Then
            assertEquals(2, restDays.size());
            assertTrue(restDays.contains(LocalDate.of(2024, 1, 6))); // Saturday
            assertTrue(restDays.contains(LocalDate.of(2024, 1, 7))); // Sunday
        }

        @Test
        @DisplayName("Should return mid-week rest days for wrapped work week")
        void should_return_mid_week_rest_days_for_wrapped_work_week() {
            // Given
            val workshift = WorkshiftDTO.builder().startDate(DayOfWeek.FRIDAY).endDate(DayOfWeek.TUESDAY).build();

            LocalDate startDate = LocalDate.of(2024, 1, 1); // Monday
            LocalDate endDate = LocalDate.of(2024, 1, 7); // Sunday

            // When
            List<LocalDate> restDays = WorkshiftUtil.getRestDaysListForRange(startDate, endDate, workshift);

            // Then
            assertEquals(2, restDays.size());
            assertTrue(restDays.contains(LocalDate.of(2024, 1, 3))); // Wednesday
            assertTrue(restDays.contains(LocalDate.of(2024, 1, 4))); // Thursday
        }

        @Test
        @DisplayName("Should return empty list when no rest days in range")
        void should_return_empty_list_when_no_rest_days_in_range() {
            // Given
            val workshift = WorkshiftDTO.builder().startDate(DayOfWeek.MONDAY).endDate(DayOfWeek.FRIDAY).build();

            LocalDate startDate = LocalDate.of(2024, 1, 1); // Monday
            LocalDate endDate = LocalDate.of(2024, 1, 5); // Friday

            // When
            List<LocalDate> restDays = WorkshiftUtil.getRestDaysListForRange(startDate, endDate, workshift);

            // Then
            assertTrue(restDays.isEmpty());
        }

        @Test
        @DisplayName("Should handle single day range correctly")
        void should_handle_single_day_range_correctly() {
            // Given
            val workshift = WorkshiftDTO.builder().startDate(DayOfWeek.MONDAY).endDate(DayOfWeek.FRIDAY).build();

            LocalDate singleDay = LocalDate.of(2024, 1, 6); // Saturday

            // When
            List<LocalDate> restDays = WorkshiftUtil.getRestDaysListForRange(singleDay, singleDay, workshift);

            // Then
            assertEquals(1, restDays.size());
            assertTrue(restDays.contains(singleDay));
        }

        @Test
        @DisplayName("Should handle multiple weeks range correctly")
        void should_handle_multiple_weeks_range_correctly() {
            // Given
            val workshift = WorkshiftDTO.builder().startDate(DayOfWeek.MONDAY).endDate(DayOfWeek.FRIDAY).build();

            LocalDate startDate = LocalDate.of(2024, 1, 1); // Monday
            LocalDate endDate = LocalDate.of(2024, 1, 14); // Sunday (2 weeks)

            // When
            List<LocalDate> restDays = WorkshiftUtil.getRestDaysListForRange(startDate, endDate, workshift);

            // Then
            assertEquals(4, restDays.size()); // 2 weekends = 4 days
            assertTrue(restDays.contains(LocalDate.of(2024, 1, 6))); // First Saturday
            assertTrue(restDays.contains(LocalDate.of(2024, 1, 7))); // First Sunday
            assertTrue(restDays.contains(LocalDate.of(2024, 1, 13))); // Second Saturday
            assertTrue(restDays.contains(LocalDate.of(2024, 1, 14))); // Second Sunday
        }

    }

    @Nested
    class EdgeCases {

        static Stream<Arguments> singleDayWorkWeekTestCases() {
            LocalDate monday = LocalDate.of(2024, 1, 1); // Monday
            return Stream.of(Arguments.of(DayOfWeek.MONDAY, monday, false), // Monday work day, testing Monday
                Arguments.of(DayOfWeek.MONDAY, monday.plusDays(1), true), // Monday work day, testing Tuesday
                Arguments.of(DayOfWeek.FRIDAY, monday.plusDays(4), false), // Friday work day, testing Friday
                Arguments.of(DayOfWeek.FRIDAY, monday.plusDays(5), true) // Friday work day, testing Saturday
            );
        }

        @ParameterizedTest
        @MethodSource("singleDayWorkWeekTestCases")
        @DisplayName("Should handle single day work weeks correctly")
        void should_handle_single_day_work_weeks_correctly(DayOfWeek workDay, LocalDate testDate,
            boolean expectedIsRestDay) {
            // Given
            val workshift = WorkshiftDTO.builder().startDate(workDay).endDate(workDay).build();

            // When
            boolean result = WorkshiftUtil.isRestDay(testDate, workshift);

            // Then
            assertEquals(expectedIsRestDay, result);
        }

        @Test
        @DisplayName("Should handle full week work schedule correctly")
        void should_handle_full_week_work_schedule_correctly() {
            // Given
            val workshift = WorkshiftDTO.builder().startDate(DayOfWeek.SUNDAY).endDate(DayOfWeek.SATURDAY).build();

            LocalDate startDate = LocalDate.of(2024, 1, 1); // Monday
            LocalDate endDate = LocalDate.of(2024, 1, 7); // Sunday

            // When
            List<LocalDate> restDays = WorkshiftUtil.getRestDaysListForRange(startDate, endDate, workshift);

            // Then
            assertTrue(restDays.isEmpty()); // No rest days in a full week work schedule
        }

    }

}
