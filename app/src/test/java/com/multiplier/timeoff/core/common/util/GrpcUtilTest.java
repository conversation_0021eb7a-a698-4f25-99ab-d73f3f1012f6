package com.multiplier.timeoff.core.common.util;

import com.google.protobuf.Timestamp;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class GrpcUtilTest {

    @Test
    void testMapToTimestampWithLocalDate() {
        LocalDate localDate = LocalDate.of(2022, 1, 1);
        Timestamp timestamp = GrpcUtil.mapToTimestamp(localDate);

        assertNotNull(timestamp);
        assertEquals(localDate.atStartOfDay().toEpochSecond(ZoneOffset.UTC), timestamp.getSeconds());
        assertEquals(localDate.atStartOfDay().getNano(), timestamp.getNanos());
    }

    @Test
    void testMapToTimestampWithLocalDateNull() {
        Timestamp timestamp = GrpcUtil.mapToTimestamp((LocalDate) null);

        assertNotNull(timestamp);
        assertEquals(Timestamp.getDefaultInstance(), timestamp);
    }

    @Test
    void testMapToTimestampWithInstant() {
        Instant instant = Instant.now();
        Timestamp timestamp = GrpcUtil.mapToTimestamp(instant);

        assertNotNull(timestamp);
        assertEquals(instant.getEpochSecond(), timestamp.getSeconds());
        assertEquals(instant.getNano(), timestamp.getNanos());
    }

    @Test
    void testMapToTimestampWithInstantNull() {
        Timestamp timestamp = GrpcUtil.mapToTimestamp((Instant) null);

        assertNotNull(timestamp);
        assertEquals(Timestamp.getDefaultInstance(), timestamp);
    }
}
