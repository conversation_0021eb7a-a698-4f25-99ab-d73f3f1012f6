package com.multiplier.timeoff.core.security;

import com.multiplier.common.transport.user.CurrentUser;
import com.multiplier.common.transport.user.UserContext;
import com.multiplier.common.transport.user.UserScopes;
import com.multiplier.common.transport.user.attribute.ContractAccessService;
import com.multiplier.company.schema.grpc.CompanyOuterClass;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ApprovalServiceAdapter;
import com.multiplier.timeoff.adapters.CompanyServiceAdapter;
import com.multiplier.timeoff.repository.model.TimeoffDBO;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.val;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.access.AccessDeniedException;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)
class AuthorizationServiceTest {

    @Mock
    private ContractAccessService contractAccessService;
    @Mock
    private CurrentUser currentUser;
    @Mock
    private CompanyServiceAdapter companyServiceAdapter;
    @Mock
    private ApprovalServiceAdapter approvalServiceAdapter;
    @InjectMocks
    private AuthorizationService authorizationService;

    @Nested
    class FilterTimeOffsTest {

        @Test
        void should_return_empty_list_when_timeOffs_are_empty() {
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            List<TimeoffDBO> timeOffs = Collections.emptyList();

            List<TimeoffDBO> result = authorizationService.filterTimeOffs(dfe, timeOffs);

            assertTrue(result.isEmpty());
        }

        @Test
        void should_filter_timeoffs_when_contracts_are_present() {
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            TimeoffDBO timeOff1 = TimeoffDBO.builder()
                    .contractId(1L)
                    .build();
            TimeoffDBO timeOff2 = TimeoffDBO.builder()
                    .contractId(2L)
                    .build();
            TimeoffDBO timeOff3 = TimeoffDBO.builder()
                    .contractId(3L)
                    .build();

            List<TimeoffDBO> timeOffs = Arrays.asList(timeOff1, timeOff2, timeOff3);

            Set<Long> timeoffContractIds = Set.of(1L, 2L, 3L);
            when(contractAccessService.applyContractAttributes(dfe, timeoffContractIds)).thenReturn(Set.of(1L, 3L));

            List<TimeoffDBO> result = authorizationService.filterTimeOffs(dfe, timeOffs);

            assertEquals(2, result.size());
            assertTrue(result.contains(timeOff1));
            assertTrue(result.contains(timeOff3));
        }

        @Test
        void should_throw_error_when_calling_contract_access_service() {
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            TimeoffDBO timeOff1 = TimeoffDBO.builder()
                    .contractId(1L)
                    .build();
            TimeoffDBO timeOff2 = TimeoffDBO.builder()
                    .contractId(2L)
                    .build();
            TimeoffDBO timeOff3 = TimeoffDBO.builder()
                    .contractId(3L)
                    .build();

            List<TimeoffDBO> timeOffs = Arrays.asList(timeOff1, timeOff2, timeOff3);

            Set<Long> timeoffContractIds = Set.of(1L, 2L, 3L);
            when(contractAccessService.applyContractAttributes(dfe, timeoffContractIds)).thenThrow(
                    new RuntimeException("Runtime Exception")
            );

            List<TimeoffDBO> results = authorizationService.filterTimeOffs(dfe, timeOffs);

            assertTrue(results.isEmpty());
        }

    }

    @Nested
    class AuthorizeTest {

        @Test
        void should_throw_access_denied_error_if_member_id_does_not_match_in_scopes() {
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            long contractMemberId = 45L;

            ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .setMemberId(contractMemberId)
                    .build();

            when(currentUser.getContext()).thenReturn(getMemberUserContext(10L, 47L)); // 47L ≠ 45L

            val ex = assertThrows(AccessDeniedException.class, () -> authorizationService.authorize(dfe, contract));

            assertEquals("Access denied for user id : 10", ex.getMessage());
            verifyNoInteractions(contractAccessService); // Should not reach access check
        }

        @Test
        void should_authorize_if_member_id_matches_in_scopes() {
            long memberId = 45L;
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .setMemberId(memberId)
                    .build();

            when(currentUser.getContext()).thenReturn(getMemberUserContext(10L, memberId));

            // Should not throw exception
            authorizationService.authorize(dfe, contract);

            verifyNoInteractions(contractAccessService); // Should not check access
        }

        @Test
        void should_throw_access_denied_if_user_does_not_have_access_and_is_not_approver() {
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserContext(10L, 1L, 500L));
            when(contractAccessService.hasContractAccess(dfe, contract.getId())).thenReturn(false);

            // Mock company user as non-manager
            val companyUser = CompanyOuterClass.CompanyUser.newBuilder()
                    .setId(1L)
                    .setIsManager(false)
                    .build();
            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(companyUser);

            val ex = assertThrows(AccessDeniedException.class, () -> authorizationService.authorize(dfe, contract));

            assertEquals("Access denied for user id : 10", ex.getMessage());
        }

        @Test
        void should_authorize_if_user_has_access_to_contract() {
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserContext(10L, 1L, 500L));
            when(contractAccessService.hasContractAccess(dfe, contract.getId())).thenReturn(true);

            authorizationService.authorize(dfe, contract);

            verify(contractAccessService).hasContractAccess(dfe, contract.getId());
        }

        @Test
        void should_authorize_if_user_is_approver_for_contract() {
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserContext(10L, 2L, 500L));
            when(contractAccessService.hasContractAccess(dfe, contract.getId())).thenReturn(false);

            // Mock company user as manager with managed contracts
            val companyUser = CompanyOuterClass.CompanyUser.newBuilder()
                    .setId(2L)
                    .setIsManager(true)
                    .build();
            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(companyUser);
            when(approvalServiceAdapter.getManagedContractIds(companyUser.getId())).thenReturn(List.of(contract.getId()));

            authorizationService.authorize(dfe, contract);

            verify(contractAccessService).hasContractAccess(dfe, contract.getId());
            verify(companyServiceAdapter).getCompanyUser(any());
            verify(approvalServiceAdapter).getManagedContractIds(companyUser.getId());
        }

        @Test
        void should_throw_access_denied_if_manager_is_not_approver_for_contract() {
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);

            ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .build();

            when(currentUser.getContext()).thenReturn(getCompanyUserUserContext(10L, 2L, 500L));
            when(contractAccessService.hasContractAccess(dfe, contract.getId())).thenReturn(false);

            // Mock company user as manager but not managing this contract
            val companyUser = CompanyOuterClass.CompanyUser.newBuilder()
                    .setId(2L)
                    .setIsManager(true)
                    .build();
            when(companyServiceAdapter.getCompanyUser(any())).thenReturn(companyUser);
            when(approvalServiceAdapter.getManagedContractIds(companyUser.getId())).thenReturn(List.of(999L)); // Different contract

            val ex = assertThrows(AccessDeniedException.class, () -> authorizationService.authorize(dfe, contract));

            assertEquals("Access denied for user id : 10", ex.getMessage());
        }

        @Test
        void should_return_false_when_member_checks_if_approver_for_contract() throws Exception {
            // Test the defensive condition in isApproverForContract method
            // Members cannot be timeoff approvers, so the method should return false
            long contractId = 1L;
            long memberId = 45L;

            when(currentUser.getContext()).thenReturn(getMemberUserContext(10L, memberId));

            // Use reflection to access the private isApproverForContract method
            Method isApproverForContractMethod = AuthorizationService.class.getDeclaredMethod("isApproverForContract", Long.class);
            isApproverForContractMethod.setAccessible(true);

            boolean result = (boolean) isApproverForContractMethod.invoke(authorizationService, contractId);

            assertFalse(result, "Member should not be able to be an approver for any contract");
            // Should not interact with company service or approval service for members
            verifyNoInteractions(companyServiceAdapter);
            verifyNoInteractions(approvalServiceAdapter);
        }

        @Test
        void should_return_empty_list_when_contract_ids_are_empty() {
            DgsDataFetchingEnvironment dfe = mock(DgsDataFetchingEnvironment.class);
            List<Long> contractIds = Collections.emptyList();

            var result = authorizationService.filterAccessibleContractIds(dfe, contractIds);

            assertTrue(result.isEmpty());
            verifyNoInteractions(contractAccessService);
        }

    }

    private UserContext getCompanyUserUserContext(Long userId, Long companyUserId, Long companyId) {
        return new UserContext(
                userId,
                "username",
                "company",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        null,
                        companyId,
                        null,
                        companyUserId,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

    private UserContext getMemberUserContext(Long userId, Long memberId) {
        return new UserContext(
                userId,
                "username",
                "member",
                Collections.emptySet(),
                "signupChannel",
                "firstName",
                "lastName",
                "auth",
                new UserScopes(
                        memberId,
                        10L,
                        null,
                        null,
                        null,
                        null,
                        false
                ),
                "type",
                List.of()
        );
    }

}
