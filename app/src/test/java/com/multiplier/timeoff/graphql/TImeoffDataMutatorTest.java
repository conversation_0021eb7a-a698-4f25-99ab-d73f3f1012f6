package com.multiplier.timeoff.graphql;

import com.multiplier.timeoff.processor.AllocationProcessor;
import com.multiplier.timeoff.processor.CarryForwardExpirationProcessor;
import com.multiplier.timeoff.processor.CarryForwardProcessor;
import com.multiplier.timeoff.processor.FixAllocationProcessor;
import com.multiplier.timeoff.service.DefinitionService;
import com.multiplier.timeoff.service.TimeoffService;
import com.multiplier.timeoff.service.TimeoffSummaryService;
import com.multiplier.timeoff.service.TimeoffTypeService;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TimeoffDataMutatorTest {

    @Mock
    private TimeoffService timeoffService;

    @Mock
    private AllocationProcessor allocationProcessor;

    @Mock
    private CarryForwardProcessor carryForwardProcessor;

    @Mock
    private CarryForwardExpirationProcessor carryForwardExpirationProcessor;

    @Mock
    private TimeoffTypeService timeoffTypeService;

    @Mock
    private DefinitionService definitionService;

    @Mock
    private TimeoffSummaryService timeoffSummaryService;

    @Mock
    private DgsDataFetchingEnvironment dfe;

    @Mock
    private FixAllocationProcessor fixAllocationProcessor;

    @InjectMocks
    private TimeoffDataMutator timeoffDataMutator;


    @Nested
    class SaveAsDraft {
        @Test
        void shouldSaveTimeOffAsDraft() {
            // Given
            TimeOffSaveAsDraftInput input = TimeOffSaveAsDraftInput.newBuilder().build();
            TimeOff expectedTimeOff = TimeOff.newBuilder().id(1L).build();

            when(timeoffService.saveAsDraft(any(), any())).thenReturn(expectedTimeOff);

            // When
            TimeOff result = timeoffDataMutator.saveAsDraft(input, dfe);

            // Then
            assertNotNull(result);
            assertEquals(expectedTimeOff, result);
            verify(timeoffService).saveAsDraft(input, dfe);
        }
    }

    @Nested
    class RevokeTimeOff {
        @Test
        void shouldRevokeTimeOff() {
            // Given
            Long id = 1L;
            TimeOff expectedTimeOff = TimeOff.newBuilder().id(id).build();

            when(timeoffService.revoke(any(), any())).thenReturn(expectedTimeOff);

            // When
            TimeOff result = timeoffDataMutator.revoke(id, dfe);

            // Then
            assertNotNull(result);
            assertEquals(expectedTimeOff, result);
            verify(timeoffService).revoke(dfe, id);
        }
    }

    @Nested
    class UpdateTimeOff {
        @Test
        void shouldUpdateTimeOff() {
            // Given
            Long id = 1L;
            TimeOffUpdateInput input = TimeOffUpdateInput.newBuilder().build();
            TimeOff expectedTimeOff = TimeOff.newBuilder().id(id).build();

            when(timeoffService.update(any(), any(), any())).thenReturn(expectedTimeOff);

            // When
            TimeOff result = timeoffDataMutator.update(id, input, dfe);

            // Then
            assertNotNull(result);
            assertEquals(expectedTimeOff, result);
            verify(timeoffService).update(id, input, dfe);
        }
    }

    @Nested
    class DeleteTimeOff {
        @Test
        void shouldDeleteTimeOff() {
            // Given
            Long id = 1L;

            // When
            TaskResponse result = timeoffDataMutator.delete(id, dfe);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Successfully deleted Time-off.", result.getMessage());
            verify(timeoffService).delete(id, dfe);
        }
    }

    @Nested
    class TriggerTimeoffAllocation {
        @Test
        void shouldTriggerTimeoffAllocationSuccessfully() {
            // Given
            TimeOffAllocationInput input = TimeOffAllocationInput.newBuilder().build();

            // When
            TaskResponse result = timeoffDataMutator.triggerTimeoffAllocation(input);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Timeoff balance allocated successfully.", result.getMessage());
            verify(allocationProcessor).allocateTimeoffBalances(input);
        }

        @Test
        void shouldHandleAllocationFailure() {
            // Given
            TimeOffAllocationInput input = TimeOffAllocationInput.newBuilder().build();
            RuntimeException exception = new RuntimeException("Allocation failed");
            doThrow(exception).when(allocationProcessor).allocateTimeoffBalances(input);

            // When
            TaskResponse result = timeoffDataMutator.triggerTimeoffAllocation(input);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("Timeoff balance allocation failed"));
        }
    }

    @Nested
    class TriggerTimeoffCarryForward {
        @Test
        void shouldTriggerTimeoffCarryForwardSuccessfully() {
            // Given
            TimeOffAllocationInput input = TimeOffAllocationInput.newBuilder().build();

            // When
            TaskResponse result = timeoffDataMutator.triggerTimeoffCarryForward(input);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Timeoff balance carried successfully.", result.getMessage());
            verify(carryForwardProcessor).carryForwardTimeoffBalances(input);
        }

        @Test
        void shouldHandleCarryForwardFailure() {
            // Given
            TimeOffAllocationInput input = TimeOffAllocationInput.newBuilder().build();
            RuntimeException exception = new RuntimeException("Carry forward failed");
            doThrow(exception).when(carryForwardProcessor).carryForwardTimeoffBalances(input);

            // When
            TaskResponse result = timeoffDataMutator.triggerTimeoffCarryForward(input);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("Timeoff balance carry forward failed"));
        }
    }

    @Nested
    class TriggerCarryForwardExpiration {
        @Test
        void shouldTriggerCarryForwardExpirationSuccessfully() {
            // Given
            TimeOffAllocationInput input = TimeOffAllocationInput.newBuilder().build();

            // When
            TaskResponse result = timeoffDataMutator.triggerCarryForwardExpiration(input);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Carry forward expiration triggered successfully.", result.getMessage());
            verify(carryForwardExpirationProcessor).expireCarryForwardTimeoffBalances(input);
        }

        @Test
        void shouldHandleExpirationFailure() {
            // Given
            TimeOffAllocationInput input = TimeOffAllocationInput.newBuilder().build();
            RuntimeException exception = new RuntimeException("Expiration failed");
            doThrow(exception).when(carryForwardExpirationProcessor).expireCarryForwardTimeoffBalances(input);

            // When
            TaskResponse result = timeoffDataMutator.triggerCarryForwardExpiration(input);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("Carry forward expiration failed"));
        }
    }

    @Nested
    class BulkRevokeTimeOff {
        @Test
        void shouldBulkRevokeTimeOffs() {
            // Given
            List<Long> ids = List.of(1L, 2L);
            TaskResponse expectedResponse = TaskResponse.newBuilder()
                    .success(true)
                    .message("Success")
                    .build();

            when(timeoffService.bulkRevokeTimeOffs(ids)).thenReturn(expectedResponse);

            // When
            TaskResponse result = timeoffDataMutator.timeOffBulkRevoke(ids);

            // Then
            assertNotNull(result);
            assertEquals(expectedResponse, result);
            verify(timeoffService).bulkRevokeTimeOffs(ids);
        }
    }

    @Nested
    class UpdateTimeOffSummary {
        @Test
        void shouldUpdateTimeOffSummarySuccessfully() {
            // Given
            List<UpdateTimeOffSummaryInput> input = List.of(UpdateTimeOffSummaryInput.newBuilder().build());

            // When
            TaskResponse result = timeoffDataMutator.updateTimeOffSummary(input);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Timeoff summaries are successfully updated.", result.getMessage());
            verify(timeoffSummaryService).updateTimeOffSummary(input);
        }

        @Test
        void shouldHandleUpdateTimeOffSummaryFailure() {
            // Given
            List<UpdateTimeOffSummaryInput> input = List.of(UpdateTimeOffSummaryInput.newBuilder().build());
            RuntimeException exception = new RuntimeException("Update failed");
            doThrow(exception).when(timeoffSummaryService).updateTimeOffSummary(input);

            // When
            TaskResponse result = timeoffDataMutator.updateTimeOffSummary(input);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("Summary update process failed"));
        }
    }

    @Nested
    class BackfillTimeoffAllocationRecords {
        @Test
        void shouldBackfillTimeoffAllocationRecordsSuccessfully() {
            // Given
            TimeOffAllocationInput input = TimeOffAllocationInput.newBuilder().build();

            // When
            TaskResponse result = timeoffDataMutator.backfillTimeoffAllocationRecords(input);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Timeoff allocation record backfilled successfully.", result.getMessage());
            verify(allocationProcessor).backfillAllocationRecords(input);
        }

        @Test
        void shouldHandleBackfillAllocationFailure() {
            // Given
            TimeOffAllocationInput input = TimeOffAllocationInput.newBuilder().build();
            RuntimeException exception = new RuntimeException("Backfill failed");
            doThrow(exception).when(allocationProcessor).backfillAllocationRecords(input);

            // When
            TaskResponse result = timeoffDataMutator.backfillTimeoffAllocationRecords(input);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("Timeoff allocation record backfill failed"));
        }
    }

    @Nested
    class BackfillTimeoffCarryForwardRecords {

        @Test
        void shouldHandleBackfillCarryForwardFailure() {
            // Given
            TimeOffAllocationInput input = TimeOffAllocationInput.newBuilder().build();
            RuntimeException exception = new RuntimeException("Backfill failed");
            doThrow(exception).when(carryForwardProcessor).backfillCarryForwardRecords(input);

            // When
            TaskResponse result = timeoffDataMutator.backfillTimeoffCarryForwardRecords(input);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("Timeoff carry forward record backfill failed"));
        }
    }

    @Nested
    class BackfillTimeoffApprovedOn {
        @Test
        void shouldBackfillTimeoffApprovedOnSuccessfully() {
            // Given
            List<Long> ids = List.of(1L, 2L);

            // When
            TaskResponse result = timeoffDataMutator.backfillTimeoffApprovedOn(ids);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Timeoff.approved_on backfilled successfully.", result.getMessage());
            verify(timeoffService).backfillTimeoffApprovedOn(ids);
        }

        @Test
        void shouldHandleBackfillApprovedOnFailure() {
            // Given
            List<Long> ids = List.of(1L, 2L);
            RuntimeException exception = new RuntimeException("Backfill failed");
            doThrow(exception).when(timeoffService).backfillTimeoffApprovedOn(ids);

            // When
            TaskResponse result = timeoffDataMutator.backfillTimeoffApprovedOn(ids);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("Timeoff.approved_on backfill failed"));
        }
    }

    @Nested
    class FixEntitlementChangeRecords {
        @Test
        void shouldFixEntitlementChangeRecordsSuccessfully() {
            // Given
            List<Long> contractIds = List.of(1L, 2L);
            List<String> debuggingInfos = List.of("Info1", "Info2");
            when(timeoffService.fixEntitlementChangeRecords(any())).thenReturn(debuggingInfos);

            // When
            TaskResponse result = timeoffDataMutator.fixEntitlementChangeRecords(contractIds);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertTrue(result.getMessage().contains("Success with debugging infos"));
            verify(timeoffService).fixEntitlementChangeRecords(any());
        }

        @Test
        void shouldHandleFixEntitlementChangeRecordsFailure() {
            // Given
            List<Long> contractIds = List.of(1L, 2L);
            RuntimeException exception = new RuntimeException("Fix failed");
            doThrow(exception).when(timeoffService).fixEntitlementChangeRecords(any());

            // When
            TaskResponse result = timeoffDataMutator.fixEntitlementChangeRecords(contractIds);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("An exception occurred"));
        }
    }

    @Nested
    class ChangeTimeOffDateV2 {
        @Test
        void shouldChangeTimeOffDateV2Successfully() {
            // Given
            TimeOffChangeDateInput input = TimeOffChangeDateInput.newBuilder().build();
            List<String> debuggingInfos = List.of("Info1");
            when(timeoffService.changeTimeOffDateV2(any())).thenReturn(debuggingInfos);

            // When
            TaskResponse result = timeoffDataMutator.changeTimeOffDateV2(input);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertTrue(result.getMessage().contains("Success with debugging infos"));
            verify(timeoffService).changeTimeOffDateV2(input);
        }

        @Test
        void shouldHandleChangeTimeOffDateV2Failure() {
            // Given
            TimeOffChangeDateInput input = TimeOffChangeDateInput.newBuilder().build();
            RuntimeException exception = new RuntimeException("Change failed");
            doThrow(exception).when(timeoffService).changeTimeOffDateV2(any());

            // When
            TaskResponse result = timeoffDataMutator.changeTimeOffDateV2(input);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("An exception occurred"));
        }
    }

    @Nested
    class TimeOffTypeOperations {
        @Test
        void shouldCreateTimeOffType() {
            // Given
            TimeOffTypeCreateInput input = TimeOffTypeCreateInput.newBuilder().build();
            TimeOffTypeInfo expectedType = TimeOffTypeInfo.newBuilder().typeId(1L).build();
            when(timeoffTypeService.createTimeOffType(input)).thenReturn(expectedType);

            // When
            TimeOffTypeInfo result = timeoffDataMutator.timeOffTypeCreate(input);

            // Then
            assertNotNull(result);
            assertEquals(expectedType, result);
            verify(timeoffTypeService).createTimeOffType(input);
        }

        @Test
        void shouldUpdateTimeOffType() {
            // Given
            TimeOffTypeUpdateInput input = TimeOffTypeUpdateInput.newBuilder().build();
            TimeOffTypeInfo expectedType = TimeOffTypeInfo.newBuilder().typeId(1L).build();
            when(timeoffTypeService.updateTimeOffType(input)).thenReturn(expectedType);

            // When
            TimeOffTypeInfo result = timeoffDataMutator.timeOffTypeUpdate(input);

            // Then
            assertNotNull(result);
            assertEquals(expectedType, result);
            verify(timeoffTypeService).updateTimeOffType(input);
        }

        @Test
        void shouldDeleteTimeOffType() {
            // Given
            Long id = 1L;
            TimeOffTypeInfo expectedType = TimeOffTypeInfo.newBuilder().typeId(id).build();
            when(timeoffTypeService.deleteTimeOffType(id)).thenReturn(expectedType);

            // When
            TimeOffTypeInfo result = timeoffDataMutator.timeOffTypeDelete(id);

            // Then
            assertNotNull(result);
            assertEquals(expectedType, result);
            verify(timeoffTypeService).deleteTimeOffType(id);
        }
    }

    @Nested
    class TimeOffPolicyOperations {
        @Test
        void shouldCreateTimeOffPolicy() {
            // Given
            TimeOffPolicyCreateInput input = TimeOffPolicyCreateInput.newBuilder().build();
            TimeOffTypeDefinition expectedPolicy = TimeOffTypeDefinition.newBuilder().id(1L).build();
            when(definitionService.createCompanyDefinition(input)).thenReturn(expectedPolicy);

            // When
            TimeOffTypeDefinition result = timeoffDataMutator.timeOffPolicyCreate(input);

            // Then
            assertNotNull(result);
            assertEquals(expectedPolicy, result);
            verify(definitionService).createCompanyDefinition(input);
        }

        @Test
        void shouldDeleteTimeOffPolicy() {
            // Given
            Long policyId = 1L;
            TimeOffTypeDefinition expectedPolicy = TimeOffTypeDefinition.newBuilder().id(policyId).build();
            when(definitionService.deleteDefinition(policyId)).thenReturn(expectedPolicy);

            // When
            TimeOffTypeDefinition result = timeoffDataMutator.timeOffPolicyDelete(policyId);

            // Then
            assertNotNull(result);
            assertEquals(expectedPolicy, result);
            verify(definitionService).deleteDefinition(policyId);
        }

        @Test
        void shouldUpdateTimeOffPolicy() {
            // Given
            TimeOffPolicyUpdateInput input = TimeOffPolicyUpdateInput.newBuilder().build();
            TimeOffTypeDefinition expectedPolicy = TimeOffTypeDefinition.newBuilder().id(1L).build();
            when(definitionService.updateDefinition(input)).thenReturn(expectedPolicy);

            // When
            TimeOffTypeDefinition result = timeoffDataMutator.timeOffPolicyUpdate(input);

            // Then
            assertNotNull(result);
            assertEquals(expectedPolicy, result);
            verify(definitionService).updateDefinition(input);
        }

        @Test
        void shouldValidateTimeOffPolicyUsers() {
            // Given
            TimeOffPolicyValidateUsersInput input = TimeOffPolicyValidateUsersInput.newBuilder().build();
            TimeOffPolicyUsersValidationResult expectedResult = TimeOffPolicyUsersValidationResult.newBuilder().build();
            when(definitionService.validateDefinitionAssignment(input, dfe)).thenReturn(expectedResult);

            // When
            TimeOffPolicyUsersValidationResult result = timeoffDataMutator.timeOffPolicyUsersValidate(input, dfe);

            // Then
            assertNotNull(result);
            assertEquals(expectedResult, result);
            verify(definitionService).validateDefinitionAssignment(input, dfe);
        }

        @Test
        void shouldAssignUsersToTimeOffPolicy() {
            // Given
            TimeOffPolicyAssignUsersInput input = TimeOffPolicyAssignUsersInput.newBuilder().build();
            TimeOffTypeDefinition expectedPolicy = TimeOffTypeDefinition.newBuilder().id(1L).build();
            when(definitionService.assignOrUnassignUsers(dfe, input)).thenReturn(expectedPolicy);

            // When
            TimeOffTypeDefinition result = timeoffDataMutator.timeOffPolicyAssignUsers(input, dfe);

            // Then
            assertNotNull(result);
            assertEquals(expectedPolicy, result);
            verify(definitionService).assignOrUnassignUsers(dfe, input);
        }
    }

    @Nested
    class BackfillOperations {
        @Test
        void shouldBackfillDefinitionIdInEntitlement() {
            // Given
            Long companyId = 1L;

            // When
            TaskResponse result = timeoffDataMutator.backFillDefinitionIdInEntitlement(companyId);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Back-fill process started successfully", result.getMessage());
            verify(timeoffService).backFillDefinitionIdsForEntitlements(companyId);
        }

        @Test
        void shouldMigrateCompanyDefinitions() {
            // Given
            List<Long> companyIds = List.of(1L, 2L);
            boolean forAll = false;

            // When
            TaskResponse result = timeoffDataMutator.migrateCompanyDefinitions(companyIds, forAll);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Migrating company definitions, process started successfully", result.getMessage());
            verify(definitionService).migrateCompanyLevelDefinitionsToEntityLevel(companyIds, forAll);
        }

        @Test
        void shouldHandleMigrateCompanyDefinitionsFailure() {
            // Given
            List<Long> companyIds = List.of(1L, 2L);
            boolean forAll = false;
            RuntimeException exception = new RuntimeException("Migration failed");
            doThrow(exception).when(definitionService).migrateCompanyLevelDefinitionsToEntityLevel(any(), anyBoolean());

            // When
            TaskResponse result = timeoffDataMutator.migrateCompanyDefinitions(companyIds, forAll);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("Error while migrating company definitions to entity level"));
            assertTrue(result.getMessage().contains("Migration failed"));
            verify(definitionService).migrateCompanyLevelDefinitionsToEntityLevel(companyIds, forAll);
        }
    }

    @Nested
    class TriggerLegacyTimeoffReallocation {
        @Test
        void shouldTriggerLegacyTimeoffReallocationSuccessfully() {
            // Given
            LocalDate currentDate = LocalDate.now();

            // When
            TaskResponse result = timeoffDataMutator.triggerLegacyTimeoffReallocation();

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Timeoff balance reallocation job started.", result.getMessage());
            verify(timeoffService).reallocateTimeoff(currentDate);
        }

        @Test
        void shouldHandleLegacyTimeoffReallocationFailure() {
            // Given
            RuntimeException exception = new RuntimeException("Reallocation failed");
            doThrow(exception).when(timeoffService).reallocateTimeoff(any());

            // When
            TaskResponse result = timeoffDataMutator.triggerLegacyTimeoffReallocation();

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("Timeoff balance reallocation failed"));
        }
    }

    @Nested
    class MigrateFromBackToWorkDateToEndDate {
        @Test
        void shouldReturnDeprecatedMessage() {
            // When
            TaskResponse result = timeoffDataMutator.migrateFromBackToWorkDateToEndDate();

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertEquals("This mutation is deprecated. Please contact squad phoenix for more details", result.getMessage());
        }
    }

    @Nested
    class ChangeTimeOffDate {
        @Test
        void shouldChangeTimeOffDateSuccessfully() {
            // Given
            long id = 1L;
            LocalDate startDate = LocalDate.now();
            LocalDate endDate = startDate.plusDays(5);
            Boolean ignoresValidations = true;
            List<String> debuggingInfos = List.of("Info1", "Info2");

            when(timeoffService.changeTimeOffDate(id, startDate, endDate, ignoresValidations))
                    .thenReturn(debuggingInfos);

            // When
            TaskResponse result = timeoffDataMutator.changeTimeOffDate(id, startDate, endDate, ignoresValidations);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertTrue(result.getMessage().contains("Success with debugging infos"));
            assertTrue(result.getMessage().contains("Info1"));
            assertTrue(result.getMessage().contains("Info2"));
            verify(timeoffService).changeTimeOffDate(id, startDate, endDate, ignoresValidations);
        }

        @Test
        void shouldChangeTimeOffDateWithNullIgnoresValidations() {
            // Given
            long id = 1L;
            LocalDate startDate = LocalDate.now();
            LocalDate endDate = startDate.plusDays(5);
            List<String> debuggingInfos = List.of("Info1");

            when(timeoffService.changeTimeOffDate(id, startDate, endDate, false))
                    .thenReturn(debuggingInfos);

            // When
            TaskResponse result = timeoffDataMutator.changeTimeOffDate(id, startDate, endDate, null);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertTrue(result.getMessage().contains("Success with debugging infos"));
            verify(timeoffService).changeTimeOffDate(id, startDate, endDate, false);
        }

        @Test
        void shouldHandleChangeTimeOffDateFailure() {
            // Given
            long id = 1L;
            LocalDate startDate = LocalDate.now();
            LocalDate endDate = startDate.plusDays(5);
            Boolean ignoresValidations = true;
            RuntimeException exception = new RuntimeException("Change failed");

            when(timeoffService.changeTimeOffDate(id, startDate, endDate, ignoresValidations))
                    .thenThrow(exception);

            // When
            TaskResponse result = timeoffDataMutator.changeTimeOffDate(id, startDate, endDate, ignoresValidations);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("An exception occurred"));
            assertTrue(result.getMessage().contains("Kindly contact @Thang"));
            assertTrue(result.getMessage().contains("Change failed"));
            verify(timeoffService).changeTimeOffDate(id, startDate, endDate, ignoresValidations);
        }
    }

    @Nested
    class BackfillRulesForExistingCompanyDefinitions {
        @Test
        void shouldReturnDeprecatedMessage() {
            // Given
            Long companyId = 1L;
            boolean forAll = true;

            // When
            TaskResponse result = timeoffDataMutator.backfillRulesForExistingCompanyDefinitions(companyId, forAll);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertEquals("This mutation is deprecated. Please contact squad phoenix for more details", result.getMessage());
        }

        @Test
        void shouldReturnDeprecatedMessageWithNullCompanyId() {
            // Given
            boolean forAll = true;

            // When
            TaskResponse result = timeoffDataMutator.backfillRulesForExistingCompanyDefinitions(null, forAll);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertEquals("This mutation is deprecated. Please contact squad phoenix for more details", result.getMessage());
        }
    }

    @Nested
    class FixTimeoffAllocations {
        @Test
        void shouldFixTimeoffAllocationsSuccessfully() {
            // Given
            FixTimeoffAllocationInput input = FixTimeoffAllocationInput.newBuilder()
                    .timeoffSummaryIds(List.of(1L, 2L))
                    .dryRun(true)
                    .build();

            // When
            TaskResponse result = timeoffDataMutator.fixTimeoffAllocations(input);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Fix timeoff allocation triggered successfully.", result.getMessage());
            verify(fixAllocationProcessor).fixTimeoffAllocations(input.getTimeoffSummaryIds(), input.getDryRun());
        }

        @Test
        void shouldHandleFixTimeoffAllocationsFailure() {
            // Given
            FixTimeoffAllocationInput input = FixTimeoffAllocationInput.newBuilder()
                    .timeoffSummaryIds(List.of(1L, 2L))
                    .dryRun(true)
                    .build();
            RuntimeException exception = new RuntimeException("Fix failed");
            doThrow(exception).when(fixAllocationProcessor).fixTimeoffAllocations(any(), anyBoolean());

            // When
            TaskResponse result = timeoffDataMutator.fixTimeoffAllocations(input);

            // Then
            assertNotNull(result);
            assertFalse(result.getSuccess());
            assertTrue(result.getMessage().contains("Fix timeoff allocation failed"));
            assertTrue(result.getMessage().contains("Fix failed"));
            verify(fixAllocationProcessor).fixTimeoffAllocations(input.getTimeoffSummaryIds(), input.getDryRun());
        }

        @Test
        void shouldHandleFixTimeoffAllocationsWithEmptyIds() {
            // Given
            FixTimeoffAllocationInput input = FixTimeoffAllocationInput.newBuilder()
                    .timeoffSummaryIds(List.of())
                    .dryRun(true)
                    .build();

            // When
            TaskResponse result = timeoffDataMutator.fixTimeoffAllocations(input);

            // Then
            assertNotNull(result);
            assertTrue(result.getSuccess());
            assertEquals("Fix timeoff allocation triggered successfully.", result.getMessage());
            verify(fixAllocationProcessor).fixTimeoffAllocations(input.getTimeoffSummaryIds(), input.getDryRun());
        }
    }

}