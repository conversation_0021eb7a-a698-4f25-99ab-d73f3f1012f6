package com.multiplier.timeoff.graphql;

import com.multiplier.common.transport.auth.MPLAuthorization;
import com.multiplier.contract.schema.contract.ContractOuterClass;
import com.multiplier.timeoff.adapters.ContractServiceAdapter;
import com.multiplier.timeoff.graphql.dataloader.TimeOffEntriesDataLoader;
import com.multiplier.timeoff.service.*;
import com.multiplier.timeoff.types.*;
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment;
import lombok.val;
import org.dataloader.DataLoader;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TimeoffDataFetcherTest {
    @Mock
    MPLAuthorization mplAuthorization;

    @Mock
    TimeoffQuery timeoffQuery;

    @Mock
    TimeoffSummaryQuery timeoffSummaryQuery;

    @Mock
    TimeoffService timeoffService;

    @Mock
    ContractServiceAdapter contractServiceAdapter;

    @Mock
    TimeoffTypeService timeoffTypeService;

    @Mock
    DefinitionService definitionService;

    @Mock
    DgsDataFetchingEnvironment dfe;
    @Mock
    TimeoffCalendarService timeoffCalendarService;
    @Mock
    TimeoffSummaryBreakdownService timeoffSummaryBreakdownService;

    @InjectMocks
    TimeoffDataFetcher timeoffDataFetcher;

    @Nested
    class timeOffs {
        TimeOffFilter graphFilter = TimeOffFilter.newBuilder()
                .ids(List.of(1L))
                .contractIds(List.of(1L))
                .timeOffStatus(List.of(TimeOffStatus.APPROVED))
                .build();

        @Test
        void callsService() {
            timeoffDataFetcher.timeOffs(graphFilter, null);
            verify(timeoffQuery).getAllForOperationsWithPagination(any(), any());
        }
    }

    @Nested
    class timeOffSummaries {
        TimeOffSummaryFilter graphFilter = TimeOffSummaryFilter.newBuilder()
                .contractIds(List.of(1L))
                .contractStatus(ContractStatus.ACTIVE)
                .build();

        @Test
        void callsService() {
            timeoffDataFetcher.timeOffSummaries(graphFilter, null);
            verify(timeoffSummaryQuery).getAllForOperationsWithPagination(any(), any());
        }
    }

    @Nested
    class getAll {
        @Test
        void shouldGetContractTimeOff() {
            Long id = 1L;
            LocalDateTime fromDate = LocalDateTime.now();
            LocalDateTime toDate = LocalDateTime.now().plusDays(1);
            Contract contract = Contract.newBuilder().id(1L).build();
            ContractTimeOff expectedTimeOff = ContractTimeOff.newBuilder().build();

            when(dfe.getSource()).thenReturn(contract);
            when(timeoffService.getContractTimeOff(contract.getId(), id, fromDate, toDate))
                    .thenReturn(expectedTimeOff);

            ContractTimeOff result = timeoffDataFetcher.getAll(id, fromDate, toDate, dfe);

            assertEquals(expectedTimeOff, result);
            verify(timeoffService).getContractTimeOff(contract.getId(), id, fromDate, toDate);
        }
    }

    @Nested
    class getTimeOffsForCompany {
        @Test
        void shouldGetTimeOffsForCompany() {
            Long id = 1L;
            LocalDateTime fromDate = LocalDateTime.now();
            LocalDateTime toDate = LocalDateTime.now().plusDays(1);
            List<TimeOff> expectedTimeOffs = List.of(TimeOff.newBuilder().build());

            when(timeoffQuery.getAllForCompany(any(), any())).thenReturn(expectedTimeOffs);

            List<TimeOff> result = timeoffDataFetcher.getTimeOffsForCompany(id, fromDate, toDate, dfe);

            assertEquals(expectedTimeOffs, result);
            verify(timeoffQuery).getAllForCompany(any(), any());
        }
    }

    @Nested
    class getTimeOffEntitlementsForCompliance {
        @Test
        void shouldGetTimeOffEntitlementsForCompliance() {
            Compliance compliance = new ComplianceMultiplierEOR();
            compliance.setContract(Contract.newBuilder().id(1L).build());
            ContractOuterClass.Contract contract = ContractOuterClass.Contract.newBuilder()
                    .setId(1L)
                    .setCompanyId(1L)
                    .setMemberId(1L)
                    .build();
            List<ContractTimeOffEntitlement> expectedEntitlements = List.of(ContractTimeOffEntitlement.newBuilder().build());

            when(dfe.getSource()).thenReturn(compliance);
            when(contractServiceAdapter.getContractByIdAnyStatus(1L)).thenReturn(contract);
            when(mplAuthorization.allowed("view.company.contract.compliance")).thenReturn(true);
            when(mplAuthorization.isCompanyUserForCompanyId(1L)).thenReturn(true);
            when(timeoffService.getTimeOffEntitlementsForContract(1L)).thenReturn(expectedEntitlements);

            List<ContractTimeOffEntitlement> result = timeoffDataFetcher.getTimeOffEntitlementsForCompliance(dfe);

            assertEquals(expectedEntitlements, result);
            verify(timeoffService).getTimeOffEntitlementsForContract(1L);
        }
    }

    @Nested
    class companyTimeOffTypes {
        @Test
        void shouldGetCompanyTimeOffTypes() {
            Company company = Company.newBuilder().id(1L).build();
            TimeOffTypeFilter filter = TimeOffTypeFilter.newBuilder().build();
            List<TimeOffTypeInfo> expectedTypes = List.of(TimeOffTypeInfo.newBuilder().build());

            when(dfe.getSource()).thenReturn(company);
            when(timeoffTypeService.findCompanyTimeOffTypes(1L, filter)).thenReturn(expectedTypes);

            List<TimeOffTypeInfo> result = timeoffDataFetcher.companyTimeOffTypes(dfe, filter);

            assertEquals(expectedTypes, result);
            verify(timeoffTypeService).findCompanyTimeOffTypes(1L, filter);
        }
    }

    @Nested
    class companyTimeOffPolicies {
        @Test
        void shouldGetCompanyTimeOffPolicies() {
            CompanyTimeOffPolicyFilter filter = CompanyTimeOffPolicyFilter.newBuilder().build();
            List<TimeOffTypeDefinition> expectedPolicies = List.of(TimeOffTypeDefinition.newBuilder().build());

            when(definitionService.findCompanyDefinitions(filter)).thenReturn(expectedPolicies);

            List<TimeOffTypeDefinition> result = timeoffDataFetcher.companyTimeOffPolicies(filter);

            assertEquals(expectedPolicies, result);
            verify(definitionService).findCompanyDefinitions(filter);
        }
    }

    @Nested
    class timeOffPolicyAssignmentDetails {
        @Test
        void shouldGetTimeOffPolicyAssignmentDetails() {
            Long policyId = 1L;
            TimeOffPolicyAssignmentDetailsResult expectedResult = TimeOffPolicyAssignmentDetailsResult.newBuilder().build();

            when(definitionService.findDefinitionAssignmentDetails(policyId)).thenReturn(expectedResult);

            TimeOffPolicyAssignmentDetailsResult result = timeoffDataFetcher.timeOffPolicyAssignmentDetails(policyId);

            assertEquals(expectedResult, result);
            verify(definitionService).findDefinitionAssignmentDetails(policyId);
        }
    }

    @Nested
    class getTimeOffContractRequirements {
        @Test
        void shouldGetTimeOffContractRequirements() {
            Long contractId = 1L;
            TimeOffContractRequirements expectedRequirements = TimeOffContractRequirements.newBuilder().build();

            when(timeoffService.getTimeOffContractRequirements(contractId, dfe)).thenReturn(expectedRequirements);

            TimeOffContractRequirements result = timeoffDataFetcher.getTimeOffContractRequirements(contractId, dfe);

            assertEquals(expectedRequirements, result);
            verify(timeoffService).getTimeOffContractRequirements(contractId, dfe);
        }
    }

    @Nested
    class timeOffPolicies {
        @Test
        void shouldGetTimeOffPolicies() {
            TimeOffPolicyFilter filter = TimeOffPolicyFilter.newBuilder().build();
            List<TimeOffTypeDefinition> expectedPolicies = List.of(TimeOffTypeDefinition.newBuilder().build());

            when(definitionService.getDefinitions(filter)).thenReturn(expectedPolicies);

            List<TimeOffTypeDefinition> result = timeoffDataFetcher.timeOffPolicies(filter);

            assertEquals(expectedPolicies, result);
            verify(definitionService).getDefinitions(filter);
        }
    }

    @Nested
    class timeoffEncashmentBalance {
        @Test
        void shouldGetTimeOffEncashmentBalance() {
            TimeOffEncashmentInput input = TimeOffEncashmentInput.newBuilder()
                    .contractId(1L)
                    .lastWorkingDate(LocalDate.now())
                    .build();
            List<TimeOffBalanceEncashment> expectedEncashments = List.of(TimeOffBalanceEncashment.newBuilder().build());

            when(timeoffSummaryQuery.getEncashmentBalance(input, dfe))
                    .thenReturn(expectedEncashments);

            List<TimeOffBalanceEncashment> result = timeoffDataFetcher.timeoffEncashmentBalance(input, dfe);

            assertEquals(expectedEncashments, result);
            verify(timeoffSummaryQuery).getEncashmentBalance(input, dfe);
        }
    }

    @Nested
    class timeOffCalendar {
        @Test
        void shouldGetTimeOffCalendar() {
            TimeOffCalendarFilter filter = TimeOffCalendarFilter.newBuilder().build();
            val expectedResult = TimeOffCalendar.newBuilder().build();
            when(timeoffCalendarService.getTimeOffCalendar(dfe, filter)).thenReturn(expectedResult);

            TimeOffCalendar result = timeoffDataFetcher.timeOffCalendar(dfe, filter);

            assertEquals(expectedResult, result);

        }
    }

    @Nested
    class timeOffSummaryBreakdown {
        @Test
        void shouldGetTimeOffSummaryBreakdown() {
            TimeOffSummaryBreakdownInput input = TimeOffSummaryBreakdownInput.newBuilder().build();
            val expectedResult = List.of(
                    TimeOffSummaryBreakdown.newBuilder().isCurrentSummary(true).build(),
                    TimeOffSummaryBreakdown.newBuilder().isCurrentSummary(false).build()
            );
            when(timeoffSummaryBreakdownService.getSummaryBreakdown(dfe, input)).thenReturn(expectedResult);

            List<TimeOffSummaryBreakdown> result = timeoffDataFetcher.timeOffSummaryBreakdown(input, dfe);

            assertNotNull(result);
            assertEquals(2, result.size());
            assertTrue(result.get(0).getIsCurrentSummary());
            assertFalse(result.get(1).getIsCurrentSummary());
        }
    }

    @Nested
    class timeoffEntries {
        @Mock
        DataLoader<Object, Object> dataLoader;

        @Test
        void shouldLoadTimeoffEntriesUsingDataLoader() {
            // Given
            Long timeOffId = 123L;
            TimeOff timeOff = TimeOff.newBuilder().id(timeOffId).build();
            CompletableFuture<Object> expectedFuture = CompletableFuture.completedFuture(List.of());

            when(dfe.getSource()).thenReturn(timeOff);
            when(dfe.getDataLoader(TimeOffEntriesDataLoader.class)).thenReturn(dataLoader);
            when(dataLoader.load(timeOffId)).thenReturn(expectedFuture);

            // When
            CompletableFuture<List<TimeOffEntry>> result = timeoffDataFetcher.timeoffEntries(dfe);

            // Then
            assertNotNull(result);
            verify(dfe).getSource();
            verify(dfe).getDataLoader(TimeOffEntriesDataLoader.class);
            verify(dataLoader).load(timeOffId);
        }
    }

}