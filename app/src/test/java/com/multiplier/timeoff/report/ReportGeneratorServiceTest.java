package com.multiplier.timeoff.report;

import com.multiplier.timeoff.report.domain.*;
import com.multiplier.timeoff.report.generator.GeneratorConfigStore;
import com.multiplier.timeoff.report.generator.ReportGeneratorService;
import com.multiplier.timeoff.types.DocumentReadable;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.junit.jupiter.api.DisplayNameGeneration;
import org.junit.jupiter.api.DisplayNameGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores.class)

class ReportGeneratorServiceTest {

    @Mock
    private GeneratorConfigStore generatorConfigStore;

    @InjectMocks
    private ReportGeneratorService reportGeneratorService;

    @Test
    void report_generate_success_csv_empty_data() {
        ReportCategory reportCategory = ReportCategory.EXPENSE_REPORT;
        List<ReportRow> fieldData = Arrays.asList(ReportRow.builder().build());

        when(generatorConfigStore.getReportName(any())).thenReturn("EXPENSE_REPORT.xlsx");
        when(generatorConfigStore.getReportGenerationType(any())).thenReturn(ReportGenerationType.CSV);

        DocumentReadable result = reportGeneratorService.generateReport(reportCategory, fieldData);

        assertNotNull(result);
    }

    @Test
    void report_generate_success_xls_empty_data() {
        ReportCategory reportCategory = ReportCategory.EXPENSE_REPORT;
        ReportRow reportRow = ReportRow.builder()
                .xlBackgroundColor(IndexedColors.AQUA)
                .reportRowCellValues(Arrays.asList(ReportRowCellValue.builder().reportRowCellType(ReportRowCellType.DATETIME).columnValue(LocalDateTime.of(2024, 6, 2, 8,30, 0)).build()))
                .build();
        List<ReportRow> fieldData = Arrays.asList(reportRow);

        when(generatorConfigStore.getColumnNames(any())).thenReturn(Arrays.asList("Date"));
        when(generatorConfigStore.getReportName(any())).thenReturn("EXPENSE_REPORT.xlsx");
        when(generatorConfigStore.getReportGenerationType(any())).thenReturn(ReportGenerationType.XLSX);

        DocumentReadable result = reportGeneratorService.generateReport(reportCategory, fieldData);

        assertNotNull(result);
    }

    @Test
    void report_generate_multirow_success_csv() {
        ReportCategory reportCategory = ReportCategory.EXPENSE_REPORT;
        ReportRow reportRow1 = ReportRow.builder()
                .xlBackgroundColor(IndexedColors.BLUE)
                .reportRowCellValues(Arrays.asList(ReportRowCellValue.builder().reportRowCellType(ReportRowCellType.DATETIME).columnValue(LocalDateTime.of(2024, 6, 4, 4,30, 0)).build()))
                .build();
        ReportRow reportRow2 = ReportRow.builder()
                .xlBackgroundColor(IndexedColors.AQUA)
                .reportRowCellValues(Arrays.asList(ReportRowCellValue.builder().reportRowCellType(ReportRowCellType.DATETIME).columnValue(LocalDateTime.of(2024, 6, 2, 8,30, 0)).build()))
                .build();
        List<ReportRow> fieldData = Arrays.asList(reportRow1, reportRow2);

        when(generatorConfigStore.getColumnNames(any())).thenReturn(Arrays.asList("Date"));
        when(generatorConfigStore.getReportName(any())).thenReturn("EXPENSE_REPORT.xlsx");
        when(generatorConfigStore.getReportGenerationType(any())).thenReturn(ReportGenerationType.CSV);

        DocumentReadable result = reportGeneratorService.generateReport(reportCategory, fieldData);

        assertNotNull(result);
    }

    @Test
    void report_generate_multirow_success_xlsx() {
        ReportCategory reportCategory = ReportCategory.EXPENSE_REPORT;
        ReportRow reportRow1 = ReportRow.builder()
                .xlBackgroundColor(IndexedColors.BLUE)
                .reportRowCellValues(Arrays.asList(ReportRowCellValue.builder().reportRowCellType(ReportRowCellType.DATETIME).columnValue(LocalDateTime.of(2024, 6, 4, 4,30, 0)).build()))
                .build();
        ReportRow reportRow2 = ReportRow.builder()
                .xlBackgroundColor(IndexedColors.AQUA)
                .reportRowCellValues(Arrays.asList(ReportRowCellValue.builder().reportRowCellType(ReportRowCellType.DATETIME).columnValue(LocalDateTime.of(2024, 6, 2, 8,30, 0)).build()))
                .build();
        List<ReportRow> fieldData = Arrays.asList(reportRow1, reportRow2);

        when(generatorConfigStore.getColumnNames(any())).thenReturn(Arrays.asList("Date"));
        when(generatorConfigStore.getReportName(any())).thenReturn("EXPENSE_REPORT.xlsx");
        when(generatorConfigStore.getReportGenerationType(any())).thenReturn(ReportGenerationType.XLSX);

        DocumentReadable result = reportGeneratorService.generateReport(reportCategory, fieldData);

        assertNotNull(result);
    }

    @Test
    void report_generate_multisheet_multirow_success_xlsx() {
        ReportCategory reportCategory = ReportCategory.EXPENSE_REPORT;
        ReportRow reportRow1 = ReportRow.builder()
                .xlBackgroundColor(IndexedColors.BLUE)
                .reportRowCellValues(Arrays.asList(ReportRowCellValue.builder().reportRowCellType(ReportRowCellType.DATETIME).columnValue(LocalDateTime.of(2024, 6, 4, 4,30, 0)).build()))
                .build();
        ReportRow reportRow2 = ReportRow.builder()
                .xlBackgroundColor(IndexedColors.AQUA)
                .reportRowCellValues(Arrays.asList(ReportRowCellValue.builder().reportRowCellType(ReportRowCellType.DATETIME).columnValue(LocalDateTime.of(2024, 6, 2, 8,30, 0)).build()))
                .build();
        List<ReportRow> fieldDataSheet1 = Arrays.asList(reportRow1, reportRow2);
        List<ReportRow> fieldDataSheet2 = Arrays.asList(reportRow1, reportRow2);

        when(generatorConfigStore.getHeaderStyle(any())).thenReturn("BOLD_CENTER_BORDER_ALL");
        when(generatorConfigStore.getColumnNames(any())).thenReturn(Arrays.asList("Date"));
        when(generatorConfigStore.getReportName(any())).thenReturn("EXPENSE_REPORT.xlsx");

        Map<String, List<ReportRow>> fieldData = new HashMap<>();
        fieldData.put("Sheet1", fieldDataSheet1);
        fieldData.put("Sheet2", fieldDataSheet2);

        DocumentReadable result = reportGeneratorService.generateMultiSheetReport(reportCategory, fieldData);

        assertNotNull(result);
    }

}
