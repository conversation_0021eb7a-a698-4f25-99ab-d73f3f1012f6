import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.jetbrains.kotlin.gradle.utils.extendsFrom
import org.springframework.boot.gradle.plugin.SpringBootPlugin
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

plugins {
	alias(libs.plugins.kotlin.jvm)
	alias(libs.plugins.kotlin.plugin.spring)
	alias(libs.plugins.kotlin.plugin.jpa)
	alias(libs.plugins.kotlin.plugin.lombok)
	alias(libs.plugins.spring.boot)
	alias(libs.plugins.liquibase)
}

dependencies {

	// Dependency Management
	implementation(platform(SpringBootPlugin.BOM_COORDINATES))
	implementation(platform(libs.spring.cloud.dependencies))

	// Project
	implementation(project(":schema"))

	// Multiplier
	implementation(multiplier.grpc.common)
	implementation(multiplier.grpc.bulkupload)
	implementation(libs.multiplier.growthbook.sdk)
	implementation(multiplier.platform.spring.starter) {
		exclude(group = "com.multiplier", module = "access-control-spring")
	}
	implementation(multiplier.platform.spring.job.starter)
	implementation(libs.access.control.spring)

	implementation(libs.multiplier.core.service.schema)

	implementation(libs.multiplier.ai)
	implementation(libs.multiplier.contract.service.schema)
	implementation(libs.multiplier.country.service.schema)
	implementation(libs.multiplier.member.service.schema)
	implementation(libs.multiplier.company.service.schema)
	implementation(libs.multiplier.pigeon.service.schema)
	implementation(libs.multiplier.pigeon.service.client)
	implementation(libs.multiplier.leave.compliance.service.schema)
	implementation(libs.multiplier.org.management.service.schema) {
		exclude(group = "net.devh", module = "grpc-server-spring-boot-starter")
	}

	implementation(libs.multiplier.timeoff.service.graph)

	// Kotlin
	implementation(libs.kotlin.reflect)
	implementation(libs.kotlin.stdlib.jdk8)
	implementation(libs.kotlinx.coroutines.core)

	// Spring
	implementation(libs.spring.boot.starter.actuator)
	implementation(libs.spring.boot.starter.web)
	implementation(libs.spring.boot.starter.security)
	implementation(libs.spring.boot.starter.validation)
	implementation(libs.spring.boot.starter.data.jpa)
	implementation(libs.spring.cloud.starter.openfeign)
	implementation(libs.spring.boot.starter.thymeleaf)
	implementation(libs.spring.boot.starter.mail)
	implementation(libs.spring.kafka)
	implementation(libs.spring.boot.configuration.processor)
	annotationProcessor(libs.spring.boot.configuration.processor)

	// GraphQL
	implementation(libs.graphql.dgs.spring.boot.starter)
	implementation(libs.graphql.java.extended.scalars)

	constraints {
		implementation("com.graphql-java:graphql-java") {
			version {
				strictly("[22,23]")
			}
		}
		implementation("com.google.protobuf:protobuf-java") {
			version {
				strictly("[3.25.5]")
			}
		}
		implementation("org.bouncycastle:bcprov-jdk18on:1.78")
		implementation("commons-beanutils:commons-beanutils:1.11.0")
	}

	// GRPC
	implementation(libs.grpc.spring.boot.starter)

	// Serialization
	implementation(libs.json)
	implementation(libs.kafka.protobuf.serde)
	implementation(libs.opencsv)
	implementation(libs.poi.ooxml)

	// Database
	implementation(libs.hibernate.envers)
	implementation(libs.hibernate.types)
	runtimeOnly(libs.postgresql)

	// Shedlock
	implementation(libs.shedlock.spring)
	implementation(libs.shedlock.provider.jdbc.template)

	// Logging
	implementation(libs.kotlin.logging.jvm)
	runtimeOnly(libs.janino) // Logback conditions
	runtimeOnly(libs.logstash.logback.encoder) // Structured (JSON) logging

	// other dependencies
	implementation(libs.problem.spring.web)
	implementation(libs.apache.commons.text)
	implementation(libs.apache.commons.compress)
	implementation(libs.io.vavr)
	implementation(libs.io.jsonwebtoken.jjwt.api)
	implementation(libs.io.jsonwebtoken.jjwt.impl)
	implementation(libs.io.jsonwebtoken.jjwt.jackson)
	implementation(libs.netty.common)
	implementation(libs.apache.kafka.clients)

	// Liquibase
	implementation(libs.liquibase.core)
	liquibaseRuntime(libs.liquibase.core)
	liquibaseRuntime(libs.liquibase.hibernate5)
	liquibaseRuntime(libs.postgresql)

	// Test
	testImplementation(libs.spring.boot.starter.test)
	testImplementation(libs.mapstruct)
	testImplementation(libs.mockito.kotlin)
	testImplementation(libs.mockito.inline)
	testImplementation(libs.h2database)
	testImplementation(libs.com.ninja.squad)
	testImplementation(libs.spring.boot.starter.webflux)
	testImplementation(libs.spring.kafka.test)
	testImplementation(libs.javafaker)
	testImplementation(libs.testcontainers)

	// TODO: check if we can add this to tasks.withType(Test)
	testAnnotationProcessor("org.projectlombok:lombok:1.18.30")
}

configurations.all {
	resolutionStrategy.eachDependency {
		if (requested.module.toString() == "org.yaml:snakeyaml") {
			artifactSelection {
				selectArtifact(DependencyArtifact.DEFAULT_TYPE, null, null)
			}
		}
	}
}

java {
	sourceCompatibility = JavaVersion.VERSION_17
	targetCompatibility = JavaVersion.VERSION_17

	withSourcesJar()
}

tasks.withType<KotlinCompile> {
	compilerOptions {
		jvmTarget.set(JvmTarget.JVM_17)
	}
}

tasks.withType<JavaCompile> {
	dependencies {
		annotationProcessor(platform(SpringBootPlugin.BOM_COORDINATES))
		annotationProcessor("org.projectlombok:lombok-mapstruct-binding:0.2.0")
		annotationProcessor("org.projectlombok:lombok:1.18.24")
		annotationProcessor("org.mapstruct:mapstruct-processor:1.4.2.Final")

		compileOnly("org.mapstruct:mapstruct:1.4.2.Final")
		implementation("org.projectlombok:lombok:1.18.24")

	}
}

// Configure test source sets
sourceSets {
	create("integrationTest") {
		java {
			srcDir("src/test/java")
			include("**/integrationTests/**")
		}
		resources {
			srcDir("src/test/resources")
		}
		compileClasspath += sourceSets.main.get().output + sourceSets.test.get().output
		runtimeClasspath += sourceSets.main.get().output + sourceSets.test.get().output
	}
}

// Configure dependencies for integration tests
configurations {
	getByName("integrationTestImplementation") {
		extendsFrom(configurations.testImplementation.get())
	}
	getByName("integrationTestRuntimeOnly") {
		extendsFrom(configurations.testRuntimeOnly.get())
	}
}

// Configure regular unit tests to exclude integration tests
tasks.withType<Test> {
	useJUnitPlatform()
	// Exclude integration tests from regular test runs
	exclude("**/integrationTests/**")
}

// Create a separate task for integration tests
val integrationTest = tasks.register<Test>("integrationTest") {
	description = "Runs integration tests."
	group = "verification"

	testClassesDirs = sourceSets["integrationTest"].output.classesDirs
	classpath = sourceSets["integrationTest"].runtimeClasspath

	useJUnitPlatform()

	// Only include integration tests
	include("**/integrationTests/**")

	// Set system properties for integration tests
	systemProperty("spring.profiles.active", "integration-test")

	// Ensure integration tests run after unit tests
	shouldRunAfter(tasks.test)

	// Configure test reporting
	reports {
		html.required.set(true)
		junitXml.required.set(true)
	}

	// Set test output directory
	reports.html.outputLocation.set(layout.buildDirectory.dir("reports/integrationTests"))
	reports.junitXml.outputLocation.set(layout.buildDirectory.dir("test-results/integrationTest"))
}

// Create a task to run all tests (unit + integration)
tasks.register("testAll") {
	description = "Runs all tests including integration tests."
	group = "verification"
	dependsOn(tasks.test, integrationTest)
}

tasks.withType<org.springframework.boot.gradle.tasks.run.BootRun> {
	val debuggerPort = System.getProperty("DEBUGGER_PORT")?.toInt() ?: 5005

	jvmArgs = listOf(
		"-Xdebug",
		"-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=0.0.0.0:${debuggerPort}"
	)
}

configurations.liquibaseRuntime.extendsFrom(configurations.runtimeOnly)


val diffChangelogFile by extra {
	val date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
	"src/main/resources/liquibase/changelog/${date}_changelog.xml"
}

val runList: String? by extra
configure<org.liquibase.gradle.LiquibaseExtension> {
	activities.register("main") {
		this.arguments = mapOf(
			"driver" to "org.postgresql.Driver",
			"url" to "****************************************",
			"referenceUrl" to "hibernate:spring:com.multiplier.core?dialect=org.hibernate.dialect.PostgreSQLDialect&hibernate.physical_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringPhysicalNamingStrategy&hibernate.implicit_naming_strategy=org.springframework.boot.orm.jpa.hibernate.SpringImplicitNamingStrategy",
			"username" to "timeoff",
			"password" to "",
			"changeLogFile" to "src/main/resources/db/changelog/db.changelog-master.xml",
			"defaultSchemaName" to "timeoff",
			"logLevel" to "debug",
			"classpath" to "src/main/resources/"
		)
	}

	runList = runList ?: "main"
}


// Skip building the plain jar
tasks.getByName<Jar>("jar") {
	enabled = false
}
