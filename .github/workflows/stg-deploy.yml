name: Deploy to Staging [Env]
on:
  workflow_dispatch:
  push:
    branches: 
      - main

##
# ECR_REPOSITORY: stg-app-tech-timeoffservice-ecr
# container-name: timeoffService
# service: timeoffService

jobs:
  deploy:
    # if: github.ref == 'refs/heads/main'
    name: Deploy
    uses: Multiplier-Core/devops-github-shared-pipelines/.github/workflows/be-generic-deployment.yml@main
    secrets: inherit
    with:
      deploy_target: staging
      service_name: timeoffService
      ecr_repository: stg-app-tech-timeoffservice-ecr
      aws_main_account: STG