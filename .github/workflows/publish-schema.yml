name: Publish schema to CodeArtifact
on:
  workflow_dispatch:
    tags:
      - '**'

permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    if: startsWith(github.ref, 'refs/tags/')

    name: Publish
    runs-on: ubuntu-latest

    steps:
    - name: Info tag
      run: echo ${{ github.ref }}

    - name: Checkout
      uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: Set up JDK 17
      uses: actions/setup-java@v1
      with:
        java-version: 17

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: arn:aws:iam::${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }}:role/prd-apse1-github-workflow-iamrole
        aws-region: ap-southeast-1
        role-duration-seconds: 3600

    - name: Fetch AWS CodeArtifact Token
      run: echo "CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain multiplier-artifacts --domain-owner ${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }} --query authorizationToken --output text)" >> $GITHUB_ENV

    - name: Build & publish
      run: ./gradlew -p schema publish
