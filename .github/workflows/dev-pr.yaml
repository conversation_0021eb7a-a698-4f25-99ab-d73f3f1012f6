name: PR Validation

on:
  pull_request:
    branches:
      - main

permissions:
  id-token: write
  contents: read

jobs:
  build_check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }}:role/prd-apse1-github-workflow-iamrole
          aws-region: ap-southeast-1
          role-duration-seconds: 3600

      - name: Fetch AWS CodeArtifact Token
        run: echo "CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain multiplier-artifacts --domain-owner ************ --query authorizationToken --output text)" >> $GITHUB_ENV

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: zulu
          java-version: 17

      - name: Code Build
        run: chmod +x gradlew && ./gradlew build --info --stacktrace --no-daemon --console=plain

