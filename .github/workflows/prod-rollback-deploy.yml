name: Deploy to Production [Rollback]
run-name: Deploy ${{ github.ref_name }} to production [Rollback]
on:
  workflow_dispatch:
    tags:
      - '**'

##
# ECR_REPOSITORY: timeoff-service
# container-name: timeoff-service
# service: timeoff-service

jobs:
 deploy:
    # only a tag is allowed to deploy
    if: startsWith(github.ref, 'refs/tags/')
    name: Deploy
    uses: Multiplier-Core/devops-github-shared-pipelines/.github/workflows/be-generic-deployment.yml@main
    secrets: inherit
    with:
      deploy_target: production
      service_name: timeoff-service
      ecr_repository: timeoff-service
      allow_rollback: true
      service_name_dr: timeoffService
