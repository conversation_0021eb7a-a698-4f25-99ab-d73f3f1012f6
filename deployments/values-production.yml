environments:
  PLATFORM_TIMEOFF_KAFKA_BOOTSTRAP-SERVERS: b-2.kafka-cluster-pro.8h6lkf.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-3.kafka-cluster-pro.8h6lkf.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-1.kafka-cluster-pro.8h6lkf.c2.kafka.ap-southeast-1.amazonaws.com:9092
  SPRING_PROFILES_ACTIVE: prod
  awslogs-group: /ecs/timeoff-service-task-definition-production
  awslogs-stream-prefix: ecs
kind: v1
name: timeoff-service
resources:
  cpu: 2048
  memory: 4096
secrets:
  APM_SERVER_URL: /prd/app/tech/services/shared/url/apmServer/param
  APM_TOKEN: arn:aws:secretsmanager:ap-southeast-1:133139256227:secret:/mgt/monitoring/devops/elasticsearch/apmtoken/secret-qmnKQx
  MULTIPLIER_CORE_SERVICE_URL: /prd/app/tech/services/shared/url/graphql/coreService/param
  GRPC_CLIENT_LEAVE-COMPLIANCE-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/apse1/shared/url/grpc/leave-compliance-service
  JWT_PUBLIC_KEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/employee/jwt/publickey/production
  SPRING_DATASOURCE_PASSWORD: arn:aws:ssm:ap-southeast-1:778085304246:parameter/database/timeoff/user/password/production
  SPRING_DATASOURCE_URL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/timeoffService/db/url/param
  SPRING_DATASOURCE_USERNAME: arn:aws:ssm:ap-southeast-1:778085304246:parameter/database/timeoff/user/production
  GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/grpc/userService/param
  GRPC_CLIENT_AI-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/apse1/shared/url/grpc/ai-service
  GRPC_CLIENT_ACCESS-CONTROL-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/apse1/shared/url/grpc/access-control-service
  GROWTHBOOK_BASEURL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/url/growthbook/param
  GROWTHBOOK_ENVKEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/prd/app/tech/services/shared/growthbook/env/key/param

