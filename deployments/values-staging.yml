environments:
  GRPC_SERVER_PORT: '9090'
  GRPC_SERVER_SECURITY_ENABLED: 'true'
  JAVA_HEAP_MAX_MEM: -Xmx1g
  SERVER_PORT: '8080'
  SPRING_PROFILES_ACTIVE: stage
  awslogs-group: /stg/app/tech/timeoffService/cloudwatchLogGroup
  awslogs-stream-prefix: ecs
kind: v2
name: timeoffService
resources:
  cpu: 512
  memory: 2048
secrets:
  APM_SERVER_URL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/apmServer/param
  APM_TOKEN: arn:aws:secretsmanager:ap-southeast-1:133139256227:secret:/mgt/monitoring/devops/elasticsearch/apmtoken/secret-qmnKQx
  GROWTHBOOK_BASEURL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/growthbook/param
  GROWTHBOOK_ENVKEY: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/growthbook/env/key/param
  GRPC_CLIENT_ACCESS-CONTROL-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/apse1/shared/url/grpc/access-control-service
  GRPC_CLIENT_COMPANY-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/companyService/param
  GRPC_CLIENT_CONTRACT-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/contractService/param
  GRPC_CLIENT_CORE-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/coreService/param
  GRPC_CLIENT_COUNTRY-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/countryService/param
  GRPC_CLIENT_LEAVE-COMPLIANCE-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/leaveComplianceService/param
  GRPC_CLIENT_MEMBER-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/memberService/param
  GRPC_CLIENT_ORG-MANAGEMENT-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/orgManagementService/param
  GRPC_CLIENT_PIGEON-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/pigeonService/param
  GRPC_CLIENT_AI-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/apse1/shared/url/grpc/ai-service
  JWT_PUBLIC_KEY: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/employee/jwt/publicKey/param
  MULTIPLIER_CORE_SERVICE_URL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/graphql/coreService/param
  PLATFORM_FRONTEND_BASEURL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/platform/frontend/param
  PLATFORM_TIMEOFF_KAFKA_BOOTSTRAP-SERVERS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  SPRING_DATASOURCE_PASSWORD: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/timeoffService/db/user/password/param
  SPRING_DATASOURCE_URL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/timeoffService/db/url/param
  SPRING_DATASOURCE_USERNAME: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/timeoffService/db/user/param
  GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/userService/param
