environments:
  APM_SERVER_URL: http://fleet.elk.usemultiplier.local:8200
  MULTIPLIER_CORE_SERVICE_URL: http://core-service.release.local.usemultiplier.com/graphql
  PLATFORM_TIMEOFF_KAFKA_BOOTSTRAP-SERVERS: b-2.kafka-cluster-rel.2cwaen.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-1.kafka-cluster-rel.2cwaen.c2.kafka.ap-southeast-1.amazonaws.com:9092
  SPRING_PROFILES_ACTIVE: release
  awslogs-group: /ecs/timeoff-service-task-definition-release
  awslogs-stream-prefix: ecs
kind: v1
name: timeoff-service
resources:
  cpu: 1024
  memory: 2048
secrets:
  APM_TOKEN: /monitoring/elasticsearch/apmToken/staging-release
  GRPC_CLIENT_LEAVE-COMPLIANCE-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/apse1/shared/url/grpc/leave-compliance-service
  JWT_PUBLIC_KEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/employee/jwt/publickey/release
  SPRING_DATASOURCE_PASSWORD: arn:aws:ssm:ap-southeast-1:778085304246:parameter/database/timeoff/user/password/release
  SPRING_DATASOURCE_URL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/timeoffService/db/url/param
  SPRING_DATASOURCE_USERNAME: arn:aws:ssm:ap-southeast-1:778085304246:parameter/database/timeoff/user/release
  GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/grpc/userService/param
  GRPC_CLIENT_AI-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/apse1/shared/url/grpc/ai-service
  GRPC_CLIENT_ACCESS-CONTROL-SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/apse1/shared/url/grpc/access-control-service
  GROWTHBOOK_BASEURL: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/url/growthbook/param
  GROWTHBOOK_ENVKEY: arn:aws:ssm:ap-southeast-1:778085304246:parameter/rel/app/tech/services/shared/growthbook/env/key/param
