#!/bin/bash

export CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token \
        --domain multiplier-artifacts \
        --domain-owner 778085304246 \
        --query authorizationToken \
        --output text)

MONITOR_DIR="./app/src"

echo "Starting app, monitoring $MONITOR_DIR for changes"

GRADLE_ARGS="$GRADLE_ARGS -x test --watch-fs"

gradle app:bootRun $GRADLE_ARGS &
while inotifywait --event modify --event move --event create --event delete \
            -r $MONITOR_DIR; do
      gradle build $GRADLE_ARGS
done
