# Integration Tests Configuration

This document describes the integration test setup for the timeoff-service project.

## Overview

Integration tests have been configured to run separately from unit tests to improve build performance and allow for on-demand execution. This setup ensures that:

1. **Regular builds (`./gradlew build`, `./gradlew clean build`)** only run unit tests
2. **Integration tests run only when explicitly requested**
3. **Integration tests are properly isolated** with their own source set and dependencies

## Directory Structure

```
app/src/test/java/com/multiplier/timeoff/
├── integrationTests/           # Integration test classes
│   ├── TimeoffSummaryServiceIntegrationTest.java
│   └── ... (other integration tests)
└── ... (unit test classes)
```

## Available Gradle Tasks

### Unit Tests Only (Default Behavior)
```bash
# Run only unit tests
./gradlew test

# Build project with unit tests only
./gradlew build
./gradlew clean build
```

### Integration Tests Only
```bash
# Run only integration tests
./gradlew integrationTest
```

### All Tests (Unit + Integration)
```bash
# Run both unit and integration tests
./gradlew testAll
```

## Configuration Details

### Source Set Configuration
- **Integration Test Source Set**: Configured to include files from `src/test/java/**/integrationTests/**`
- **Classpath**: Integration tests have access to main, test, and integration test dependencies
- **Resources**: Shared with regular test resources

### Test Profiles
- Integration tests use `@ActiveProfiles("integration-test")`
- Configuration file: `application-integration-test.yml`
- Database: H2 in-memory database for isolation

### Dependencies
- Integration tests inherit all test dependencies
- Additional dependencies can be added to `integrationTestImplementation` configuration if needed

## Best Practices

### Writing Integration Tests
1. **Place integration tests** in the `integrationTests` package
2. **Use `@ActiveProfiles("integration-test")`** annotation
3. **Use `@SpringBootTest`** for full application context
4. **Use `@Transactional`** at method level for data isolation
5. **Follow existing naming conventions** from other test classes

### Example Integration Test Structure
```java
@SpringBootTest
@ActiveProfiles("integration-test")
@TestMethodOrder(OrderAnnotation.class)
class MyServiceIntegrationTest {
    
    @Autowired
    private MyService myService;
    
    @Test
    @Transactional
    void should_perform_integration_test() {
        // Test implementation
    }
}
```

### Database Configuration
- Use H2 database to avoid impact on real data
- Apply `@Transactional` at method level for proper cleanup
- Use repository methods that match actual implementation


## CI/CD Integration

### Recommended Pipeline Steps
1. **Fast feedback**: Run unit tests first (`./gradlew test`)
2. **Integration validation**: Run integration tests (`./gradlew integrationTest`)
3. **Full validation**: Optionally run all tests (`./gradlew testAll`)

### Environment-Specific Execution
```bash
# Development - quick feedback
./gradlew test

# Pre-commit - comprehensive testing
./gradlew testAll

# CI/CD - staged approach
./gradlew test && ./gradlew integrationTest
```

## Troubleshooting

### Common Issues
1. **Integration tests not found**: Ensure tests are in `integrationTests` package
2. **Compilation errors**: Check that integration tests use proper Java syntax
3. **Profile not active**: Verify `@ActiveProfiles("integration-test")` annotation

### Debugging
```bash
# Run with debug information
./gradlew integrationTest --info --debug

# Check test discovery
./gradlew integrationTest --dry-run
```

## Migration Notes

### Existing Integration Tests
- All existing integration tests in `integrationTests` folder are automatically included
- No code changes required for test logic
- Fixed compilation issues (e.g., Lombok `val` → Java `Field`)

### Future Integration Tests
- Place new integration tests in `integrationTests` package
- Follow established patterns and naming conventions
- Use appropriate annotations and profiles


