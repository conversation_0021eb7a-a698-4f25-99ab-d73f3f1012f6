rootProject.name = "timeoff-service"

include(
    "app",
    "schema"
)

dependencyResolutionManagement {
    versionCatalogs {

        create("multiplier") {
            version("multiplier-starter", "2.3.2")
            version("grpc-common", "1.2.24")

            library("platform.spring-starter", "com.multiplier.platform", "spring-starter").versionRef("multiplier-starter")
            library("platform.spring-job-starter", "com.multiplier.platform", "spring-jobs-starter").versionRef("multiplier-starter")

            library("grpc-common", "com.multiplier.grpc", "grpc-common").versionRef("grpc-common")
            library("grpc-bulkupload", "com.multiplier.grpc", "grpc-bulkupload").versionRef("grpc-common")
        }

        create("libs") {
            version("kotlin", "2.1.0")
            version("grpc", "1.46.0")
            version("shedlock", "4.36.0")
            version("grpcKotlin", "1.4.1")
            version("protobuf", "3.25.5")
            version("testcontainers", "1.17.2")
            // Overriding access-control version to use the updated version
            version("access-control", "3.0.11")

            plugin("kotlin-jvm", "org.jetbrains.kotlin.jvm").versionRef("kotlin")
            plugin("kotlin-plugin-spring", "org.jetbrains.kotlin.plugin.spring").versionRef("kotlin")
            plugin("kotlin-plugin-jpa", "org.jetbrains.kotlin.plugin.jpa").versionRef("kotlin")
            plugin("kotlin-plugin-lombok", "org.jetbrains.kotlin.plugin.lombok").versionRef("kotlin")
            plugin("spring-boot", "org.springframework.boot").version("3.3.12")
            plugin("protobuf", "com.google.protobuf").version("0.9.4")
            plugin("liquibase", "org.liquibase.gradle").version("2.2.0")
            plugin("sonarqube", "org.sonarqube").version("4.4.1.3373")

            // Multiplier
            library("multiplier.growthbook-sdk", "com.multiplier", "growthbook-sdk").version("1.0.3")
            // Overriding access-control version to use the updated version
            library("access-control-spring", "com.multiplier", "access-control-spring").versionRef("access-control")

            library("multiplier.core-service-schema", "com.multiplier", "core-service-schema").version("1.1.167")

            library("multiplier.contract-service-schema", "com.multiplier", "contract-service-schema").version("1.14.132")
            library("multiplier.country-service-schema", "com.multiplier", "country-service-schema").version("0.2.24")
            library("multiplier.member-service-schema", "com.multiplier", "member-service-schema").version("1.0.97")
            library("multiplier.org-management-service-schema", "com.multiplier", "org-management-service-schema").version("0.0.25")
            library("multiplier.company-service-schema", "com.multiplier", "company-service-schema").version("3.5.96")
            library("multiplier.pigeon-service-schema", "com.multiplier", "pigeon-service-schema").version("1.0.3")
            library("multiplier.pigeon-service-client", "com.multiplier", "pigeon-service-client").version("1.0.3")
            library("multiplier.timeoff-service-graph", "com.multiplier", "timeoff-service-graph").version("1.14.716")
            library("multiplier.leave-compliance-service-schema", "com.multiplier", "leave-compliance-service-grpc-schema").version("0.0.6")
            library("multiplier.ai", "com.multiplier.ai", "sdk").version("SDK-3-SNAPSHOT")

            // Kotlin
            library("kotlin-reflect", "org.jetbrains.kotlin", "kotlin-reflect").withoutVersion()
            library("kotlin-stdlib-jdk8", "org.jetbrains.kotlin", "kotlin-stdlib-jdk8").withoutVersion()
            library("kotlinx-coroutines-core", "org.jetbrains.kotlinx", "kotlinx-coroutines-core").withoutVersion()

            // Spring
            library("spring-boot-starter-actuator", "org.springframework.boot", "spring-boot-starter-actuator").withoutVersion()
            library("spring-boot-starter-web", "org.springframework.boot", "spring-boot-starter-web").withoutVersion()
            library("spring-boot-starter-security", "org.springframework.boot", "spring-boot-starter-security").withoutVersion()
            library("spring-boot-starter-validation", "org.springframework.boot", "spring-boot-starter-validation").withoutVersion()
            library("spring-boot-starter-data-jpa", "org.springframework.boot", "spring-boot-starter-data-jpa").withoutVersion()
            library("spring-cloud-starter-openfeign", "org.springframework.cloud", "spring-cloud-starter-openfeign").withoutVersion()
            library("spring-kafka", "org.springframework.kafka", "spring-kafka").version("3.0.8")
            library("spring-cloud-dependencies", "org.springframework.cloud", "spring-cloud-dependencies").version("2023.0.1")
            library("spring-boot-starter-thymeleaf", "org.springframework.boot", "spring-boot-starter-thymeleaf").withoutVersion()
            library("spring-boot-starter-mail", "org.springframework.boot", "spring-boot-starter-mail").withoutVersion()
            library("spring-boot-devtools", "org.springframework.boot", "spring-boot-devtools").withoutVersion()
            library("spring-boot-configuration-processor", "org.springframework.boot", "spring-boot-configuration-processor").version("2.6.7")

            // GraphQL
            library("graphql-dgs-spring-boot-starter", "com.netflix.graphql.dgs", "graphql-dgs-spring-boot-starter").version("9.1.3")
            library("graphql-java-extended-scalars", "com.graphql-java", "graphql-java-extended-scalars").version("22.0")

            // Serialization
            library("json", "org.json", "json").version("20231013")
            library("kafka-protobuf-serde", "com.github.daniel-shuy", "kafka-protobuf-serde").version("2.2.0")
            library("opencsv", "com.opencsv", "opencsv").version("5.5.2")
            library("poi-ooxml", "org.apache.poi", "poi-ooxml").version("5.4.0")

            // Database
            library("hibernate-envers", "org.hibernate.orm", "hibernate-envers").withoutVersion()
            library("hibernate-types", "io.hypersistence", "hypersistence-utils-hibernate-63").version("3.7.5")
            library("postgresql", "org.postgresql", "postgresql").withoutVersion()

            // Shedlock
            library("shedlock-spring", "net.javacrumbs.shedlock", "shedlock-spring").versionRef("shedlock")
            library("shedlock-provider-jdbc-template", "net.javacrumbs.shedlock", "shedlock-provider-jdbc-template").versionRef("shedlock")


            // Liquibase
            library("liquibase-core", "org.liquibase", "liquibase-core").version("4.8.0")
            library("liquibase-hibernate5", "org.liquibase.ext", "liquibase-hibernate6").withoutVersion()

            // GRPC
            library("grpc-spring-boot-starter", "net.devh", "grpc-spring-boot-starter").version("3.1.0.RELEASE")
            library("grpc-kotlin-stub", "io.grpc", "grpc-kotlin-stub").versionRef("grpcKotlin")
            library("grpc-protobuf", "io.grpc", "grpc-protobuf").versionRef("grpc")
            library("protobuf-kotlin", "com.google.protobuf", "protobuf-kotlin").versionRef("protobuf")


            // other dependencies
            library("problem-spring-web", "org.zalando", "problem-spring-web").version("0.29.1")
            library("apache-commons-text", "org.apache.commons", "commons-text").version("1.10.0")
            library("apache-commons-compress", "org.apache.commons", "commons-compress").version("1.26.0")
            library("io-vavr", "io.vavr", "vavr").version("0.10.4")
            library("io-jsonwebtoken-jjwt-api", "io.jsonwebtoken", "jjwt-api").version("0.11.2")
            library("io-jsonwebtoken-jjwt-impl", "io.jsonwebtoken", "jjwt-impl").version("0.11.2")
            library("io-jsonwebtoken-jjwt-jackson", "io.jsonwebtoken", "jjwt-jackson").version("0.11.5")
            library("netty-common", "io.netty", "netty-common").version("4.1.118.Final")
            library("apache-kafka-clients", "org.apache.kafka", "kafka-clients").version("3.9.1")

            // Logging
            library("janino", "org.codehaus.janino", "janino").version("3.1.8") // Logback conditions
            library("logstash-logback-encoder", "net.logstash.logback", "logstash-logback-encoder").version("7.2") // Structured (JSON) logging
            library("kotlin-logging-jvm", "io.github.microutils", "kotlin-logging-jvm").version("3.0.5")

            // Test
            library("spring-boot-starter-test", "org.springframework.boot", "spring-boot-starter-test").withoutVersion()
            library("mapstruct", "org.mapstruct", "mapstruct").version("1.4.2.Final")
            library("mockito-kotlin", "org.mockito.kotlin", "mockito-kotlin").version("4.1.0")
            library("mockito-inline", "org.mockito", "mockito-inline").version("4.6.1")
            library("h2database", "com.h2database", "h2").version("2.1.214")
            library("com-ninja-squad", "com.ninja-squad", "springmockk").version("3.1.1")
            library("spring-boot-starter-webflux", "org.springframework.boot", "spring-boot-starter-webflux").version("2.7.1")
            library("spring-kafka-test", "org.springframework.kafka", "spring-kafka-test").withoutVersion()
            library("javafaker", "com.github.javafaker", "javafaker").version("1.0.2")
            library("testcontainers", "org.testcontainers", "testcontainers").versionRef("testcontainers")

        }
    }
}